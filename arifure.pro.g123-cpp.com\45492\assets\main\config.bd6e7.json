{"importBase": "import", "nativeBase": "native", "name": "main", "deps": [], "uuids": ["f10DPKHZZJDo5Lr/2Ntbvs", "1e1583de8", "2f/iTwe7BCIL3XlsN4UxGF", "61Haos/flMWJILVDOLjlgW", "baIUdvKGZPgZxNbjWTFuRI", "c6iudGYnhEk4LB2kqw6cFt", "d5j4AhkolNnqsIP/ovqElw", "e5RkyYIENDIppjKmhP+Vjj", "ebbk0fcxRCCJp/i2gFY3jp", "fdjsU2o1RKF5x0TziDw3jI", "00FQ8HZNxMvI032NfekWDu@f9941", "00rQgXDxFHZLDJjtkkR0gN@f9941", "01E/QU/aZMyqKnEs8lgXVy@f9941", "0722e7a18", "08peoyqBhHzL9an9EmP+/A@f9941", "09NcmwUH5Crq3a3RZj5JIz@f9941", "09cJ+x8Y9CPIqHucDHULRp@f9941", "0cb37b8e1", "0e1583de8", "10OsfQjaFNJaMTZM6vHfZ4@f9941", "1186obsthM87MGDf1a/3L5@f9941", "12uUUHFUdH85ozuUL+6N2G@f9941", "133Go4W9FG7bSheiWas6BW@f9941", "176oGn9fFKx6wMQkjDwTPH@f9941", "1dezwRWiVLIIdsyCyckem9@f9941", "1e1583de8@6c48a", "1fXA/vfSRALZvSgO3J37cj@f9941", "1f9K7lkWBO4LXWQWQz5YCN@f9941", "20X3oOp+BPu72bVuyNMvvt@f9941", "21ROW13ExId41J90S/udTL@f9941", "22GZ9DJdFEjZ2Dt0DYmu/G@f9941", "22qajbi3tOlp52fZRD3B6i@f9941", "259kqkpGFJ7pu5HmFqKOmg@f9941", "26/7FJl6lLjpK8paW3Ep0F@f9941", "28bfwm51dOHoLVvMxcWZRh@f9941", "2fWv8d2+xFgLaYFWkxKM1E@f9941", "2f/iTwe7BCIL3XlsN4UxGF@6c48a", "2f/iTwe7BCIL3XlsN4UxGF@f9941", "31RSWN9hFGPpk4kIvziOTq@f9941", "31m6oJF7JAy5Wknpx5jo/x@f9941", "34WOL0JpxJB4DLWkVhIawu@f9941", "36t4JL9etGRI/B9fFz27RY@f9941", "37YGicNrNCQJsrA30gOdPK@f9941", "3eneJcjvJEOJCNzrf5Rg2t@f9941", "3fnJCq3hZPRqAusq6KiVus@f9941", "42tOHJLvNOELAAucw44kXS@f9941", "47nZzjUcdO3osa+hxOjfG+@f9941", "49QPmm2n1FjqSGDS7ynOOF@f9941", "49iZwBVTJM35ZOGIgkKB5N@f9941", "4aT1r8aClPVYO9p3I51VUW@f9941", "4dEYtPXa9DIYIQAkdmByx6@f9941", "4dfUWCUhZH6rpiHgls7e8S@f9941", "4e93fZBeBHs77FDSVQ/BYu@f9941", "52T+cWRYpOO66Qp0bzESMq@f9941", "56huHWslpGBKhslaaBwpny@f9941", "5aeAp8lLlHfoUa5QR5V1Im@f9941", "61Haos/flMWJILVDOLjlgW@6c48a", "61Haos/flMWJILVDOLjlgW@f9941", "63RlDqLw9NmachCV1hpDIo@f9941", "69kQDuSEBNsZSlF+4EaTHn@f9941", "6bkRuePQJBe5K6Oo/UJdOG@f9941", "6eS+ZYOfhKxp5Ruj1Dh4U+@f9941", "70tm3f1pdMdbULs6FjfOH1@f9941", "726c6s8LNKX7Jq1LT4X42T@f9941", "735j4kNjVO5paEbEQAk1Ur@f9941", "74uLs4uJdAXaEjirKQsPCV@f9941", "78JOA1N+tK1pIDG1G4MSwR@f9941", "7c6yUrSyNAaY1aGkTcD7qf@f9941", "7dTFBE6vFJza1Lpwo4w24i@f9941", "7f9qsqhpJN0K62rvhup9Yj@f9941", "86gnoxNAVGHKoMShFcHdq7@f9941", "87ks68my9EGpW+CjbARA/D@f9941", "8dlaOoohZHbpVKCe9+rzot@f9941", "8dnkU6sIlL0btiKEM3lxby@f9941", "8eDH3REVRJcqVanKsWO2jq@f9941", "8esgF/4NJKA4U67TKFSc9s@f9941", "91r+DCnc1D7L49HhrhpSJh@f9941", "96OjaSTftMQZK+jRVR3PQj@f9941", "9696EGZY5DXoBZXqIU6bO/@f9941", "979slcPNRGc5NyAmAQsKsJ@f9941", "98k1PtEW9Ooo0VQAU4EUAi@f9941", "a20AwQ4PtLbavCkaJS2vz2@f9941", "a46r8crUpB9pOcyQktP1nK@f9941", "a706xo1xJKqJZtYLtvX9lJ@f9941", "a8BHAzCMpKSYmU2h3yqNr1@f9941", "a9otIiXNZOyJZEwA2vjT2y@f9941", "aad+cCVBNN5avEaDfwTpHP@f9941", "acXp2+zypBwoe4IitqLzBJ@f9941", "bcYSR+eAtFMKASehs/vR6N@f9941", "c5KY/CZehGWbfx1W9EvB3R@f9941", "c54W2phb1GaooGQ7gb3wCQ@f9941", "c6iudGYnhEk4LB2kqw6cFt@6c48a", "c6iudGYnhEk4LB2kqw6cFt@f9941", "ceMBITAB1LZqEnciuRrDwe@f9941", "cfdiXHt0tFgLmOqAa6Bjjk@f9941", "d1idkd38lCMYjlLdHlQS91@f9941", "d2QdHc53lO9az/kVxCzcZW@f9941", "d3MsCkRllNh6SO92swRx80@f9941", "d3h38ABDhPg67xtIYZpTc+@f9941", "d4Z8bbIStECqiEpiunvwxY@f9941", "d5j4AhkolNnqsIP/ovqElw@6c48a", "d5j4AhkolNnqsIP/ovqElw@f9941", "dcwUZqxgJDXLyPwA3lVD1P@f9941", "dd3Frbq/FPSIa0ba3HdY8F@f9941", "dexeEAq8pGmYgqs31xiVMq@f9941", "e1/bYaYoRKE7OSThIWyImA@f9941", "e2ISDa1rVBZYJQfdjA+2Lj@f9941", "e5RkyYIENDIppjKmhP+Vjj@6c48a", "e5RkyYIENDIppjKmhP+Vjj@f9941", "e7fzb+p+lPe5BlWygcXn6z@f9941", "e8aSAUb2VM3r6QKkhyxrUb@f9941", "eaGl8HIN1G2JUzOm7C28kz@f9941", "ebbk0fcxRCCJp/i2gFY3jp@6c48a", "ebbk0fcxRCCJp/i2gFY3jp@f9941", "edcrr/RYdO/I7T9po4gdvy@f9941", "eduTpsj8JAdI2Zgza+7Ivt@f9941", "f2+QyLTstN9rZlVpHztw/d@f9941", "f5v5lpoolH263ronk4iTG3@f9941", "fcvMzb23hNTYCq7fJi7q4M@f9941", "fc5KYzrxRGZZFwHFncwS3y@f9941"], "paths": {"0": ["db:/assets/Main", 0, 1], "4": ["db:/internal/physics/default-physics-material", 2, 1], "9": ["db:/internal/default_renderpipeline/builtin-forward", 1, 1]}, "scenes": {"db://assets/Main.scene": 0}, "packs": {"0722e7a18": [25, 36, 56, 91, 100, 107, 112], "0cb37b8e1": [37, 57, 92, 101, 108, 113, 0], "0e1583de8": [10, 11, 12, 14, 15, 16, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 93, 94, 95, 96, 97, 98, 99, 102, 103, 104, 105, 106, 109, 110, 111, 114, 115, 116, 117, 118, 119]}, "versions": {"import": [13, "9dc7d", 17, "d8f24", 18, "0027a", 1, "7a760", 2, "698b0", 3, "3aed3", 4, "8bce7", 5, "3aed3", 6, "3aed3", 7, "3aed3", 8, "698b0", 9, "731f4"], "native": [1, "3a9d3", 2, "23516", 3, "d5673", 5, "65097", 6, "7735b", 7, "f03a8", 8, "d46c2"]}, "redirect": [], "debug": false, "extensionMap": {}, "hasPreloadScript": true, "dependencyRelationships": {}, "types": ["cc.SceneAsset", "cc.RenderPipeline", "cc.PhysicsMaterial"]}