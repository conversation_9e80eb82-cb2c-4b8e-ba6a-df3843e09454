<!DOCTYPE html><html><head>
  <meta charset="utf-8"/>

  <title>平凡职业造就世界最强</title>
  <link rel="icon" href="./des.ico"/>
  <link rel="apple-touch-icon" href="./des.ico"/>
  <link rel="apple-touch-icon-precomposed" href="./des.ico"/>
  
  

  <script>
    document.write("<script async id='flashlaunch-script' data-appid='arifure' data-appkey='0c1cca77c2c8d67052e3519c316e15ad' src='https://fl.g123.jp/flash-sdk.js?v=0.1.5'><\/script>");
  </script>
<script>
        if(location.href.indexOf(".pro.")!=-1&& location.href.indexOf("lang") != -1){
            document.write("<script src='https://platform-sc.g123.jp/cp-sdk/g123-cp-sdk.umd.js'><\/script>")
        }else if(location.href.indexOf(".stg.")!=-1&& location.href.indexOf("lang") != -1){
            document.write("<script src='https://platform-sc.stg.g123.jp/cp-sdk/g123-cp-sdk.umd.js'><\/script>")
        }
        </script>
  <script type="text/javascript" src="difflibrary/nosleep/NoSleep.min.js"></script>

  <!--http://www.html5rocks.com/en/mobile/mobifying/-->
  <meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1,minimum-scale=1,maximum-scale=1,minimal-ui=true"/>

  <!--https://developer.apple.com/library/safari/documentation/AppleApplications/Reference/SafariHTMLRef/Articles/MetaTags.html-->
  <meta name="apple-mobile-web-app-capable" content="yes"/>
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/>
  <meta name="format-detection" content="telephone=no"/>

  <!-- force webkit on 360 -->
  <meta name="renderer" content="webkit"/>
  <meta name="force-rendering" content="webkit"/>
  <!-- force edge on IE -->
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
  <meta name="msapplication-tap-highlight" content="no"/>

  <!-- force full screen on some browser -->
  <meta name="full-screen" content="yes"/>
  <meta name="x5-fullscreen" content="true"/>
  <meta name="360-fullscreen" content="true"/>

  <!--fix fireball/issues/3568 -->
  <!--<meta name="browsermode" content="application.a8bbb.js">-->
  <meta name="x5-page-mode" content="app"/>

  <!--<link rel="apple-touch-icon" href=".png" />-->
  <!--<link rel="apple-touch-icon-precomposed" href=".png" />-->

  <link rel="stylesheet" type="text/css" href="style.f76d1.css"/>

</head>
<body>
  <div id="GameDiv" cc_exact_fit_screen="true">
      <div id="Cocos3dGameContainer">
        <canvas id="GameCanvas" oncontextmenu="event.preventDefault()" tabindex="99"></canvas>
      </div>
    </div>
  

<!-- Polyfills bundle. -->

<script src="src/polyfills.bundle.5adbf.js" charset="utf-8"> </script>


<!-- SystemJS support. -->
<script src="src/system.bundle.543e6.js" charset="utf-8"> </script>

<!-- Import map -->
<script src="src/import-map.56970.json" type="systemjs-importmap" charset="utf-8"> </script>

<script>
    System.import('./index.a56a2.js').catch(function(err) { console.error(err); })
</script>

<script type="text/javascript">
            var noSleep = new NoSleep();
            document.getElementById("GameCanvas").ontouchstart = document.getElementById("GameCanvas").onclick = function() {
              if(!noSleep.isEnabled) {
                noSleep.enable();
              }
            }
        </script>
  

</body></html>