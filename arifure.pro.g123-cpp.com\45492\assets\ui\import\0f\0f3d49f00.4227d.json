[1, ["69/GUg+utEoLhb9e2vW8ZZ@f9941", "e7FAotE69J1KEBRuhMf5jy@f9941", "d1Vw10eFBP3rXm9M08+EJ8@f9941", "1aAO/6bPRLs4lqbV2KX3Ze@f9941", "24elrkpUpBOZBu64+qgA8f@f9941", "b0npEaocVKQZe7wW0GMNiV", "452UFDwKpE4bt1Cz4gi+AT", "cb1oWWMORD+4OS4th8cbY2@f9941", "10I8iCpWVA062UoMj6NsQ4", "f3AYpggYZEiK67KPDWT0hv@f9941", "c5SF+6ttVHZqzYYwTBRTWe@f9941", "6fyCV8EPxFPqzUrKP7RT3k@f9941", "28Q0xYmUZJhK1J8EBugU7q@f9941", "d0wtX26ixDn7WbwgKp8gpo@f9941", "c4M6BY8ElGkrG3JIc9PnvF@f9941", "4eRJzyLx1II6mW+SIwxRx0@f9941", "fdbAyk+BpO2p+EDIGKF/M8@f9941", "64lSvS+G5B8byIQUc3dbfv@f9941", "28bfwm51dOHoLVvMxcWZRh@f9941", "7etddWZ5VJzrth0EsH83Xx@f9941", "1b8OuJK3NGaI0tBJZgGTm0@f9941", "bbETP+byhBN4TeISsx9XKY@f9941", "03b1pJ5DtMCIDN2E0plpZs@f9941", "9eqPkL9mhA6YFtq5FVdLrZ@f9941", "6fTC0HCGlJqYQcrdEzq9zk@f9941", "57TVhXLx9EcbwrJXsxDvaN@f9941", "97ZFBd01RNdYwCnWnrpqC8@f9941", "2112wG6KpDs6YEV51xMGny@f9941", "388m/lvPhKnbUnqte4IpN2@f9941", "8aORR6phJB4rRZNVpu9/t5@f9941", "adiDL4L1dM/JtzbYekpyuo@f9941", "59kOVJaLpOIalZ/4Xd3NgB", "26/7FJl6lLjpK8paW3Ep0F@f9941", "f8SmaapW5AKoNI0q/gflKi@f9941", "64v14TOS5OxJu79yyEpkdw@f9941", "ac9IQ6s49Np6mn44GoTwYF@f9941", "aeQe0+4dpDirHC85eVadeQ@f9941", "03MK4DP3tGNJJyWLhNxkJf@f9941", "90r61kHORJx4XNropnnVgh@f9941", "bfHzqmw3ZLVaI9b5kAbq1/@f9941", "0ejkkMN61Dx697e1EQO0IK@f9941", "4cnL3vqgxJ3Yg452+iRCt9", "b33LS0mUlGY7fYq7Zm7em3", "c69/1J7Y9CU4Njtd5e0g5w", "e1nudC4xJKxZkrt1OL9jlp@f9941", "4bvUePSW1My72lVB0hkqFh", "fcfATM00NP6aMUfMeuFPxG", "02Y4Iu7TJLZ77hMnN0WXvM", "4cfntbF3VLbr2IbmbJDecV", "c2xKWYJGJC6rDgMPn+Jw4h@f9941", "48yTckjNZFyoA106oeDn4r", "d2YRdRZ5NMT5O9QwcUGHSe", "b72HBuaZ1A+r7B/MAtOmD0@f9941", "08SfNwBGtCirYcqN3UUgq8@f9941", "08SfNwBGtCirYcqN3UUgq8@6c48a", "fcETiZw+BJzZLUCSkuvU8q@f9941", "31Ew+3Q2lBd7FbsJ6tBubI@f9941", "68+boD6RJMnqhlK7irZ1NS@f9941", "a2wtSHnrJCBqaFoXZ2V/hZ@f9941", "1ef2nvtFBCCL33lxTSXy7m@f9941", "35Ae/Kd6tGuapXHCd/eFGk@f9941", "7etddWZ5VJzrth0EsH83Xx@6c48a", "755kBSZslFQrePM3Zj5bTT@f9941", "90BayPteNMsIyD5sD73f+Y@f9941", "e1traprj1M+bRWjuiBsm1+@f9941", "c7T+QyLOpKjqw+1r4SKLuX@f9941", "2fEAn4+utIt6DaGXrsIo+E@f9941", "ea9eMMjKNOQLGhvMqCH4iL@f9941", "062cyJU4tJmb3VGkwFIuuH", "9dt9erSkBDUbU+Pa74Ge5t@f9941", "7aOgSZw4NLTKl4HGfRCacd@f9941", "55lmbq019GbYTNhlYp79sE@f9941", "b72HBuaZ1A+r7B/MAtOmD0@6c48a", "80TUjPz1xPG5JmWhrNFStf@f9941"], ["node", "_spriteFrame", "spriteFrame", "root", "targetInfo", "asset", "data", "_parent", "_target", "_defaultClip", "value", "target", "_textureSource", "source", "_content", "_skeletonData"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_lpos", "_children", "_lscale", "_lrot", "_euler"], -2, 4, 9, 1, 5, 2, 5, 5, 5], ["cc.Label", ["_string", "_actualFontSize", "_isBold", "_lineHeight", "_enableOutline", "_outlineWidth", "_fontSize", "_overflow", "_enableWrapText", "_horizontalAlign", "_verticalAlign", "node", "__prefab", "_outlineColor", "_color"], -8, 1, 4, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "__prefab", "_spriteFrame", "_color"], -1, 1, 4, 6, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_affectedByScale", "_spacingY", "node", "__prefab"], -2, 1, 4], ["StatusData", ["status", "fileId", "label_font", "gradient_material", "active", "grayscale", "rotation", "scale", "size", "position", "spriteFrame"], -3, 5, 5, 5, 5, 6], "cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "_prefab", "_parent", "_components", "__editorExtras__", "_children", "_lpos", "_lscale"], 0, 4, 1, 12, 11, 2, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_verticalCenter", "_bottom", "node", "__prefab"], -2, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedComponents", "mountedChil<PERSON>n", "removedComponents"], 2, 1, 9, 9, 9, 9], ["cc.TargetOverrideInfo", ["propertyPath", "target", "targetInfo", "source", "sourceInfo"], 2, 1, 4, 1, 4], ["cc.Animation", ["playOnLoad", "node", "__prefab", "_clips", "_defaultClip"], 2, 1, 4, 3, 6], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_normalColor", "_target"], 1, 1, 4, 5, 1], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["sp.Skeleton", ["_premultipliedAlpha", "_preCacheMode", "defaultSkin", "node", "__prefab", "_skeletonData"], 0, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetInfo", ["localID"], 2], ["cc.UIOpacity", ["node", "__prefab"], 3, 1, 4], ["7563fP+WuxL1p5rM8Q2NGlY", ["node", "__prefab", "spfs"], 3, 1, 4, 3], ["7563fP+WuxL1p5rM8Q2NGlY", ["spfs", "node", "__prefab"], 2, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["011c8MZ++JCbqPChKjiX2MO", ["_statusIndex", "currStatusName", "statusNameArray", "node", "__prefab", "statusNodes", "statusData"], 0, 1, 4, 2, 9], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 2], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 6], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 6], ["cc.MountedChildrenInfo", ["targetInfo", "nodes"], 3, 4, 2], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4], ["<PERSON><PERSON>", ["bounceDuration", "brake", "vertical", "node", "__prefab", "_content"], 0, 1, 4, 1], ["ad253TrMIpHFZgL3Bp6KTbe", ["node", "__prefab", "tmpNode", "pageChangeEvent", "renderEvent", "selectedEvent"], 3, 1, 4, 1, 4, 4, 4], ["cc.ClickEvent", [], 3]], [[16, 0, 2], [18, 0, 1, 2, 3, 4, 5, 5], [20, 0, 2], [7, 0, 1, 2, 1], [30, 0, 1, 2, 3], [31, 0, 1, 2, 2], [29, 0, 1, 2, 3], [28, 0, 1, 2, 2], [2, 0, 4, 5, 6, 2], [0, 0, 1, 7, 6, 5, 8, 3], [0, 0, 1, 7, 6, 5, 8, 10, 3], [2, 4, 5, 6, 1], [4, 0, 1, 2, 3, 9, 6, 7, 8, 10, 5], [7, 0, 1, 2, 3, 1], [19, 0, 1, 2, 3, 4, 5, 4], [0, 3, 4, 7, 5, 3], [22, 0, 1, 2, 1], [9, 0, 1, 2, 2], [4, 0, 1, 4, 2, 3, 9, 6, 7, 8, 10, 6], [0, 0, 1, 9, 6, 5, 3], [15, 0, 2], [0, 0, 1, 7, 6, 5, 3], [4, 0, 1, 4, 2, 3, 6, 7, 8, 10, 6], [0, 0, 1, 7, 9, 6, 5, 8, 3], [12, 0, 1, 2, 3, 4, 5, 3], [0, 0, 2, 1, 7, 9, 6, 5, 8, 4], [0, 0, 2, 1, 7, 6, 5, 4], [2, 2, 0, 4, 5, 6, 3], [35, 0, 1, 2, 2], [0, 0, 1, 7, 9, 6, 5, 8, 10, 3], [0, 0, 2, 1, 7, 6, 5, 8, 11, 12, 4], [2, 4, 5, 1], [21, 0, 1, 1], [26, 0, 1, 2, 1], [0, 0, 1, 7, 9, 6, 5, 3], [0, 0, 2, 1, 7, 6, 5, 8, 4], [0, 0, 2, 1, 7, 6, 5, 8, 10, 4], [3, 5, 6, 1], [4, 0, 1, 2, 3, 6, 7, 8, 10, 5], [4, 0, 1, 4, 2, 3, 9, 6, 7, 8, 6], [13, 1, 2, 1], [0, 0, 1, 9, 6, 5, 8, 3], [0, 0, 1, 7, 6, 5, 10, 3], [8, 0, 1, 2, 5, 6, 4], [2, 0, 4, 5, 2], [11, 0, 1, 2, 3, 4, 2], [3, 0, 1, 2, 5, 6, 4], [25, 0, 1, 2, 3, 4, 5, 6, 4], [32, 0, 1, 2, 2], [38, 1], [0, 0, 2, 1, 7, 9, 6, 5, 4], [0, 0, 2, 1, 7, 6, 5, 8, 11, 10, 12, 4], [7, 0, 1, 3, 1], [10, 0, 3, 4, 1, 2, 2], [2, 1, 4, 5, 6, 2], [2, 0, 1, 4, 5, 6, 3], [2, 3, 4, 5, 6, 2], [11, 1, 2, 3, 4, 1], [3, 0, 1, 2, 3, 5, 6, 5], [3, 0, 2, 5, 6, 3], [24, 0, 1, 1], [12, 0, 1, 2, 3, 3], [4, 0, 1, 2, 3, 9, 6, 7, 8, 5], [4, 0, 1, 5, 2, 3, 9, 6, 7, 8, 10, 6], [27, 0, 1, 1], [1, 0, 9, 1, 6, 3, 7, 8, 2, 4, 5, 11, 12, 13, 11], [1, 0, 1, 6, 3, 7, 2, 4, 5, 11, 12, 13, 9], [1, 0, 1, 3, 7, 2, 4, 5, 11, 12, 14, 8], [36, 0, 1, 2, 3, 4, 5, 4], [14, 0, 1, 3, 4, 3], [0, 3, 4, 5, 3], [0, 0, 2, 1, 9, 6, 5, 8, 4], [0, 0, 2, 1, 7, 9, 6, 5, 10, 4], [0, 0, 1, 9, 6, 5, 8, 10, 3], [6, 2, 3, 6, 2], [6, 0, 1, 4, 7, 5, 3, 3], [6, 0, 1, 4, 5, 3, 8, 9, 3], [8, 0, 3, 5, 6, 3], [8, 0, 4, 5, 6, 3], [17, 0, 1, 2, 3, 4, 5, 3], [10, 0, 1, 2, 2], [2, 2, 0, 4, 5, 3], [2, 1, 4, 5, 2], [2, 0, 4, 5, 7, 6, 2], [23, 0, 1, 2, 2], [3, 0, 1, 4, 3, 5, 6, 5], [3, 0, 1, 5, 6, 3], [3, 0, 1, 2, 4, 5, 6, 5], [13, 0, 1, 2, 2], [9, 0, 1, 3, 2, 2], [9, 0, 1, 4, 3, 2, 5, 2], [33, 0, 1, 2, 2], [34, 0, 1, 1], [1, 0, 1, 6, 3, 2, 11, 12, 14, 6], [1, 0, 1, 3, 2, 4, 5, 11, 12, 7], [1, 0, 1, 6, 8, 2, 4, 11, 12, 14, 7], [1, 0, 10, 1, 3, 7, 2, 4, 5, 11, 12, 9], [1, 0, 1, 6, 3, 7, 8, 2, 4, 5, 11, 12, 14, 13, 10], [1, 0, 1, 6, 3, 2, 4, 5, 11, 12, 13, 8], [1, 0, 1, 6, 3, 2, 4, 11, 12, 13, 7], [1, 0, 1, 6, 3, 7, 2, 11, 12, 14, 7], [1, 0, 9, 1, 6, 3, 7, 2, 4, 5, 11, 12, 13, 10], [1, 0, 1, 3, 2, 4, 5, 11, 12, 13, 7], [1, 0, 1, 6, 3, 8, 2, 4, 5, 11, 12, 13, 9], [1, 0, 1, 3, 2, 4, 5, 11, 12, 14, 13, 7], [14, 2, 0, 1, 3, 4, 5, 4], [37, 0, 1, 2, 3, 4, 5, 1]], [[[[20, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [19, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", 33554432, [-22], [[3, -20, [0, "6fUyudUT1IFo6FvRHKFGRJ"], [5, 640, 1280]], [43, 45, 720, 1280, -21, [0, "6b1cvTbIZFyJGZXVrsGtve"]]], [79, "95ptoTxBhATrAjGlSVUme9", null, -19, 0, [[80, ["tmpNode"], -14, [2, ["25jeScHL9Bw62w3lnrjz/K"]]], [53, ["statusNodes", "0"], -16, [2, ["5aCWZzUQFB/KTItqF64ZHr"]], -15, [2, ["5eZAD6dg1PkIthJGeTXCdA"]]], [53, ["statusNodes", "1"], -18, [2, ["5aCWZzUQFB/KTItqF64ZHr"]], -17, [2, ["5cvq8qCo5I7pXjdLcFwDsb"]]]], [-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13]]], [23, "bg_top_hb1", 33554432, 1, [-29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50], [[3, -23, [0, "47Cs6IYElOBK5jyf6MFKqL"], [5, 554, 1009]], [8, 0, -24, [0, "8alF/hURBNQI7ZB7VkpNvQ"], 65], [77, 2, 40, -25, [0, "9cuLwX7iRKEaoddALs0/+J"]], [45, true, -26, [0, "9fcSDWfM1JK5oFSCrHUd1x"], [66, 67, 68], 69], [32, -27, [0, "edpn/ApyxCH5GdO4SeeEFv"]], [16, -28, [0, "bejeOf/ylHhJM3zVkTBdxT"], [70, 71, 72]]], [1, "79BcNar8tDmZucBHLjoa8/", null, null, null, 1, 0], [1, 0, 40, 0]], [23, "neirong", 33554432, 2, [-54, -55, -56, -57, -58, -59, -60, -61], [[13, -51, [0, "a3QgYPOkZJ4KVNFx4r+tnU"], [5, 598, 388], [0, 0.5, 0]], [37, -52, [0, "0e6gSAIQlP2qHwwFh4Xxv5"]], [60, -53, [0, "94OxpFeqNFJ6siBNfCa2KT"]]], [1, "cerzioAkRI6529DDgCbJQ+", null, null, null, 1, 0], [1, 0, -538, 0]], [29, "btn_sq1", 33554432, 2, [-71, -72, -73], [[3, -62, [0, "33zqFWhplJnobvN+bVL/nW"], [5, 133, 140]], [27, 1, 0, -63, [0, "800z5w+upKQr1ChEwQm5oS"], 19], [24, 3, 0.9, -65, [0, "743u9tSGRB27zbpKvDlxFN"], [4, 4292269782], -64], [47, 0, "常态", ["常态", "空", "锁"], -70, [0, "73U1fON0JKwYUt7MKrMkmh"], [-66, -67, -68, -69], [[12, "常态", "e80ZBkmKZFlJa5mjqHZWrE", null, null, [1, -210, -70, 0], [3, 0, 0, 0, 1], [1, 0.8, 0.8, 1], [0, 133, 140], 20], [22, "常态", "a5A/97R+tAuqG2XnleHHgi", false, null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 40, 49], 21], [22, "常态", "6ebRKxIjBBwLK+K0gS6sjZ", false, null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 48, 48], 22], [62, "常态", "3a+BwZOxpDxacSmTsYLUtn", null, null, [1, -1, 5, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 110]], [12, "空", "e80ZBkmKZFlJa5mjqHZWrE", null, null, [1, -210, -70, 0], [3, 0, 0, 0, 1], [1, 0.8, 0.8, 1], [0, 133, 140], 23], [22, "空", "a5A/97R+tAuqG2XnleHHgi", false, null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 40, 49], 24], [38, "空", "6ebRKxIjBBwLK+K0gS6sjZ", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 48, 48], 25], [39, "空", "3a+BwZOxpDxacSmTsYLUtn", false, null, null, [1, -1, 5, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 110]], [63, "锁", "e80ZBkmKZFlJa5mjqHZWrE", true, null, null, [1, -210, -70, 0], [3, 0, 0, 0, 1], [1, 0.8, 0.8, 1], [0, 133, 140], 26], [38, "锁", "a5A/97R+tAuqG2XnleHHgi", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 40, 49], 27], [22, "锁", "6ebRKxIjBBwLK+K0gS6sjZ", false, null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 48, 48], 28], [39, "锁", "3a+BwZOxpDxacSmTsYLUtn", false, null, null, [1, -1, 5, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 110]]]]], [1, "e80ZBkmKZFlJa5mjqHZWrE", null, null, null, 1, 0], [1, -210, -70, 0], [1, 0.8, 0.8, 1]], [29, "btn_sq2", 33554432, 2, [-83, -84, -85], [[3, -74, [0, "60HP+Q605NM6iTGO09lVdS"], [5, 133, 140]], [27, 1, 0, -75, [0, "281jeemFZFWq6z0eBF/Yfz"], 31], [24, 3, 0.9, -77, [0, "87v0JWrzRGb41bBDlfZUa+"], [4, 4292269782], -76], [47, 0, "常态", ["常态", "空", "锁"], -82, [0, "83S4XVLohAtqpPY7kgCRev"], [-78, -79, -80, -81], [[12, "常态", "087KiLTnBLDrSry36dvdlh", null, null, [1, 210, -69.99999999999989, 0], [3, 0, 0, 0, 1], [1, 0.8, 0.8, 1], [0, 133, 140], 32], [22, "常态", "17B0MVmoFEQ7w8zvUtZMTD", false, null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 40, 49], 33], [22, "常态", "60mkr7CjRDLbSJCKCztssk", false, null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 48, 48], 34], [62, "常态", "74C4tUY1tJ5JQWxE/FdDTG", null, null, [1, -1, 5, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 110]], [12, "空", "087KiLTnBLDrSry36dvdlh", null, null, [1, 210, -70, 0], [3, 0, 0, 0, 1], [1, 0.8, 0.8, 1], [0, 133, 140], 35], [22, "空", "17B0MVmoFEQ7w8zvUtZMTD", false, null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 40, 49], 36], [38, "空", "60mkr7CjRDLbSJCKCztssk", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 48, 48], 37], [39, "空", "74C4tUY1tJ5JQWxE/FdDTG", false, null, null, [1, -1, 5, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 110]], [63, "锁", "087KiLTnBLDrSry36dvdlh", true, null, null, [1, 210, -70, 0], [3, 0, 0, 0, 1], [1, 0.8, 0.8, 1], [0, 133, 140], 38], [38, "锁", "17B0MVmoFEQ7w8zvUtZMTD", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 40, 49], 39], [22, "锁", "60mkr7CjRDLbSJCKCztssk", false, null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 48, 48], 40], [39, "锁", "74C4tUY1tJ5JQWxE/FdDTG", false, null, null, [1, -1, 5, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 110]]]]], [1, "087KiLTnBLDrSry36dvdlh", null, null, null, 1, 0], [1, 210, -69.99999999999989, 0], [1, 0.8, 0.8, 1]], [23, "Layout", 33554432, 2, [-91, -92], [[13, -86, [0, "0622w+L0BNjqUOQXU2uFrc"], [5, 598, 600], [0, 0.5, 0]], [37, -87, [0, "0eNCgvuVxP9ZlKpPaf3nK6"]], [40, -88, [0, "d764b2+IhOhJ7bjymw10Aq"]], [33, -89, [0, "e7Kl/UvB5NL5RvHN7ZyDfU"], [4, 16777215]], [60, -90, [0, "35xoeazo9CpqA+J54sTwZh"]]], [1, "74ZcjkbExCh4t0MgPuBrtZ", null, null, null, 1, 0], [1, 0, -149, 0]], [25, "btn_ty_tb1", false, 33554432, 2, [-96, -97, -98, -99], [[3, -93, [0, "fa+GlaJVVF1q5Gij6OlLPs"], [5, 66, 84]], [24, 3, 0.9, -95, [0, "beR+hdOfxFC5darmBxUOXo"], [4, 4292269782], -94]], [1, "f8kX7GApxAyYhgP5S01ceN", null, null, null, 1, 0], [1, 220, 330, 0]], [29, "xingxing1", 33554432, 2, [-102, -103, -104, -105, -106], [[3, -100, [0, "fcUAi9KuZHk5vVcqGlRBs6"], [5, 430, 80]], [46, 1, 1, 10, -101, [0, "d9It76/oVHpoEaOc4WQTkQ"]]], [1, "bc4CFEKodNz4oKCKvmP6/v", null, null, null, 1, 0], [1, 0, -130, 0], [1, 0.5, 0.5, 1]], [15, 0, {}, 3, [14, "03XfDFaqNOdp6SCwdhIQpD", null, null, -108, [89, "fcFjR2e1FJ67PHYGWMjBir", 1, [[64, [2, ["5cvq8qCo5I7pXjdLcFwDsb"]], [-107]]], [[7, ["_lpos"], [2, ["03XfDFaqNOdp6SCwdhIQpD"]], [1, 0, 95.295, 0]], [6, "btn_ty1", ["_name"], [2, ["03XfDFaqNOdp6SCwdhIQpD"]]], [7, ["_lrot"], [2, ["03XfDFaqNOdp6SCwdhIQpD"]], [3, 0, 0, 0, 1]], [7, ["_euler"], [2, ["03XfDFaqNOdp6SCwdhIQpD"]], [1, 0, 0, 0]], [6, 52, ["_fontSize"], [2, ["bc648ctydDD5l0O0o5vV7i"]]], [6, 52, ["_actualFontSize"], [2, ["bc648ctydDD5l0O0o5vV7i"]]], [7, ["_contentSize"], [2, ["77Mt/A7RxGrJ0Q7zFDQxkY"]], [5, 260, 80.64]], [7, ["_contentSize"], [2, ["1657nTijNEh7Bns5UYA0sV"]], [5, 160, 69]], [7, ["_contentSize"], [2, ["89Kmzp7M5JRJxKSraI3F2C"]], [5, 190, 64]], [6, "培养", ["_string"], [2, ["bc648ctydDD5l0O0o5vV7i"]]], [7, ["_lscale"], [2, ["5cvq8qCo5I7pXjdLcFwDsb"]], [1, 0.5, 0.5, 1]], [6, 2, ["_overflow"], [2, ["bc648ctydDD5l0O0o5vV7i"]]], [6, 1, ["_sizeMode"], [2, ["43Z+zpMHNH95/fghPESQqh"]]], [7, ["_lpos"], [2, ["5cvq8qCo5I7pXjdLcFwDsb"]], [1, 0, 2, 0]]]], 55]], [2, ["79BcNar8tDmZucBHLjoa8/"]], [2, ["79BcNar8tDmZucBHLjoa8/"]], [2, ["79BcNar8tDmZucBHLjoa8/"]], [2, ["79BcNar8tDmZucBHLjoa8/"]], [2, ["79BcNar8tDmZucBHLjoa8/"]], [70, 0, {}, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -122, [90, "5fC44YTbxPua4Mt9pn8tDv", 1, [[92, [2, ["2cTijBh8lPqJeuR/xSNIgJ"]], [-121]]], [[64, [2, ["22bXP8nJtLsrSLfD0EMRWt"]], [-120]]], [[4, "ty_tab_1", ["_name"], -109], [5, ["_lpos"], -110, [1, 0, -36.5, 0]], [5, ["_lrot"], -111, [3, 0, 0, 0, 1]], [5, ["_euler"], -112, [1, 0, 0, 0]], [5, ["_contentSize"], -113, [5, 118, 73]], [5, ["_anchorPoint"], -114, [0, 0, 0.5]], [7, ["_lpos"], [2, ["82XblSaaJB3bi+IN0UAIS8"]], [1, -242, 0, 0]], [7, ["_lpos"], [2, ["2cTijBh8lPqJeuR/xSNIgJ"]], [1, 0, 0, 0]], [4, 0, ["_left"], -115], [4, 0, ["_right"], -116], [5, ["_anchorPoint"], -117, [0, 0, 0.5]], [7, ["_contentSize"], [2, ["47Cs6IYElOBK5jyf6MFKqL"]], [5, 484, 73]], [7, ["_contentSize"], [2, ["d5Cf3kv3dEMKi5AgyVsuFq"]], [5, 484, 73]], [5, ["_contentSize"], -118, [5, 484, 73]], [6, 3, ["_spacingX"], [2, ["4bwF0BQ9xEB50X2eNr4IB5"]]], [4, true, ["_active"], -119]], [[2, ["bci/enPcZKRIrnMIzs1Mdc"]]]], 57]], [41, "view", 33554432, [-127], [[13, -123, [0, "afOdjruHZJN744GF3zRXI9"], [5, 484, 73], [0, 0, 0.5]], [40, -124, [0, "0dp8ETGp1BjplXI0w6opGH"]], [33, -125, [0, "81DShpUDNIH6FdCICn+Jch"], [4, 16777215]], [43, 45, 240, 250, -126, [0, "59Xw89Cj5ChpL6aliHW4Pn"]]], [1, "82XblSaaJB3bi+IN0UAIS8", null, null, null, 1, 0], [1, -242, 0, 0]], [41, "click_close", 33554432, [-130, -131, -132], [[3, -128, [0, "f51l8w7L5He4MQb/8C0TCX"], [5, 270, 50]], [58, 1, 1, 2, true, -129, [0, "6cV+qPLDtBT5Os/dn7E9lL"]]], [1, "a2gZsQnAROwJ9QmEhCOUsA", null, null, null, 1, 0], [1, 0, -86, 0]], [42, "txt_gb", 33554432, 17, [[3, -133, [0, "b5dbpjK+RJD4cyBgmRe5fU"], [5, 308, 75.6]], [93, "點擊空白處關閉", 44, 44, 60, true, -134, [0, "33W36zALRJQIzpae3yrC7N"], [4, 3221225471]], [78, 2, -32, -135, [0, "4e1lZKzdpC37u4mFF4xtXW"]], [28, "djkb", -136, [0, "2fUEJntZ9MJKe0QlQaF7of"]], [28, "djkb", -137, [0, "44PMF3hB1MAJweyfGKYob9"]]], [1, "04BXOKa9lJ/4u6i4J3Llri", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [23, "Mask", 33554432, 2, [-141], [[3, -138, [0, "64bz4GK2NAm6qdA4AUB0XI"], [5, 300, 300]], [40, -139, [0, "c4sofE7TZOzKWUlSLZIDvo"]], [33, -140, [0, "e6r/WgQDZCb5GWRCmSMJBm"], [4, 16777215]]], [1, "2fNG++Bs1LzYAOvTeVrO2v", null, null, null, 1, 0], [1, 118.343, 5.711, 0]], [23, "img_pf_pj", 33554432, 2, [-145], [[3, -142, [0, "da6XUwKONOC7wCjt17Vd4H"], [5, 88, 39]], [11, -143, [0, "21OvpfTtxOU4WPgMk0mReD"], 8], [16, -144, [0, "acgXWuqSND/q+XKxRTOCzv"], [9, 10, 11, 12, 13]]], [1, "89wiwHtdhBBKyvYgqHvUTM", null, null, null, 1, 0], [1, -220, 312, 0]], [25, "Layout", false, 33554432, 2, [-148, -149], [[13, -146, [0, "c4D0Z8WyNHA6ci8y6xhM/d"], [5, 81, 42], [0, 0, 0.5]], [46, 1, 1, 2, -147, [0, "c3knaUwUJNd55B7/+srglb"]]], [1, "fcE5Dm/3JKCbo0AxA3reIP", null, null, null, 1, 0], [1, -258, 274, 0]], [9, "btn_you", 33554432, 2, [[3, -150, [0, "845yQ4lxdJpossHCfY76Gp"], [5, 52, 68]], [27, 1, 0, -151, [0, "1fodhCDNdEdqlNDE3oL3le"], 15], [24, 3, 0.9, -153, [0, "50N31xI6pGP60147eDOAjz"], [4, 4292269782], -152]], [1, "37N2elym9Ep67RKoUB1fDE", null, null, null, 1, 0], [1, 234, 108, 0]], [10, "btn_zuo", 33554432, 2, [[3, -154, [0, "47DxDNLcJLFZAr3H0btaLF"], [5, 52, 68]], [27, 1, 0, -155, [0, "d9QvLmK2FCTKqzTLs5rQA3"], 16], [24, 3, 0.9, -157, [0, "30PK9bnatDQIQu2d9LQ19I"], [4, 4292269782], -156]], [1, "05XJT6eDNGRIXyMqHEEmue", null, null, null, 1, 0], [1, -234, 108, 0], [1, -1, 1, 1]], [2, ["cd8ZiUa7RE4YhEMZ+TmPzP"]], [2, ["79BcNar8tDmZucBHLjoa8/"]], [2, ["0dpBBsahVNwKBm3pB6bLA5"]], [23, "bottom", 33554432, 2, [15, 17], [[13, -158, [0, "a4U0x1NjJPOa2Waj6QhUSm"], [5, 484, 111], [0, 0.5, 1]], [85, 1, 2, -12, true, -159, [0, "44lu7ysjdH3KloklFYU5Kn"]]], [1, "bfyDwwlDNKjrQOTQ9Wc3OR", null, null, null, 1, 0], [1, 0, -498, 0]], [74, 0, [14, "03XfDFaqNOdp6SCwdhIQpD", null, null, -160, [17, "e5jWF5+ttIdpInK6YA8s/J", 1, [[6, "btn_tab1", ["_name"], [2, ["03XfDFaqNOdp6SCwdhIQpD"]]], [7, ["_lpos"], [2, ["03XfDFaqNOdp6SCwdhIQpD"]], [1, 59, 0, 0]], [7, ["_lrot"], [2, ["03XfDFaqNOdp6SCwdhIQpD"]], [3, 0, 0, 0, 1]], [7, ["_euler"], [2, ["03XfDFaqNOdp6SCwdhIQpD"]], [1, 0, 0, 0]], [7, ["_contentSize"], [2, ["89Kmzp7M5JRJxKSraI3F2C"]], [5, 118, 52]], [6, 2, ["_sizeMode"], [2, ["43Z+zpMHNH95/fghPESQqh"]]], [48, ["_spriteFrame"], [2, ["43Z+zpMHNH95/fghPESQqh"]], 59], [6, false, ["_isTrimmedMode"], [2, ["43Z+zpMHNH95/fghPESQqh"]]], [48, ["spfs", "0"], [2, ["f1n6QovQpGR6hj6oK+YI9e"]], 60], [48, ["spfs", "1"], [2, ["f1n6QovQpGR6hj6oK+YI9e"]], 61], [6, "", ["_string"], [2, ["bc648ctydDD5l0O0o5vV7i"]]], [7, ["_lpos"], [2, ["5cvq8qCo5I7pXjdLcFwDsb"]], [1, 0, 0, 0]], [7, ["_contentSize"], [2, ["1657nTijNEh7Bns5UYA0sV"]], [5, 118, 73]], [6, 44, ["_fontSize"], [2, ["bc648ctydDD5l0O0o5vV7i"]]], [6, 44, ["_actualFontSize"], [2, ["bc648ctydDD5l0O0o5vV7i"]]], [6, 48, ["_lineHeight"], [2, ["bc648ctydDD5l0O0o5vV7i"]]], [7, ["_contentSize"], [2, ["77Mt/A7RxGrJ0Q7zFDQxkY"]], [5, 0, 60.480000000000004]]]], 58], [{}, "mountedRoot", 1, 15]], [34, "content", 33554432, 16, [28], [[13, -161, [0, "796NkxJLFMmoFXDsOY+zK3"], [5, 118, 73], [0, 0, 0.5]], [46, 1, 1, 3, -162, [0, "4bwF0BQ9xEB50X2eNr4IB5"]]], [1, "2cTijBh8lPqJeuR/xSNIgJ", null, null, null, 1, 0]], [2, ["79BcNar8tDmZucBHLjoa8/"]], [2, ["03XfDFaqNOdp6SCwdhIQpD"]], [23, "icon_ysz_qz1", 33554432, 2, [-165], [[3, -163, [0, "c8+AklVCRErZiPhdmVv0N0"], [5, 104, 88]], [8, 0, -164, [0, "50vlfvgbdHHqmlhgN41iKZ"], 5]], [1, "b8XkMkzIdFQoX9NV2f8aI/", null, null, null, 1, 0], [1, 220, 324, 0]], [42, "Label", 33554432, 32, [[3, -166, [0, "15CgSU8ZZORJD6Dy3p6v8P"], [5, 126, 61.44]], [94, "已上阵", 40, 44, true, true, 3, -167, [0, "76XZGgbSVLX5VJdePtHRY7"]], [28, "uihuoban_8", -168, [0, "d0jXbLOnxGJZN+bIh3x0tC"]]], [1, "a1E/JsxcJBmKPUudueiM0l", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [29, "icon_zz_1", 33554432, 2, [-171], [[3, -169, [0, "3c8u4ZW/RHIoWM81f6PN09"], [5, 51, 53]], [8, 0, -170, [0, "cfUCVlOlJCVZsxyg7IG54L"], 7]], [1, "40OFcLW3pGLb2fKw/kvyg9", null, null, null, 1, 0], [1, -240, 348, 0], [1, 0.7, 0.7, 1]], [10, "txt_pf_pj1", 33554432, 20, [[3, -172, [0, "301ZOlp1tIUab4MfCXFKTS"], [5, 76, 54.4]], [95, "經典", 36, 36, false, true, true, -173, [0, "28UfkJ9MRHULRiX+6FZqyJ"], [4, 4294827262]], [28, "uihuoban_62", -174, [0, "22ghm7XWhK1JBVrLLYHq0b"]]], [1, "1e8Fhe/+BE+qLMFJdrlJMs", null, null, null, 1, 0], [1, 0, 0.5, 0], [1, 0.5, 0.5, 1]], [9, "item", 33554432, 4, [[3, -175, [0, "f2q+SE0JhBh6u8r54J23Sv"], [5, 110, 110]], [44, 2, -176, [0, "a2h3kMPi5PH6Lodq6PNhXQ"]]], [1, "3a+BwZOxpDxacSmTsYLUtn", null, null, null, 1, 0], [1, -1, 5, 0]], [26, "img_suo2", false, 33554432, 4, [[3, -177, [0, "64KtDkXjBLhrUt49gHvrau"], [5, 40, 49]], [11, -178, [0, "a2EVsc/NhMkrTg2gfnpaEx"], 17]], [1, "a5A/97R+tAuqG2XnleHHgi", null, null, null, 1, 0]], [26, "btn_jia_4", false, 33554432, 4, [[3, -179, [0, "ebd8s6eIRNzI9eGSNug5qT"], [5, 48, 48]], [11, -180, [0, "d8E3YhlLZIGrBYX6p3yaZc"], 18]], [1, "6ebRKxIjBBwLK+K0gS6sjZ", null, null, null, 1, 0]], [9, "item", 33554432, 5, [[3, -181, [0, "02/gpDUKtJ0K2AwMXKBGMZ"], [5, 110, 110]], [44, 2, -182, [0, "460w0teoBNirY6IqjO0RTt"]]], [1, "74C4tUY1tJ5JQWxE/FdDTG", null, null, null, 1, 0], [1, -1, 5, 0]], [26, "img_suo2", false, 33554432, 5, [[3, -183, [0, "f9phosAg5G4IrWcOW4Uf6h"], [5, 40, 49]], [11, -184, [0, "31AwAvJSpCbolE+A5brHmc"], 29]], [1, "17B0MVmoFEQ7w8zvUtZMTD", null, null, null, 1, 0]], [26, "btn_jia_4", false, 33554432, 5, [[3, -185, [0, "41npNaHgRJ45ZF6+Whe2oC"], [5, 48, 48]], [11, -186, [0, "81HRKDrZ9NyaVU8vq0iL0y"], 30]], [1, "60mkr7CjRDLbSJCKCztssk", null, null, null, 1, 0]], [2, ["eeoXHNF6JHCZD795RQ9Goj"]], [2, ["eeoXHNF6JHCZD795RQ9Goj"]], [2, ["eeoXHNF6JHCZD795RQ9Goj"]], [2, ["eeoXHNF6JHCZD795RQ9Goj"]], [2, ["eeoXHNF6JHCZD795RQ9Goj"]], [2, ["79BcNar8tDmZucBHLjoa8/"]], [2, ["c46/YsCPVOJYA4mWEpNYRx"]], [75, "ScrollView", 33554432, 15, [16], [[[3, -187, [0, "d5Cf3kv3dEMKi5AgyVsuFq"], [5, 484, 73]], [68, 0.23, 0.75, false, -188, [0, "ccLr04+FBGkpXpOZIQzzDj"], 29], -189], 4, 4, 1], [1, "22bXP8nJtLsrSLfD0EMRWt", null, null, null, 1, 0]], [9, "lihui", 33554432, 6, [[52, -190, [0, "7cG/K20Z5GG7DJ0ToowKC1"], [0, 0.5, 0.39850672187514957]], [69, false, 0, -191, [0, "19l0yADyNKfbZ9HO8LfPMw"]]], [1, "6dm5pP0wlIlK+oNm8lHzh3", null, null, null, 1, 0], [1, 193.869, 118, 0]], [9, "lihui1", 33554432, 6, [[13, -192, [0, "cb2BZDEl5I+KxqeB11FXyT"], [5, 421, 916], [0, 0.5, 1]], [44, 2, -193, [0, "55R2habFhD8YiSouFGL9/G"]]], [1, "793R2TYjxPLabd6E/AdAGp", null, null, null, 1, 0], [1, 0, 510.84, 0]], [9, "bg_top_hb2", 33554432, 2, [[3, -194, [0, "d3X3L1RkdEwaVpwLllNiXM"], [5, 518, 71]], [11, -195, [0, "01QnNyNKtLg7cub25XjZbj"], 0]], [1, "ffAFu8bw5Ct7ErCUi3w7nF", null, null, null, 1, 0], [1, -3, -128, 0]], [35, "img_huoban_xian1", false, 33554432, 2, [[3, -196, [0, "22PJaNRPNGg4p7U8oJ5VyB"], [5, 520, 31]], [8, 0, -197, [0, "bbMDEh4ohLuZp6hosGNLoA"], 1]], [1, "e5OqSPs8tI04jvbXIZIhhX", null, null, null, 1, 0], [1, 0, -146, 0]], [21, "btn_tab_1", 33554432, 7, [[3, -198, [0, "79Pab4eahALZiuuPOc+Iuu"], [5, 66, 84]], [54, false, -199, [0, "04V9OJGg1EEK6hOt4O4eF8"], 2]], [1, "de/Q7ggUZLVIWy4Gc2Q+RC", null, null, null, 1, 0]], [9, "img_ty_jt", 33554432, 7, [[3, -200, [0, "0bAp13zlZP67rMtRcu0eOV"], [5, 38, 43]], [55, 2, false, -201, [0, "ed8c6G+v9Nn6bO1d1+j2FR"], 3]], [1, "7f7D5H1AVHLIb2sbQHs9Jr", null, null, null, 1, 0], [1, 0, 14, 0]], [10, "txt_tab_1", 33554432, 7, [[13, -202, [0, "ddXnC6hQhIprjMEbvpf8mA"], [5, 180, 80], [0, 0.5, 0]], [96, "總属性", 2, 41, 44, 2, true, true, 3, -203, [0, "b2cWRF73dMKJpAIrDP4Juk"]]], [1, "afS8mibtZG2KwIhhSAR4Qk", null, null, null, 1, 0], [1, 0, -40, 0], [1, 0.5, 0.5, 1]], [10, "txt_tab_djs", 33554432, 7, [[3, -204, [0, "35ssFR9UpL9JxecmyYLsXQ"], [5, 180, 60]], [97, "", 36, 36, 44, 2, false, true, true, 3, -205, [0, "91N1shY4JBIrA1TixMkBzZ"], [4, **********], [4, **********]]], [1, "f5jyuBlXpI0ZTPYiQic4ve", null, null, null, 1, 0], [1, 0, -50, 0], [1, 0.5, 0.5, 1]], [9, "xia<PERSON>n", 33554432, 19, [[52, -206, [0, "bcH6KyettLHqVun4Ay9znQ"], [0, 0.5, 0]], [69, false, 0, -207, [0, "efl4vW2mRFHr34QlZF/mY7"]]], [1, "af2/xqbIJFjLsvAcpEipa7", null, null, null, 1, 0], [1, 100, -135, 0]], [35, "img_xian_1", false, 33554432, 2, [[3, -208, [0, "3a0sk08kdO+J5Juj+ijHHy"], [5, 140, 3]], [8, 0, -209, [0, "f5o2PqYzpIC5xjvr+7px2t"], 4]], [1, "3annHs8CJDcbTHLMMfUpD8", null, null, null, 1, 0], [1, -148, 328, 0]], [10, "txt_dj1", 33554432, 2, [[3, -210, [0, "0cTaCPlRRKM7guU4M/3TbP"], [5, 188.34375, 86.64]], [98, "Lv.120", 60, 60, 64, true, true, 3, -211, [0, "70IptkpANOtLuuNuMgqwcp"], [4, **********]]], [1, "425c/4X6pKm7KkQmEdFLMZ", null, null, null, 1, 0], [1, 0, 436, 0], [1, 0.5, 0.5, 1]], [10, "txt_hb_name1", 33554432, 2, [[13, -212, [0, "c2GCSSZuFPjI6VUDwDMdTc"], [5, 400, 100], [0, 0, 0.5]], [65, "緹奧·庫拉+20", 0, 52, 52, 56, 2, false, true, true, 3, -213, [0, "a3ScsYL5dIaIRQxL5RKZ3D"], [4, **********]]], [1, "05gyBjs4tNVJr6nTOjQ6Eq", null, null, null, 1, 0], [1, -218, 350, 0], [1, 0.5, 0.5, 1]], [9, "icon_sx_shui", 33554432, 34, [[3, -214, [0, "dcRU0RLbdJ/aOVel9rPsVD"], [5, 36, 37]], [8, 0, -215, [0, "872KUvFlNJcYdcrq+OTx7z"], 6]], [1, "3d6WZkPFJDLa0EYfM2yZfo", null, null, null, 1, 0], [1, 0, 1, 0]], [9, "icon_zy1_1", 33554432, 21, [[3, -216, [0, "9beIFtOaNE6pxgxA9RKXSJ"], [5, 35, 35]], [11, -217, [0, "263KUgfg1AeaCrDkygqqdI"], 14]], [1, "f6t0mT++dOro2rHTy9tOyw", null, null, null, 1, 0], [1, 17.5, 0, 0]], [9, "txt_hb_name2", 33554432, 21, [[13, -218, [0, "773ZF7ispNvYBJK2sRY7sV"], [5, 44, 34.24], [0, 0, 0.5]], [99, "神子", 20, 20, 24, true, true, -219, [0, "b6/VA8bi9PuqMczcJUUCeo"], [4, **********]]], [1, "56cHFOfBlNRKGm5FR0cvh4", null, null, null, 1, 0], [1, 37, 0, 0]], [9, "icon_xx_1", 33554432, 8, [[3, -220, [0, "224R2ymxJFjbiNhzmMFVJD"], [5, 78, 80]], [8, 0, -221, [0, "casK5PVlNPdLqMvuRV5D8v"], 41]], [1, "7dywlxkqVIHq/la9p2QD2s", null, null, null, 1, 0], [1, -176, 0, 0]], [9, "icon_xx_2", 33554432, 8, [[3, -222, [0, "50looKSmBJ1rMmXYtbVt54"], [5, 78, 80]], [8, 0, -223, [0, "f3aez1tGdKQ7uQ02bQa9nz"], 42]], [1, "a9pWY5GxtL2rlcEJ7iIRjQ", null, null, null, 1, 0], [1, -88, 0, 0]], [21, "icon_xx_3", 33554432, 8, [[3, -224, [0, "ecFhFODOpMsZsxOv2muSV1"], [5, 78, 80]], [8, 0, -225, [0, "abSyrdwT9KarEzAcwsShcI"], 43]], [1, "daMvtY1bNL2Y46q4xvIfUa", null, null, null, 1, 0]], [9, "icon_xx_4", 33554432, 8, [[3, -226, [0, "1c/BU2B4ZGYa6ffvIhriQR"], [5, 78, 80]], [8, 0, -227, [0, "f4olbQuRJOvJ3lz1+/ay8k"], 44]], [1, "45MYGmpw5N2r0i36vhcJci", null, null, null, 1, 0], [1, 88, 0, 0]], [9, "icon_xx_5", 33554432, 8, [[3, -228, [0, "67LawzmzJAmZhjulvHVsW8"], [5, 78, 80]], [8, 0, -229, [0, "5cZMSBw3NMgpq6kLu9M78p"], 45]], [1, "8897N1cURHcr3pRzZF2nHx", null, null, null, 1, 0], [1, 176, 0, 0]], [15, 0, {}, 2, [14, "cd8ZiUa7RE4YhEMZ+TmPzP", null, null, -230, [17, "3afNdC33hOAb99OlUuuL5h", 1, [[4, "ty_zw", ["_name"], 24], [5, ["_lpos"], 24, [1, 0, -315.494, 0]], [5, ["_lrot"], 24, [3, 0, 0, 0, 1]], [5, ["_euler"], 24, [1, 0, 0, 0]], [4, false, ["_active"], 24], [6, "已達到最大星級", ["_string"], [2, ["bc2uWjqHtNapRxXJ0PmLNM"]]]]], 46]], [15, 0, {}, 3, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -231, [17, "2ejdaluwJN6rfSvvUCdpZ/", 1, [[5, ["_lpos"], 10, [1, -190.125, 256.86, 0]], [4, "ty_item1", ["_name"], 10], [5, ["_lrot"], 10, [3, 0, 0, 0, 1]], [5, ["_euler"], 10, [1, 0, 0, 0]], [4, true, ["_active"], 10], [6, false, ["_active"], [2, ["d6i3dsjTBAsYIwkgrHXPK3"]]], [5, ["_lscale"], 10, [1, 0.9, 0.9, 1]], [6, true, ["_active"], [2, ["c91yTbP25N/4XFQDypTQOW"]]], [4, "", ["_string"], 42], [6, false, ["_active"], [2, ["f51AN7O7FJgK4aYDFNvVXm"]]], [4, 0, ["_horizontalAlign"], 42], [7, ["_lpos"], [2, ["8ev1z6IH5OgYw4mQ5jIqFq"]], [1, 56.227, -37, 0]], [4, 22, ["_fontSize"], 42], [4, 22, ["_actualFontSize"], 42]]], 47]], [15, 0, {}, 3, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -232, [17, "faWrxUg09CcIQJDKU4u9dw", 1, [[5, ["_lpos"], 11, [1, -62.615, 321.158, 0]], [4, "ty_item2", ["_name"], 11], [5, ["_lrot"], 11, [3, 0, 0, 0, 1]], [5, ["_euler"], 11, [1, 0, 0, 0]], [4, true, ["_active"], 11], [6, false, ["_active"], [2, ["d6i3dsjTBAsYIwkgrHXPK3"]]], [5, ["_lscale"], 11, [1, 0.9, 0.9, 1]], [6, false, ["_active"], [2, ["c91yTbP25N/4XFQDypTQOW"]]], [4, "", ["_string"], 43], [6, false, ["_active"], [2, ["f51AN7O7FJgK4aYDFNvVXm"]]], [4, 0, ["_horizontalAlign"], 43], [7, ["_lpos"], [2, ["8ev1z6IH5OgYw4mQ5jIqFq"]], [1, 56.227, -37, 0]], [4, 22, ["_fontSize"], 43], [4, 22, ["_actualFontSize"], 43]]], 48]], [15, 0, {}, 3, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -233, [17, "22TzKMvYBGp51B2sp1S0AD", 1, [[5, ["_lpos"], 12, [1, 62.029, 320.902, 0]], [4, "ty_item3", ["_name"], 12], [5, ["_lrot"], 12, [3, 0, 0, 0, 1]], [5, ["_euler"], 12, [1, 0, 0, 0]], [4, true, ["_active"], 12], [6, false, ["_active"], [2, ["d6i3dsjTBAsYIwkgrHXPK3"]]], [5, ["_lscale"], 12, [1, 0.9, 0.9, 1]], [6, false, ["_active"], [2, ["c91yTbP25N/4XFQDypTQOW"]]], [4, "", ["_string"], 44], [6, false, ["_active"], [2, ["f51AN7O7FJgK4aYDFNvVXm"]]], [4, 0, ["_horizontalAlign"], 44], [7, ["_lpos"], [2, ["8ev1z6IH5OgYw4mQ5jIqFq"]], [1, 56.227, -37, 0]], [4, 22, ["_fontSize"], 44], [4, 22, ["_actualFontSize"], 44]]], 49]], [15, 0, {}, 3, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -234, [17, "8bBT9JBTVIE5f/xdd7z/4v", 1, [[5, ["_lpos"], 13, [1, -62.079, 205.634, 0]], [4, "ty_item4", ["_name"], 13], [5, ["_lrot"], 13, [3, 0, 0, 0, 1]], [5, ["_euler"], 13, [1, 0, 0, 0]], [4, true, ["_active"], 13], [6, false, ["_active"], [2, ["d6i3dsjTBAsYIwkgrHXPK3"]]], [5, ["_lscale"], 13, [1, 0.9, 0.9, 1]], [6, false, ["_active"], [2, ["c91yTbP25N/4XFQDypTQOW"]]], [4, "", ["_string"], 45], [6, false, ["_active"], [2, ["f51AN7O7FJgK4aYDFNvVXm"]]], [4, 0, ["_horizontalAlign"], 45], [7, ["_lpos"], [2, ["8ev1z6IH5OgYw4mQ5jIqFq"]], [1, 56.227, -37, 0]], [4, 22, ["_fontSize"], 45], [4, 22, ["_actualFontSize"], 45]]], 50]], [15, 0, {}, 3, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -235, [17, "0dAjw6hgtOdpznrfFIjP9V", 1, [[5, ["_lpos"], 14, [1, 61.876, 205.281, 0]], [4, "ty_item5", ["_name"], 14], [5, ["_lrot"], 14, [3, 0, 0, 0, 1]], [5, ["_euler"], 14, [1, 0, 0, 0]], [4, true, ["_active"], 14], [6, false, ["_active"], [2, ["d6i3dsjTBAsYIwkgrHXPK3"]]], [5, ["_lscale"], 14, [1, 0.9, 0.9, 1]], [6, false, ["_active"], [2, ["c91yTbP25N/4XFQDypTQOW"]]], [4, "", ["_string"], 46], [6, false, ["_active"], [2, ["f51AN7O7FJgK4aYDFNvVXm"]]], [4, 0, ["_horizontalAlign"], 46], [7, ["_lpos"], [2, ["8ev1z6IH5OgYw4mQ5jIqFq"]], [1, 56.227, -37, 0]], [4, 22, ["_fontSize"], 46], [4, 22, ["_actualFontSize"], 46]]], 51]], [15, 0, {}, 3, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -244, [17, "ccahOf5L5AnLziOa1RvM41", 1, [[5, ["_lpos"], 25, [1, 191.404, 256.994, 0]], [4, "ty_item_herd", ["_name"], 25], [5, ["_lrot"], 25, [3, 0, 0, 0, 1]], [5, ["_euler"], 25, [1, 0, 0, 0]], [5, ["_lscale"], 25, [1, 0.9, 0.9, 1]], [6, false, ["_active"], [2, ["2dWqaGkmNFHIz/JUNCiwtm"]]], [6, true, ["_active"], [2, ["4aanujv+pPyZacqbkMDG40"]]], [4, true, ["_active"], -236], [5, ["_lpos"], -237, [1, 303.4, 0, 0]], [7, ["_contentSize"], [2, ["33+jqHJYxEm5wgc0QUr166"]], [5, 342.4, 320]], [4, true, ["_active"], -238], [4, true, ["_active"], -239], [4, true, ["_active"], -240], [5, ["_lpos"], -241, [1, 105.1, 0, 0]], [5, ["_lpos"], -242, [1, 171.2, 0, 0]], [5, ["_lpos"], -243, [1, 237.29999999999998, 0, 0]]]], 52]], [15, 0, {}, 3, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -247, [17, "51bxjS0fpFn6LVLn2cHLSW", 1, [[4, "list_xh1", ["_name"], 47], [5, ["_lpos"], 47, [1, 0, 142.442, 0]], [5, ["_lrot"], 47, [3, 0, 0, 0, 1]], [5, ["_euler"], 47, [1, 0, 0, 0]], [91, ["_spriteFrame"], -245, 54], [7, ["_contentSize"], [2, ["6eLK82LLdNRKcY/pydBpFc"]], [5, 28, 28]], [4, 2, ["_sizeMode"], -246], [7, ["_lpos"], [2, ["ab6OaQyhFLSaBnxtHZuhoA"]], [1, 16, 0, 0]], [7, ["_contentSize"], [2, ["47Cs6IYElOBK5jyf6MFKqL"]], [5, 90.615234375, 30]], [7, ["_lpos"], [2, ["5dxakxnn1IH5IOSWRbNW25"]], [1, -31.3076171875, 0, 0]], [4, "x2100", ["_string"], 26], [7, ["_contentSize"], [2, ["feEue8VsVDB4gRggznsuay"]], [5, 117.23046875, 66.48]], [5, ["_color"], 26, [4, 4281742902]], [4, 40, ["_fontSize"], 26], [4, 40, ["_actualFontSize"], 26], [4, true, ["_enableOutline"], 26]]], 53]], [76, "txt_tab_1", 33554432, 9, [[[3, -248, [0, "77Mt/A7RxGrJ0Q7zFDQxkY"], [5, 260, 80.64]], [66, "培养", 53, 52, 64, 2, true, true, 3, -249, [0, "bc648ctydDD5l0O0o5vV7i"], [4, 4282867204]], -250], 4, 4, 1], [1, "5cvq8qCo5I7pXjdLcFwDsb", null, null, null, 1, 0], [1, 0, 2, 0], [1, 0.5, 0.5, 1]], [15, 0, {}, 2, [14, "c46/YsCPVOJYA4mWEpNYRx", null, null, -251, [17, "14YldjNXpHYZ1/tcZRLCiR", 1, [[4, "ty_zdl_1", ["_name"], 48], [5, ["_lpos"], 48, [1, 6, 392, 0]], [5, ["_lrot"], 48, [3, 0, 0, 0, 1]], [5, ["_euler"], 48, [1, 0, 0, 0]]]], 56]], [10, "img_jt1", 33554432, 17, [[3, -252, [0, "40C7olH8lHx7ys6o/Lxu8D"], [5, 56, 31]], [11, -253, [0, "31RbOQdblImIQ0gaW0dzo2"], 62]], [1, "24SjoZ5HtN25W/lhbwj2TK", null, null, null, 1, 0], [1, -107, 0, 0], [1, -1, 1, 1]], [9, "img_jt2", 33554432, 17, [[3, -254, [0, "64NcgXBohDdqWa/flInCzB"], [5, 56, 31]], [11, -255, [0, "61HN93GLxFtIMB7PwNh86g"], 63]], [1, "02htI2Cz5F659yfx9QQ/i0", null, null, null, 1, 0], [1, 107, 0, 0]], [15, 0, {}, 2, [14, "03XfDFaqNOdp6SCwdhIQpD", null, null, -256, [17, "09D70tk/lM1oqz/+TxWmor", 1, [[4, "icon_ty_pz1", ["_name"], 31], [5, ["_lpos"], 31, [1, -204, 276, 0]], [5, ["_lrot"], 31, [3, 0, 0, 0, 1]], [5, ["_euler"], 31, [1, 0, 0, 0]], [4, false, ["_active"], 31]]], 64]], [2, ["ed47B0VyNBqYyltMm4GWR+"]], [2, ["0776FEED1KQos0Rdw5Hzig"]], [2, ["e9jaHf/bJHb4e0wKZQUIAA"]], [2, ["58rdXR/QpBW4fGWscpkCQn"]], [2, ["90X4zuDxlM2ap0ok+kkml6"]], [28, "uihuoban_1", 78, [0, "26Kgc7rUBI0LDBlvtwGsly"]], [106, 49, [0, "34vP1+9PZKR4TuYcuLnleD"], 28, [49], [49], [49]], [2, ["796NkxJLFMmoFXDsOY+zK3"]], [2, ["afOdjruHZJN744GF3zRXI9"]], [2, ["59Xw89Cj5ChpL6aliHW4Pn"]]], 0, [0, -1, 82, 0, -2, 15, 0, -3, 28, 0, -4, 79, 0, -5, 9, 0, -6, 77, 0, -7, 76, 0, -8, 75, 0, -9, 74, 0, -10, 73, 0, -11, 72, 0, -12, 71, 0, -13, 70, 0, 11, 15, 0, 11, 9, 0, 13, 9, 0, 11, 9, 0, 13, 9, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 52, 0, -3, 53, 0, -4, 7, 0, -5, 19, 0, -6, 59, 0, -7, 60, 0, -8, 61, 0, -9, 32, 0, -10, 34, 0, -11, 20, 0, -12, 21, 0, -13, 22, 0, -14, 23, 0, -15, 4, 0, -16, 5, 0, -17, 8, 0, -18, 70, 0, -19, 3, 0, -20, 79, 0, -21, 27, 0, -22, 82, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 71, 0, -2, 72, 0, -3, 73, 0, -4, 74, 0, -5, 75, 0, -6, 76, 0, -7, 77, 0, -8, 9, 0, 0, 4, 0, 0, 4, 0, 8, 4, 0, 0, 4, 0, -1, 4, 0, -2, 37, 0, -3, 38, 0, -4, 36, 0, 0, 4, 0, -1, 36, 0, -2, 37, 0, -3, 38, 0, 0, 5, 0, 0, 5, 0, 8, 5, 0, 0, 5, 0, -1, 5, 0, -2, 40, 0, -3, 41, 0, -4, 39, 0, 0, 5, 0, -1, 39, 0, -2, 40, 0, -3, 41, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 50, 0, -2, 51, 0, 0, 7, 0, 8, 7, 0, 0, 7, 0, -1, 54, 0, -2, 55, 0, -3, 56, 0, -4, 57, 0, 0, 8, 0, 0, 8, 0, -1, 65, 0, -2, 66, 0, -3, 67, 0, -4, 68, 0, -5, 69, 0, -1, 88, 0, 3, 9, 0, 4, 30, 0, 4, 30, 0, 4, 30, 0, 4, 30, 0, 4, 90, 0, 4, 91, 0, 4, 92, 0, 4, 92, 0, 4, 90, 0, 4, 91, 0, 4, 30, 0, -1, 89, 0, -1, 28, 0, 3, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, -1, 29, 0, 0, 17, 0, 0, 17, 0, -1, 80, 0, -2, 18, 0, -3, 81, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, -1, 58, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, -1, 35, 0, 0, 21, 0, 0, 21, 0, -1, 63, 0, -2, 64, 0, 0, 22, 0, 0, 22, 0, 8, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 8, 23, 0, 0, 23, 0, 0, 27, 0, 0, 27, 0, 3, 28, 0, 0, 29, 0, 0, 29, 0, 0, 32, 0, 0, 32, 0, -1, 33, 0, 0, 33, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, -1, 62, 0, 0, 35, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 49, 0, 0, 49, 0, -3, 89, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, 0, 57, 0, 0, 58, 0, 0, 58, 0, 0, 59, 0, 0, 59, 0, 0, 60, 0, 0, 60, 0, 0, 61, 0, 0, 61, 0, 0, 62, 0, 0, 62, 0, 0, 63, 0, 0, 63, 0, 0, 64, 0, 0, 64, 0, 0, 65, 0, 0, 65, 0, 0, 66, 0, 0, 66, 0, 0, 67, 0, 0, 67, 0, 0, 68, 0, 0, 68, 0, 0, 69, 0, 0, 69, 0, 3, 70, 0, 3, 71, 0, 3, 72, 0, 3, 73, 0, 3, 74, 0, 3, 75, 0, 4, 83, 0, 4, 83, 0, 4, 84, 0, 4, 85, 0, 4, 86, 0, 4, 86, 0, 4, 85, 0, 4, 84, 0, 3, 76, 0, 4, 87, 0, 4, 87, 0, 3, 77, 0, 0, 78, 0, 0, 78, 0, -3, 88, 0, 3, 79, 0, 0, 80, 0, 0, 80, 0, 0, 81, 0, 0, 81, 0, 3, 82, 0, 6, 1, 15, 7, 27, 16, 7, 49, 17, 7, 27, 28, 7, 29, 256], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, -1, -2, -3, -4, -5, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 10, 10, 10, 1, 1, 5, 1, -1, -2, -3, 9, -1, -2, -3], [32, 33, 34, 35, 36, 37, 14, 38, 9, 9, 15, 9, 15, 39, 40, 16, 16, 2, 3, 4, 4, 2, 3, 4, 2, 3, 4, 2, 3, 2, 3, 4, 4, 2, 3, 4, 2, 3, 4, 2, 3, 1, 1, 1, 1, 1, 41, 5, 5, 5, 5, 5, 42, 43, 44, 45, 46, 47, 48, 49, 10, 17, 18, 18, 50, 19, 6, 51, 8, 8, 19, 52, 53]], [[[20, "ty_tab_1"], [41, "ty_tab_1", 33554432, [-3], [[3, -2, [0, "47Cs6IYElOBK5jyf6MFKqL"], [5, 610, 73]]], [1, "79BcNar8tDmZucBHLjoa8/", null, null, null, -1, 0], [1, 0, 50, 0]], [19, "view", 33554432, [-8], [[3, -4, [0, "afOdjruHZJN744GF3zRXI9"], [5, 610, 73]], [40, -5, [0, "0dp8ETGp1BjplXI0w6opGH"]], [33, -6, [0, "81DShpUDNIH6FdCICn+Jch"], [4, 16777215]], [43, 45, 240, 250, -7, [0, "59Xw89Cj5ChpL6aliHW4Pn"]]], [1, "82XblSaaJB3bi+IN0UAIS8", null, null, null, 1, 0]], [34, "ScrollView", 33554432, 1, [2], [[3, -9, [0, "d5Cf3kv3dEMKi5AgyVsuFq"], [5, 610, 73]], [81, 1, 0, -10, [0, "bci/enPcZKRIrnMIzs1Mdc"]], [68, 0.23, 0.75, false, -12, [0, "ccLr04+FBGkpXpOZIQzzDj"], -11]], [1, "22bXP8nJtLsrSLfD0EMRWt", null, null, null, 1, 0]], [9, "content", 33554432, 2, [[13, -13, [0, "796NkxJLFMmoFXDsOY+zK3"], [5, 0, 73], [0, 0, 0.5]], [86, 1, 1, -14, [0, "4bwF0BQ9xEB50X2eNr4IB5"]]], [1, "2cTijBh8lPqJeuR/xSNIgJ", null, null, null, 1, 0], [1, -305, 0, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 3, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, 0, 3, 0, 0, 3, 0, 14, 4, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 6, 1, 2, 7, 3, 14], [], [], []], [[{"name": "bg_top_hb_3", "rect": {"x": 0, "y": 0, "width": 550, "height": 1003}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 550, "height": 1003}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-275, -501.5, 0, 275, -501.5, 0, -275, 501.5, 0, 275, 501.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1003, 550, 1003, 0, 0, 550, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -275, "y": -501.5, "z": 0}, "maxPos": {"x": 275, "y": 501.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [12], [54]], [[[20, "icon_ty_pz1"], [19, "icon_ty_pz1", 33554432, [-5], [[3, -2, [0, "1657nTijNEh7Bns5UYA0sV"], [5, 148, 148]], [31, -3, [0, "09TXWZ2Z1HZae+Mzg0dCAF"]], [84, [null, null, null], -4, [0, "dbRYJvfJBIT4/KDWkpdsLw"]]], [1, "03XfDFaqNOdp6SCwdhIQpD", null, null, null, -1, 0]], [10, "wz_huoban_pz4", 33554432, 1, [[3, -6, [0, "65xGpVvmdM5Zyg+1JEcJH+"], [5, 113, 81]], [11, -7, [0, "27YcIcYjpLAbhi0IHBFnfU"], 0], [16, -8, [0, "dalcc/cXlAEqnLwfAAoefC"], [1, 2, 3, 4]]], [1, "5eJbybyddFkq/q+SlJFpDS", null, null, null, 1, 0], [1, 0, -2, 0], [1, 0.6, 0.6, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 6, 1, 8], [0, 0, 0, 0, 0], [1, -1, -2, -3, -4], [20, 20, 55, 56, 57]], [[[20, "btn_ty1"], [19, "btn_ty1", 33554432, [-5, -6], [[3, -2, [0, "1657nTijNEh7Bns5UYA0sV"], [5, 190, 90]], [24, 3, 0.9, -4, [0, "8eFPdJ6WlJk47YJqq4w+Iz"], [4, 4292269782], -3]], [1, "03XfDFaqNOdp6SCwdhIQpD", null, null, null, -1, 0]], [21, "btn_tab_1", 33554432, 1, [[3, -7, [0, "89Kmzp7M5JRJxKSraI3F2C"], [5, 190, 64]], [54, false, -8, [0, "43Z+zpMHNH95/fghPESQqh"], 0], [16, -9, [0, "2fM+Ep5oJCAbVEaTu9NhXO"], [1, 2, 3]]], [1, "5eZAD6dg1PkIthJGeTXCdA", null, null, null, 1, 0]], [10, "txt_tab_1", 33554432, 1, [[3, -10, [0, "77Mt/A7RxGrJ0Q7zFDQxkY"], [5, 345, 80.64]], [66, "", 53, 52, 64, 2, true, true, 3, -11, [0, "bc648ctydDD5l0O0o5vV7i"], [4, 4282867204]]], [1, "5cvq8qCo5I7pXjdLcFwDsb", null, null, null, 1, 0], [1, 0, 2, 0], [1, 0.5, 0.5, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 8, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 6, 1, 11], [0, 0, 0, 0], [1, -1, -2, -3], [21, 21, 58, 59]], [[[20, "btn_tab1"], [19, "btn_tab1", 33554432, [-5, -6, -7], [[3, -2, [0, "1657nTijNEh7Bns5UYA0sV"], [5, 118, 52]], [24, 3, 0.9, -4, [0, "8eFPdJ6WlJk47YJqq4w+Iz"], [4, 4292269782], -3]], [1, "03XfDFaqNOdp6SCwdhIQpD", null, null, null, -1, 0]], [21, "btn_tab_1", 33554432, 1, [[3, -8, [0, "89Kmzp7M5JRJxKSraI3F2C"], [5, 118, 52]], [55, 2, false, -9, [0, "43Z+zpMHNH95/fghPESQqh"], 0], [16, -10, [0, "f1n6QovQpGR6hj6oK+YI9e"], [1, 2]]], [1, "5eZAD6dg1PkIthJGeTXCdA", null, null, null, 1, 0]], [10, "txt_tab_1", 33554432, 1, [[3, -11, [0, "77Mt/A7RxGrJ0Q7zFDQxkY"], [5, 220, 80.64]], [100, "", 45, 44, 64, 2, true, -12, [0, "bc648ctydDD5l0O0o5vV7i"], [4, **********]]], [1, "5cvq8qCo5I7pXjdLcFwDsb", null, null, null, 1, 0], [1, 0, 1.5, 0], [1, 0.5, 0.5, 1]], [35, "icon_hongdian1", false, 33554432, 1, [[3, -13, [0, "00yHyKWaBHLL0bXScKNVGL"], [5, 39, 39]], [8, 0, -14, [0, "ed0ocAJoNOoptiZxP41/ci"], 3]], [1, "few6jM5+FNxLj19cPlUYc/", null, null, null, 1, 0], [1, 52, 16, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 8, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 6, 1, 14], [0, 0, 0, 0], [1, -1, -2, 1], [10, 10, 17, 60]], [[{"name": "bg_top_hb_1", "rect": {"x": 0, "y": 0, "width": 550, "height": 1003}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 550, "height": 1003}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-275, -501.5, 0, 275, -501.5, 0, -275, 501.5, 0, 275, 501.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1003, 550, 1003, 0, 0, 550, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -275, "y": -501.5, "z": 0}, "maxPos": {"x": 275, "y": 501.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [12], [61]], [[[20, "ty_item1"], [19, "ty_item1", 33554432, [-5], [[3, -2, [0, "47Cs6IYElOBK5jyf6MFKqL"], [5, 113, 115]], [59, 1, 2, -3, [0, "46wgLU6vlHt54vDxwmo6H/"]], [61, 3, 0.95, -4, [0, "ceCLQjtbZCD7jVqYXKEJ5k"]]], [1, "79BcNar8tDmZucBHLjoa8/", null, null, null, -1, 0]], [34, "item1", 33554432, 1, [-9, -10, -11, -12, -13, -14], [[3, -6, [0, "800U7Nj4lFVZTNPOORHgRn"], [5, 113, 115]], [57, -7, [0, "627EWxKghLx6uRo4WDrsj9"], [16], 17], [32, -8, [0, "99iHfiq7hGupwA+5mGvVOo"]]], [1, "58m892tA5BQagOFZNqC4iA", null, null, null, 1, 0]], [50, "pj_kuang_hui1", false, 33554432, 2, [-17, -18], [[3, -15, [0, "d3AtRMiJNCOYRAaUItr2qu"], [5, 110, 110]], [8, 0, -16, [0, "31IP5pa1VA76/aggSUz5Pz"], 7]], [1, "c91yTbP25N/4XFQDypTQOW", null, null, null, 1, 0]], [26, "kapai_xz1", false, 33554432, 2, [[3, -19, [0, "234+0KgaRKP4Oyw5E8wuzg"], [5, 135, 136]], [27, 1, 0, -20, [0, "d65WkruK9JBLqnkpusf4Fa"], 13], [45, true, -21, [0, "82d/HULiVNnIe/ZM8Xe+wN"], [14], 15], [32, -22, [0, "02ZF5BEgBK7aQ7v73tkrjm"]]], [1, "d6i3dsjTBAsYIwkgrHXPK3", null, null, null, 1, 0]], [9, "kapai_di", 33554432, 2, [[3, -23, [0, "83Iz4xnIJBp4B4NrxAK6Pg"], [5, 110, 112]], [8, 0, -24, [0, "e3MbdbnGpLSJv7jZQUGKXO"], 0], [16, -25, [0, "30SwXnGDZOzJhfcbZFqmvt"], [1, 2, 3, 4, 5, 6]]], [1, "42Rk0WEWpD9J2OZPPe3K6X", null, null, null, 1, 0], [1, 0, -1, 0]], [42, "item_icon", 33554432, 2, [[3, -26, [0, "0ciXTZfuFGvIhz57RrMKZw"], [5, 110, 110]], [37, -27, [0, "cfQr5+YJVLOqqnwVbtaBq0"]], [31, -28, [0, "4eDwtJcn1JlYXC2pBZ9vb6"]]], [1, "b9Ux/7GgBHjY7TOGJJiEpi", null, null, null, 1, 0], [1, 0.9, 0.9, 1]], [36, "pj_kuang_sp1", false, 33554432, 2, [[3, -29, [0, "e4lauM7zFO4aX6clqVrRLz"], [5, 93, 90]], [8, 0, -30, [0, "9724Q2/XBDebIdwKApd6Kd"], 8], [16, -31, [0, "3cXBsYOTxFgb9BVVnSJlAo"], [9, 10, 11, 12]]], [1, "f51AN7O7FJgK4aYDFNvVXm", null, null, null, 1, 0], [1, -32, -32, 0], [1, 0.35, 0.35, 1]], [10, "txt_item_jstj1", 33554432, 3, [[3, -32, [0, "46ZZq382dFa7LcUcAkKils"], [5, 180, 60]], [67, "Lv.100", 40, 60, 2, true, true, 3, -33, [0, "bey4sFeBtPDaHbYh+oX/Qj"], [4, 4290953922]]], [1, "51g0VuoRpO8LzDHgEFWoXH", null, null, null, 1, 0], [1, 0, 14, 0], [1, 0.5, 0.5, 1]], [10, "txt_item_jstj2", 33554432, 3, [[3, -34, [0, "d5ute3KyVLmqZvSuEdvhFO"], [5, 180, 60]], [67, "解鎖", 40, 60, 2, true, true, 3, -35, [0, "27sDHsU/xAV5kINrQNKYPw"], [4, 4290953922]]], [1, "1cWi93TqRDt5YPaW9LXMQn", null, null, null, 1, 0], [1, 0, -14, 0], [1, 0.5, 0.5, 1]], [10, "txt_item_dj", 33554432, 2, [[13, -36, [0, "2a+NEz1hJH37Gy8QtP+C4Q"], [5, 220, 96], [0, 1, 0.5]], [101, "", 2, 49, 48, 70, 2, true, true, 3, -37, [0, "eeoXHNF6JHCZD795RQ9Goj"], [4, **********]]], [1, "8ev1z6IH5OgYw4mQ5jIqFq", null, null, null, 1, 0], [1, 50, 38, 0], [1, 0.5, 0.5, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, -3, 3, 0, -4, 10, 0, -5, 7, 0, -6, 4, 0, 0, 3, 0, 0, 3, 0, -1, 8, 0, -2, 9, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 6, 1, 37], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, -1, -2, -3, -4, -5, -6, 1, 1, -1, -2, -3, -4, 1, -1, 9, -1, 9], [7, 7, 22, 23, 24, 25, 26, 27, 11, 11, 28, 12, 29, 30, 31, 31, 6, 6]], [[[20, "ty_item_herd"], [19, "ty_item_herd", 33554432, [-5], [[3, -2, [0, "47Cs6IYElOBK5jyf6MFKqL"], [5, 113, 115]], [59, 1, 2, -3, [0, "46wgLU6vlHt54vDxwmo6H/"]], [61, 3, 0.95, -4, [0, "ceCLQjtbZCD7jVqYXKEJ5k"]]], [1, "79BcNar8tDmZucBHLjoa8/", null, null, null, -1, 0]], [34, "item_herd", 33554432, 1, [-9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21], [[3, -6, [0, "634dUdqhVOiY6JSWfACj35"], [5, 113, 115]], [57, -7, [0, "7dMei/nyJIQb/AE09QdrRo"], [66], 67], [32, -8, [0, "32AFbaFZJEcK8Jek1cxOxT"]]], [1, "bfkmkpywRNi6tCGTO09lKo", null, null, null, 1, 0]], [29, "xingxing1", 33554432, 2, [-24, -25, -26, -27, -28], [[13, -22, [0, "33+jqHJYxEm5wgc0QUr166"], [5, 11.9, 88], [0, 0, 0.5]], [87, 1, 1, -11.9, -14.7, -23, [0, "8cpzef941DCKpnKMsFRy0d"]]], [1, "bbnWFGYBNL/oJkBl2wsMC8", null, null, null, 1, 0], [1, -50.572, -40.788, 0], [1, 0.28, 0.28, 1]], [71, "hc_xx", false, 33554432, [-36, -37, -38, -39, -40], [[3, -29, [0, "626qJ+aw9Dp7xcDjgWauPK"], [5, 72, 40]], [47, 0, "1", ["1", "2", "3", "4", "5"], -35, [0, "bb3+uVwZZEJpheIjWxtI2t"], [-30, -31, -32, -33, -34], [[12, "1", "aaWiEHAZVKhYT4nbxArs1z", null, null, [1, 3.891, 8.866, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 28], [18, "1", "39dzph+lBFOJA7tzVBrlPY", false, null, null, [1, 16.157, 19.661, 0], [3, 0, 0, 0.25966187574449157, 0.9656995962952725], [1, 0.6, 0.6, 1], [0, 74, 71], 29], [18, "1", "4dsROAT71IGqetJn1zwtEB", false, null, null, [1, -9.253, -2.697, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 30], [18, "1", "7a+V8YB85I6YszKHQ6K68d", false, null, null, [1, 13.494, -3.855, 0], [3, 0, 0, -0.14090123193758267, 0.9900236577165575], [1, 0.6, 0.6, 1], [0, 74, 71], 31], [18, "1", "a3wNamZbFBC7xbKYPddXwZ", false, null, null, [1, 4.241, 8.866999999999962, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 32], [12, "5", "aaWiEHAZVKhYT4nbxArs1z", null, null, [1, 19.079, 25.335, 0], [3, 0, 0, -0.05843522251824643, 0.9982912023899855], [1, 0.6, 0.6, 1], [0, 74, 71], 33], [12, "5", "39dzph+lBFOJA7tzVBrlPY", null, null, [1, -11.214, 19.275, 0], [3, 0, 0, -0.016579868187697525, 0.9998625445384374], [1, 0.6, 0.6, 1], [0, 74, 71], 34], [12, "5", "4dsROAT71IGqetJn1zwtEB", null, null, [1, -11.181, -4.239, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 35], [12, "5", "7a+V8YB85I6YszKHQ6K68d", null, null, [1, 4.327, 9.825, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 36], [12, "5", "a3wNamZbFBC7xbKYPddXwZ", null, null, [1, 17.735, -5.012, 0], [3, 0, 0, 0.17364817766693033, 0.984807753012208], [1, 0.6, 0.6, 1], [0, 74, 71], 37], [12, "2", "aaWiEHAZVKhYT4nbxArs1z", null, null, [1, 18.927, 7.235, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 38], [12, "2", "39dzph+lBFOJA7tzVBrlPY", null, null, [1, -10.114, 6.938, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 39], [18, "2", "4dsROAT71IGqetJn1zwtEB", false, null, null, [1, -9.253, -2.697, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 40], [18, "2", "7a+V8YB85I6YszKHQ6K68d", false, null, null, [1, 13.494, -3.855, 0], [3, 0, 0, -0.14090123193758267, 0.9900236577165575], [1, 0.6, 0.6, 1], [0, 74, 71], 41], [18, "2", "a3wNamZbFBC7xbKYPddXwZ", false, null, null, [1, 4.241, 8.866999999999962, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 42], [12, "3", "aaWiEHAZVKhYT4nbxArs1z", null, null, [1, 21.398, -2.922, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 43], [12, "3", "39dzph+lBFOJA7tzVBrlPY", null, null, [1, -10.749, -2.475, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 44], [12, "3", "4dsROAT71IGqetJn1zwtEB", null, null, [1, 4.626, 20.182, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 45], [18, "3", "7a+V8YB85I6YszKHQ6K68d", false, null, null, [1, 13.494, -3.855, 0], [3, 0, 0, -0.14090123193758267, 0.9900236577165575], [1, 0.6, 0.6, 1], [0, 74, 71], 46], [18, "3", "a3wNamZbFBC7xbKYPddXwZ", false, null, null, [1, 4.241, 8.866999999999962, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 47], [12, "4", "aaWiEHAZVKhYT4nbxArs1z", null, null, [1, 3.7894999999999897, 31.38600000000008, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 48], [12, "4", "39dzph+lBFOJA7tzVBrlPY", null, null, [1, -18.214, 8.976, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 49], [12, "4", "4dsROAT71IGqetJn1zwtEB", null, null, [1, 25.807, 8.976, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 50], [12, "4", "7a+V8YB85I6YszKHQ6K68d", null, null, [1, 3.789, -13.654, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 51], [18, "4", "a3wNamZbFBC7xbKYPddXwZ", false, null, null, [1, 4.241, 8.866999999999962, 0], [3, 0, 0, 0, 1], [1, 0.6, 0.6, 1], [0, 74, 71], 52]]]], [1, "2b1b1G4MhJJpzeq+C4LWRK", null, null, null, 1, 0], [1, -16.548, 4.792, 0]], [25, "icon_zy1_2", false, 33554432, 2, [-44], [[3, -41, [0, "c5tlpSJilD8awmo6021iGh"], [5, 36, 36]], [8, 0, -42, [0, "b33PhHNnBCd6PKwURgR0Ko"], 16], [16, -43, [0, "57TUoA2MdCeZJJDt0guHpC"], [17, 18]]], [1, "2dWqaGkmNFHIz/JUNCiwtm", null, null, null, 1, 0], [1, -38, 38, 0]], [72, "icon_ysz2", false, 33554432, 2, [-47, -48], [[3, -45, [0, "bfg9+n1kxDvbcCZ/LscIKE"], [5, 110, 87]], [31, -46, [0, "25L4VNNzRIpoMygT5dvgVd"]]], [1, "d7bEv3OPNG441XD6ypHuqU", null, null, null, 1, 0], [1, 0.9, 0.9, 1]], [19, "mask_tx", 33554432, [-52], [[3, -49, [0, "4dos1Q7OhGvKXU8XHbQPZA"], [5, 35, 35]], [88, 1, -50, [0, "005xYLpE1G4YKpzo3Kq/u+"]], [33, -51, [0, "715Kv0UqxN46S+f0kdpJ5O"], [4, 16777215]]], [1, "34ZjVTx3pAU4y8JKiTdsSb", null, null, null, 1, 0]], [73, "img_hb_tj_di3", 33554432, [4, -55], [[3, -53, [0, "74+VRQmkFKQpdv+nZjQ+NU"], [5, 154, 150]], [56, false, -54, [0, "b6CgAcdftGW4ujX1yXS/Za"], 54]], [1, "b9HhbGxT9BJ497a5mmvq+s", null, null, null, 1, 0], [1, 7.8, -13, 0], [1, 0.9, 0.9, 1]], [35, "kapai_xz1", false, 33554432, 2, [[3, -56, [0, "234+0KgaRKP4Oyw5E8wuzg"], [5, 135, 136]], [27, 1, 0, -57, [0, "d65WkruK9JBLqnkpusf4Fa"], 62], [45, true, -58, [0, "42zdusTDRMlqux8i9YFEz9"], [63], 64], [32, -59, [0, "33GV1FLTpL0JBeT9GsvLj0"]]], [1, "d6i3dsjTBAsYIwkgrHXPK3", null, null, null, 1, 0], [1, 0, -1, 0]], [9, "kapai_di", 33554432, 2, [[3, -60, [0, "83Iz4xnIJBp4B4NrxAK6Pg"], [5, 113, 115]], [8, 0, -61, [0, "e3MbdbnGpLSJv7jZQUGKXO"], 0], [16, -62, [0, "30SwXnGDZOzJhfcbZFqmvt"], [1, 2, 3, 4, 5, 6]]], [1, "42Rk0WEWpD9J2OZPPe3K6X", null, null, null, 1, 0], [1, 0, -1, 0]], [10, "item_icon", 33554432, 2, [[3, -63, [0, "0ciXTZfuFGvIhz57RrMKZw"], [5, 110, 110]], [37, -64, [0, "cfQr5+YJVLOqqnwVbtaBq0"]], [82, false, -65, [0, "4eDwtJcn1JlYXC2pBZ9vb6"]]], [1, "b9Ux/7GgBHjY7TOGJJiEpi", null, null, null, 1, 0], [1, 0, 0.5, 0], [1, 0.9, 0.9, 1]], [10, "icon_sx_1", 33554432, 5, [[3, -66, [0, "35koymrjNGQKUqrwdMN0Dj"], [5, 30, 42]], [31, -67, [0, "d8r3imxhBML7a7SwSVDI65"]], [16, -68, [0, "06k8VsXzpEN73ZcnaviNz5"], [12, 13, 14, 15]]], [1, "50b5wwz79MgIolLVtz7GC/", null, null, null, 1, 0], [1, -0.625, 0, 0], [1, 0.6, 0.6, 1]], [10, "Label", 33554432, 6, [[3, -69, [0, "3abNWRn0BDrZyKc0phDrLh"], [5, 126, 61.44]], [102, "已上阵", 40, 44, true, true, 3, -70, [0, "1f8HeqTLpH7aJd0PjYmsoY"], [4, 4280229916]], [28, "uihuoban_8", -71, [0, "2cljWExUhNgJVOYZ/g2Bim"]]], [1, "f4chIEGCFN84Fk4TyITM9c", null, null, null, 1, 0], [1, 0, -13.648, 0], [1, 0.5, 0.5, 1]], [25, "icon_zy1_4", false, 33554432, 2, [7], [[3, -72, [0, "a4x/jyKDJA46TvMqTIP0Su"], [5, 36, 36]], [8, 0, -73, [0, "f67dDa0G1MNLvpXPWkHt1S"], 21]], [1, "3fafIg7QJK95tp5mXAWHA2", null, null, null, 1, 0], [1, -37.73199999999997, 38.188999999999965, 0]], [50, "bg_tujian_di5", false, 33554432, 2, [-75, 8], [[3, -74, [0, "60/RAgpdxNFZG4wqpTsdL1"], [5, 132, 205]]], [1, "acK1OK6nFP3YXqGHtHPt1a", null, null, null, 1, 0]], [10, "icon_xx_x1", 33554432, 4, [[3, -76, [0, "23f2JT5rdImIW5pCAZnPFs"], [5, 74, 71]], [11, -77, [0, "6eLQjRyJVErK5Eg3VgonsE"], 23]], [1, "0apcyT4x1GIIw6a+v4elrs", null, null, null, 1, 0], [1, 3.891, 8.866, 0], [1, 0.6, 0.6, 1]], [51, "icon_xx_x2", false, 33554432, 4, [[3, -78, [0, "021sFhp7tGvLWQ4NxXLFwz"], [5, 74, 71]], [11, -79, [0, "4cmRqxg3pOPa5gDY/sZl+t"], 24]], [1, "e8JKnODhtBtq52b0mJ1XGi", null, null, null, 1, 0], [1, 16.157, 19.661, 0], [3, 0, 0, 0.25966187574449157, 0.9656995962952725], [1, 0.6, 0.6, 1], [1, 0, 0, 30.099999999999998]], [36, "icon_xx_x3", false, 33554432, 4, [[3, -80, [0, "39HFujutRJGrYJEo8WEMd2"], [5, 74, 71]], [11, -81, [0, "04JgybuERPzJo0dwLEHAnb"], 25]], [1, "b0yHLCGRtHfLs7juGr+nkF", null, null, null, 1, 0], [1, -9.253, -2.697, 0], [1, 0.6, 0.6, 1]], [51, "icon_xx_x4", false, 33554432, 4, [[3, -82, [0, "caSjVehqZH/at6J7Tg0DMW"], [5, 74, 71]], [11, -83, [0, "89ClRA+itHgofXDKF7C4kP"], 26]], [1, "3e4RXG++NEeZpmnj9ol3RW", null, null, null, 1, 0], [1, 13.494, -3.855, 0], [3, 0, 0, -0.14090123193758267, 0.9900236577165575], [1, 0.6, 0.6, 1], [1, 0, 0, -16.2]], [36, "icon_xx_x5", false, 33554432, 4, [[3, -84, [0, "eeJ3dq0MdOhpRzdUhYERc7"], [5, 74, 71]], [11, -85, [0, "e5z3OrcY5B/aXS0SUiol0d"], 27]], [1, "e1YyUzS6ZPcJrp1UwQ+PXJ", null, null, null, 1, 0], [1, 4.241, 8.866999999999962, 0], [1, 0.6, 0.6, 1]], [36, "pj_kuang_sp1", false, 33554432, 2, [[3, -86, [0, "eefxDdHMRHxLaWZk/4T9uD"], [5, 93, 90]], [8, 0, -87, [0, "013x4v+WtBSKjFbL8qajNv"], 55], [16, -88, [0, "28TtfR8N9CGYDK/3NdpAFj"], [56, 57, 58, 59]]], [1, "0br7c3QvFM0r29VOtfar3n", null, null, null, 1, 0], [1, -32, -32, 0], [1, 0.35, 0.35, 1]], [25, "pj_kuang_hui1", false, 33554432, 2, [-91], [[3, -89, [0, "76251ANRZBaprdfQQo4pJZ"], [5, 113, 115]], [8, 0, -90, [0, "f0oK3gfVVB/Ysqwfz17VMv"], 61]], [1, "0anY0MYOxO/Z9hVJjv/0jT", null, null, null, 1, 0], [1, 0, -1, 0]], [25, "bg_di2", false, 33554432, 2, [-94], [[3, -92, [0, "56wbKK5ihAhK0+zeY8wFln"], [5, 122, 22]], [56, false, -93, [0, "59etWw+P9OWrRsLMzUYV3r"], 65]], [1, "2f4ao8X7dB/JreY4NVjmxE", null, null, null, 1, 0], [1, 0, -70.629, 0]], [30, "icon_xx_1", false, 33554432, 3, [[3, -95, [0, "88IkD0YShOFKkUrqK69JwE"], [5, 78, 80]], [8, 0, -96, [0, "35GgF9pqJEEqeZ321Tn7AX"], 7]], [1, "4aanujv+pPyZacqbkMDG40", null, null, null, 1, 0], [1, 39, 0, 0], [3, 0, 0, 0.15643446504023087, 0.9876883405951378], [1, 0, 0, 18]], [30, "icon_xx_2", false, 33554432, 3, [[3, -97, [0, "20x1cl2C5Neb+9RtzVredW"], [5, 78, 80]], [8, 0, -98, [0, "9cxeOjo9pI3ISdvmZB9Kl2"], 8]], [1, "58rdXR/QpBW4fGWscpkCQn", null, null, null, 1, 0], [1, 39, 0, 0], [3, 0, 0, 0.15643446504023087, 0.9876883405951378], [1, 0, 0, 18]], [30, "icon_xx_3", false, 33554432, 3, [[3, -99, [0, "deusj0qZZP/Id9lNMWXxgn"], [5, 78, 80]], [8, 0, -100, [0, "0eYN4SgLpBBIihO+tqTxxB"], 9]], [1, "e9jaHf/bJHb4e0wKZQUIAA", null, null, null, 1, 0], [1, 105.1, 0, 0], [3, 0, 0, 0.15643446504023087, 0.9876883405951378], [1, 0, 0, 18]], [30, "icon_xx_4", false, 33554432, 3, [[3, -101, [0, "4eKswmJ99BJrjfsQbL/XjI"], [5, 78, 80]], [8, 0, -102, [0, "220cVtSFBCLrns+PTMfH/1"], 10]], [1, "0776FEED1KQos0Rdw5Hzig", null, null, null, 1, 0], [1, 171.2, 0, 0], [3, 0, 0, 0.15643446504023087, 0.9876883405951378], [1, 0, 0, 18]], [30, "icon_xx_5", false, 33554432, 3, [[3, -103, [0, "64CbzUddpEDKvA/M2aVY5o"], [5, 78, 80]], [8, 0, -104, [0, "37kSIdDE9PEZOEsbjmdeG/"], 11]], [1, "ed47B0VyNBqYyltMm4GWR+", null, null, null, 1, 0], [1, 237.29999999999998, 0, 0], [3, 0, 0, 0.15643446504023087, 0.9876883405951378], [1, 0, 0, 18]], [10, "txt_item_dj", 33554432, 2, [[13, -105, [0, "2a+NEz1hJH37Gy8QtP+C4Q"], [5, 157, 60], [0, 1, 0.5]], [65, "", 2, 44, 44, 52, 2, false, true, true, 3, -106, [0, "eeoXHNF6JHCZD795RQ9Goj"], [4, **********]]], [1, "8ev1z6IH5OgYw4mQ5jIqFq", null, null, null, 1, 0], [1, 51, 41, 0], [1, 0.5, 0.5, 1]], [26, "pj_kuang_mask1", false, 33554432, 2, [[3, -107, [0, "d3AtRMiJNCOYRAaUItr2qu"], [5, 113, 115]], [11, -108, [0, "31IP5pa1VA76/aggSUz5Pz"], 19]], [1, "c91yTbP25N/4XFQDypTQOW", null, null, null, 1, 0]], [21, "icon_ysz_qz2", 33554432, 6, [[3, -109, [0, "e5lw6LrxVEBYcP5rNSZ/lY"], [5, 110, 87]], [11, -110, [0, "ceXedKAFpDp6DOrAvmZj00"], 20]], [1, "29MdMtLIxA4r1ScBbUZDil", null, null, null, 1, 0]], [21, "icon_tx_1", 33554432, 7, [[3, -111, [0, "606TA8kEhHO7wqCfh+eRAX"], [5, 34.9, 35.9]], [31, -112, [0, "9aZqwEH8tKP5wny41UCaHi"]]], [1, "02MF/d1AROM7A3n2GBKJlq", null, null, null, 1, 0]], [21, "kapai_1", 33554432, 15, [[3, -113, [0, "e51kk5xcZNA7OMzYGNC/RT"], [5, 113, 115]], [83, 0, -114, [0, "5bb/B4LDRMZIV+C0DM3nKM"], [4, 2181038080], 22]], [1, "ebrrubEJBGTJYM3hO+gyBu", null, null, null, 1, 0]], [9, "ani_hblb_tjlq", 33554432, 8, [[3, -115, [0, "47/jdMJ9xNCY6l9HRo5k/L"], [5, 200, 200]], [105, "default", false, 0, -116, [0, "c7KsrqGUZCZpyKv2qGPWcR"], 53]], [1, "18oYLAM4ZPpKl8du4keRyK", null, null, null, 1, 0], [1, -8.376, 15.4, 0]], [21, "pj_kuang_wh1", 33554432, 22, [[3, -117, [0, "ebeZRShwNGHLNdMhu67NtQ"], [5, 84, 84]], [11, -118, [0, "b2I4o6zOtI2YvbTCY86eYK"], 60]], [1, "08hEwPt61Fc7tsjasyigDx", null, null, null, 1, 0]], [10, "txt_sq_name1", 33554432, 23, [[3, -119, [0, "fcmxFXbBNBxrypY3HwB9v8"], [5, 6, 71.52]], [103, "", 34, 34, 52, false, true, true, 3, -120, [0, "a2fk2u7uxFQ4f+loE4Y1FZ"], [4, **********]]], [1, "e9vaf6qpVKcItEK4zqbpr1", null, null, null, 1, 0], [1, 0.5, 0, 0], [1, 0.5, 0.5, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 10, 0, -2, 11, 0, -3, 3, 0, -4, 29, 0, -5, 5, 0, -6, 30, 0, -7, 6, 0, -8, 14, 0, -9, 15, 0, -10, 21, 0, -11, 22, 0, -12, 9, 0, -13, 23, 0, 0, 3, 0, 0, 3, 0, -1, 24, 0, -2, 25, 0, -3, 26, 0, -4, 27, 0, -5, 28, 0, 0, 4, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, -4, 19, 0, -5, 20, 0, 0, 4, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, -4, 19, 0, -5, 20, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 12, 0, 0, 6, 0, 0, 6, 0, -1, 31, 0, -2, 13, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 32, 0, 0, 8, 0, 0, 8, 0, -2, 34, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -1, 33, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, -1, 35, 0, 0, 23, 0, 0, 23, 0, -1, 36, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 6, 1, 4, 7, 8, 7, 7, 14, 8, 7, 15, 120], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, -1, -2, -3, -4, -5, -6, 1, 1, 1, 1, 1, -1, -2, -3, -4, 1, -1, -2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 15, 1, 1, -1, -2, -3, -4, 1, 1, 1, -1, 9, 1, -1, 9], [7, 7, 22, 23, 24, 25, 26, 1, 1, 1, 1, 1, 62, 63, 64, 14, 13, 13, 65, 66, 67, 13, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 69, 12, 12, 28, 11, 29, 70, 27, 30, 8, 8, 71, 6, 6]], [[{"name": "bg_top_hb_2", "rect": {"x": 0, "y": 0, "width": 550, "height": 1003}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 550, "height": 1003}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-275, -501.5, 0, 275, -501.5, 0, -275, 501.5, 0, 275, 501.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1003, 550, 1003, 0, 0, 550, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -275, "y": -501.5, "z": 0}, "maxPos": {"x": 275, "y": 501.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [12], [72]], [[[20, "list_xh1"], [19, "list_xh1", 33554432, [-4, -5], [[3, -2, [0, "47Cs6IYElOBK5jyf6MFKqL"], [5, 56.90625, 30]], [58, 1, 1, 4, true, -3, [0, "46wgLU6vlHt54vDxwmo6H/"]]], [1, "79BcNar8tDmZucBHLjoa8/", null, null, null, -1, 0]], [9, "token", 33554432, 1, [[3, -6, [0, "6eLK82LLdNRKcY/pydBpFc"], [5, 31, 31]], [11, -7, [0, "90X4zuDxlM2ap0ok+kkml6"], 0]], [1, "5dxakxnn1IH5IOSWRbNW25", null, null, null, 1, 0], [1, -12.953125, 0, 0]], [10, "txt_sx_1", 33554432, 1, [[3, -8, [0, "feEue8VsVDB4gRggznsuay"], [5, 43.8125, 66.48]], [104, "*1", 40, 48, true, true, 3, -9, [0, "0dpBBsahVNwKBm3pB6bLA5"], [4, 4281742902], [4, 4294967295]]], [1, "ab6OaQyhFLSaBnxtHZuhoA", null, null, null, 1, 0], [1, 17.5, 0, 0], [1, 0.5, 0.5, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 6, 1, 9], [0], [1], [73]]]]