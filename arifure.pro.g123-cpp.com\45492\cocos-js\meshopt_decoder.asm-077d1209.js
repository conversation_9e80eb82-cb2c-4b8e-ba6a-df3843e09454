System.register(["./_virtual_cc-1b85bdd4.js"],(function(e){"use strict";var r;return{setters:[function(e){r=e.c}],execute:function(){var t=r((function(e){var r=function(){var e={},r=Promise.resolve().then((function(){e.exports=function(e){for(var r,t=new Uint8Array(123),n=25;n>=0;--n)t[48+n]=52+n,t[65+n]=n,t[97+n]=26+n;function o(){!function(e,r,n){for(var o,f,a=0,i=r,s=n.length,c=r+(3*s>>2)-("="==n[s-2])-("="==n[s-1]);a<s;a+=4)o=t[n.charCodeAt(a+1)],f=t[n.charCodeAt(a+2)],e[i++]=t[n.charCodeAt(a)]<<2|o>>4,i<c&&(e[i++]=o<<4|f>>2),i<c&&(e[i++]=f<<6|t[n.charCodeAt(a+3)])}(r,1024,"EGQAAA==")}return t[43]=62,t[47]=63,function(e){var t=new ArrayBuffer(65536),n=new Int8Array(t),f=new Int16Array(t),a=new Int32Array(t),i=new Uint8Array(t),s=Math.imul;e.abort;var c=25616;function u(e,r,t){t|=0;var o=0;if(3&((r|=0)|(e|=0)|0)|0)o=e;else{if(t>>>0>=16)for(o=e;a[o>>2]=0|a[r>>2],a[(o+4|0)>>2]=0|a[(r+4|0)>>2],a[(o+8|0)>>2]=0|a[(r+8|0)>>2],a[(o+12|0)>>2]=0|a[(r+12|0)>>2],r=r+16|0,o=o+16|0,(t=t+-16|0)>>>0>15;);else o=e;if(!(t>>>0<4))for(;a[o>>2]=0|a[r>>2],r=r+4|0,o=o+4|0,(t=t+-4|0)>>>0>3;);}if(t)for(;n[o>>0]=0|i[r>>0],o=o+1|0,r=r+1|0,t=t+-1|0;);return 0|e}function d(){return t.byteLength/65536|0}function b(e){e|=0;var o=0|d(),c=o+e|0;if(o<c&&c<65536){var u=new ArrayBuffer(s(c,65536));new Int8Array(u).set(n),n=new Int8Array(u),f=new Int16Array(u),a=new Int32Array(u),i=new Uint8Array(u),t=u,r=i}return o}return r=i,o(),{memory:Object.create(Object.prototype,{grow:{value:b},buffer:{get:function(){return t}}}),__wasm_call_ctors:function(){},meshopt_decodeVertexBuffer:function(e,r,t,o,f){e|=0,r|=0,o|=0;var d,b=0,l=0,k=0,p=0,v=0,A=0,h=0,w=0,m=0,x=0,y=0,_=0,g=0,I=0,U=0,B=0,E=0,j=0,O=0,S=0,L=0,N=0,R=0,q=0,C=0,T=0,M=0,G=0,P=0,V=0,z=0,F=0,Q=0,W=0,D=0,H=0,X=0,J=0,K=0,Y=0,Z=0,$=0,ee=0,re=0,te=0,ne=0,oe=0,fe=0,ae=0,ie=0,se=0,ce=0,ue=0,de=0,be=0,le=0,ke=0,pe=0;c=d=c-8704|0,p=-2;e:if(!((1+(t|=0)|0)>>>0>(f|=0)>>>0)&&(p=-1,160==(0|i[o>>0]))){A=0|u(0|d,0|(g=o+f|0)-t,0|t),f=8192/(t>>>0)|0,p=o+1|0;r:if(t){L=(f=16368&f|0)>>>0<256?f:256,B=0;t:for(;;){if(B>>>0>=r>>>0)break r;I=(3+((f=(y=(B+L|0)>>>0<r>>>0?L:r-B|0)+15|0)>>>4|0)|0)>>>2|0;n:{o:{f:{if(x=-16&f|0){E=0,U=1,S=A+256|0,m=p;a:for(;;){if((g-m|0)>>>0<I>>>0)break o;if(!((g-(p=m+I|0)|0)>>>0<24)){f=16,h=0;i:for(;;){o=f,f=(l=f+-16|0)+(A+8448|0)|0;s:{c:switch(0|3&((0|i[(m+(l>>>6|0)|0)>>0])>>>(6&h|0)|0)){default:v=f,w=0,a[f>>2]=0,a[(f+4|0)>>2]=w,w=0,a[(v=f+8|0)>>2]=0,a[(v+4|0)>>2]=w;break s;case 1:N=b=(l=0|i[p>>0])>>>6|0,b=3==(0|b),n[f>>0]=(ke=0|i[(p+4|0)>>0],pe=N,b?ke:pe),b=(p+4|0)+b|0,R=k=3&(l>>>4|0)|0,k=3==(0|k),n[((f=(A+8448|0)+o|0)-15|0)>>0]=(ke=0|i[b>>0],pe=R,k?ke:pe),b=b+k|0,q=k=3&(l>>>2|0)|0,k=3==(0|k),n[(f+-14|0)>>0]=(ke=0|i[b>>0],pe=q,k?ke:pe),b=b+k|0,C=l=3&l|0,l=3==(0|l),n[(f+-13|0)>>0]=(ke=0|i[b>>0],pe=C,l?ke:pe),b=b+l|0,T=k=(l=0|i[(p+1|0)>>0])>>>6|0,k=3==(0|k),n[(f+-12|0)>>0]=(ke=0|i[b>>0],pe=T,k?ke:pe),b=b+k|0,M=k=3&(l>>>4|0)|0,k=3==(0|k),n[(f+-11|0)>>0]=(ke=0|i[b>>0],pe=M,k?ke:pe),b=b+k|0,G=k=3&(l>>>2|0)|0,k=3==(0|k),n[(f+-10|0)>>0]=(ke=0|i[b>>0],pe=G,k?ke:pe),b=b+k|0,P=l=3&l|0,l=3==(0|l),n[(f+-9|0)>>0]=(ke=0|i[b>>0],pe=P,l?ke:pe),b=b+l|0,V=k=(l=0|i[(p+2|0)>>0])>>>6|0,k=3==(0|k),n[(f+-8|0)>>0]=(ke=0|i[b>>0],pe=V,k?ke:pe),b=b+k|0,z=k=3&(l>>>4|0)|0,k=3==(0|k),n[(f+-7|0)>>0]=(ke=0|i[b>>0],pe=z,k?ke:pe),b=b+k|0,F=k=3&(l>>>2|0)|0,k=3==(0|k),n[(f+-6|0)>>0]=(ke=0|i[b>>0],pe=F,k?ke:pe),b=b+k|0,Q=l=3&l|0,l=3==(0|l),n[(f+-5|0)>>0]=(ke=0|i[b>>0],pe=Q,l?ke:pe),l=b+l|0,W=b=(p=0|i[(p+3|0)>>0])>>>6|0,b=3==(0|b),n[(f+-4|0)>>0]=(ke=0|i[l>>0],pe=W,b?ke:pe),l=l+b|0,D=b=3&(p>>>4|0)|0,b=3==(0|b),n[(f+-3|0)>>0]=(ke=0|i[l>>0],pe=D,b?ke:pe),l=l+b|0,H=b=3&(p>>>2|0)|0,b=3==(0|b),n[(f+-2|0)>>0]=(ke=0|i[l>>0],pe=H,b?ke:pe),X=f+-1|0,f=l+b|0,J=p=3&p|0,p=3==(0|p),n[X>>0]=(ke=0|i[f>>0],pe=J,p?ke:pe),p=f+p|0;break s;case 2:K=b=(l=0|i[p>>0])>>>4|0,b=15==(0|b),n[f>>0]=(ke=0|i[(p+8|0)>>0],pe=K,b?ke:pe),b=(p+8|0)+b|0,Y=l=15&l|0,l=15==(0|l),n[((f=(A+8448|0)+o|0)-15|0)>>0]=(ke=0|i[b>>0],pe=Y,l?ke:pe),l=b+l|0,Z=k=(b=0|i[(p+1|0)>>0])>>>4|0,k=15==(0|k),n[(f+-14|0)>>0]=(ke=0|i[l>>0],pe=Z,k?ke:pe),l=l+k|0,$=b=15&b|0,b=15==(0|b),n[(f+-13|0)>>0]=(ke=0|i[l>>0],pe=$,b?ke:pe),l=l+b|0,ee=k=(b=0|i[(p+2|0)>>0])>>>4|0,k=15==(0|k),n[(f+-12|0)>>0]=(ke=0|i[l>>0],pe=ee,k?ke:pe),l=l+k|0,re=b=15&b|0,b=15==(0|b),n[(f+-11|0)>>0]=(ke=0|i[l>>0],pe=re,b?ke:pe),l=l+b|0,te=k=(b=0|i[(p+3|0)>>0])>>>4|0,k=15==(0|k),n[(f+-10|0)>>0]=(ke=0|i[l>>0],pe=te,k?ke:pe),l=l+k|0,ne=b=15&b|0,b=15==(0|b),n[(f+-9|0)>>0]=(ke=0|i[l>>0],pe=ne,b?ke:pe),l=l+b|0,oe=k=(b=0|i[(p+4|0)>>0])>>>4|0,k=15==(0|k),n[(f+-8|0)>>0]=(ke=0|i[l>>0],pe=oe,k?ke:pe),l=l+k|0,fe=b=15&b|0,b=15==(0|b),n[(f+-7|0)>>0]=(ke=0|i[l>>0],pe=fe,b?ke:pe),l=l+b|0,ae=k=(b=0|i[(p+5|0)>>0])>>>4|0,k=15==(0|k),n[(f+-6|0)>>0]=(ke=0|i[l>>0],pe=ae,k?ke:pe),l=l+k|0,ie=b=15&b|0,b=15==(0|b),n[(f+-5|0)>>0]=(ke=0|i[l>>0],pe=ie,b?ke:pe),l=l+b|0,se=k=(b=0|i[(p+6|0)>>0])>>>4|0,k=15==(0|k),n[(f+-4|0)>>0]=(ke=0|i[l>>0],pe=se,k?ke:pe),l=l+k|0,ce=b=15&b|0,b=15==(0|b),n[(f+-3|0)>>0]=(ke=0|i[l>>0],pe=ce,b?ke:pe),l=l+b|0,ue=b=(p=0|i[(p+7|0)>>0])>>>4|0,b=15==(0|b),n[(f+-2|0)>>0]=(ke=0|i[l>>0],pe=ue,b?ke:pe),de=f+-1|0,f=l+b|0,be=p=15&p|0,p=15==(0|p),n[de>>0]=(ke=0|i[f>>0],pe=be,p?ke:pe),p=f+p|0;break s;case 3:}w=0|i[p>>0]|(0|i[(p+1|0)>>0])<<8|0|(0|i[(p+2|0)>>0])<<16|0|(0|i[(p+3|0)>>0])<<24|0,v=0|i[(p+4|0)>>0]|(0|i[(p+5|0)>>0])<<8|0|(0|i[(p+6|0)>>0])<<16|0|(0|i[(p+7|0)>>0])<<24|0,le=w,w=f,j=le,n[f>>0]=j,n[(f+1|0)>>0]=j>>>8|0,n[(f+2|0)>>0]=j>>>16|0,n[(f+3|0)>>0]=j>>>24|0,n[(f+4|0)>>0]=v,n[(f+5|0)>>0]=v>>>8|0,n[(f+6|0)>>0]=v>>>16|0,n[(f+7|0)>>0]=v>>>24|0,v=0|i[(_=p+8|0)>>0]|(0|i[(_+1|0)>>0])<<8|0|(0|i[(_+2|0)>>0])<<16|0|(0|i[(_+3|0)>>0])<<24|0,w=0|i[(_+4|0)>>0]|(0|i[(_+5|0)>>0])<<8|0|(0|i[(_+6|0)>>0])<<16|0|(0|i[(_+7|0)>>0])<<24|0,O=v,n[(v=f+8|0)>>0]=O,n[(v+1|0)>>0]=O>>>8|0,n[(v+2|0)>>0]=O>>>16|0,n[(v+3|0)>>0]=O>>>24|0,n[(v+4|0)>>0]=w,n[(v+5|0)>>0]=w>>>8|0,n[(v+6|0)>>0]=w>>>16|0,n[(v+7|0)>>0]=w>>>24|0,p=p+16|0}if(o>>>0>=x>>>0||(h=h+2|0,f=o+16|0,!((g-p|0)>>>0>23)))break i}if(o>>>0<x>>>0)break f;if(!p)break f;if(y)for(h=0|i[(A+E|0)>>0],f=A+8448|0,o=S,l=y;h=(((m=0|i[f>>0])>>>1|0)^(0-(1&m|0)|0)|0)+h|0,n[o>>0]=h,o=o+t|0,f=f+1|0,l=l+-1|0;);if(S=S+1|0,U=(E=E+1|0)>>>0<t>>>0,m=p,(0|E)!=(0|t))continue a;break n}break a}if(p=0,1&U|0)break o;break n}if(k=p+s(I,t)|0,y){x=0,U=1,b=A+256|0;a:for(;;){if((g-p|0)>>>0<I>>>0)break o;if(!p)break f;for(p=p+I|0,h=0|i[(A+x|0)>>0],f=A+8448|0,o=b,l=y;h=(((m=0|i[f>>0])>>>1|0)^(0-(1&m|0)|0)|0)+h|0,n[o>>0]=h,o=o+t|0,f=f+1|0,l=l+-1|0;);if(b=b+1|0,U=(x=x+1|0)>>>0<t>>>0,(0|x)==(0|t))break a}p=k;break n}f=0,U=1;a:for(;;){if((g-p|0)>>>0<I>>>0)break o;if(!p)break f;if(p=p+I|0,U=(f=f+1|0)>>>0<t>>>0,(0|t)==(0|f))break a}p=k;break n}if(p=0,!(1&U|0))break n}p=-2;break e}if(u(0|e+s(B,t),0|A+256,0|s(y,t)),u(0|A,0|(A+256|0)+s(y+-1|0,t),0|t),B=y+B|0,!p)break t}p=-2;break e}p=(0|g-p)==(0|(t>>>0>32?t:32))?0:-3}return c=d+8704|0,0|p},meshopt_decodeIndexBuffer:function(e,r,t,o,u){e|=0,t|=0,o|=0;var d,b=0,l=0,k=0,p=0,v=0,A=0,h=0,w=0,m=0,x=0,y=0,_=0,g=0,I=0,U=0,B=0,E=0,j=0,O=0,S=0;c=d=c-192|0,p=-2;e:if(!((17+(y=((r|=0)>>>0)/3|0)|0)>>>0>(u|=0)>>>0||(p=-1,224!=(0|240&(I=0|i[o>>0]))||(b=15&I|0)>>>0>1))){if(function(e,r,t){r|=0,t|=0;var o=0,f=0;if(3&(e|=0)|0)o=e;else{if(f=s(255&r|0,16843009),t>>>0>=16)for(o=e;a[o>>2]=f,a[(o+12|0)>>2]=f,a[(o+8|0)>>2]=f,a[(o+4|0)>>2]=f,o=o+16|0,(t=t+-16|0)>>>0>15;);else o=e;if(!(t>>>0<4))for(;a[o>>2]=f,o=o+4|0,(t=t+-4|0)>>>0>3;);}if(t)for(;n[o>>0]=r,o=o+1|0,t=t+-1|0;);}(0|d+64,255,128),x=-1,a[(w=d+56|0)>>2]=-1,a[(w+4|0)>>2]=x,x=-1,a[(w=d+48|0)>>2]=-1,a[(w+4|0)>>2]=x,x=-1,a[(w=d+40|0)>>2]=-1,a[(w+4|0)>>2]=x,x=-1,a[(w=d+32|0)>>2]=-1,a[(w+4|0)>>2]=x,x=-1,a[(w=d+24|0)>>2]=-1,a[(w+4|0)>>2]=x,x=-1,a[(w=d+16|0)>>2]=-1,a[(w+4|0)>>2]=x,w=d,x=-1,a[(d+8|0)>>2]=-1,a[(d+12|0)>>2]=x,w=d,x=-1,a[d>>2]=-1,a[(d+4|0)>>2]=x,B=(o+u|0)-16|0,p=(I=o+1|0)+y|0,r){j=1==(0|b)?13:15,_=0,m=0,y=0,o=0,u=0;r:for(;;){if(!(p>>>0<=B>>>0)){p=-2;break e}t:if((b=0|i[I>>0])>>>0>239)if(b>>>0>253){if(h=(g=(v=0|i[p>>0])?_:0)+(b=254==(0|b))|0,U=15&v|0,E=v>>>4|0,v>>>0>15?(A=h,h=0|a[(d+((15&(o-E|0)|0)<<2|0)|0)>>2]):A=h+1|0,U?(_=A,A=0|a[(d+((15&(o-v|0)|0)<<2|0)|0)>>2]):_=A+1|0,b)b=p+1|0;else{b=p+2|0,l=255&(k=0|n[(p+1|0)>>0])|0;n:if(!((0|k)>-1)){g=p+6|0,l=127&l|0,p=7;o:{f:for(;;){if(l=(127&(k=0|n[b>>0])|0)<<p|0|l|0,(0|k)>-1)break o;if(b=b+1|0,35==(0|(p=p+7|0)))break f}b=g;break n}b=b+1|0}g=m=((l>>>1|0)^(0-(1&l|0)|0)|0)+m|0}if(15==(0|E)){l=b+1|0,k=255&(p=0|n[b>>0])|0;n:if(!((0|p)>-1)){h=b+5|0,k=127&k|0,p=7;o:{f:for(;;){if(k=(127&(b=0|n[l>>0])|0)<<p|0|k|0,(0|b)>-1)break o;if(l=l+1|0,35==(0|(p=p+7|0)))break f}l=h;break n}l=l+1|0}h=m=((k>>>1|0)^(0-(1&k|0)|0)|0)+m|0}else l=b;if(15==(0|U)){p=l+1|0,k=255&(b=0|n[l>>0])|0;n:if(!((0|b)>-1)){A=l+5|0,k=127&k|0,b=7;o:{f:for(;;){if(k=(127&(l=0|n[p>>0])|0)<<b|0|k|0,(0|l)>-1)break o;if(p=p+1|0,35==(0|(b=b+7|0)))break f}p=A;break n}p=p+1|0}A=m=((k>>>1|0)^(0-(1&k|0)|0)|0)+m|0}else p=l;2!=(0|t)?(a[(b=e+(y<<2|0)|0)>>2]=g,a[(b+4|0)>>2]=h,a[(b+8|0)>>2]=A):(f[(b=e+(y<<1|0)|0)>>1]=g,f[(b+2|0)>>1]=h,f[(b+4|0)>>1]=A),a[(b=(d+64|0)+(u<<3|0)|0)>>2]=h,a[(b+4|0)>>2]=g,a[(d+(o<<2|0)|0)>>2]=g,a[(b=(d+64|0)+((15&(u+1|0)|0)<<3|0)|0)>>2]=A,a[(b+4|0)>>2]=h,a[(d+((15&(o=o+1|0)|0)<<2|0)|0)>>2]=h,a[(b=(d+64|0)+((15&(u+2|0)|0)<<3|0)|0)>>2]=g,a[(b+4|0)>>2]=A,a[(d+((15&(o=o+(v>>>0<16|15==(0|E)|0)|0)|0)<<2|0)|0)>>2]=A,o=o+(!U|15==(0|U)|0)|0,u=u+3|0}else A=_+1|0,k=(l=0|i[(B+(15&b|0)|0)>>0])>>>0<16,O=A,S=0|a[(d+((15&(o-(l>>>4|0)|0)|0)<<2|0)|0)>>2],b=k?O:S,h=A+k|0,A=15&l|0,O=0|a[(d+((15&(o-l|0)|0)<<2|0)|0)>>2],S=h,l=A?O:S,A=!A,2!=(0|t)?(a[(v=e+(y<<2|0)|0)>>2]=_,a[(v+4|0)>>2]=b,a[(v+8|0)>>2]=l):(f[(v=e+(y<<1|0)|0)>>1]=_,f[(v+2|0)>>1]=b,f[(v+4|0)>>1]=l),a[(d+(o<<2|0)|0)>>2]=_,a[(v=(d+64|0)+(u<<3|0)|0)>>2]=b,a[(v+4|0)>>2]=_,a[(d+((15&(o=o+1|0)|0)<<2|0)|0)>>2]=b,a[(v=(d+64|0)+((15&(u+1|0)|0)<<3|0)|0)>>2]=l,a[(v+4|0)>>2]=b,a[(d+((o=15&(o+k|0)|0)<<2|0)|0)>>2]=l,a[(b=(d+64|0)+((u=15&(u+2|0)|0)<<3|0)|0)>>2]=_,a[(b+4|0)>>2]=l,u=u+1|0,o=o+A|0,_=h+A|0;else{if(h=0|a[(4+(l=(d+64|0)+((15&(u+((k=-1^b|0)>>>4|0)|0)|0)<<3|0)|0)|0)>>2],v=0|a[l>>2],!((b=15&b|0)>>>0>=j>>>0)){O=0|a[(d+((15&(o+k|0)|0)<<2|0)|0)>>2],S=_,l=b?O:S,b=!b,2!=(0|t)?(a[(k=e+(y<<2|0)|0)>>2]=v,a[(k+4|0)>>2]=h,a[(k+8|0)>>2]=l):(f[(k=e+(y<<1|0)|0)>>1]=v,f[(k+2|0)>>1]=h,f[(k+4|0)>>1]=l),_=_+b|0,a[(k=(d+64|0)+(u<<3|0)|0)>>2]=l,a[(k+4|0)>>2]=h,a[(d+(o<<2|0)|0)>>2]=l,a[(k=(d+64|0)+((u=15&(u+1|0)|0)<<3|0)|0)>>2]=v,a[(k+4|0)>>2]=l,o=o+b|0,u=u+1|0;break t}if(15==(0|b)){b=p+1|0,k=255&(l=0|n[p>>0])|0;n:if((0|l)<=-1){p=p+5|0,k=127&k|0,l=7;o:for(;k=(127&(A=0|n[b>>0])|0)<<l|0|k|0,!((0|A)>-1);)if(b=b+1|0,35==(0|(l=l+7|0)))break n;p=b+1|0}else p=b;m=((k>>>1|0)^(0-(1&k|0)|0)|0)+m|0}else m=1+((m+b|0)+(-4^b|0)|0)|0;2!=(0|t)?(a[(b=e+(y<<2|0)|0)>>2]=v,a[(b+4|0)>>2]=h,a[(b+8|0)>>2]=m):(f[(b=e+(y<<1|0)|0)>>1]=v,f[(b+2|0)>>1]=h,f[(b+4|0)>>1]=m),a[(b=(d+64|0)+(u<<3|0)|0)>>2]=m,a[(b+4|0)>>2]=h,a[(d+(o<<2|0)|0)>>2]=m,a[(b=(d+64|0)+((u=15&(u+1|0)|0)<<3|0)|0)>>2]=v,a[(b+4|0)>>2]=m,o=o+1|0,u=u+1|0}if(I=I+1|0,u=15&u|0,o=15&o|0,!((y=y+3|0)>>>0<r>>>0))break r}}p=(0|p)==(0|B)?0:-3}return c=d+192|0,0|p},sbrk:function(e){e|=0;var r,t,n=0;return e=(r=0|a[256])+(-4&(e+3|0)|0)|0,a[256]=e,(e>>>0<=(t=d()<<16|0)>>>0||(n=-1,-1!=(0|b(0|(65535+(e-t|0)|0)>>>16))))&&(n=r),0|n}}}(e)}({abort:function(e,r,t,n){throw Error("abort: "+e+" at "+r+":"+t+":"+n)}}),e.exports.__wasm_call_ctors()}));function t(r,t,n,o,f,a){var i=e.exports.sbrk,s=n+3&-4,c=i(s*o),u=i(f.length),d=new Uint8Array(e.exports.memory.buffer);d.set(f,u);var b=r(c,n,o,u,f.length);if(0==b&&a&&a(c,s,o),t.set(d.subarray(c,c+n*o)),i(c-i(0)),0!=b)throw new Error("Malformed buffer data: "+b)}var n={NONE:"",OCTAHEDRAL:"meshopt_decodeFilterOct",QUATERNION:"meshopt_decodeFilterQuat",EXPONENTIAL:"meshopt_decodeFilterExp"},o={ATTRIBUTES:"meshopt_decodeVertexBuffer",TRIANGLES:"meshopt_decodeIndexBuffer",INDICES:"meshopt_decodeIndexSequence"},f=[],a=0;function i(e){var r={object:new Worker(e),pending:0,requests:{}};return r.object.onmessage=function(e){var t=e.data;r.pending-=t.count,r.requests[t.id][t.action](t.value),delete r.requests[t.id]},r}function s(e){for(var r="var instance; var ready = WebAssembly.instantiate(new Uint8Array(["+new Uint8Array(function(e){for(var r=new Uint8Array(e.length),t=0;t<e.length;++t){var n=e.charCodeAt(t);r[t]=n>96?n-97:n>64?n-39:n+4}var o=0;for(t=0;t<e.length;++t)r[o++]=r[t]<60?wasmpack[r[t]]:64*(r[t]-60)+r[++t];return r.buffer.slice(0,o)}(wasm))+"]), {}).then(function(result) { instance = result.instance; instance.exports.__wasm_call_ctors(); });self.onmessage = workerProcess;"+t.toString()+c.toString(),n=new Blob([r],{type:"text/javascript"}),o=URL.createObjectURL(n),a=0;a<e;++a)f[a]=i(o);URL.revokeObjectURL(o)}function c(n){r.then((function(){var r=n.data;try{var o=new Uint8Array(r.count*r.size);t(e.exports[r.mode],o,r.count,r.size,r.source,e.exports[r.filter]),self.postMessage({id:r.id,count:r.count,action:"resolve",value:o},[o.buffer])}catch(e){self.postMessage({id:r.id,count:r.count,action:"reject",value:e})}}))}return{ready:r,supported:!0,useWorkers:function(e){s(e)},decodeVertexBuffer:function(r,o,f,a,i){t(e.exports.meshopt_decodeVertexBuffer,r,o,f,a,e.exports[n[i]])},decodeIndexBuffer:function(r,n,o,f){t(e.exports.meshopt_decodeIndexBuffer,r,n,o,f)},decodeIndexSequence:function(r,n,o,f){t(e.exports.meshopt_decodeIndexSequence,r,n,o,f)},decodeGltfBuffer:function(r,f,a,i,s,c){t(e.exports[o[s]],r,f,a,i,e.exports[n[c]])},decodeGltfBufferAsync:function(i,s,c,u,d){return f.length>0?function(e,r,t,n,o){for(var i=f[0],s=1;s<f.length;++s)f[s].pending<i.pending&&(i=f[s]);return new Promise((function(f,s){var c=new Uint8Array(t),u=a++;i.pending+=e,i.requests[u]={resolve:f,reject:s},i.object.postMessage({id:u,count:e,size:r,source:c,mode:n,filter:o},[c.buffer])}))}(i,s,c,o[u],n[d]):r.then((function(){var r=new Uint8Array(i*s);return t(e.exports[o[u]],r,i,s,c,e.exports[n[d]]),r}))}}}();e.exports=r}));e({default:t,__moduleExports:t})}}}));
