[1, ["fcfATM00NP6aMUfMeuFPxG", "28bfwm51dOHoLVvMxcWZRh@f9941", "7dj5uJT9FMn6OrOOx83tfK@f9941", "84Vs33lNtGvZsMENwV5RIh@f9941", "9fzqq9/o5DuYKqInM0IXhE", "c5BqtF8JdHBrCoyD9gRvrT@f9941", "b335AdVtJF1r+ngLYorZIz@f9941", "7dj5uJT9FMn6OrOOx83tfK@6c48a"], ["node", "_spriteFrame", "root", "asset", "data", "_skeletonData", "_textureSource"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_lpos", "_children", "_lscale", "_euler"], -2, 4, 9, 1, 5, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame", "_color"], 1, 1, 4, 6, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_right", "_bottom", "_left", "_top", "node", "__prefab"], -4, 1, 4], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isBold", "_lineHeight", "_enableOutline", "_outlineWidth", "_overflow", "_enableWrapText", "node", "__prefab", "_color", "_outlineColor"], -6, 1, 4, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_affectedByScale", "node", "__prefab"], -1, 1, 4], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4], ["cc.TargetInfo", ["localID"], 2], ["sp.Skeleton", ["defaultSkin", "_premultipliedAlpha", "_preCacheMode", "node", "__prefab", "_sockets", "_skeletonData"], 0, 1, 4, 9, 6], ["sp.Skeleton.SpineSocket", ["path", "target"], 2, 1], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4]], [[7, 0, 2], [4, 0, 1, 2, 1], [9, 0, 1, 2, 3, 4, 5, 5], [18, 0, 1, 2, 2], [13, 0, 2], [0, 0, 1, 7, 9, 6, 5, 8, 3], [1, 0, 2, 3, 2], [1, 2, 3, 1], [0, 0, 1, 7, 6, 5, 8, 10, 3], [19, 0, 1, 2, 3], [0, 0, 1, 7, 6, 5, 3], [1, 2, 3, 4, 1], [12, 0, 1, 2, 2], [0, 0, 1, 7, 9, 6, 5, 3], [0, 0, 1, 7, 6, 5, 10, 3], [0, 0, 1, 7, 9, 6, 5, 8, 10, 3], [0, 3, 4, 7, 5, 3], [2, 0, 3, 7, 8, 3], [10, 0, 1, 2, 3, 4, 5, 4], [3, 0, 1, 2, 3, 5, 6, 9, 10, 12, 7], [3, 0, 1, 2, 4, 3, 5, 6, 9, 10, 11, 8], [15, 0, 1, 2], [16, 0, 1, 2, 2], [17, 0, 1, 2, 3], [6, 0, 2], [0, 0, 1, 9, 6, 5, 3], [0, 0, 1, 7, 9, 6, 5, 8, 11, 3], [0, 0, 2, 1, 7, 9, 6, 5, 8, 11, 4], [0, 0, 2, 1, 7, 6, 5, 4], [0, 0, 7, 6, 5, 10, 2], [0, 0, 7, 6, 5, 8, 10, 2], [0, 0, 1, 7, 6, 5, 8, 3], [4, 0, 1, 2, 3, 1], [2, 0, 1, 2, 7, 8, 4], [2, 0, 4, 7, 8, 3], [2, 0, 5, 3, 6, 4, 1, 2, 7, 8, 8], [8, 0, 1, 2, 3, 4, 5, 4], [1, 0, 2, 3, 4, 2], [1, 1, 0, 2, 3, 5, 4, 3], [1, 1, 2, 3, 4, 2], [11, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 4, 3, 9, 10, 11, 6], [3, 0, 1, 2, 4, 7, 8, 3, 5, 6, 9, 10, 11, 12, 10], [14, 0, 1, 2, 3, 4, 5, 6, 4]], [[[[24, "gonghuizhan_ppz"], [25, "gonghuizhan_ppz", 33554432, [-6, -7, -8, -9], [[1, -4, [0, "46qsLDjUBGq6Oi/LNQr53p"], [5, 640, 1280]], [33, 45, 640, 1280, -5, [0, "24B8bNcU1FH5bEpCN6DKbr"]]], [36, "c46/YsCPVOJYA4mWEpNYRx", null, null, -3, 0, [-1, -2]]], [13, "neirong", 33554432, 1, [-12, -13, -14, -15, -16, -17], [[1, -10, [0, "aeuMtHjRtC1bWf0uC3Xmn0"], [5, 640, 1280]], [6, 0, -11, [0, "b9/tKwTBFH7Yyd2CLOQL/p"]]], [2, "f8xVg6+URN44vtxjoil+BG", null, null, null, 1, 0]], [26, "img_ppcg_l", 33554432, 2, [-21], [[1, -18, [0, "a9mDk0Ap5HYKKgddEeSzJ4"], [5, 231.162, 152]], [7, -19, [0, "3cRHPocBtF7ZLB3+ahUacW"]], [17, 32, 408.419, -20, [0, "c2Ry8ImpdEs7LRfGBXK+Xn"]]], [2, "6cofmMkMdGfp2ZBZARjinX", null, null, null, 1, 0], [1, -204, -1.5, 0], [1, 0, 0, 2.504478065487657e-06]], [27, "img_ppcg_r", false, 33554432, 2, [-25], [[1, -22, [0, "91jhw0F4RApqiCE9dlon6F"], [5, 231.162, 152]], [7, -23, [0, "24AI1auD1NOotriVnLCLdq"]], [17, 32, 0.4189999999999827, -24, [0, "eeKICS2bBNubXTYE4hA6Ca"]]], [2, "22A4JGNdBOg7NOIrv/iv7M", null, null, null, 1, 0], [1, 204, -1.5, 0], [1, 0, 0, 2.504478065487657e-06]], [5, "wjxx2", 33554432, 4, [-28, -29, -30], [[1, -26, [0, "8cescdlOVDlbAZQUCW/qbM"], [5, 133.3, 136]], [6, 0, -27, [0, "ccWpoTYuFI1qVHc0ss2kF9"]]], [2, "c5P5Qh/p9Jh5An0fYB1iVR", null, null, null, 1, 0], [1, -0.881, 2.245, 0]], [5, "wjxx1", 33554432, 3, [-33, -34, -35], [[1, -31, [0, "60RmGz2yBOOLbUSqAEHrXm"], [5, 133.3, 136]], [6, 0, -32, [0, "deWa5vfJVOk4nZuuNAXk+e"]]], [2, "92Lh1/wclLHpRYPPt1zMSE", null, null, null, 1, 0], [1, -0.881, 2.245, 0]], [5, "click_close", 33554432, 1, [-38, -39, -40], [[1, -36, [0, "c0UVPALkRHWpBx5o7ZpuPN"], [5, 270, 24]], [40, 1, 1, 2, true, -37, [0, "8d3pGzVkdOzKY8u/GyZNZS"]]], [2, "50NFxhF7RFnqHCB8EJumMr", null, null, null, 1, 0], [1, 0, -330, 0]], [14, "txt_gb", 33554432, 7, [[1, -41, [0, "5f3UoQvgdFQaTRrt5UVomX"], [5, 308, 75.6]], [41, "點擊空白處關閉", 44, 44, 60, true, -42, [0, "14q8Up5npEKp0SNHq8OdZ1"], [4, 3221225471]], [34, 2, -32, -43, [0, "f0isMh8BpAVKn6Yf9y0b18"]], [12, "djkb", -44, [0, "2149+5wMlFJasmHxdB+1g0"]], [12, "djkb", -45, [0, "99ojEzl8pNzKCZkdgHlTkI"]]], [2, "9dVjghCwRBDY7u6xR63ieG", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [13, "bg", 33554432, 1, [-48], [[1, -46, [0, "daC3mWHrZHTYuqb5PY0eBm"], [5, 640, 1440]], [37, 0, -47, [0, "973ty/VEtBP7g+ano4N+lD"], 1]], [2, "e9oKX3CRRGKLjcUUFuBjQo", null, null, null, 1, 0]], [28, "bg2", false, 33554432, 11, [[1, -49, [0, "a0BGzItAxC56zRsQFTtXZ9"], [5, 900, 1700]], [38, 1, 0, -50, [0, "cdZ3DeQc5CrJUe2djRJHUf"], [4, 3422552064], 0], [35, 45, -130, -130, -130, -130, 700, 1440, -51, [0, "c0Lwz/or1IArdI1asDe41d"]]], [2, "87UnktkztA26VIyjL/hWvN", null, null, null, 1, 0]], [15, "item_qz2", 33554432, 5, [-54], [[1, -52, [0, "758Oefz8JGRZWEAv/O4s4l"], [5, 113, 117]], [6, 0, -53, [0, "48rJY1elFP/IDcqeh7U2Ql"]]], [2, "eaIVcJ8VlNUJQ2dK+GRkv5", null, null, null, 1, 0], [1, 0, -2.06899999999996, 0], [1, 0.6, 0.6, 1]], [5, "jbs_jccg", 33554432, 2, [-57], [[1, -55, [0, "ec2Zjc6+xN26hTzECbJ3k6"], [5, 202, 56]], [39, 1, -56, [0, "bcbivfYnRNIqHKUNJDV5Jr"], 4]], [2, "30aJ8aJV9MUbkCtQdZYJcX", null, null, null, 1, 0], [1, 0, -123.782, 0]], [8, "txt_sx_1", 33554432, 14, [[1, -58, [0, "b7I/ISBCNCTpCzCPOLGYFd"], [5, 290, 69]], [42, "", 48, 48, 50, 2, false, true, true, 3, -59, [0, "5ezq2kUHtC2IVbbXQNIyFE"], [4, 4281190399], [4, 4280098330]], [12, "gonghuizhan93", -60, [0, "eeH4VPw/NHEqzdiGGI0hIm"]]], [2, "66bjDxORFE1ItmHysfsq6u", null, null, null, 1, 0], [1, 0, 1.808, 0], [1, 0.5, 0.5, 1]], [5, "img_ghz_title1", 33554432, 2, [-63], [[1, -61, [0, "2bqUqanVlC76uO96CCpilJ"], [5, 640, 133]], [11, -62, [0, "01qB63J2VM35oqQnbvPBW/"], 5]], [2, "5d/7ZotN9Ng5ApPSWGQ3r+", null, null, null, 1, 0], [1, 0, 118.57, 0]], [15, "item_qz1", 33554432, 6, [-66], [[1, -64, [0, "51EQE08ahHcrB8s70YxxcH"], [5, 113, 117]], [6, 0, -65, [0, "634HkLLWlKn5yfQAdEKm2H"]]], [2, "1cNsIBlt1EkZOQ7ySqhRgT", null, null, null, 1, 0], [1, 0, -2.06899999999996, 0], [1, 0.6, 0.6, 1]], [10, "ani_ghz_pipei", 33554432, 1, [[32, -67, [0, "74XZZOuAVGg4iBDC7bgOv8"], [5, 802.9999389648438, 1282], [0, 0.5230385689770667, 0.4960998439937597]], [43, "default", false, 0, -68, [0, "be7Z6kKwhNqoy7e9urSlFU"], [[21, "root/ui4", 3], [21, "root/ui1", 4]], 2]], [2, "76my2mcTZMWo1XlitV/k1U", null, null, null, 1, 0]], [8, "txt_name2", 33554432, 5, [[1, -69, [0, "d92aKo0rtCqZ8fPbFlFRGN"], [5, 6, 56.4]], [19, "", 36, 36, true, true, 3, -70, [0, "8drPUI8GFOt670yLFVLFQ2"], [4, 4280229916]]], [2, "74DAhDgrVEML98qHkNXBkw", null, null, null, 1, 0], [1, 0, 52.839, 0], [1, 0.5, 0.5, 1]], [10, "img_tuteng2", 33554432, 13, [[1, -71, [0, "efsDaeOYJOM5cuibWZMqX8"], [5, 40, 36]], [7, -72, [0, "23E93QVylLIZE+JRPGUuIw"]]], [2, "a5vIA+3dZKTauAhAeiUsRF", null, null, null, 1, 0]], [16, 0, {}, 5, [18, "c46/YsCPVOJYA4mWEpNYRx", null, null, -73, [22, "31lXsYjb1AOY5QL3K1QeZj", 1, [[23, "ty_zl2", ["_name"], 9], [3, ["_lpos"], 9, [1, 0, -52.949, 0]], [3, ["_lrot"], 9, [3, 0, 0, 0, 1]], [3, ["_euler"], 9, [1, 0, 0, 0]], [3, ["_lscale"], 9, [1, 0.7, 0.7, 1]], [9, "bg_zl2", ["_name"], [4, ["d4HiuUWCxEkYe/Pz7S6SbU"]]], [9, "wz_zl2", ["_name"], [4, ["749LXfhDlNKpFGD9kWraJg"]]]]], 3]], [14, "txt_pp1", 33554432, 16, [[1, -74, [0, "5dqEhFcFpHL7LHIU+jsZ2z"], [5, 6, 94.2]], [20, "", 60, 60, 70, true, true, 3, -75, [0, "5egnv3jL1A45Qr7kDZhgyq"], [4, 4288806655]]], [2, "c7iBmM1fJLaYi9MHXSlxdL", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [29, "img_dw1", 2, [[1, -76, [0, "b1horYXiVLJojxr/30GV8o"], [5, 293, 267]], [7, -77, [0, "42A5MrKfpJZo5GWMGqVWEf"]]], [2, "d5ReTVL+1FXKgtJCvN4tes", null, null, null, 1, 0], [1, 0.4, 0.4, 1]], [30, "txt_dw1", 2, [[1, -78, [0, "906HIQ0dZKUI+QrPe+uX9p"], [5, 8, 83.6]], [20, "", 42, 42, 60, true, true, 4, -79, [0, "82jk0JS6FCEoCr+M9pZ1pw"], [4, 4288806655]]], [2, "cfNLqqtZNN+5VpbgT1O7Kk", null, null, null, 1, 0], [1, 0, -37.591, 0], [1, 0.5, 0.5, 1]], [8, "txt_name1", 33554432, 6, [[1, -80, [0, "actdhuNy1EIpnrC7xd62bn"], [5, 6, 56.4]], [19, "", 36, 36, true, true, 3, -81, [0, "5bzG28pU9FQrxXL/t89EVU"], [4, 4280229916]]], [2, "46bG73zkVJ7IHj+ZXhZLYv", null, null, null, 1, 0], [1, 0, 52.839, 0], [1, 0.5, 0.5, 1]], [10, "img_tuteng1", 33554432, 17, [[1, -82, [0, "ddZ6QrkNlMkpVl6K36OosZ"], [5, 40, 36]], [7, -83, [0, "e5Cdz2r0ZDt4jbbT10sfqP"]]], [2, "8fVsgDnzhPgbnx3FBBDDLP", null, null, null, 1, 0]], [16, 0, {}, 6, [18, "c46/YsCPVOJYA4mWEpNYRx", null, null, -84, [22, "c0sLwKM7JGHqYtwdS2TpAh", 1, [[23, "ty_zl1", ["_name"], 10], [3, ["_lpos"], 10, [1, 0, -52.949, 0]], [3, ["_lrot"], 10, [3, 0, 0, 0, 1]], [3, ["_euler"], 10, [1, 0, 0, 0]], [3, ["_lscale"], 10, [1, 0.7, 0.7, 1]], [9, "bg_zl1", ["_name"], [4, ["d4HiuUWCxEkYe/Pz7S6SbU"]]], [9, "wz_zl1", ["_name"], [4, ["749LXfhDlNKpFGD9kWraJg"]]]]], 6]], [8, "img_jt1", 33554432, 7, [[1, -85, [0, "5a+VftCS5NNoSeeXbFY6yI"], [5, 56, 31]], [11, -86, [0, "c1/Pq1gO1LnZOSTsZ4ULXg"], 7]], [2, "2ckUz+Lt1AMr4h9hqUgR5c", null, null, null, 1, 0], [1, -107, 0, 0], [1, -1, 1, 1]], [31, "img_jt2", 33554432, 7, [[1, -87, [0, "41f8cOFL1BBb5it32+I06/"], [5, 56, 31]], [11, -88, [0, "76WQc50v1I9qiHNxXDsqK/"], 8]], [2, "1dgrEDG4RJy7sHSZWDGNUu", null, null, null, 1, 0], [1, 107, 0, 0]]], 0, [0, -1, 21, 0, -2, 27, 0, 2, 1, 0, 0, 1, 0, 0, 1, 0, -1, 11, 0, -2, 18, 0, -3, 2, 0, -4, 7, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -3, 14, 0, -4, 16, 0, -5, 23, 0, -6, 24, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 6, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 5, 0, 0, 5, 0, 0, 5, 0, -1, 19, 0, -2, 13, 0, -3, 21, 0, 0, 6, 0, 0, 6, 0, -1, 25, 0, -2, 17, 0, -3, 27, 0, 0, 7, 0, 0, 7, 0, -1, 28, 0, -2, 8, 0, -3, 29, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 11, 0, 0, 11, 0, -1, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, -1, 20, 0, 0, 14, 0, 0, 14, 0, -1, 15, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, -1, 22, 0, 0, 17, 0, 0, 17, 0, -1, 26, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 2, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 2, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 4, 1, 88], [0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 5, 3, 1, 1, 3, 1, 1], [2, 3, 4, 0, 5, 6, 0, 1, 1]], [[{"name": "default_sprite_splash", "rect": {"x": 0, "y": 0, "width": 2, "height": 2}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 2, "height": 2}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-1, -1, 0, 1, -1, 0, -1, 1, 0, 1, 1, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2, 2, 2, 0, 0, 2, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -1, "y": -1, "z": 0}, "maxPos": {"x": 1, "y": 1, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [6], [7]]]]