(function(root){var exports=undefined,module=undefined,require=undefined;var define=undefined;var self=root,window=root,global=root,globalThis=root;(function(){/**
 * 全局 Tooltip 管理器
 * 负责创建、定位和显示唯一的 Tooltip 实例
 */class TooltipManager{constructor(parent){this.tooltip=null;this.tooltipTimer=null;this.parent=parent;this.applyStyles();}applyStyles(){const style=document.createElement("style");style.textContent=`
            .global-inspector-tooltip {
                position: fixed; /* 使用 fixed 定位，相对于视口 */
                background-color: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 8px 12px;
                border-radius: 5px;
                font-size: 12px;
                z-index: 9999; /* 确保在最顶层 */
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.3s ease;
                white-space: nowrap; /* 防止文本换行 */
            }

            .global-inspector-tooltip.show {
                opacity: 1;
            }
        `;document.head.appendChild(style);}show(message,targetElement){// 清除上一个计时器
if(this.tooltipTimer){clearTimeout(this.tooltipTimer);}// 如果 tooltip 不存在，则创建它
if(!this.tooltip){this.tooltip=document.createElement("div");this.tooltip.className="global-inspector-tooltip";this.parent.appendChild(this.tooltip);}// 设置文本内容
this.tooltip.textContent=message;// 计算位置
const targetRect=targetElement.getBoundingClientRect();const tooltipRect=this.tooltip.getBoundingClientRect();// 计算定位：在目标元素上方 40px，并水平居中
const top=targetRect.top-40;const left=targetRect.left+targetRect.width/2-tooltipRect.width/2;this.tooltip.style.top=`${top}px`;this.tooltip.style.left=`${left}px`;// 强制重绘以应用初始样式
void this.tooltip.offsetWidth;// 显示 tooltip
this.tooltip.classList.add("show");// 2秒后移除
this.tooltipTimer=window.setTimeout(()=>{if(this.tooltip){this.tooltip.classList.remove("show");}},2000);}}/**
 * 抽屉UI组件
 * 在Chrome窗口左侧创建可收起和打开的插件界面
 */class DrawerUI{constructor(){this.container=null;this.content=null;this.toggleButton=null;this.resizeHandle=null;this.isOpen=false;this.toggleCallback=null;this.isResizing=false;this.startX=0;this.startWidth=0;this.minWidth=150;this.maxWidth=800;this.defaultWidth=300;this.tooltipManager=null;}/**
   * 创建抽屉UI
   */create(){this.createContainer();this.createToggleButton();this.createContent();this.createResizeHandle();this.applyStyles();this.bindEvents();this.bindResizeEvents();this.setInitialState();// 初始化 Tooltip 管理器
this.tooltipManager=new TooltipManager(this.container);}/**
   * 创建主容器
   */createContainer(){this.container=document.createElement("div");this.container.id="node-inspector-drawer";this.container.style.width=`${this.defaultWidth}px`;document.body.appendChild(this.container);}/**
   * 创建切换按钮
   */createToggleButton(){this.toggleButton=document.createElement("div");this.toggleButton.id="node-inspector-toggle";this.toggleButton.innerHTML="节<br>点<br>树";this.toggleButton.title="切换节点调试器";this.container.appendChild(this.toggleButton);}/**
   * 创建内容区域
   */createContent(){this.content=document.createElement("div");this.content.id="node-inspector-content";this.container.appendChild(this.content);}/**
   * 创建拖拽调整手柄
   */createResizeHandle(){this.resizeHandle=document.createElement("div");this.resizeHandle.id="node-inspector-resize-handle";this.container.appendChild(this.resizeHandle);}/**
   * 应用样式
   */applyStyles(){const style=document.createElement("style");style.textContent=`
            #node-inspector-drawer {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                background: #2d2d2d;
                border-right: 1px solid #444;
                z-index: 10000;
                transition: transform 0.3s ease;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 12px;
                color: #fff;
                box-shadow: 2px 0 10px rgba(0,0,0,0.3);
                min-width: ${this.minWidth}px;
                max-width: ${this.maxWidth}px;
            }

            .resource-detail-name a,.resource-detail-name a:hover{color: #fff;}

            #node-inspector-drawer.closed {
                transform: translateX(-100%);
            }

            #node-inspector-toggle {
                position: absolute;
                top: calc(50vh - 100px);
                right: -30px;
                transform: translateY(-50%);
                width: 30px;
                height: 160px;
                background: #2d2d2d;
                border: 1px solid #444;
                border-left: none;
                border-radius: 0 5px 5px 0;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-size: 14px;
                color: #fff;
                user-select: none;
                transition: background 0.2s ease;
            }

            #node-inspector-toggle:hover {
                background: #3d3d3d;
            }

            #node-inspector-resize-handle {
                position: absolute;
                top: 0;
                right: 0;
                width: 4px;
                height: 100%;
                background: transparent;
                cursor: col-resize;
                z-index: 1001;
                transition: background 0.2s ease;
            }

            #node-inspector-resize-handle:hover {
                background: #0078d4;
            }

            #node-inspector-content {
                width: 100%;
                height: 100%;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                padding-right: 4px;
            }

            .node-inspector-panel {
                background: #2d2d2d;
                border-bottom: 1px solid #444;
                overflow: auto;
            }

            .node-inspector-panel h3 {
                margin: 0;
                padding: 10px;
                background: #1e1e1e;
                border-bottom: 1px solid #444;
                font-size: 14px;
                font-weight: bold;
            }

            .node-tree {
                flex: 1;
                min-height: 200px;
                display: flex;
                flex-direction: column;
                overflow: auto;
            }

            .property-panel {
                flex: 0 0 auto;
                min-height: auto;
            }

            /* 滚动条样式 */
            .node-inspector-panel::-webkit-scrollbar {
                width: 8px;
            }

            .node-inspector-panel::-webkit-scrollbar-track {
                background: #1e1e1e;
            }

            .node-inspector-panel::-webkit-scrollbar-thumb {
                background: #555;
                border-radius: 4px;
            }

            .node-inspector-panel::-webkit-scrollbar-thumb:hover {
                background: #666;
            }

            /* 拖拽时的样式 */
            body.resizing-drawer {
                user-select: none;
                cursor: col-resize !important;
            }

            body.resizing-drawer * {
                pointer-events: none !important;
            }

            body.resizing-drawer #node-inspector-drawer {
                pointer-events: auto !important;
            }

            body.resizing-drawer #node-inspector-resize-handle {
                pointer-events: auto !important;
            }

            /* 防止iframe或canvas干扰 */
            body.resizing-drawer iframe,
            body.resizing-drawer canvas,
            body.resizing-drawer embed,
            body.resizing-drawer object {
                pointer-events: none !important;
            }
        `;document.head.appendChild(style);}/**
   * 绑定事件
   */bindEvents(){if(this.toggleButton){this.toggleButton.addEventListener("click",()=>{this.toggle();});}}/**
   * 切换抽屉状态
   */toggle(){if(!this.container)return;this.isOpen=!this.isOpen;if(this.isOpen){this.container.classList.remove("closed");}else{this.container.classList.add("closed");}// 触发回调
if(this.toggleCallback){this.toggleCallback(this.isOpen);}}/**
   * 设置切换状态回调
   */onToggle(callback){this.toggleCallback=callback;}/**
   * 获取内容容器
   */getContentContainer(){return this.content;}/**
   * 销毁抽屉
   */destroy(){if(this.container&&this.container.parentNode){this.container.parentNode.removeChild(this.container);}this.container=null;this.content=null;this.toggleButton=null;this.resizeHandle=null;}/**
   * 设置初始状态
   */setInitialState(){if(!this.container)return;// 设置为收起状态
this.container.classList.add("closed");}/**
   * 绑定拖拽事件
   */bindResizeEvents(){if(!this.resizeHandle)return;this.resizeHandle.addEventListener("mousedown",e=>{this.startResize(e);});// 使用捕获阶段监听，确保在所有其他元素之前捕获事件
document.addEventListener("mousemove",e=>{this.handleResize(e);},{capture:true});document.addEventListener("mouseup",e=>{this.stopResize();},{capture:true});// 添加鼠标离开窗口的处理
document.addEventListener("mouseleave",()=>{this.stopResize();});// 防止在iframe中丢失事件
window.addEventListener("blur",()=>{this.stopResize();});}/**
   * 开始拖拽调整
   */startResize(e){this.isResizing=true;this.startX=e.clientX;this.startWidth=this.container.clientWidth;document.body.classList.add("resizing-drawer");this.resizeHandle.style.background="#0078d4";// 阻止默认行为和事件冒泡
e.preventDefault();e.stopPropagation();e.stopImmediatePropagation();}/**
   * 处理拖拽调整
   */handleResize(e){if(!this.isResizing)return;const deltaX=e.clientX-this.startX;const newWidth=Math.max(this.minWidth,Math.min(this.maxWidth,this.startWidth+deltaX));this.container.style.width=`${newWidth}px`;// 阻止事件传播
e.preventDefault();e.stopPropagation();e.stopImmediatePropagation();}/**
   * 停止拖拽调整
   */stopResize(){if(!this.isResizing)return;this.isResizing=false;document.body.classList.remove("resizing-drawer");this.resizeHandle.style.background="";}showTooltip(message,targetElement){if(this.tooltipManager){this.tooltipManager.show(message,targetElement);}}/**
   * 重置宽度（收起时调用）
   */resetWidth(){if(this.container){this.container.style.width=`${this.defaultWidth}px`;}}}/**
 * @Author: Gongxh
 * @Date: 2025-06-03
 * @Description:
 */function isCreator3x(){const cc=window.cc;const ccVersion=(cc===null||cc===void 0?void 0:cc.ENGINE_VERSION)||"";return ccVersion.startsWith("3.")||ccVersion.startsWith("4.");}class Tool{static log(...msg){// console.log(...msg);
}}/**
 * @Author: Gongxh
 * @Date: 2025-06-25
 * @Description: 内存计算用的帮助类
 */class MemoryHelper{/** 计算资源大小 */static calculate(asset){try{const type=asset.__classname__.replace('cc.','');if(!this._loged_type.has(type)&&!this._ignore_type.has(type)){this._loged_type.set(type,true);Tool.log("************************************************");Tool.log("资源类型",type);Tool.log("原始数据:",asset);}let size=0;switch(type){case"cc_Texture2D":size=this.calculateTextureSize(asset);break;case"ImageAsset":case"cc_ImageAsset":size=this.calculateImageAssetSize(asset);break;case"AudioClip":case"cc_AudioClip":size=this.calculateAudioSize(asset);break;case"Prefab":case"cc_Prefab":size=this.calculatePrefabSize(asset);break;case"Material":case"cc_Material":size=this.calculateMaterialSize(asset);break;case"Mesh":case"cc_Mesh":size=this.calculateMeshSize(asset);break;case"AnimationClip":case"cc_AnimationClip":size=this.calculateAnimationSize(asset);break;case"EffectAsset":case"cc_EffectAsset":size=this.calculateEffectSize(asset);break;case"TTFFont":case"cc_TTFFont":size=this.calculateFontSize(asset);break;case"JsonAsset":case"cc_JsonAsset":size=this.calculateJSONSize(asset.json);break;case"BufferAsset":case"cc_BufferAsset":size=this.calculateBufferSize(asset);break;case"sp_SkeletonData":case"sp.SkeletonData":case"SkeletonData":size=this.calculateSkeletonDataSize(asset);break;case"TextAsset":case"cc_TextAsset":size=this.calculateTextAssetSize(asset);break;case"Asset":size=this.calculateAssetSize(asset);break;default:size=-1;break;}if(!this._loged_type.has(type)&&!this._ignore_type.has(type)){this._loged_type.set(type,true);console.log("大小:",size);}return size;}catch(error){console.warn("计算资源大小时出错:",error);return 100*1024;// 100KB
}}/** 计算纹理内存 */static calculateTextureSize(texture){try{// 优先从texture对象获取尺寸信息
const width=texture.width||texture._width||0;const height=texture.height||texture._height||0;if(width>0&&height>0){// 获取像素格式信息 默认RGBA8888
let bytesPerPixel=4;let formatName="RGBA8888";// 从_format字段解析Creator 2.x格式常量
if(texture._format!==undefined){const formatInfo=this.getFormatInfoFrom2x(texture._format);bytesPerPixel=formatInfo.bytesPerPixel;formatName=formatInfo.name;}// 尝试从其他方式获取格式
else if(texture.getPixelFormat){const format=texture.getPixelFormat();bytesPerPixel=this.getBytesPerPixel(format);formatName=format;}else if(texture.pixelFormat){bytesPerPixel=this.getBytesPerPixel(texture.pixelFormat);formatName=texture.pixelFormat;}else if(texture._pixelFormat){bytesPerPixel=this.getBytesPerPixel(texture._pixelFormat);formatName=texture._pixelFormat;}// 基础内存计算
let memorySize=width*height*bytesPerPixel;// 检查是否压缩纹理
const isCompressed=texture._compressed||texture._texture&&texture._texture._compressed||false;if(isCompressed){// 压缩纹理通常比未压缩的小很多
memorySize=Math.floor(memorySize/4);// 大概压缩比例
}// 检查是否有Mipmap
const hasMipmaps=texture._genMipmaps||texture.genMipmaps||texture._texture&&texture._texture._genMipmaps||false;if(hasMipmaps){// Mipmap会增加约1/3的内存
memorySize=Math.floor(memorySize*1.33);}// console.log(`纹理内存计算: ${width}x${height}, 格式: ${formatName}, 压缩: ${isCompressed}, Mipmap: ${hasMipmaps}, 内存: ${this.formatBytes(memorySize)}`);
return memorySize;}// 尝试从_nativeTexture获取信息
if(texture._nativeTexture){const nativeTexture=texture._nativeTexture;if(nativeTexture.width&&nativeTexture.height){return nativeTexture.width*nativeTexture.height*4;// 默认RGBA
}}// 尝试从_texture内部对象获取信息
if(texture._texture){const innerTexture=texture._texture;if(innerTexture._width&&innerTexture._height){let size=innerTexture._width*innerTexture._height*4;if(innerTexture._compressed){size=Math.floor(size/4);}return size;}}// 尝试从ImageBitmap获取信息
if(texture._image&&texture._image.width&&texture._image.height){return texture._image.width*texture._image.height*4;}// 最后尝试从URL估算
if(texture._nativeUrl){const url=texture._nativeUrl.toLowerCase();if(url.includes(".png")||url.includes(".jpg")||url.includes(".jpeg")){// 根据文件名中的尺寸信息估算
const sizeMatch=url.match(/(\d+)x(\d+)/);if(sizeMatch){const w=parseInt(sizeMatch[1]);const h=parseInt(sizeMatch[2]);return w*h*4;}// 默认中等尺寸纹理
return 64*64*4;// 给个默认值
}}return 64*64*4;// 给个默认值
}catch(error){console.warn("计算纹理大小时出错:",error);return 64*64*4;// 给个默认值
}}/** 根据2.x格式常量获取格式信息 */static getFormatInfoFrom2x(format){// Creator 2.x 格式常量映射 (基于官方enums定义)
const formatMap={// 压缩格式
0:{bytesPerPixel:0.5,name:"RGB_DXT1"},// TEXTURE_FMT_RGB_DXT1
1:{bytesPerPixel:0.5,name:"RGBA_DXT1"},// TEXTURE_FMT_RGBA_DXT1
2:{bytesPerPixel:1,name:"RGBA_DXT3"},// TEXTURE_FMT_RGBA_DXT3
3:{bytesPerPixel:1,name:"RGBA_DXT5"},// TEXTURE_FMT_RGBA_DXT5
4:{bytesPerPixel:0.5,name:"RGB_ETC1"},// TEXTURE_FMT_RGB_ETC1
5:{bytesPerPixel:0.25,name:"RGB_PVRTC_2BPPV1"},// TEXTURE_FMT_RGB_PVRTC_2BPPV1
6:{bytesPerPixel:0.25,name:"RGBA_PVRTC_2BPPV1"},// TEXTURE_FMT_RGBA_PVRTC_2BPPV1
7:{bytesPerPixel:0.5,name:"RGB_PVRTC_4BPPV1"},// TEXTURE_FMT_RGB_PVRTC_4BPPV1
8:{bytesPerPixel:0.5,name:"RGBA_PVRTC_4BPPV1"},// TEXTURE_FMT_RGBA_PVRTC_4BPPV1
// 基础格式
9:{bytesPerPixel:1,name:"A8"},// TEXTURE_FMT_A8
10:{bytesPerPixel:1,name:"L8"},// TEXTURE_FMT_L8
11:{bytesPerPixel:2,name:"L8_A8"},// TEXTURE_FMT_L8_A8
12:{bytesPerPixel:2,name:"R5_G6_B5"},// TEXTURE_FMT_R5_G6_B5
13:{bytesPerPixel:2,name:"R5_G5_B5_A1"},// TEXTURE_FMT_R5_G5_B5_A1
14:{bytesPerPixel:2,name:"R4_G4_B4_A4"},// TEXTURE_FMT_R4_G4_B4_A4
15:{bytesPerPixel:3,name:"RGB8"},// TEXTURE_FMT_RGB8
16:{bytesPerPixel:4,name:"RGBA8"},// TEXTURE_FMT_RGBA8
17:{bytesPerPixel:6,name:"RGB16F"},// TEXTURE_FMT_RGB16F
18:{bytesPerPixel:8,name:"RGBA16F"},// TEXTURE_FMT_RGBA16F
19:{bytesPerPixel:12,name:"RGB32F"},// TEXTURE_FMT_RGB32F
20:{bytesPerPixel:16,name:"RGBA32F"},// TEXTURE_FMT_RGBA32F
21:{bytesPerPixel:4,name:"R32F"},// TEXTURE_FMT_R32F
22:{bytesPerPixel:4,name:"111110F"},// TEXTURE_FMT_111110F
23:{bytesPerPixel:3,name:"SRGB"},// TEXTURE_FMT_SRGB
24:{bytesPerPixel:4,name:"SRGBA"},// TEXTURE_FMT_SRGBA
// 深度格式
25:{bytesPerPixel:2,name:"D16"},// TEXTURE_FMT_D16
26:{bytesPerPixel:4,name:"D32"},// TEXTURE_FMT_D32
27:{bytesPerPixel:4,name:"D24S8"},// TEXTURE_FMT_D24S8
// ETC2格式
28:{bytesPerPixel:0.5,name:"RGB_ETC2"},// TEXTURE_FMT_RGB_ETC2
29:{bytesPerPixel:1,name:"RGBA_ETC2"},// TEXTURE_FMT_RGBA_ETC2
// ASTC格式 (Adaptive Scalable Texture Compression)
30:{bytesPerPixel:1,name:"RGBA_ASTC_4X4"},// TEXTURE_FMT_RGBA_ASTC_4X4
31:{bytesPerPixel:0.8,name:"RGBA_ASTC_5X4"},// TEXTURE_FMT_RGBA_ASTC_5X4
32:{bytesPerPixel:0.64,name:"RGBA_ASTC_5X5"},// TEXTURE_FMT_RGBA_ASTC_5X5
33:{bytesPerPixel:0.53,name:"RGBA_ASTC_6X5"},// TEXTURE_FMT_RGBA_ASTC_6X5
34:{bytesPerPixel:0.44,name:"RGBA_ASTC_6X6"},// TEXTURE_FMT_RGBA_ASTC_6X6
35:{bytesPerPixel:0.32,name:"RGBA_ASTC_8X5"},// TEXTURE_FMT_RGBA_ASTC_8X5 ← 格式35
36:{bytesPerPixel:0.27,name:"RGBA_ASTC_8X6"},// TEXTURE_FMT_RGBA_ASTC_8X6
37:{bytesPerPixel:0.25,name:"RGBA_ASTC_8X8"},// TEXTURE_FMT_RGBA_ASTC_8X8
38:{bytesPerPixel:0.2,name:"RGBA_ASTC_10X5"},// TEXTURE_FMT_RGBA_ASTC_10X5
39:{bytesPerPixel:0.17,name:"RGBA_ASTC_10X6"},// TEXTURE_FMT_RGBA_ASTC_10X6
40:{bytesPerPixel:0.125,name:"RGBA_ASTC_10X8"},// TEXTURE_FMT_RGBA_ASTC_10X8
41:{bytesPerPixel:0.1,name:"RGBA_ASTC_10X10"},// TEXTURE_FMT_RGBA_ASTC_10X10
42:{bytesPerPixel:0.083,name:"RGBA_ASTC_12X10"},// TEXTURE_FMT_RGBA_ASTC_12X10
43:{bytesPerPixel:0.069,name:"RGBA_ASTC_12X12"},// TEXTURE_FMT_RGBA_ASTC_12X12
// SRGB ASTC格式
44:{bytesPerPixel:1,name:"SRGBA_ASTC_4X4"},// TEXTURE_FMT_SRGBA_ASTC_4X4
45:{bytesPerPixel:0.8,name:"SRGBA_ASTC_5X4"},// TEXTURE_FMT_SRGBA_ASTC_5X4
46:{bytesPerPixel:0.64,name:"SRGBA_ASTC_5X5"},// TEXTURE_FMT_SRGBA_ASTC_5X5
47:{bytesPerPixel:0.53,name:"SRGBA_ASTC_6X5"},// TEXTURE_FMT_SRGBA_ASTC_6X5
48:{bytesPerPixel:0.44,name:"SRGBA_ASTC_6X6"},// TEXTURE_FMT_SRGBA_ASTC_6X6
49:{bytesPerPixel:0.32,name:"SRGBA_ASTC_8X5"},// TEXTURE_FMT_SRGBA_ASTC_8X5
50:{bytesPerPixel:0.27,name:"SRGBA_ASTC_8X6"},// TEXTURE_FMT_SRGBA_ASTC_8X6
51:{bytesPerPixel:0.25,name:"SRGBA_ASTC_8X8"},// TEXTURE_FMT_SRGBA_ASTC_8X8
52:{bytesPerPixel:0.2,name:"SRGBA_ASTC_10X5"},// TEXTURE_FMT_SRGBA_ASTC_10X5
53:{bytesPerPixel:0.17,name:"SRGBA_ASTC_10X6"},// TEXTURE_FMT_SRGBA_ASTC_10X6
54:{bytesPerPixel:0.125,name:"SRGBA_ASTC_10X8"},// TEXTURE_FMT_SRGBA_ASTC_10X8
55:{bytesPerPixel:0.1,name:"SRGBA_ASTC_10X10"},// TEXTURE_FMT_SRGBA_ASTC_10X10
56:{bytesPerPixel:0.083,name:"SRGBA_ASTC_12X10"},// TEXTURE_FMT_SRGBA_ASTC_12X10
57:{bytesPerPixel:0.069,name:"SRGBA_ASTC_12X12"}// TEXTURE_FMT_SRGBA_ASTC_12X12
};return formatMap[format]||{bytesPerPixel:4,name:"RGBA8"};}/** 根据3.x格式常量获取格式信息 */static getFormatInfoFrom3x(format){// Creator 3.x gfx.Format 枚举映射 (基于官方源码)
const formatMap={// 基础格式
0:{bytesPerPixel:4,name:"UNKNOWN"},1:{bytesPerPixel:1,name:"A8"},2:{bytesPerPixel:1,name:"L8"},3:{bytesPerPixel:2,name:"LA8"},4:{bytesPerPixel:1,name:"R8"},5:{bytesPerPixel:1,name:"R8SN"},6:{bytesPerPixel:1,name:"R8UI"},7:{bytesPerPixel:1,name:"R8I"},8:{bytesPerPixel:2,name:"R16F"},9:{bytesPerPixel:2,name:"R16UI"},10:{bytesPerPixel:2,name:"R16I"},11:{bytesPerPixel:4,name:"R32F"},12:{bytesPerPixel:4,name:"R32UI"},13:{bytesPerPixel:4,name:"R32I"},14:{bytesPerPixel:2,name:"RG8"},15:{bytesPerPixel:2,name:"RG8SN"},16:{bytesPerPixel:2,name:"RG8UI"},17:{bytesPerPixel:2,name:"RG8I"},18:{bytesPerPixel:4,name:"RG16F"},19:{bytesPerPixel:4,name:"RG16UI"},20:{bytesPerPixel:4,name:"RG16I"},21:{bytesPerPixel:8,name:"RG32F"},22:{bytesPerPixel:8,name:"RG32UI"},23:{bytesPerPixel:8,name:"RG32I"},24:{bytesPerPixel:3,name:"RGB8"},25:{bytesPerPixel:3,name:"SRGB8"},26:{bytesPerPixel:3,name:"RGB8SN"},27:{bytesPerPixel:3,name:"RGB8UI"},28:{bytesPerPixel:3,name:"RGB8I"},29:{bytesPerPixel:6,name:"RGB16F"},30:{bytesPerPixel:6,name:"RGB16UI"},31:{bytesPerPixel:6,name:"RGB16I"},32:{bytesPerPixel:12,name:"RGB32F"},33:{bytesPerPixel:12,name:"RGB32UI"},34:{bytesPerPixel:12,name:"RGB32I"},35:{bytesPerPixel:4,name:"RGBA8"},// 格式35 = RGBA8
36:{bytesPerPixel:4,name:"BGRA8"},37:{bytesPerPixel:4,name:"SRGB8_A8"},38:{bytesPerPixel:4,name:"RGBA8SN"},39:{bytesPerPixel:4,name:"RGBA8UI"},40:{bytesPerPixel:4,name:"RGBA8I"},41:{bytesPerPixel:8,name:"RGBA16F"},42:{bytesPerPixel:8,name:"RGBA16UI"},43:{bytesPerPixel:8,name:"RGBA16I"},44:{bytesPerPixel:16,name:"RGBA32F"},45:{bytesPerPixel:16,name:"RGBA32UI"},46:{bytesPerPixel:16,name:"RGBA32I"},// 特殊格式
47:{bytesPerPixel:2,name:"R5G6B5"},48:{bytesPerPixel:4,name:"R11G11B10F"},49:{bytesPerPixel:2,name:"RGB5A1"},50:{bytesPerPixel:2,name:"RGBA4"},51:{bytesPerPixel:4,name:"RGB10A2"},52:{bytesPerPixel:4,name:"RGB10A2UI"},53:{bytesPerPixel:4,name:"RGB9E5"},// 深度模板格式
54:{bytesPerPixel:4,name:"DEPTH"},55:{bytesPerPixel:4,name:"DEPTH_STENCIL"},// 压缩格式 - BC系列 (DXT)
56:{bytesPerPixel:0.5,name:"BC1"},57:{bytesPerPixel:0.5,name:"BC1_ALPHA"},58:{bytesPerPixel:0.5,name:"BC1_SRGB"},59:{bytesPerPixel:0.5,name:"BC1_SRGB_ALPHA"},60:{bytesPerPixel:1,name:"BC2"},61:{bytesPerPixel:1,name:"BC2_SRGB"},62:{bytesPerPixel:1,name:"BC3"},63:{bytesPerPixel:1,name:"BC3_SRGB"},64:{bytesPerPixel:0.5,name:"BC4"},65:{bytesPerPixel:0.5,name:"BC4_SNORM"},66:{bytesPerPixel:1,name:"BC5"},67:{bytesPerPixel:1,name:"BC5_SNORM"},68:{bytesPerPixel:1,name:"BC6H_UF16"},69:{bytesPerPixel:1,name:"BC6H_SF16"},70:{bytesPerPixel:1,name:"BC7"},71:{bytesPerPixel:1,name:"BC7_SRGB"},// ETC系列压缩格式
72:{bytesPerPixel:0.5,name:"ETC_RGB8"},73:{bytesPerPixel:0.5,name:"ETC2_RGB8"},74:{bytesPerPixel:0.5,name:"ETC2_SRGB8"},75:{bytesPerPixel:0.5,name:"ETC2_RGB8_A1"},76:{bytesPerPixel:0.5,name:"ETC2_SRGB8_A1"},77:{bytesPerPixel:1,name:"ETC2_RGBA8"},78:{bytesPerPixel:1,name:"ETC2_SRGB8_A8"},79:{bytesPerPixel:0.5,name:"EAC_R11"},80:{bytesPerPixel:0.5,name:"EAC_R11SN"},81:{bytesPerPixel:1,name:"EAC_RG11"},82:{bytesPerPixel:1,name:"EAC_RG11SN"},// PVRTC系列压缩格式
83:{bytesPerPixel:0.25,name:"PVRTC_RGB2"},84:{bytesPerPixel:0.25,name:"PVRTC_RGBA2"},85:{bytesPerPixel:0.5,name:"PVRTC_RGB4"},86:{bytesPerPixel:0.5,name:"PVRTC_RGBA4"},87:{bytesPerPixel:0.25,name:"PVRTC2_2BPP"},88:{bytesPerPixel:0.5,name:"PVRTC2_4BPP"},// ASTC系列压缩格式
89:{bytesPerPixel:1,name:"ASTC_RGBA_4X4"},90:{bytesPerPixel:0.8,name:"ASTC_RGBA_5X4"},91:{bytesPerPixel:0.64,name:"ASTC_RGBA_5X5"},92:{bytesPerPixel:0.53,name:"ASTC_RGBA_6X5"},93:{bytesPerPixel:0.44,name:"ASTC_RGBA_6X6"},94:{bytesPerPixel:0.4,name:"ASTC_RGBA_8X5"},95:{bytesPerPixel:0.33,name:"ASTC_RGBA_8X6"},96:{bytesPerPixel:0.25,name:"ASTC_RGBA_8X8"},97:{bytesPerPixel:0.32,name:"ASTC_RGBA_10X5"},98:{bytesPerPixel:0.27,name:"ASTC_RGBA_10X6"},99:{bytesPerPixel:0.2,name:"ASTC_RGBA_10X8"},100:{bytesPerPixel:0.16,name:"ASTC_RGBA_10X10"},101:{bytesPerPixel:0.13,name:"ASTC_RGBA_12X10"},102:{bytesPerPixel:0.11,name:"ASTC_RGBA_12X12"},// ASTC SRGB系列
103:{bytesPerPixel:1,name:"ASTC_SRGBA_4X4"},104:{bytesPerPixel:0.8,name:"ASTC_SRGBA_5X4"},105:{bytesPerPixel:0.64,name:"ASTC_SRGBA_5X5"},106:{bytesPerPixel:0.53,name:"ASTC_SRGBA_6X5"},107:{bytesPerPixel:0.44,name:"ASTC_SRGBA_6X6"},108:{bytesPerPixel:0.4,name:"ASTC_SRGBA_8X5"},109:{bytesPerPixel:0.33,name:"ASTC_SRGBA_8X6"},110:{bytesPerPixel:0.25,name:"ASTC_SRGBA_8X8"},111:{bytesPerPixel:0.32,name:"ASTC_SRGBA_10X5"},112:{bytesPerPixel:0.27,name:"ASTC_SRGBA_10X6"},113:{bytesPerPixel:0.2,name:"ASTC_SRGBA_10X8"},114:{bytesPerPixel:0.16,name:"ASTC_SRGBA_10X10"},115:{bytesPerPixel:0.13,name:"ASTC_SRGBA_12X10"},116:{bytesPerPixel:0.11,name:"ASTC_SRGBA_12X12"}};return formatMap[format]||{bytesPerPixel:4,name:"Unknown"};}/** 计算TextAsset资源大小 */static calculateTextAssetSize(textAsset){try{// 获取文本内容
let textContent="";// 尝试不同的属性获取文本内容
if(textAsset.text){textContent=textAsset.text;}else if(textAsset._text){textContent=textAsset._text;}else if(textAsset.nativeAsset&&typeof textAsset.nativeAsset==="string"){textContent=textAsset.nativeAsset;}else if(textAsset._file&&typeof textAsset._file==="string"){textContent=textAsset._file;}if(textContent&&typeof textContent==="string"){// 计算字符串的内存大小
// JavaScript字符串使用UTF-16编码，每个字符2字节
const textSize=textContent.length*2;// 添加对象本身的开销（大约100字节）
const objectOverhead=100;const totalSize=textSize+objectOverhead;// console.log(`TextAsset内存计算: 名称=${textAsset._name || 'unknown'}, 文本长度=${textContent.length}字符, 内存=${this.formatBytes(totalSize)}`);
return totalSize;}// 如果没有找到文本内容，返回默认大小
console.warn("TextAsset没有找到文本内容，使用默认大小");return 1024;// 1KB默认值
}catch(error){console.warn("计算TextAsset大小时出错:",error);return 1024;// 1KB默认值
}}/** 计算Asset资源大小（主要用于Spine Atlas等） */static calculateAssetSize(asset){var _a,_b;try{// 检查是否为Spine Atlas文件
if(asset._native===".atlas"||((_b=(_a=asset._native)===null||_a===void 0?void 0:_a.endsWith)===null||_b===void 0?void 0:_b.call(_a,".atlas"))){return this.calculateSpineAtlasSize(asset);}// 检查其他类型的Asset
if(asset._file&&typeof asset._file==="string"){// 计算文本内容的大小（UTF-8编码）
const textSize=new Blob([asset._file]).size;return textSize;}// 默认Asset大小
return 1024;// 1KB
}catch(error){console.warn("计算Asset大小时出错:",error);return 1024;// 默认1KB
}}/** 计算Spine Atlas文件大小 */static calculateSpineAtlasSize(atlas){try{// Atlas文本内容大小（只计算描述文件本身）
const textContent=atlas._file||atlas.nativeAsset||"";const textSize=new Blob([textContent]).size;// console.log(`Spine Atlas内存计算: 文本大小=${this.formatBytes(textSize)}`);
return textSize;}catch(error){console.warn("计算Spine Atlas大小时出错:",error);return 1024*2;// 默认2KB
}}/** 计算图像资源大小 */static calculateImageAssetSize(imageAsset){try{// 获取图像的尺寸信息
const width=imageAsset._width||imageAsset.width||0;const height=imageAsset._height||imageAsset.height||0;if(width>0&&height>0){// ImageAsset通常存储原始像素数据
// 默认按RGBA8888格式计算（每像素4字节）
let bytesPerPixel=4;let formatName="RGBA8888";// 尝试从不同的属性获取格式信息
if(imageAsset._format!==undefined){// 使用3.x的格式解析
const formatInfo=this.getFormatInfoFrom3x(imageAsset._format);bytesPerPixel=formatInfo.bytesPerPixel;formatName=formatInfo.name;}else if(imageAsset.format!==undefined){const formatInfo=this.getFormatInfoFrom3x(imageAsset.format);bytesPerPixel=formatInfo.bytesPerPixel;formatName=formatInfo.name;}// 计算原始像素数据内存
const pixelDataSize=width*height*bytesPerPixel;// ImageAsset可能还包含其他数据
let totalSize=pixelDataSize;// 如果有原始数据缓冲区
if(imageAsset._nativeData&&imageAsset._nativeData.byteLength){totalSize=Math.max(totalSize,imageAsset._nativeData.byteLength);}// 如果有Canvas元素
if(imageAsset._canvas){// Canvas会占用额外的内存
totalSize+=pixelDataSize*0.1;// 约10%的额外开销
}// 如果有ImageBitmap
if(imageAsset._imageBitmap){totalSize+=pixelDataSize*0.05;// 约5%的额外开销
}// console.log(`图像资源内存计算: ${width}x${height}, 格式: ${formatName}, 像素数据: ${this.formatBytes(pixelDataSize)}, 总内存: ${this.formatBytes(totalSize)}`);
return totalSize;}// 尝试从文件大小估算
if(imageAsset._file&&imageAsset._file.size){// 原始文件大小作为参考，但内存中通常会更大（解压后）
const fileSize=imageAsset._file.size;const estimatedMemory=fileSize*3;// 估算解压后是原文件3倍大小
// console.log(`图像资源内存估算(从文件): 文件大小=${this.formatBytes(fileSize)}, 估算内存=${this.formatBytes(estimatedMemory)}`);
return estimatedMemory;}// 尝试从URL信息估算
if(imageAsset._nativeUrl||imageAsset.nativeUrl){const url=(imageAsset._nativeUrl||imageAsset.nativeUrl||"").toLowerCase();// 根据文件格式估算
if(url.includes(".png")){return 256*256*4;// PNG通常较大，默认256x256 RGBA
}else if(url.includes(".jpg")||url.includes(".jpeg")){return 512*512*3;// JPEG通常RGB格式，默认512x512
}else if(url.includes(".webp")){return 256*256*4;// WebP默认256x256 RGBA
}}// 默认图像资源大小
return 128*128*4;// 128x128 RGBA8888
}catch(error){console.warn("计算图像资源大小时出错:",error);return 128*128*4;// 默认值
}}/** 计算音频大小 */static calculateAudioSize(audio){try{// Creator 3.x版本：AudioBuffer位于_player._player._audioBuffer
if(audio._player&&audio._player._player&&audio._player._player._audioBuffer){const audioBuffer=audio._player._player._audioBuffer;// Web Audio API中，AudioBuffer的每个样本是32位浮点数（4字节）
// 内存占用 = 样本数量 × 声道数 × 每样本字节数
const memorySize=audioBuffer.length*audioBuffer.numberOfChannels*4;// console.log(`音频内存计算(3.x): 样本数=${audioBuffer.length}, 声道数=${audioBuffer.numberOfChannels}, 采样率=${audioBuffer.sampleRate}Hz, 时长=${audioBuffer.duration.toFixed(3)}s, 内存=${this.formatBytes(memorySize)}`);
return memorySize;}// 如果有AudioBuffer，精确计算内存占用（2.x版本或其他路径）
if(audio._audio&&audio._audio instanceof AudioBuffer){const audioBuffer=audio._audio;// Web Audio API中，AudioBuffer的每个样本是32位浮点数（4字节）
// 内存占用 = 样本数量 × 声道数 × 每样本字节数
const memorySize=audioBuffer.length*audioBuffer.numberOfChannels*4;// console.log(`音频内存计算(2.x): 样本数=${audioBuffer.length}, 声道数=${audioBuffer.numberOfChannels}, 采样率=${audioBuffer.sampleRate}Hz, 时长=${audioBuffer.duration.toFixed(3)}s, 内存=${this.formatBytes(memorySize)}`);
return memorySize;}// 尝试从其他音频属性获取信息
if(audio._audioBuffer){const audioBuffer=audio._audioBuffer;if(audioBuffer.length&&audioBuffer.numberOfChannels){return audioBuffer.length*audioBuffer.numberOfChannels*4;}if(audioBuffer.byteLength){return audioBuffer.byteLength;}}// 尝试从序列化数据获取大小
if(audio._serialized){const serialized=audio._serialized;if(serialized.data&&serialized.data.byteLength){return serialized.data.byteLength;}if(serialized.data&&serialized.data.length){return serialized.data.length;}}// 根据持续时间和采样率估算
let duration=0;// Creator 3.x版本多个可能的duration属性
if(audio._duration>0){duration=audio._duration;}else if(audio._player&&audio._player._player&&audio._player._player.duration>0){duration=audio._player._player.duration;}else if(audio._meta&&audio._meta.duration>0){duration=audio._meta.duration;}else if(audio.duration>0){duration=audio.duration;}if(duration>0){// 假设44.1kHz采样率，双声道，32位浮点数
const sampleRate=44100;const channels=2;const bytesPerSample=4;const estimatedSize=duration*sampleRate*channels*bytesPerSample;// console.log(`音频内存估算: 时长=${duration.toFixed(3)}s, 估算内存=${this.formatBytes(estimatedSize)}`);
return estimatedSize;}// 尝试从URL获取文件类型并估算大小
let url="";if(audio._meta&&audio._meta.url){url=audio._meta.url.toLowerCase();}else if(audio._nativeUrl){url=audio._nativeUrl.toLowerCase();}else if(audio.url){url=audio.url.toLowerCase();}else if(audio._player&&audio._player._player&&audio._player._player.src){url=audio._player._player.src.toLowerCase();}if(url.includes(".mp3")){return 128*1024;// MP3压缩比较高，128KB
}else if(url.includes(".wav")){return 512*1024;// WAV无压缩，512KB
}else if(url.includes(".ogg")){return 200*1024;// OGG中等压缩，200KB
}else if(url.includes(".m4a")||url.includes(".aac")){return 150*1024;// AAC格式，150KB
}return 128*1024;// 默认128KB
}catch(error){console.warn("计算音频大小时出错:",error);return 128*1024;// 128KB
}}/**
   * 计算预制体大小
   */static calculatePrefabSize(prefab){try{let totalSize=0;// 基础Prefab对象自身的大小
totalSize+=2048;// 2KB基础大小
// 计算data节点树的大小
if(prefab.data){totalSize+=this.calculateNodeTreeSize(prefab.data);}// 如果没有data，尝试其他可能的节点引用
if(totalSize<=2048&&prefab._data){totalSize+=this.calculateNodeTreeSize(prefab._data);}// 根据预制体名称和URL进行智能估算
if(totalSize<=2048){const prefabName=(prefab._name||"").toLowerCase();const url=(prefab._nativeUrl||"").toLowerCase();if(prefabName.includes("ui")||url.includes("ui")||prefabName.includes("button")){totalSize=50*1024;// UI元素，50KB
}else if(prefabName.includes("character")||prefabName.includes("player")||url.includes("character")){totalSize=150*1024;// 角色预制体，150KB
}else if(prefabName.includes("effect")||prefabName.includes("particle")||url.includes("effect")){totalSize=80*1024;// 特效预制体，80KB
}else if(prefabName.includes("scene")||prefabName.includes("level")){totalSize=200*1024;// 场景预制体，200KB
}else{totalSize=30*1024;// 普通预制体，30KB
}}// console.log(`预制体内存计算: ${prefab._name}, 内存=${this.formatBytes(totalSize)}`);
return totalSize;}catch(error){console.warn("计算预制体大小时出错:",error);return 30*1024;// 默认30KB
}}/**
   * 递归计算节点树大小
   */static calculateNodeTreeSize(node){if(!node)return 0;let nodeSize=0;try{// 节点基础大小
nodeSize+=1024;// 每个节点基础1KB
// 计算组件大小
if(node._components&&Array.isArray(node._components)){node._components.forEach(component=>{nodeSize+=this.calculateComponentSize(component);});}// 计算变换矩阵等数据
if(node._trs&&node._trs.byteLength){nodeSize+=node._trs.byteLength;}if(node._worldMatrix){nodeSize+=64;// 4x4矩阵
}// 递归计算子节点
if(node._children&&Array.isArray(node._children)){node._children.forEach(child=>{nodeSize+=this.calculateNodeTreeSize(child);});}}catch(error){console.warn("计算节点树大小时出错:",error);nodeSize=1024;// 默认1KB
}return nodeSize;}/**
   * 计算组件大小
   */static calculateComponentSize(component){var _a;if(!component)return 0;try{const componentType=((_a=component.constructor)===null||_a===void 0?void 0:_a.name)||"";// 根据组件类型估算大小
switch(componentType){case"cc_Sprite":case"Sprite":return 2048;// 精灵组件，2KB
case"cc_Label":case"Label":return 1024;// 文本组件，1KB
case"cc_Button":case"Button":return 1536;// 按钮组件，1.5KB
case"cc_Animation":case"Animation":return 4096;// 动画组件，4KB
case"cc_Collider":case"Collider":return 512;// 碰撞器组件，512B
case"cc_RigidBody":case"RigidBody":return 1024;// 刚体组件，1KB
case"cc_AudioSource":case"AudioSource":return 768;// 音频源组件，768B
case"cc_ParticleSystem":case"ParticleSystem":return 8192;// 粒子系统，8KB
default:return 512;// 默认组件大小，512B
}}catch(error){return 512;// 默认512B
}}/**
   * 计算材质大小
   */static calculateMaterialSize(material){try{let totalSize=0;// 材质基础大小
totalSize+=1024;// 1KB基础大小
// 计算关联的Effect大小
if(material._effect){// 如果有Effect，计算其大小
totalSize+=this.calculateEffectSize(material._effect);}else if(material._effectAsset){// 如果有EffectAsset，计算其大小
totalSize+=this.calculateEffectSize(material._effectAsset);}// 计算材质属性大小
if(material._props){try{// 材质属性包含各种uniform值
totalSize+=JSON.stringify(material._props).length*2;}catch(e){totalSize+=1024;// 估算属性大小1KB
}}// 计算材质定义大小
if(material._defines){try{totalSize+=JSON.stringify(material._defines).length*2;}catch(e){totalSize+=512;// 估算定义大小512B
}}// 计算材质状态大小
if(material._states){totalSize+=256;// 渲染状态大小
}// 如果没有关联的Effect，根据材质名称进行估算
if(totalSize<=2048){const materialName=(material._name||"").toLowerCase();if(materialName.includes("builtin")){// 内置材质
totalSize=8*1024;// 8KB
}else if(materialName.includes("ui")||materialName.includes("sprite")){// UI材质
totalSize=4*1024;// 4KB
}else if(materialName.includes("particle")||materialName.includes("effect")){// 粒子材质
totalSize=12*1024;// 12KB
}else if(materialName.includes("3d")||materialName.includes("pbr")){// 3D材质
totalSize=16*1024;// 16KB
}else{// 自定义材质
totalSize=6*1024;// 6KB
}}// 引用计数影响
const refCount=material.refCount||material._ref||1;if(refCount>5){// 高引用计数可能意味着更多的实例化数据
totalSize+=2*1024;// 额外2KB
}// console.log(`材质内存计算: ${material._name}, 引用=${refCount}, Effect=${!!material._effect}, 内存=${this.formatBytes(totalSize)}`);
return totalSize;}catch(error){console.warn("计算材质大小时出错:",error);return 6*1024;// 默认6KB
}}/**
   * 计算网格大小
   */static calculateMeshSize(mesh){try{// 根据顶点数计算网格大小
if(mesh.vertexCount){return mesh.vertexCount*32;// 每个顶点约32字节
}return 1024*1024;// 默认1MB
}catch(error){console.warn("计算网格大小时出错:",error);return 1024*1024;// 1MB
}}/**
   * 计算动画大小
   */static calculateAnimationSize(animation){try{let totalSize=0;// 动画基础大小
totalSize+=1024;// 1KB基础大小
// 计算动画时长相关的大小
const duration=animation._duration||animation.duration||0;const frameRate=animation.sample||animation.frameRate||60;const totalFrames=Math.ceil(duration*frameRate);// 计算曲线数据大小
if(animation.curveData&&animation.curveData.paths){const paths=animation.curveData.paths;let keyframeCount=0;let propertyCount=0;// 遍历所有路径和属性
for(const pathKey in paths){const path=paths[pathKey];if(path.props){for(const propKey in path.props){propertyCount++;const propData=path.props[propKey];if(Array.isArray(propData)){keyframeCount+=propData.length;// 每个关键帧的大小估算
propData.forEach(keyframe=>{totalSize+=32;// 基础关键帧大小（时间、值、曲线类型等）
// 如果值是数组（如position [x,y,z]），需要额外空间
if(Array.isArray(keyframe.value)){totalSize+=keyframe.value.length*8;// 每个数值8字节
}else if(typeof keyframe.value==="number"){totalSize+=8;// 数值8字节
}else if(typeof keyframe.value==="string"){totalSize+=keyframe.value.length*2;// 字符串
}// 曲线信息
if(keyframe.curve){totalSize+=16;// 曲线类型和参数
}});}}}}}// 计算事件数据大小
if(animation.events&&Array.isArray(animation.events)){animation.events.forEach(event=>{totalSize+=64;// 每个事件基础大小
if(event.func&&typeof event.func==="string"){totalSize+=event.func.length*2;}if(event.params&&Array.isArray(event.params)){totalSize+=event.params.length*16;// 每个参数16字节
}});}// 根据动画复杂度进行调整
if(totalFrames>300){// 长动画
totalSize+=4*1024;// 额外4KB
}else if(totalFrames>100){// 中等长度动画
totalSize+=2*1024;// 额外2KB
}// 根据动画名称进行智能估算（如果上面计算的大小太小）
if(totalSize<2048){const animName=(animation._name||"").toLowerCase();if(animName.includes("ui")||animName.includes("button")){totalSize=4*1024;// UI动画，4KB
}else if(animName.includes("character")||animName.includes("walk")||animName.includes("run")){totalSize=16*1024;// 角色动画，16KB
}else if(animName.includes("effect")||animName.includes("particle")){totalSize=8*1024;// 特效动画，8KB
}else if(animName.includes("camera")||animName.includes("scene")){totalSize=12*1024;// 相机/场景动画，12KB
}else{totalSize=6*1024;// 通用动画，6KB
}}return totalSize;}catch(error){console.warn("计算动画大小时出错:",error);return 6*1024;// 默认6KB
}}/**
   * 计算字体大小
   */static calculateFontSize(font){try{let estimatedSize=0;// TTF字体在内存中的占用主要包括：
// 1. 字体文件本身的数据
// 2. 字形缓存
// 3. 字体度量信息
// 根据字体名称和类型进行智能估算
const fontName=(font._name||"").toLowerCase();const nativeFile=(font._native||"").toLowerCase();const fontFamily=font._fontFamily||"";// 基础字体文件大小估算
if(nativeFile.includes(".ttf")){// TTF字体文件大小估算
if(fontName.includes("chinese")||fontName.includes("cn")||fontName.includes("zh")){// 中文字体通常很大，包含大量汉字字形
estimatedSize=8*1024*1024;// 8MB
}else if(fontName.includes("english")||fontName.includes("en")||fontName.includes("latin")){// 英文字体相对较小
estimatedSize=200*1024;// 200KB
}else if(fontName.includes("icon")||fontName.includes("symbol")){// 图标字体
estimatedSize=150*1024;// 150KB
}else if(fontName.includes("emoji")){// 表情字体
estimatedSize=2*1024*1024;// 2MB
}else{// 通用字体
estimatedSize=500*1024;// 500KB
}}else if(nativeFile.includes(".otf")){// OTF字体通常比TTF稍大
estimatedSize=600*1024;// 600KB
}else if(nativeFile.includes(".woff")||nativeFile.includes(".woff2")){// Web字体，通常经过压缩
estimatedSize=300*1024;// 300KB
}else{// 未知字体类型
estimatedSize=400*1024;// 400KB
}// 字形缓存大小（运行时生成的字形位图缓存）
// 这个会随着使用的字符数量增加而增加
const glyphCacheSize=1024*1024;// 假设1MB的字形缓存
estimatedSize+=glyphCacheSize;// 字体度量信息（字符宽度、高度、间距等）
const metricsSize=64*1024;// 64KB
estimatedSize+=metricsSize;// 如果引用计数较高，说明使用频繁，可能有更多缓存
const refCount=font.refCount||font._ref||1;if(refCount>3){estimatedSize+=512*1024;// 额外512KB缓存
}// console.log(`字体内存计算: ${font._name}, 文件=${font._native}, 家族=${fontFamily}, 引用=${refCount}, 内存=${this.formatBytes(estimatedSize)}`);
return estimatedSize;}catch(error){console.warn("计算字体大小时出错:",error);return 500*1024;// 默认500KB
}}static calculateJSONSize(json){try{let size=0;for(let key in json){if(json.hasOwnProperty(key)){size+=key.length*2;// 键名长度乘以2（假设每个字符2字节）
if(typeof json[key]==="string"){size+=json[key].length*2;// 字符串内容长度乘以2
}else if(typeof json[key]==="number"){size+=8;// 假设数字占用8字节（双精度浮点数）
}else if(typeof json[key]==="object"&&json[key]!==null){// 对于嵌套对象，递归调用或使用其他方法估算大小
size+=this.calculateJSONSize(json[key]);// 递归调用自身估算嵌套对象大小
}else{// 其他类型简单处理，例如布尔值、null等，通常占用较小空间，可忽略不计或简单加1或2字节处理
size+=2;// 假设其他类型占用2字节
}}}return size;// 返回估算的总大小（字节）
}catch(error){console.warn("计算JSON大小时出错:",error);return 10240;// 10KB
}}/** 计算特效大小 */static calculateEffectSize(effect){try{let totalSize=0;// 计算着色器大小
if(effect.shaders&&Array.isArray(effect.shaders)){effect.shaders.forEach(shader=>{// 每个着色器包含GLSL代码，估算大小
if(shader.glsl3){// GLSL3着色器代码
if(shader.glsl3.vert){totalSize+=shader.glsl3.vert.length*2;// 顶点着色器
}if(shader.glsl3.frag){totalSize+=shader.glsl3.frag.length*2;// 片段着色器
}}if(shader.glsl1){// GLSL1着色器代码
if(shader.glsl1.vert){totalSize+=shader.glsl1.vert.length*2;}if(shader.glsl1.frag){totalSize+=shader.glsl1.frag.length*2;}}// 着色器定义和内置变量
if(shader.defines&&Array.isArray(shader.defines)){totalSize+=shader.defines.length*50;// 每个define大约50字节
}if(shader.builtins){totalSize+=JSON.stringify(shader.builtins).length*2;}});}// 计算技术(techniques)大小
if(effect.techniques&&Array.isArray(effect.techniques)){effect.techniques.forEach(technique=>{if(technique.passes&&Array.isArray(technique.passes)){technique.passes.forEach(pass=>{// 每个pass包含渲染状态、属性等
totalSize+=1024;// 基础pass大小
// 混合状态
if(pass.blendState){totalSize+=256;}// 光栅化状态
if(pass.rasterizerState){totalSize+=128;}// 属性
if(pass.properties){totalSize+=JSON.stringify(pass.properties).length*2;}// 程序名称
if(pass.program){totalSize+=pass.program.length*2;}});}});}// 计算Effect对象本身的大小
if(effect._effect){totalSize+=2048;// Effect对象基础大小
}// 属性大小
if(effect.properties){try{totalSize+=JSON.stringify(effect.properties).length*2;}catch(e){totalSize+=512;// 估算属性大小
}}// 如果没有计算出大小，根据Effect类型给出默认值
if(totalSize===0){const effectName=effect._name||"";if(effectName.includes("builtin")){totalSize=8*1024;// 内置Effect，8KB
}else if(effectName.includes("ui")||effectName.includes("sprite")){totalSize=4*1024;// UI相关Effect，4KB
}else if(effectName.includes("particle")||effectName.includes("effect")){totalSize=16*1024;// 粒子特效，16KB
}else{totalSize=12*1024;// 自定义Effect，12KB
}}// console.log(`特效内存计算: ${effect._name}, 着色器数=${effect.shaders?.length || 0}, 技术数=${effect.techniques?.length || 0}, 内存=${this.formatBytes(totalSize)}`);
return totalSize;}catch(error){console.warn("计算特效大小时出错:",error);return 8*1024;// 默认8KB
}}/** 计算BufferAsset大小 */static calculateBufferSize(buffer){try{// 优先从_buffer属性获取ArrayBuffer的大小
if(buffer._buffer&&buffer._buffer instanceof ArrayBuffer){const arrayBuffer=buffer._buffer;// console.log(`BufferAsset内存计算: ${buffer._name}, ArrayBuffer大小=${arrayBuffer.byteLength}字节, 内存=${this.formatBytes(arrayBuffer.byteLength)}`);
return arrayBuffer.byteLength;}// 尝试从buffer属性获取
if(buffer.buffer&&buffer.buffer instanceof ArrayBuffer){return buffer.buffer.byteLength;}// 尝试从其他可能的属性获取大小
if(buffer.byteLength!==undefined){return buffer.byteLength;}if(buffer.length!==undefined){return buffer.length;}// 尝试从序列化数据获取大小
if(buffer._serialized&&buffer._serialized.data){if(buffer._serialized.data.byteLength){return buffer._serialized.data.byteLength;}if(buffer._serialized.data.length){return buffer._serialized.data.length;}}// 根据文件扩展名估算默认大小
const url=(buffer._nativeUrl||buffer.url||"").toLowerCase();if(url.includes(".bin")){return 50*1024;// .bin文件默认50KB
}else if(url.includes(".dat")){return 30*1024;// .dat文件默认30KB
}return 10*1024;// 默认10KB
}catch(error){console.warn("计算BufferAsset大小时出错:",error);return 10*1024;// 默认10KB
}}/** 计算SkeletonData大小 */static calculateSkeletonDataSize(skeleton){try{let totalSize=0;// 基础骨骼数据大小
totalSize+=2048;// 2KB基础大小
// 计算二进制数据大小
if(skeleton._buffer&&skeleton._buffer instanceof ArrayBuffer){totalSize+=skeleton._buffer.byteLength;}// 计算Atlas文本数据大小
if(skeleton._atlasText){totalSize+=skeleton._atlasText.length*2;// 每个字符2字节
}// 计算关联的纹理大小
if(skeleton.textures&&Array.isArray(skeleton.textures)){skeleton.textures.forEach(texture=>{if(texture){// 不重复计算纹理大小，因为纹理会单独统计
// 只计算引用的开销
totalSize+=64;// 每个纹理引用64字节
}});}// 计算骨骼JSON数据大小（如果有的话）
if(skeleton._skeletonJson){try{totalSize+=JSON.stringify(skeleton._skeletonJson).length*2;}catch(e){totalSize+=10*1024;// 估算10KB
}}else if(skeleton.skeletonJsonStr){totalSize+=skeleton.skeletonJsonStr.length*2;}// 根据骨骼动画复杂度进行调整
const textureCount=skeleton.textureNames?skeleton.textureNames.length:0;const scale=skeleton.scale||1;// 纹理数量影响
if(textureCount>5){totalSize+=8*1024;// 复杂骨骼，额外8KB
}else if(textureCount>2){totalSize+=4*1024;// 中等复杂度，额外4KB
}// 缩放影响（高分辨率骨骼）
if(scale>1.5){totalSize+=6*1024;// 高分辨率，额外6KB
}// 引用计数影响
const refCount=skeleton.refCount||skeleton._ref||1;if(refCount>3){// 高引用计数可能意味着更多的实例化数据
totalSize+=4*1024;// 额外4KB
}// 根据骨骼名称进行智能估算（如果计算的大小太小）
if(totalSize<5*1024){const skeletonName=(skeleton._name||"").toLowerCase();if(skeletonName.includes("character")||skeletonName.includes("hero")){totalSize=50*1024;// 角色骨骼，50KB
}else if(skeletonName.includes("effect")||skeletonName.includes("fx")){totalSize=30*1024;// 特效骨骼，30KB
}else if(skeletonName.includes("ui")||skeletonName.includes("button")){totalSize=15*1024;// UI骨骼，15KB
}else{totalSize=25*1024;// 通用骨骼，25KB
}}return totalSize;}catch(error){console.warn("计算骨骼数据大小时出错:",error);return 25*1024;// 默认25KB
}}/** 获取像素格式的字节数 */static getBytesPerPixel(format){switch(format){case"RGBA8888":case"BGRA8888":return 4;case"RGB888":case"BGR888":return 3;case"RGB565":case"RGBA4444":return 2;case"A8":case"L8":return 1;default:return 4;// 默认RGBA8888
}}/** 格式化字节数 */static formatBytes(bytes){if(bytes===-1){return"未计算";}if(bytes===0){return"0 B";}const k=1024;const sizes=["B","KB","MB","GB"];const i=Math.floor(Math.log(bytes)/Math.log(k));return parseFloat((bytes/Math.pow(k,i)).toFixed(2))+" "+sizes[i];}}MemoryHelper._loged_type=new Map();MemoryHelper._ignore_type=new Set([// 2.x的资源计算
"cc_Texture2D","cc_AudioClip","cc_EffectAsset","cc_Prefab","cc_TTFFont","cc_JsonAsset","cc_BufferAsset","cc_Material","cc_AnimationClip","cc_TextAsset","sp_SkeletonData",// 3.x的资源计算
"JsonAsset","BufferAsset","AudioClip","Prefab","EffectAsset","TTFFont","Material","AnimationClip","ImageAsset","SkeletonData","TextAsset","Asset"// Spine Atlas等资源
]);/**
 * 资源内存帮助类
 * 提供资源获取和内存计算的静态方法
 */class AssetsHelper{/** 获取总内存 */static getTotalMemory(){try{const cc=window.cc;if(cc&&cc.sys){if(cc.sys.getTotalMemory){return cc.sys.getTotalMemory();}if(cc.sys.totalMemory){return cc.sys.totalMemory;}}if(performance.memory){return performance.memory.jsHeapSizeLimit;}return 1024*1024*1024;// 1GB
}catch(error){return 1024*1024*1024;// 1GB
}}/** 获取已用内存 */static getUsedMemory(){return this._totalSize;}/** 已加载资源总数量 */static getTotalCount(){return this._totalCount;}/** 获取构建面板使用的数据 */static getBundleInfo(){// 清理未知资源
this._unknown_assets.length=0;// 先解析bundle信息
AssetsHelper.parseBundleInfos();// 打印查找失败的资源
if(this._unknown_assets.length>0){console.warn("资源缓存中未查到的资源:",this._unknown_assets);}// 通过 _bundleInfos 转换成数组
const bundleInfos=[];this._bundleInfos.forEach(info=>{bundleInfos.push(info);});// 根据size从大到小排序
bundleInfos.sort((a,b)=>(b.size||0)-(a.size||0));return bundleInfos;}/** 获取构建面板使用的数据（目录模式） */static getBundleDirectoryInfo(){// 清理未知资源
this._unknown_assets.length=0;// 先解析bundle信息
AssetsHelper.parseBundleInfos();// 通过 _bundleDirectoryInfos 转换成数组
const bundleDirectoryInfos=[];this._bundleDirectoryInfos.forEach(info=>{bundleDirectoryInfos.push(info);});// 根据size从大到小排序
bundleDirectoryInfos.sort((a,b)=>(b.size||0)-(a.size||0));return bundleDirectoryInfos;}/** 获取资源信息 */static getAssetInfo(uuid){return this._assets.get(uuid);}/**
   * 解析所有的bundle信息 只需要解析一次
   */static parseBundleInfos(){try{const cc=window.cc;if(!cc||!cc.assetManager||!cc.assetManager.bundles){console.warn("cc.assetManager.bundles is not available");}// 获取所有bundle信息
const bundles=cc.assetManager.bundles;const bundleInfos=bundles===null||bundles===void 0?void 0:bundles._map;if(!bundleInfos){return;}for(const key in bundleInfos){const info=bundleInfos[key];// console.log('bundleInfo', info);
const bundleName=info._config.base;const assetInfos=info._config.assetInfos;// bundle下已加载的资源数量
const count=assetInfos._count;for(const uuid in assetInfos._map){const{path}=assetInfos._map[uuid];this.addAssetInfo(uuid,path,bundleName);}}// console.log('this._assets', this._assets);
}catch(error){console.warn("获取bundle分组资源分类失败:",error);}this.parseLoadedAssets();}/** 解析已加载资源 */static parseLoadedAssets(){// console.log('尝试获取已加载资源信息');
this._bundleInfos.clear();this._bundleDirectoryInfos.clear();try{const cc=window.cc;const assetCaches=cc.assetManager.assets;if(assetCaches){// 已加载资源的总数量
this._totalCount=assetCaches._count;// console.log("资源总数量", this._totalCount);
// 已加载资源的总大小
this._totalSize=0;for(const uuid in assetCaches._map){const asset=assetCaches._map[uuid];//let type = asset.constructor.name;
let type=asset.__classname__.replace('cc.','');// console.log(" 类型:", type, " uuid:", uuid);
this.addLoadedAssets(type,uuid,asset);this.addLoadedAssetsToDirectory(type,uuid,asset);}}}catch(error){console.warn("解析已加载资源失败:",error);}this.sortBundleAssetGroups();this.sortBundleDirectoryGroups();}/** 对构建面板数组中的资源类型分组排序 */static sortBundleAssetGroups(){this._bundleInfos.forEach(info=>{for(const type in info.groups){const assetGroup=info.groups[type];assetGroup.uuids.sort((a,b)=>{const assetA=this._assets.get(a);const assetB=this._assets.get(b);return(assetB.size||0)-(assetA.size||0);});}});}/** 对构建面板数组中的目录分组排序 */static sortBundleDirectoryGroups(){this._bundleDirectoryInfos.forEach(info=>{for(const dirPath in info.directories){const directoryGroup=info.directories[dirPath];directoryGroup.uuids.sort((a,b)=>{const assetA=this._assets.get(a);const assetB=this._assets.get(b);return(assetB.size||0)-(assetA.size||0);});}});}/** 添加所有资源信息 */static addAssetInfo(uuid,path,bundleName){if(this._assets.has(uuid)){const info=this._assets.get(uuid);info.bundleName=bundleName;info.path=path;}else{this._assets.set(uuid,{path,bundleName,size:0});}}/** 添加已加载资源信息 type:资源类型 uuid:资源uuid asset:资源对象 */static addLoadedAssets(type,uuid,asset){if(!this._assets.has(uuid)){this.addAssetInfo(uuid,"unkonw","unkonw");}const info=this._assets.get(uuid);let{bundleName,path,size}=info;// console.log("path=", path);
// 避免重复计算内存，跳过重复的资源计算
const skipMemoryCalculation=this.shouldSkipMemoryCalculation(type,asset);if(skipMemoryCalculation){return;}// 如果没有资源内存占用，则计算
size=size||MemoryHelper.calculate(asset);// 更新资源的size
info.size=size;if(type=='ImageAsset'&&asset?.nativeUrl){info.nativeUrl=asset?.nativeUrl;}if(!this._bundleInfos.has(bundleName)){this._bundleInfos.set(bundleName,{name:bundleName,count:0,size:0,groups:{}});}const bundleGroup=this._bundleInfos.get(bundleName);bundleGroup.count++;if(size!==-1){this._totalSize+=size;bundleGroup.size+=size;}const assetGroup=bundleGroup.groups[type];if(assetGroup){assetGroup.count++;if(size!==-1){assetGroup.size+=size;}assetGroup.uuids=assetGroup.uuids||[];assetGroup.uuids.push(uuid);}else{bundleGroup.groups[type]={type,count:1,size,uuids:[uuid]};}}/**
   * 判断是否应该跳过内存计算
   * 根据Creator版本应用不同的过滤规则
   */static shouldSkipMemoryCalculation(type,asset){const isCreator3xVersion=isCreator3x();if(isCreator3xVersion){// Creator 3.x架构: ImageAsset → Texture2D → SpriteFrame
// ImageAsset存储原始像素数据，是真正的内存占用
// Texture2D和SpriteFrame都只是引用，应该跳过避免重复计算
if(type.includes("SpriteFrame")||type.includes("Texture2D")||type.includes("TextureCube")){return true;}// ImageAsset保留计算，这是真正的内存占用
return false;}else{// Creator 2.x架构: Texture2D → SpriteFrame
// Texture2D是真正的内存占用，SpriteFrame只是引用
if(type.includes("SpriteFrame")){return true;// SpriteFrame只是对Texture2D的引用，跳过
}// Texture2D保留计算，这是真正的内存占用
return false;}}/** 添加已加载资源信息到目录分组 type:资源类型 uuid:资源uuid asset:资源对象 */static addLoadedAssetsToDirectory(type,uuid,asset){if(!this._assets.has(uuid)){this.addAssetInfo(uuid,"unkonw","unkonw");}const info=this._assets.get(uuid);if(!info.extension){info.extension=this.getExtension(type,asset._native);}let{bundleName,path,size}=info;// 避免重复计算内存，跳过重复的资源计算
const skipMemoryCalculation=this.shouldSkipMemoryCalculation(type,asset);if(skipMemoryCalculation){return;}// 获取目录路径（去掉文件名）
const dirPath=this.getDirectoryPath(path);if(!this._bundleDirectoryInfos.has(bundleName)){this._bundleDirectoryInfos.set(bundleName,{name:bundleName,count:0,size:0,directories:{}});}const bundleDirectoryGroup=this._bundleDirectoryInfos.get(bundleName);bundleDirectoryGroup.count++;if(size!==-1){bundleDirectoryGroup.size+=size;}const directoryGroup=bundleDirectoryGroup.directories[dirPath];if(directoryGroup){directoryGroup.count++;if(size!==-1){directoryGroup.size+=size;}directoryGroup.uuids=directoryGroup.uuids||[];directoryGroup.uuids.push(uuid);}else{bundleDirectoryGroup.directories[dirPath]={path:dirPath,count:1,size,uuids:[uuid]};}}static getExtension(type,native){let extension="UNKNOWN";if(native&&typeof native==="string"){// 如果native包含点号，说明是文件名或扩展名
if(native.includes(".")){// 获取最后一个点号后的内容作为扩展名
const parts=native.split(".");extension=parts[parts.length-1].toLowerCase();}else if(native.startsWith(".")){// 如果以点号开头，去掉点号
extension=native.substring(1).toLowerCase();}else{// 如果没有点号，可能是纯扩展名
extension=native.toLowerCase();}}else{// 如果没有点号，使用类型
extension=type.toLowerCase();}// 确保扩展名是合理的长度（1-10个字符）
if(extension.length>10){extension=type.toLowerCase();}if(!this._log_set.has(extension)){this._log_set.add(extension);// console.log("后缀名", extension);
}return extension;}/** 获取文件的目录路径 */static getDirectoryPath(filePath){if(!filePath)return"/";// 处理路径分隔符
const normalizedPath=filePath.replace(/\\/g,"/");const lastSlashIndex=normalizedPath.lastIndexOf("/");if(lastSlashIndex===-1){return"/";// 根目录
}const dirPath=normalizedPath.substring(0,lastSlashIndex);return dirPath||"/";}}/** 已加载资源的总数量 */AssetsHelper._totalCount=0;/** 已加载资源占用的总内存 */AssetsHelper._totalSize=0;/** 用来存储所有的资源信息 key: 资源的uuid  value: 资源信息 */AssetsHelper._assets=new Map();/** 用来构建面板使用的数据（资源类型模式） */AssetsHelper._bundleInfos=new Map();/** 用来构建面板使用的数据（目录模式） */AssetsHelper._bundleDirectoryInfos=new Map();/** 未知的资源uuid */AssetsHelper._unknown_assets=[];/** 用来记录日志的set */AssetsHelper._log_set=new Set();/**
 * 资源内存面板组件
 * 显示Creator中的资源内存占用信息
 */class ResourceMemoryPanel{constructor(){this.container=null;this.updateInterval=null;this.isMonitoring=false;this.scrollHandler=null;// 显示模式：'type' | 'directory'
this.displayMode="type";// 新增筛选相关属性
this.filterConditions={memorySize:null,resourceTypes:[]};this.isFilterPanelVisible=false;}/**
   * 创建资源内存面板
   */create(parent){this.createContainer(parent);this.applyStyles();this.startMonitoring();}/**
   * 创建容器
   */createContainer(parent){this.container=document.createElement("div");this.container.className="node-inspector-right-panel resource-memory-panel";this.container.innerHTML=`
            <div class="resource-memory-header">
                <h3>资源内存监控</h3>
                <div class="memory-stats">
                    <div class="stat-item">
                        <span class="stat-label">已用内存:</span>
                        <span class="stat-value" id="used-memory">--</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">已加载资源数量:</span>
                        <span class="stat-value" id="loaded-assets-count">--</span>
                    </div>
                </div>
            </div>
            <div class="resource-memory-filter">
                <button class="filter-toggle-button" id="filter-toggle-btn">
                    <span class="filter-icon">🔍</span>
                    筛选条件
                    <span class="filter-count" id="filter-count" style="display: none;"></span>
                </button>
                <div class="filter-panel" id="filter-panel" style="display: none;">
                    <div class="filter-section">
                        <h4>按内存大小筛选</h4>
                        <div class="filter-size-controls">
                            <label>最小值:</label>
                            <input type="number" id="filter-min-size-input" placeholder="输入数值" min="0" step="1" value="">
                            <select id="filter-size-unit">
                                <option value="1">B</option>
                                <option value="1024" selected>KB</option>
                                <option value="1048576">MB</option>
                                <option value="1073741824">GB</option>
                            </select>
                        </div>
                    </div>
                    <div class="filter-section">
                        <h4>按资源类型筛选</h4>
                        <div class="filter-type-controls" id="filter-type-list">
                            <!-- 动态生成资源类型复选框 -->
                        </div>
                    </div>
                    <div class="filter-actions">
                        <button class="filter-apply-btn" id="filter-apply-btn">应用筛选</button>
                        <button class="filter-clear-btn" id="filter-clear-btn">清除筛选</button>
                    </div>
                </div>
            </div>
            <div class="resource-memory-content">
                <div id="resource-type-list">
                    <!-- 动态生成资源类型分块 -->
                </div>
            </div>
            <div class="resource-memory-mode-selector">
                <div class="mode-selector-label">显示模式:</div>
                <div class="mode-selector-options">
                    <label class="mode-option">
                        <input type="radio" name="display-mode" value="type" checked>
                        <span class="radio-label">资源类型模式</span>
                    </label>
                    <label class="mode-option">
                        <input type="radio" name="display-mode" value="directory">
                        <span class="radio-label">目录模式</span>
                    </label>
                </div>
            </div>
            <div class="resource-memory-footer">
                <button class="refresh-button-container" id="refresh-memory-btn">
                    <span class="refresh-icon">🔄</span>
                    刷新数据
                </button>
                <div class="last-update" id="last-update-time">
                    最后更新: 未更新
                </div>
            </div>
        `;parent.appendChild(this.container);}/**
   * 应用样式
   */applyStyles(){const style=document.createElement("style");style.textContent=`
            .resource-memory-panel {
                display: flex;
                flex-direction: column;
                height: 100%;
                overflow: hidden;
            }

            .resource-memory-header {
                flex-shrink: 0;
                padding: 10px;
                padding-right: 15px;
                background: #1a1a1a;
                border-bottom: 1px solid #444;
            }

            .resource-memory-header h3 {
                margin: 0 0 10px 0;
                color: #fff;
                font-size: 14px;
                font-weight: bold;
            }

            .resource-memory-content {
                flex: 1;
                overflow-y: scroll;
                padding: 0px 15px 0px 10px;
            }

            /* 滚动条样式 - 参考左侧抽屉 */
            .resource-memory-content::-webkit-scrollbar {
                width: 8px;
            }

            .resource-memory-content::-webkit-scrollbar-track {
                background: #1e1e1e;
            }

            .resource-memory-content::-webkit-scrollbar-thumb {
                background: #555;
                border-radius: 4px;
            }

            .resource-memory-content::-webkit-scrollbar-thumb:hover {
                background: #666;
            }

            .resource-memory-mode-selector {
                flex-shrink: 0;
                padding: 10px;
                padding-right: 15px;
                background: #1a1a1a;
                border-top: 1px solid #444;
                border-bottom: 1px solid #444;
            }

            .mode-selector-label {
                color: #ccc;
                font-size: 12px;
                margin-bottom: 8px;
            }

            .mode-selector-options {
                display: flex;
                gap: 20px;
            }

            .mode-option {
                display: flex;
                align-items: center;
                gap: 6px;
                cursor: pointer;
                font-size: 12px;
                color: #ccc;
                user-select: none;
            }

            .mode-option input[type="radio"] {
                margin: 0;
                accent-color: #007acc;
            }

            .mode-option:hover {
                color: #fff;
            }

            .radio-label {
                cursor: pointer;
            }

            .resource-type {
                margin-right: 10px;
                color: #888;
                font-size: 11px;
                font-style: italic;
                white-space: nowrap;
                flex-shrink: 0;
            }

            .resource-memory-footer {
                flex-shrink: 0;
                padding: 10px;
                padding-right: 15px;
                background: #1a1a1a;
                border-top: 1px solid #444;
                text-align: center;
            }

            .memory-stats {
                background: #1e1e1e;
                border: 1px solid #444;
                border-radius: 4px;
                padding: 10px;
                margin-bottom: 0;
            }

            .stat-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 5px 0;
                border-bottom: 1px solid #333;
            }

            .stat-item:last-child {
                border-bottom: none;
            }

            .stat-label {
                color: #ccc;
                font-size: 12px;
            }

            .stat-value {
                color: #fff;
                font-weight: bold;
                font-size: 12px;
            }

            .resource-categories,
            .texture-info,
            .audio-info {
                background: #1e1e1e;
                border: 1px solid #444;
                border-radius: 4px;
                padding: 10px;
                margin-bottom: 15px;
            }

            .resource-categories h4,
            .texture-info h4,
            .audio-info h4 {
                margin: 0 0 10px 0;
                color: #fff;
                font-size: 13px;
                font-weight: bold;
                border-bottom: 1px solid #444;
                padding-bottom: 5px;
                cursor: pointer;
                user-select: none;
                display: flex;
                align-items: center;
            }

            .bundle-group-block {
                background: #2a2a2a;
                border: 2px solid #555;
                border-radius: 6px;
                padding: 0;
                margin: 15px 0;
            }

            .bundle-group-block:first-child {
                margin-top: 10px;
            }

            .bundle-group-block:last-child {
                margin-bottom: 10px;
            }

            .bundle-group-header {
                margin: 0;
                color: #fff;
                font-size: 14px;
                font-weight: bold;
                background: #333;
                border-bottom: 1px solid #555;
                padding: 12px 15px;
                cursor: pointer;
                user-select: none;
                display: flex;
                align-items: center;
            }

            .bundle-group-header:hover {
                background: #3a3a3a;
            }

            .bundle-content {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease;
                position: relative;
            }

            .bundle-content.expanded {
                max-height: none;
                overflow: visible;
            }

            /* 移除bundle内容区域滚动条样式 */

            .resource-type-block {
                border-bottom: 1px solid #444;
            }

            .resource-type-block:last-child {
                border-bottom: none;
            }

            .resource-type-header {
                margin: 0;
                color: #fff;
                font-size: 13px;
                font-weight: bold;
                background: #2a2a2a;
                padding: 10px 15px;
                cursor: pointer;
                user-select: none;
                display: flex;
                align-items: center;
                border-bottom: 1px solid #444;
                transition: background-color 0.2s ease;
            }

            .resource-type-header:hover {
                background: #3a3a3a;
            }

            /* Bundle标题粘性样式 */
            .bundle-group-header {
                position: sticky;
                top: 0;
                z-index: 20;
                background: #333;
            }

            .bundle-group-header.is-sticky {
                background: #3a3a3a !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }

            /* 资源类型标题粘性样式 */
            .resource-type-header.sticky-header {
                position: sticky;
                z-index: 10;
                background: #2a2a2a;
                border-bottom: 2px solid #555;
            }

            .resource-type-header.is-sticky {
                background: #3a3a3a !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }

            .resource-details {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease;
                background: #1e1e1e;
            }

            .resource-details.expanded {
                max-height: none;
                overflow: visible;
            }

            .resource-detail-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 20px;
                border-bottom: 1px solid #333;
                font-size: 12px;
            }

            .resource-detail-item:last-child {
                border-bottom: none;
            }

            .resource-detail-item:hover {
                background: #333;
            }

            .resource-detail-name {
                color: #ccc;
                flex: 1;
                margin-right: 10px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .resource-detail-size {
                color: #fff;
                font-weight: bold;
                flex-shrink: 0;
            }

            .expand-toggle {
                color: #888;
                font-size: 12px;
                margin-right: 8px;
                transition: transform 0.2s ease;
                display: inline-block;
                width: 16px;
                text-align: center;
                flex-shrink: 0;
            }

            .expand-toggle.expanded {
                transform: rotate(90deg);
                color: #fff;
            }

            .bundle-count,
            .category-count {
                color: #888;
                font-weight: normal;
                font-size: 12px;
                margin-left: auto;
                flex-shrink: 0;
            }

            .bundle-title,
            .category-title {
                flex: 1;
                margin-right: 10px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                min-width: 0;
            }

            /* 当文本过长时自动缩小字号 */
            .category-title {
                font-size: 13px;
                display: flex;
                align-items: center;
                transition: font-size 0.1s ease;
            }

            .category-title.text-overflow {
                font-size: 11px;
            }

            .category-title.text-overflow-small {
                font-size: 9px;
            }

            .no-data {
                text-align: center;
                color: #888;
                padding: 40px 20px;
                font-style: italic;
            }

            .category-summary {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 5px 0;
                border-bottom: 1px solid #333;
                font-weight: bold;
            }

            .category-summary:last-child {
                border-bottom: none;
            }

            .refresh-button-container {
                background: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 8px auto;
                transition: background 0.2s ease;
            }

            .refresh-button-container:hover {
                background: #106ebe;
            }

            .refresh-button-container:active {
                background: #005a9e;
            }

            .refresh-icon {
                margin-right: 6px;
                font-size: 14px;
            }

            .refresh-button-container.loading .refresh-icon {
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            .last-update {
                color: #888;
                font-size: 10px;
            }

            .memory-tips {
                color: #f39c12;
                font-size: 9px;
                margin-top: 8px;
                padding: 6px 8px;
                background: rgba(243, 156, 18, 0.1);
                border: 1px solid rgba(243, 156, 18, 0.3);
                border-radius: 3px;
                text-align: left;
                line-height: 1.4;
            }

            /* 筛选相关样式 */
            .resource-memory-filter {
                flex-shrink: 0;
                padding: 8px 10px;
                background: #1a1a1a;
                border-bottom: 1px solid #444;
            }

            .filter-toggle-button {
                width: 100%;
                background: #2a2a2a;
                color: #fff;
                border: 1px solid #555;
                padding: 8px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background 0.2s ease;
                position: relative;
            }

            .filter-toggle-button:hover {
                background: #3a3a3a;
            }

            .filter-icon {
                margin-right: 6px;
                font-size: 14px;
            }

            .filter-count {
                background: #e74c3c;
                color: white;
                border-radius: 10px;
                padding: 2px 6px;
                margin-left: 8px;
                font-size: 10px;
                font-weight: bold;
            }

            .filter-panel {
                margin-top: 8px;
                background: #1e1e1e;
                border: 1px solid #444;
                border-radius: 4px;
                padding: 12px;
                animation: slideDown 0.2s ease;
            }

            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .filter-section {
                margin-bottom: 12px;
            }

            .filter-section:last-child {
                margin-bottom: 0;
            }

            .filter-section h4 {
                margin: 0 0 8px 0;
                color: #fff;
                font-size: 12px;
                font-weight: bold;
            }

            .filter-size-controls {
                display: grid;
                grid-template-columns: auto 1fr auto;
                gap: 8px;
                align-items: center;
            }

            .filter-size-controls label {
                color: #ccc;
                font-size: 11px;
            }

            .filter-size-controls input,
            .filter-size-controls select {
                background: #333;
                color: #fff;
                border: 1px solid #555;
                border-radius: 3px;
                padding: 4px 6px;
                font-size: 11px;
            }

            .filter-size-controls input:focus {
                outline: none;
                border-color: #0078d4;
            }

            .filter-type-controls {
                max-height: 120px;
                overflow-y: auto;
                border: 1px solid #333;
                border-radius: 3px;
                padding: 6px;
                background: #333;
            }

            .filter-type-item {
                display: flex;
                align-items: center;
                padding: 4px 0;
                color: #ccc;
                font-size: 11px;
            }

            .filter-type-item input[type="checkbox"] {
                margin-right: 6px;
            }

            .filter-actions {
                display: flex;
                gap: 8px;
                margin-top: 12px;
            }

            .filter-apply-btn,
            .filter-clear-btn {
                flex: 1;
                padding: 6px 12px;
                border: none;
                border-radius: 3px;
                font-size: 11px;
                cursor: pointer;
                transition: background 0.2s ease;
            }

            .filter-apply-btn {
                background: #27ae60;
                color: white;
            }

            .filter-apply-btn:hover {
                background: #2ecc71;
            }

            .filter-clear-btn {
                background: #e74c3c;
                color: white;
            }

            .filter-clear-btn:hover {
                background: #c0392b;
            }
        `;document.head.appendChild(style);}/**
   * 绑定事件
   */bindEvents(){// 绑定展开/收起事件
this.container.addEventListener("click",e=>{const target=e.target;// 检查是否点击了bundle分组标题或其子元素
const bundleHeader=target.closest(".bundle-group-header");if(bundleHeader){const targetId=bundleHeader.getAttribute("data-target");if(targetId){this.toggleExpand(targetId);}return;}// 检查是否点击了资源类型标题或其子元素
const typeHeader=target.closest(".resource-type-header");if(typeHeader){const targetId=typeHeader.getAttribute("data-target");if(targetId){this.toggleExpand(targetId);}return;}});// 绑定模式切换事件
const modeRadios=this.container.querySelectorAll('input[name="display-mode"]');modeRadios.forEach(radio=>{radio.addEventListener("change",event=>{const target=event.target;if(target.checked){this.displayMode=target.value;this.updateResourceTypeList();}});});// 绑定刷新按钮事件
const refreshBtn=this.container.querySelector("#refresh-memory-btn");if(refreshBtn){refreshBtn.addEventListener("click",()=>{this.refreshData();});}// 绑定筛选相关事件
this.bindFilterEvents();}/**
   * 绑定筛选相关事件
   */bindFilterEvents(){// 筛选面板切换按钮
const filterToggleBtn=this.container.querySelector("#filter-toggle-btn");if(filterToggleBtn){filterToggleBtn.addEventListener("click",()=>{this.toggleFilterPanel();});}// 应用筛选按钮
const filterApplyBtn=this.container.querySelector("#filter-apply-btn");if(filterApplyBtn){filterApplyBtn.addEventListener("click",()=>{this.applyFilter();});}// 清除筛选按钮
const filterClearBtn=this.container.querySelector("#filter-clear-btn");if(filterClearBtn){filterClearBtn.addEventListener("click",()=>{this.clearFilter();});}}/**
   * 切换筛选面板显示/隐藏
   */toggleFilterPanel(){const filterPanel=this.container.querySelector("#filter-panel");if(!filterPanel)return;this.isFilterPanelVisible=!this.isFilterPanelVisible;filterPanel.style.display=this.isFilterPanelVisible?"block":"none";// 如果打开面板，则更新资源类型列表
if(this.isFilterPanelVisible){this.updateFilterTypeList();}}/**
   * 更新筛选面板中的资源类型列表
   */updateFilterTypeList(){const typeListContainer=this.container.querySelector("#filter-type-list");if(!typeListContainer)return;typeListContainer.innerHTML="";// 获取所有可用的资源类型
const bundleGroups=AssetsHelper.getBundleInfo();const allTypes=new Set();bundleGroups.forEach(bundleGroup=>{Object.keys(bundleGroup.groups).forEach(type=>{allTypes.add(type);});});// 创建复选框
Array.from(allTypes).sort().forEach(type=>{const typeItem=document.createElement("div");typeItem.className="filter-type-item";const checkbox=document.createElement("input");checkbox.type="checkbox";checkbox.id=`filter-type-${type}`;checkbox.value=type;checkbox.checked=this.filterConditions.resourceTypes.includes(type);const label=document.createElement("label");label.htmlFor=`filter-type-${type}`;label.textContent=type;typeItem.appendChild(checkbox);typeItem.appendChild(label);typeListContainer.appendChild(typeItem);});}/**
   * 应用筛选条件
   */applyFilter(){// 获取内存大小筛选条件
const minSizeInput=this.container.querySelector("#filter-min-size-input");const sizeUnitSelect=this.container.querySelector("#filter-size-unit");const minSizeValue=parseFloat(minSizeInput.value)||0;const sizeUnitMultiplier=parseInt(sizeUnitSelect.value)||1;if(minSizeValue>0){const minSizeInBytes=minSizeValue*sizeUnitMultiplier;this.filterConditions.memorySize={min:minSizeInBytes,max:Number.MAX_SAFE_INTEGER};}else{this.filterConditions.memorySize=null;}// 获取资源类型筛选条件
const typeCheckboxes=this.container.querySelectorAll('#filter-type-list input[type="checkbox"]:checked');this.filterConditions.resourceTypes=Array.from(typeCheckboxes).map(cb=>cb.value);// 更新筛选计数显示
this.updateFilterCount();// 隐藏筛选面板
this.isFilterPanelVisible=false;const filterPanel=this.container.querySelector("#filter-panel");if(filterPanel){filterPanel.style.display="none";}// 刷新数据显示
this.updateResourceTypeList();Tool.log("应用筛选条件:",this.filterConditions);}/**
   * 清除筛选条件
   */clearFilter(){// 重置筛选条件
this.filterConditions.memorySize=null;this.filterConditions.resourceTypes=[];// 重置界面控件
const minSizeInput=this.container.querySelector("#filter-min-size-input");const sizeUnitSelect=this.container.querySelector("#filter-size-unit");if(minSizeInput)minSizeInput.value="";if(sizeUnitSelect)sizeUnitSelect.value="1024";const typeCheckboxes=this.container.querySelectorAll('#filter-type-list input[type="checkbox"]');typeCheckboxes.forEach(cb=>cb.checked=false);// 更新筛选计数显示
this.updateFilterCount();// 隐藏筛选面板
this.isFilterPanelVisible=false;const filterPanel=this.container.querySelector("#filter-panel");if(filterPanel){filterPanel.style.display="none";}// 刷新数据显示
this.updateResourceTypeList();}/**
   * 更新筛选计数显示
   */updateFilterCount(){const filterCountElement=this.container.querySelector("#filter-count");if(!filterCountElement)return;let filterCount=0;if(this.filterConditions.memorySize)filterCount++;if(this.filterConditions.resourceTypes.length>0)filterCount++;if(filterCount>0){filterCountElement.textContent=`${filterCount}`;filterCountElement.style.display="inline";}else{filterCountElement.style.display="none";}}/**
   * 应用筛选条件到数据
   */applyFilterToData(bundleGroups){if(!this.filterConditions.memorySize&&this.filterConditions.resourceTypes.length===0){return bundleGroups;// 没有筛选条件，返回原始数据
}const filteredBundleGroups=[];bundleGroups.forEach(bundleGroup=>{const filteredGroups={};let bundleHasValidData=false;// 遍历bundle下的每个资源类型分组
Object.keys(bundleGroup.groups).forEach(type=>{const assetGroup=bundleGroup.groups[type];// 应用资源类型筛选
if(this.filterConditions.resourceTypes.length>0){if(!this.filterConditions.resourceTypes.includes(type)){return;// 跳过不匹配的资源类型
}}// 应用内存大小筛选
if(this.filterConditions.memorySize){const size=assetGroup.size||0;if(size<this.filterConditions.memorySize.min||size>this.filterConditions.memorySize.max){return;// 跳过不符合内存大小条件的资源组
}}// 进一步筛选资源组内的具体资源
if(this.filterConditions.memorySize&&assetGroup.uuids){const filteredUuids=[];let filteredSize=0;let filteredCount=0;assetGroup.uuids.forEach(uuid=>{const assetInfo=AssetsHelper.getAssetInfo(uuid);if(assetInfo){const assetSize=assetInfo.size||0;if(assetSize>=this.filterConditions.memorySize.min&&assetSize<=this.filterConditions.memorySize.max){filteredUuids.push(uuid);filteredSize+=assetSize;filteredCount++;}}});if(filteredUuids.length>0){filteredGroups[type]=Object.assign(Object.assign({},assetGroup),{uuids:filteredUuids,size:filteredSize,count:filteredCount});bundleHasValidData=true;}}else{// 没有内存大小筛选，或者没有详细的资源列表，直接包含整个分组
filteredGroups[type]=assetGroup;bundleHasValidData=true;}});// 如果bundle有有效数据，添加到结果中
if(bundleHasValidData){// 重新计算bundle的总大小和数量
let totalSize=0;let totalCount=0;Object.values(filteredGroups).forEach(group=>{totalSize+=group.size||0;totalCount+=group.count||0;});filteredBundleGroups.push(Object.assign(Object.assign({},bundleGroup),{groups:filteredGroups,size:totalSize,count:totalCount}));}});return filteredBundleGroups;}/**
   * 应用筛选条件到目录模式数据
   */applyFilterToDirectoryData(bundleDirectoryGroups){if(!this.filterConditions.memorySize&&this.filterConditions.resourceTypes.length===0){return bundleDirectoryGroups;// 没有筛选条件，返回原始数据
}const filteredBundleDirectoryGroups=[];bundleDirectoryGroups.forEach(bundleGroup=>{const filteredDirectories={};let bundleHasValidData=false;// 遍历bundle下的每个目录分组
Object.keys(bundleGroup.directories).forEach(dirPath=>{const directoryGroup=bundleGroup.directories[dirPath];// 进一步筛选目录内的具体资源
if(directoryGroup.uuids){const filteredUuids=[];let filteredSize=0;let filteredCount=0;directoryGroup.uuids.forEach(uuid=>{var _a,_b,_c,_d;const assetInfo=AssetsHelper.getAssetInfo(uuid);if(assetInfo){// 获取资源类型（从extension或通过CC API获取）
const cc=window.cc;const asset=(_c=(_b=(_a=cc===null||cc===void 0?void 0:cc.assetManager)===null||_a===void 0?void 0:_a.assets)===null||_b===void 0?void 0:_b._map)===null||_c===void 0?void 0:_c[uuid];const resourceType=((_d=asset===null||asset===void 0?void 0:asset.constructor)===null||_d===void 0?void 0:_d.name)||"Unknown";// 应用资源类型筛选
if(this.filterConditions.resourceTypes.length>0){if(!this.filterConditions.resourceTypes.includes(resourceType)){return;// 跳过不匹配的资源类型
}}// 应用内存大小筛选
const assetSize=assetInfo.size||0;if(this.filterConditions.memorySize){if(assetSize<this.filterConditions.memorySize.min||assetSize>this.filterConditions.memorySize.max){return;// 跳过不符合内存大小条件的资源
}}// 通过所有筛选条件
filteredUuids.push(uuid);filteredSize+=assetSize;filteredCount++;}});if(filteredUuids.length>0){filteredDirectories[dirPath]=Object.assign(Object.assign({},directoryGroup),{uuids:filteredUuids,size:filteredSize,count:filteredCount});bundleHasValidData=true;}}});// 如果bundle有有效数据，添加到结果中
if(bundleHasValidData){// 重新计算bundle的总大小和数量
let totalSize=0;let totalCount=0;Object.values(filteredDirectories).forEach(directory=>{totalSize+=directory.size||0;totalCount+=directory.count||0;});filteredBundleDirectoryGroups.push(Object.assign(Object.assign({},bundleGroup),{directories:filteredDirectories,size:totalSize,count:totalCount}));}});return filteredBundleDirectoryGroups;}/**
   * 刷新数据
   */refreshData(){const refreshBtn=this.container.querySelector("#refresh-memory-btn");if(refreshBtn){refreshBtn.classList.add("loading");refreshBtn.textContent="刷新中...";}// 模拟异步刷新
setTimeout(()=>{this.updateMemoryInfo();this.updateLastUpdateTime();if(refreshBtn){refreshBtn.classList.remove("loading");refreshBtn.innerHTML='<span class="refresh-icon">🔄</span>刷新数据';}},500);}/**
   * 更新最后更新时间
   */updateLastUpdateTime(){const lastUpdateElement=this.container.querySelector("#last-update-time");if(lastUpdateElement){const now=new Date();const timeString=now.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",second:"2-digit"});lastUpdateElement.textContent=`最后更新: ${timeString}`;}}/**
   * 切换展开/收起状态
   */toggleExpand(targetId){const content=this.container.querySelector(`#${targetId}`);// 检查元素是否存在
if(!content){console.warn(`无法找到目标元素: ${targetId}`);return;}// 找到对应的标题元素（可能是bundle-group-header或resource-type-header）
const header=this.container.querySelector(`[data-target="${targetId}"]`);const toggle=header===null||header===void 0?void 0:header.querySelector(".expand-toggle");if(content.classList.contains("expanded")){content.classList.remove("expanded");if(toggle){toggle.classList.remove("expanded");}}else{content.classList.add("expanded");if(toggle){toggle.classList.add("expanded");}}// 每次展开/收起后重新绑定粘性标题行为
setTimeout(()=>{this.addStickyHeaderBehavior();},50);}/**
   * 开始监控
   */startMonitoring(){if(this.isMonitoring)return;this.isMonitoring=true;this.bindEvents();this.updateMemoryInfo();this.updateLastUpdateTime();console.log("资源内存监控已开始（手动刷新模式）");}/**
   * 停止监控
   */stopMonitoring(){if(this.updateInterval){clearInterval(this.updateInterval);this.updateInterval=null;}this.isMonitoring=false;console.log("资源内存监控已停止");}/**
   * 更新内存信息
   */updateMemoryInfo(){try{this.updateResourceTypeList();this.updateSystemMemory();}catch(error){console.warn("更新内存信息失败:",error);}}/**
   * 更新系统内存信息
   */updateSystemMemory(){const usedMemory=AssetsHelper.getUsedMemory();const loadedAssetsCount=AssetsHelper.getTotalCount();this.updateElement("used-memory",MemoryHelper.formatBytes(usedMemory));this.updateElement("loaded-assets-count",loadedAssetsCount.toString());}/**
   * 更新资源类型分块（现在按bundle分组）
   */updateResourceTypeList(){const container=this.container.querySelector("#resource-type-list");if(!container)return;if(this.displayMode==="type"){this.renderResourceTypeMode(container);}else{this.renderDirectoryMode(container);}}/**
   * 渲染资源类型模式（保持原有逻辑）
   */renderResourceTypeMode(container){// 保存当前展开状态（记录bundle分组和资源类型分组的展开状态）
const expandedStates=new Map();const expandedContents=container.querySelectorAll(".expanded");expandedContents.forEach(content=>{if(content.id){expandedStates.set(content.id,true);}});container.innerHTML="";// 获取面板数据
let bundleGroups=AssetsHelper.getBundleInfo();if(bundleGroups.length===0){container.innerHTML='<div class="no-data">暂无资源数据，请点击刷新按钮获取数据</div>';return;}// 应用筛选条件
bundleGroups=this.applyFilterToData(bundleGroups);if(bundleGroups.length===0){container.innerHTML='<div class="no-data">没有符合筛选条件的资源数据</div>';return;}bundleGroups.forEach(bundleGroup=>{// 创建bundle分组块
const bundleBlock=document.createElement("div");bundleBlock.className="bundle-group-block";// 转义bundle名称用于CSS选择器
const escapedBundleName=this.escapeSelector(bundleGroup.name);// Bundle分组标题和展开按钮
const bundleHeader=document.createElement("h3");bundleHeader.className="bundle-group-header";bundleHeader.setAttribute("data-target",`bundle-${escapedBundleName}`);bundleHeader.innerHTML=`
                <span class="expand-toggle">▶</span>
                <span class="bundle-title">${bundleGroup.name}</span>
                <span class="bundle-count">${bundleGroup.count||0} 个资源 / ${MemoryHelper.formatBytes(bundleGroup.size||0)}</span>
            `;bundleBlock.appendChild(bundleHeader);// 创建bundle内容区域 - 可滚动的容器
const bundleContent=document.createElement("div");bundleContent.className="bundle-content";bundleContent.id=`bundle-${escapedBundleName}`;// 如果刷新前bundle是展开的，刷新后保持展开状态
if(expandedStates.get(bundleContent.id)){bundleContent.classList.add("expanded");const toggle=bundleHeader.querySelector(".expand-toggle");if(toggle)toggle.classList.add("expanded");}// 在bundle内容区域中创建资源类型分组
const groups=bundleGroup.groups;const sortedTypes=Object.keys(groups).sort((a,b)=>(groups[b].size||0)-(groups[a].size||0));sortedTypes.forEach(type=>{const assetGroup=groups[type];// 每个类型一个可展开分块
const typeBlock=document.createElement("div");typeBlock.className="resource-type-block";// 转义类型名称用于CSS选择器
const escapedType=this.escapeSelector(type);// 资源类型标题和展开按钮（支持粘性定位）
const typeHeader=document.createElement("h4");typeHeader.className="resource-type-header sticky-header";typeHeader.setAttribute("data-target",`type-${escapedBundleName}-${escapedType}`);typeHeader.innerHTML=`
                    <span class="expand-toggle">▶</span>
                    <span class="category-title">${type}</span>
                    <span class="category-count">${assetGroup.count} 个 / ${MemoryHelper.formatBytes(assetGroup.size)}</span>
                `;typeBlock.appendChild(typeHeader);// 创建资源详情列表
if(assetGroup.uuids&&assetGroup.uuids.length>0){const details=document.createElement("div");details.className="resource-details";details.id=`type-${escapedBundleName}-${escapedType}`;// 如果刷新前是展开的，刷新后保持展开状态
if(expandedStates.get(details.id)){details.classList.add("expanded");const toggle=typeHeader.querySelector(".expand-toggle");if(toggle)toggle.classList.add("expanded");}// 按UUID获取资源详情并按内存占用排序
const resourceDetails=[];assetGroup.uuids.forEach(uuid=>{const assetInfo=AssetsHelper.getAssetInfo(uuid);if(assetInfo){resourceDetails.push({name:assetInfo.path||uuid,size:assetInfo.size||0,nativeUrl:assetInfo.nativeUrl});}});// 按内存占用排序
resourceDetails.sort((a,b)=>b.size-a.size);resourceDetails.forEach(resource=>{const detailItem=document.createElement("div");detailItem.className="resource-detail-item";detailItem.innerHTML=`
                            <span class="resource-detail-name" title="${resource.name}">`+(resource.nativeUrl?"<a href='"+resource.nativeUrl+"' target='_blank'>":"")+`${resource.name}`+(resource.nativeUrl?"</a>":"")+`</span>
                            <span class="resource-detail-size">${MemoryHelper.formatBytes(resource.size)}</span>
                        `;details.appendChild(detailItem);});typeBlock.appendChild(details);}bundleContent.appendChild(typeBlock);});bundleBlock.appendChild(bundleContent);container.appendChild(bundleBlock);});// 为bundle内容区域添加滚动监听，实现粘性标题
this.addStickyHeaderBehavior();}/**
   * 渲染目录模式
   */renderDirectoryMode(container){// 保存当前展开状态
const expandedStates=new Map();const expandedContents=container.querySelectorAll(".expanded");expandedContents.forEach(content=>{if(content.id){expandedStates.set(content.id,true);}});container.innerHTML="";// 获取目录模式数据
let bundleDirectoryGroups=AssetsHelper.getBundleDirectoryInfo();if(bundleDirectoryGroups.length===0){container.innerHTML='<div class="no-data">暂无资源数据，请点击刷新按钮获取数据</div>';return;}// 应用筛选条件
bundleDirectoryGroups=this.applyFilterToDirectoryData(bundleDirectoryGroups);if(bundleDirectoryGroups.length===0){container.innerHTML='<div class="no-data">没有符合筛选条件的资源数据</div>';return;}bundleDirectoryGroups.forEach(bundleGroup=>{// 创建bundle分组块
const bundleBlock=document.createElement("div");bundleBlock.className="bundle-group-block";// 转义bundle名称用于CSS选择器
const escapedBundleName=this.escapeSelector(bundleGroup.name);// Bundle分组标题和展开按钮
const bundleHeader=document.createElement("h3");bundleHeader.className="bundle-group-header";bundleHeader.setAttribute("data-target",`bundle-dir-${escapedBundleName}`);bundleHeader.innerHTML=`
                <span class="expand-toggle">▶</span>
                <span class="bundle-title">${bundleGroup.name}</span>
                <span class="bundle-count">${bundleGroup.count||0} 个资源 / ${MemoryHelper.formatBytes(bundleGroup.size||0)}</span>
            `;bundleBlock.appendChild(bundleHeader);// 创建bundle内容区域
const bundleContent=document.createElement("div");bundleContent.className="bundle-content";bundleContent.id=`bundle-dir-${escapedBundleName}`;// 如果刷新前bundle是展开的，刷新后保持展开状态
if(expandedStates.get(bundleContent.id)){bundleContent.classList.add("expanded");const toggle=bundleHeader.querySelector(".expand-toggle");if(toggle)toggle.classList.add("expanded");}// 在bundle内容区域中创建目录分组
const directories=bundleGroup.directories;const sortedDirs=Object.keys(directories).sort((a,b)=>(directories[b].size||0)-(directories[a].size||0));sortedDirs.forEach(dirPath=>{const directoryGroup=directories[dirPath];// 每个目录一个可展开分块
const dirBlock=document.createElement("div");dirBlock.className="resource-type-block";// 转义目录路径用于CSS选择器
const escapedDirPath=this.escapeSelector(dirPath);// 目录标题和展开按钮（支持粘性定位）
const dirHeader=document.createElement("h4");dirHeader.className="resource-type-header sticky-header";dirHeader.setAttribute("data-target",`dir-${escapedBundleName}-${escapedDirPath}`);const displayPath=dirPath==="/"?"根目录":dirPath;dirHeader.innerHTML=`
                    <span class="expand-toggle">▶</span>
                    <span class="category-title">📁 ${displayPath}</span>
                    <span class="category-count">${directoryGroup.count} 个 / ${MemoryHelper.formatBytes(directoryGroup.size)}</span>
                `;dirBlock.appendChild(dirHeader);// 立即检测并调整这个标题的字号
const categoryTitle=dirHeader.querySelector(".category-title");if(categoryTitle){setTimeout(()=>this.adjustSingleTitleOverflow(categoryTitle),0);}// 创建资源详情列表
if(directoryGroup.uuids&&directoryGroup.uuids.length>0){const details=document.createElement("div");details.className="resource-details";details.id=`dir-${escapedBundleName}-${escapedDirPath}`;// 如果刷新前是展开的，刷新后保持展开状态
if(expandedStates.get(details.id)){details.classList.add("expanded");const toggle=dirHeader.querySelector(".expand-toggle");if(toggle)toggle.classList.add("expanded");}// 按UUID获取资源详情并按内存占用排序
const resourceDetails=[];directoryGroup.uuids.forEach(uuid=>{const assetInfo=AssetsHelper.getAssetInfo(uuid);if(assetInfo){const fileName=assetInfo.path?assetInfo.path.split("/").pop():"unknown";// 使用存储的文件扩展名
const fileExtension=assetInfo.extension||"UNKNOWN";resourceDetails.push({name:fileName||"unknown",size:assetInfo.size||0,type:fileExtension,nativeUrl:assetInfo.nativeUrl});}});// 按内存占用排序
resourceDetails.sort((a,b)=>b.size-a.size);resourceDetails.forEach(resource=>{const detailItem=document.createElement("div");detailItem.className="resource-detail-item";detailItem.innerHTML=`
                             <span class="resource-detail-name" title="${resource.name}">`+(resource.nativeUrl?"<a href='"+resource.nativeUrl+"' target='_blank'>":"")+`${resource.name}`+(resource.nativeUrl?"</a>":"")+`</span>
                            <span class="resource-type">[${resource.type}]</span>
                            <span class="resource-detail-size">${MemoryHelper.formatBytes(resource.size)}</span>
                        `;details.appendChild(detailItem);});dirBlock.appendChild(details);}bundleContent.appendChild(dirBlock);});bundleBlock.appendChild(bundleContent);container.appendChild(bundleBlock);});// 为bundle内容区域添加滚动监听，实现粘性标题
this.addStickyHeaderBehavior();}/**
   * 添加粘性标题行为
   */addStickyHeaderBehavior(){const mainContent=this.container.querySelector(".resource-memory-content");if(!mainContent)return;// 移除之前可能绑定的滚动事件监听器
if(this.scrollHandler){mainContent.removeEventListener("scroll",this.scrollHandler);}// 创建新的滚动处理器
this.scrollHandler=this.createStickyScrollHandler();// 绑定滚动事件监听器
mainContent.addEventListener("scroll",this.scrollHandler);// 检测并调整文本溢出的字号
this.adjustTextOverflow();}/**
   * 创建粘性滚动处理器
   */createStickyScrollHandler(){// 使用节流来优化性能
let ticking=false;return()=>{if(!ticking){requestAnimationFrame(()=>{this.updateStickyHeaders();ticking=false;});ticking=true;}};}/**
   * 更新粘性标题状态
   */updateStickyHeaders(){const mainContent=this.container.querySelector(".resource-memory-content");if(!mainContent)return;const containerTop=mainContent.getBoundingClientRect().top;// 获取所有bundle块，按文档顺序处理
const bundleBlocks=mainContent.querySelectorAll(".bundle-group-block");// 重置所有bundle标题的粘性状态
let activeStickyBundleHeader=null;let stickyTopOffset=0;bundleBlocks.forEach(bundleBlock=>{const bundleHeader=bundleBlock.querySelector(".bundle-group-header");const bundleContent=bundleBlock.querySelector(".bundle-content");if(!bundleHeader||!bundleContent)return;// 重置bundle标题状态
bundleHeader.style.top="0px";bundleHeader.classList.remove("is-sticky");// 重置所有资源类型标题的粘性状态
const typeHeaders=bundleBlock.querySelectorAll(".resource-type-header");typeHeaders.forEach(typeHeader=>{typeHeader.style.top="0px";typeHeader.classList.remove("is-sticky");});// 检查bundle是否需要粘性显示
const bundleRect=bundleHeader.getBoundingClientRect();const contentRect=bundleContent.getBoundingClientRect();// 如果bundle标题已经滚动到容器顶部之上，且其内容区域还在可视范围内
if(bundleRect.top<=containerTop&&contentRect.bottom>containerTop){activeStickyBundleHeader=bundleHeader;}});// 只让最下边需要粘性显示的bundle标题显示
if(activeStickyBundleHeader){activeStickyBundleHeader.style.top="0px";activeStickyBundleHeader.classList.add("is-sticky");stickyTopOffset=activeStickyBundleHeader.offsetHeight;// 处理这个bundle下的资源类型标题
const bundleBlock=activeStickyBundleHeader.closest(".bundle-group-block");const bundleContent=bundleBlock===null||bundleBlock===void 0?void 0:bundleBlock.querySelector(".bundle-content");if(bundleBlock&&bundleContent&&bundleContent.classList.contains("expanded")){const typeHeaders=bundleBlock.querySelectorAll(".resource-type-header");// 找到当前bundle中需要粘性显示的资源类型标题
let activeStickyTypeHeader=null;typeHeaders.forEach(typeHeader=>{const typeDetails=typeHeader.nextElementSibling;if(!typeDetails||!typeDetails.classList.contains("expanded"))return;const headerRect=typeHeader.getBoundingClientRect();const detailsRect=typeDetails.getBoundingClientRect();// 如果标题已经滚动到bundle标题下方，且其内容区域还在可视范围内
if(headerRect.top<=containerTop+stickyTopOffset&&detailsRect.bottom>containerTop+stickyTopOffset){activeStickyTypeHeader=typeHeader;}});// 只让需要的资源类型标题变成粘性
if(activeStickyTypeHeader){activeStickyTypeHeader.style.top=`${stickyTopOffset}px`;activeStickyTypeHeader.classList.add("is-sticky");}}}}/**
   * 更新元素内容
   */updateElement(id,content){const element=document.getElementById(id);if(element){element.textContent=content;}}/**
   * 销毁组件
   */destroy(){this.stopMonitoring();// 清理滚动事件监听器
if(this.scrollHandler&&this.container){const mainContent=this.container.querySelector(".resource-memory-content");if(mainContent){mainContent.removeEventListener("scroll",this.scrollHandler);}}if(this.container&&this.container.parentNode){this.container.parentNode.removeChild(this.container);}this.container=null;this.scrollHandler=null;}/**
   * 检测并调整文本溢出的字号
   */adjustTextOverflow(){if(!this.container)return;const categoryTitles=this.container.querySelectorAll(".category-title");categoryTitles.forEach(title=>{this.adjustSingleTitleOverflow(title);});}/**
   * 调整单个标题的文本溢出
   */adjustSingleTitleOverflow(title){// 只处理还没有检测过的标题
if(title.hasAttribute("data-overflow-checked")){return;}// 标记为已检测
title.setAttribute("data-overflow-checked","true");// 检查是否溢出
if(title.scrollWidth>title.clientWidth){title.classList.add("text-overflow");// 如果添加中等字号后仍然溢出，则使用小字号
setTimeout(()=>{if(title.scrollWidth>title.clientWidth){title.classList.remove("text-overflow");title.classList.add("text-overflow-small");}},250);// 等待CSS transition完成
}}/**
   * 转义CSS选择器中的特殊字符
   */escapeSelector(str){return str.replace(/[^\w-]/g,"_");}}/**
 * 右侧抽屉UI组件
 * 在Chrome窗口右侧创建可收起和打开的插件界面
 */class RightDrawerUI{constructor(){this.container=null;this.content=null;this.toggleButton=null;this.resizeHandle=null;this.isOpen=false;this.toggleCallback=null;this.isResizing=false;this.startX=0;this.startWidth=0;this.minWidth=150;this.maxWidth=800;this.defaultWidth=300;}/**
   * 创建右侧抽屉UI
   */create(){this.createContainer();this.createToggleButton();this.createContent();this.createResizeHandle();this.applyStyles();this.bindEvents();this.bindResizeEvents();this.setInitialState();}/**
   * 创建主容器
   */createContainer(){this.container=document.createElement("div");this.container.id="node-inspector-right-drawer";this.container.style.width=`${this.defaultWidth}px`;document.body.appendChild(this.container);}/**
   * 创建切换按钮
   */createToggleButton(){this.toggleButton=document.createElement("div");this.toggleButton.id="node-inspector-right-toggle";this.toggleButton.innerHTML="内<br>存<br>监<br>控";this.toggleButton.title="切换资源监控器";this.container.appendChild(this.toggleButton);}/**
   * 创建内容区域
   */createContent(){this.content=document.createElement("div");this.content.id="node-inspector-right-content";this.container.appendChild(this.content);}/**
   * 创建拖拽调整手柄
   */createResizeHandle(){this.resizeHandle=document.createElement("div");this.resizeHandle.id="node-inspector-right-resize-handle";this.container.appendChild(this.resizeHandle);}/**
   * 应用样式
   */applyStyles(){const style=document.createElement("style");style.textContent=`
            #node-inspector-right-drawer {
                position: fixed;
                top: 0;
                right: 0;
                height: 100vh;
                background: #2d2d2d;
                border-left: 1px solid #444;
                z-index: 10000;
                transition: transform 0.3s ease;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 12px;
                color: #fff;
                box-shadow: -2px 0 10px rgba(0,0,0,0.3);
                min-width: ${this.minWidth}px;
                max-width: ${this.maxWidth}px;
            }

            #node-inspector-right-drawer.closed {
                transform: translateX(100%);
            }

            #node-inspector-right-toggle {
                position: absolute;
                top: calc(50vh + 100px);
                left: -30px;
                transform: translateY(-50%);
                width: 30px;
                height: 160px;
                background: #2d2d2d;
                border: 1px solid #444;
                border-right: none;
                border-radius: 5px 0 0 5px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-size: 14px;
                color: #fff;
                user-select: none;
                transition: background 0.2s ease;
            }

            #node-inspector-right-toggle:hover {
                background: #3d3d3d;
            }

            #node-inspector-right-resize-handle {
                position: absolute;
                top: 0;
                left: 0;
                width: 4px;
                height: 100%;
                background: transparent;
                cursor: col-resize;
                z-index: 1001;
                transition: background 0.2s ease;
            }

            #node-inspector-right-resize-handle:hover {
                background: #0078d4;
            }

            #node-inspector-right-content {
                width: 100%;
                height: 100%;
                overflow: hidden;
                padding-left: 4px;
                padding-right: 12px;
                box-sizing: border-box;
                background: transparent;
            }

            .node-inspector-right-panel {
                background: #2d2d2d;
                border-bottom: 1px solid #444;
                overflow: auto;
            }

            .node-inspector-right-panel h3 {
                margin: 0;
                padding: 10px;
                background: #1e1e1e;
                border-bottom: 1px solid #444;
                font-size: 14px;
                font-weight: bold;
            }

            /* 拖拽时的样式 */
            body.resizing-right-drawer {
                user-select: none;
                cursor: col-resize !important;
            }

            body.resizing-right-drawer * {
                pointer-events: none !important;
            }

            body.resizing-right-drawer #node-inspector-right-drawer {
                pointer-events: auto !important;
            }

            body.resizing-right-drawer #node-inspector-right-resize-handle {
                pointer-events: auto !important;
            }

            /* 防止iframe或canvas干扰 */
            body.resizing-right-drawer iframe,
            body.resizing-right-drawer canvas,
            body.resizing-right-drawer embed,
            body.resizing-right-drawer object {
                pointer-events: none !important;
            }
        `;document.head.appendChild(style);}/**
   * 绑定事件
   */bindEvents(){if(this.toggleButton){this.toggleButton.addEventListener("click",()=>{this.toggle();});}}/**
   * 切换抽屉状态
   */toggle(){this.isOpen=!this.isOpen;this.updateUI();if(this.toggleCallback){this.toggleCallback(this.isOpen);}}/**
   * 设置切换回调
   */onToggle(callback){this.toggleCallback=callback;}/**
   * 获取当前状态
   */isDrawerOpen(){return this.isOpen;}/**
   * 获取内容容器
   */getContentContainer(){return this.content;}/**
   * 销毁组件
   */destroy(){if(this.container){document.body.removeChild(this.container);this.container=null;}this.content=null;this.toggleButton=null;this.resizeHandle=null;}/**
   * 设置初始状态
   */setInitialState(){this.isOpen=false;this.updateUI();}/**
   * 更新UI状态
   */updateUI(){if(this.container){if(this.isOpen){this.container.classList.remove("closed");}else{this.container.classList.add("closed");}}}/**
   * 绑定拖拽调整事件
   */bindResizeEvents(){if(!this.resizeHandle)return;this.resizeHandle.addEventListener("mousedown",e=>{this.startResize(e);});// 使用捕获阶段监听，确保在所有其他元素之前捕获事件
document.addEventListener("mousemove",e=>{this.handleResize(e);},{capture:true});document.addEventListener("mouseup",e=>{this.stopResize();},{capture:true});// 添加鼠标离开窗口的处理
document.addEventListener("mouseleave",()=>{this.stopResize();});// 防止在iframe中丢失事件
window.addEventListener("blur",()=>{this.stopResize();});}/**
   * 开始拖拽调整
   */startResize(e){this.isResizing=true;this.startX=e.clientX;this.startWidth=this.container.clientWidth;document.body.classList.add("resizing-right-drawer");this.resizeHandle.style.background="#0078d4";// 阻止默认行为和事件冒泡
e.preventDefault();e.stopPropagation();e.stopImmediatePropagation();}/**
   * 处理拖拽调整
   */handleResize(e){if(!this.isResizing)return;const deltaX=this.startX-e.clientX;const newWidth=Math.max(this.minWidth,Math.min(this.maxWidth,this.startWidth+deltaX));this.container.style.width=`${newWidth}px`;// 阻止事件传播
e.preventDefault();e.stopPropagation();e.stopImmediatePropagation();}/**
   * 停止拖拽调整
   */stopResize(){if(!this.isResizing)return;this.isResizing=false;document.body.classList.remove("resizing-right-drawer");this.resizeHandle.style.background="";}/**
   * 重置宽度
   */resetWidth(){if(this.container){this.container.style.width=`${this.defaultWidth}px`;}}}/**
 * 节点树面板
 * 显示Creator中所有节点的树状结构
 */class NodeTreePanel{constructor(){this.container=null;this.treeContainer=null;this.nodeSelectCallback=null;this.expandedNodes=new Set();this.selectedNodeId=null;this.lastNodesHash="";// 添加节点数据哈希缓存
}/**
   * 创建节点树面板
   */create(parent){this.createContainer(parent);this.applyStyles();}/**
   * 创建容器
   */createContainer(parent){this.container=document.createElement("div");this.container.className="node-inspector-panel node-tree";const header=document.createElement("h3");header.textContent="节点树";this.container.appendChild(header);this.treeContainer=document.createElement("div");this.treeContainer.className="tree-container";this.container.appendChild(this.treeContainer);parent.appendChild(this.container);}/**
   * 应用样式
   */applyStyles(){const style=document.createElement("style");style.textContent=`
            .tree-container {
                padding: 5px;
            }

            .tree-node {
                margin: 2px 0;
                user-select: none;
            }

            .tree-node-header {
                display: flex;
                align-items: center;
                padding: 4px 8px;
                cursor: pointer;
                border-radius: 3px;
                transition: background 0.2s ease;
            }

            .tree-node-header:hover {
                background: #3d3d3d;
            }

            .tree-node-header.selected {
                background: #0078d4;
            }

            .tree-node-header.inactive {
                opacity: 0.5;
            }

            .tree-node-expand {
                width: 16px;
                height: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 4px;
                font-size: 10px;
                color: #ccc;
                cursor: pointer;
            }

            .tree-node-expand.empty {
                visibility: hidden;
            }

            .tree-node-name {
                flex: 1;
                font-size: 12px;
                color: #fff;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .tree-node-children {
                margin-left: 20px;
                border-left: 1px solid #444;
                padding-left: 8px;
            }

            .tree-node-children.collapsed {
                display: none;
            }

            .tree-empty {
                padding: 20px;
                text-align: center;
                color: #888;
                font-style: italic;
            }
        `;document.head.appendChild(style);}/**
   * 更新节点树
   */updateTree(nodes){if(!this.treeContainer)return;// 计算节点数据的哈希值，用于检测变化
const currentHash=this.calculateNodesHash(nodes);// 如果数据没有变化，则不重新渲染
if(currentHash===this.lastNodesHash){return;}this.lastNodesHash=currentHash;this.treeContainer.innerHTML="";if(nodes.length===0){const emptyDiv=document.createElement("div");emptyDiv.className="tree-empty";emptyDiv.textContent="未找到节点";this.treeContainer.appendChild(emptyDiv);return;}nodes.forEach(node=>{const nodeElement=this.createNodeElement(node,0);if(this.treeContainer){this.treeContainer.appendChild(nodeElement);}});}/**
   * 计算节点数据的哈希值
   */calculateNodesHash(nodes){const nodeInfo=this.extractNodeInfo(nodes);return JSON.stringify(nodeInfo);}/**
   * 提取节点关键信息用于比较
   */extractNodeInfo(nodes){return nodes.map(node=>({id:node.id,name:node.name,active:node.active,children:this.extractNodeInfo(node.children)}));}/**
   * 创建节点元素
   */createNodeElement(nodeData,depth){const nodeDiv=document.createElement("div");nodeDiv.className="tree-node";nodeDiv.dataset.nodeId=nodeData.id;// 创建节点头部
const headerDiv=document.createElement("div");headerDiv.className="tree-node-header";if(!nodeData.active){headerDiv.classList.add("inactive");}if(this.selectedNodeId===nodeData.id){headerDiv.classList.add("selected");}// 展开/收起按钮
const expandDiv=document.createElement("div");expandDiv.className="tree-node-expand";if(nodeData.children.length>0){const isExpanded=this.expandedNodes.has(nodeData.id);expandDiv.textContent=isExpanded?"▼":"▶";expandDiv.addEventListener("click",e=>{e.stopPropagation();this.toggleNodeExpansion(nodeData.id);});}else{expandDiv.classList.add("empty");}// 节点名称
const nameDiv=document.createElement("div");nameDiv.className="tree-node-name";nameDiv.textContent=nodeData.name;headerDiv.appendChild(expandDiv);headerDiv.appendChild(nameDiv);// 点击选择节点
headerDiv.addEventListener("click",()=>{this.selectNode(nodeData);});nodeDiv.appendChild(headerDiv);// 创建子节点容器
if(nodeData.children.length>0){const childrenDiv=document.createElement("div");childrenDiv.className="tree-node-children";if(!this.expandedNodes.has(nodeData.id)){childrenDiv.classList.add("collapsed");}nodeData.children.forEach(child=>{const childElement=this.createNodeElement(child,depth+1);childrenDiv.appendChild(childElement);});nodeDiv.appendChild(childrenDiv);}return nodeDiv;}/**
   * 切换节点展开状态
   */toggleNodeExpansion(nodeId){var _a;if(this.expandedNodes.has(nodeId)){this.expandedNodes.delete(nodeId);}else{this.expandedNodes.add(nodeId);}// 更新UI
const nodeElement=(_a=this.treeContainer)===null||_a===void 0?void 0:_a.querySelector(`[data-node-id="${nodeId}"]`);if(nodeElement){const expandButton=nodeElement.querySelector(".tree-node-expand");const childrenContainer=nodeElement.querySelector(".tree-node-children");if(expandButton&&childrenContainer){const isExpanded=this.expandedNodes.has(nodeId);expandButton.textContent=isExpanded?"▼":"▶";if(isExpanded){childrenContainer.classList.remove("collapsed");}else{childrenContainer.classList.add("collapsed");}}}}/**
   * 选择节点
   */selectNode(nodeData){var _a,_b;// 更新选中状态
if(this.selectedNodeId){const prevSelected=(_a=this.treeContainer)===null||_a===void 0?void 0:_a.querySelector(`[data-node-id="${this.selectedNodeId}"] .tree-node-header`);if(prevSelected){prevSelected.classList.remove("selected");}}this.selectedNodeId=nodeData.id;const currentSelected=(_b=this.treeContainer)===null||_b===void 0?void 0:_b.querySelector(`[data-node-id="${nodeData.id}"] .tree-node-header`);if(currentSelected){currentSelected.classList.add("selected");}// 触发回调
if(this.nodeSelectCallback){this.nodeSelectCallback(nodeData);}}/**
   * 设置节点选择回调
   */onNodeSelect(callback){this.nodeSelectCallback=callback;}}/**
 * 属性面板
 * 显示和编辑选中节点的属性
 */class PropertyPanel{constructor(){this.container=null;this.propertiesContainer=null;this.propertyChangeCallback=null;this.refreshNodeCallback=null;this.showTooltipCallback=null;this.currentNode=null;this.debugButton=null;this.refreshButton=null;this.header=null;}/**
   * 创建属性面板
   */create(parent){this.createContainer(parent);this.applyStyles();}/**
   * 创建容器
   */createContainer(parent){this.container=document.createElement("div");this.container.className="node-inspector-panel property-panel";this.header=document.createElement("h3");this.header.className="property-panel-header";this.header.textContent="节点属性";// 创建按钮容器
const buttonContainer=document.createElement("div");buttonContainer.className="property-panel-buttons";// 创建手动刷新按钮
this.refreshButton=document.createElement("button");this.refreshButton.className="refresh-button";this.refreshButton.textContent="刷新";this.refreshButton.title="重新读取选中节点的属性";this.refreshButton.style.display="none";// 默认隐藏
this.refreshButton.addEventListener("click",()=>{this.refreshNodeProperties();});buttonContainer.appendChild(this.refreshButton);// 创建调试输出按钮
this.debugButton=document.createElement("button");this.debugButton.className="debug-output-button";this.debugButton.textContent="输出";this.debugButton.title="输出选中节点的路径和属性";this.debugButton.disabled=true;this.debugButton.addEventListener("click",()=>{this.outputNodeDebugInfo();});buttonContainer.appendChild(this.debugButton);this.header.appendChild(buttonContainer);this.container.appendChild(this.header);this.propertiesContainer=document.createElement("div");this.propertiesContainer.className="properties-container";this.container.appendChild(this.propertiesContainer);parent.appendChild(this.container);this.showEmptyState();}/**
   * 应用样式
   */applyStyles(){const style=document.createElement("style");style.textContent=`
            .property-panel-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                position: relative;
            }

            .property-panel-buttons {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: 4px;
                height: 22px;
            }

            .refresh-button, .debug-output-button {
                /* 重置所有默认样式 */
                margin: 0;
                padding: 0;
                border: none;
                outline: none;
                background: none;
                font-family: inherit;
                font-size: 11px;
                line-height: 1;
                text-align: center;
                color: white;
                cursor: pointer;
                transition: background-color 0.2s;
                
                /* 布局属性 */
                display: flex;
                align-items: center;
                justify-content: center;
                box-sizing: border-box;
                height: 22px;
                border-radius: 3px;
                
                /* 去除浏览器默认样式 */
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                vertical-align: baseline;
            }

            .refresh-button {
                background: #28a745;
                width: 60px;
            }

            .refresh-button:hover {
                background: #218838;
            }

            .debug-output-button {
                background: #0078d4;
                width: 30px;
            }

            .debug-output-button:hover:not(:disabled) {
                background: #106ebe;
            }

            .debug-output-button:disabled {
                background: #444;
                cursor: not-allowed;
                color: #888;
            }

            .properties-container {
                padding: 8px;
                overflow: visible;
            }

            .property-row {
                display: flex;
                align-items: center;
                margin-bottom: 6px;
                padding: 2px 0;
            }

            .property-label {
                flex: 0 0 60px;
                font-size: 11px;
                color: #ccc;
                margin-right: 6px;
                text-align: right;
            }

            .property-input {
                flex: 1;
                background: #1e1e1e;
                border: 1px solid #444;
                border-radius: 3px;
                padding: 3px 5px;
                color: #fff;
                font-size: 11px;
                font-family: monospace;
            }

            .property-input:focus {
                outline: none;
                border-color: #0078d4;
                box-shadow: 0 0 0 1px #0078d4;
            }

            .property-input[type="checkbox"] {
                flex: none;
                width: 16px;
                height: 16px;
                margin: 0;
            }

            .property-input[type="color"] {
                flex: none;
                width: 40px;
                height: 22px;
                padding: 0;
                border: none;
                border-radius: 3px;
                cursor: pointer;
            }

            .property-empty {
                padding: 15px;
                text-align: center;
                color: #888;
                font-style: italic;
                font-size: 12px;
            }

            .color-input-group {
                display: flex;
                align-items: center;
                gap: 3px;
            }

            .color-input-group input[type="number"] {
                width: 45px;
            }
        `;document.head.appendChild(style);}/**
   * 显示节点属性
   */displayNodeProperties(nodeData){if(!this.propertiesContainer)return;this.currentNode=nodeData;this.propertiesContainer.innerHTML="";// 启用调试按钮
if(this.debugButton){this.debugButton.disabled=false;}// 显示刷新按钮
if(this.refreshButton){this.refreshButton.style.display="inline-block";}// 垂直平铺显示所有属性
const properties=[{key:"active",label:"激活",value:nodeData.active,type:"boolean"},{key:"name",label:"名称",value:nodeData.name,type:"string"},{key:"x",label:"X坐标",value:nodeData.position.x,type:"number"},{key:"y",label:"Y坐标",value:nodeData.position.y,type:"number"},{key:"anchorX",label:"锚点X",value:nodeData.anchor.x,type:"number"},{key:"anchorY",label:"锚点Y",value:nodeData.anchor.y,type:"number"},{key:"width",label:"宽度",value:nodeData.size.width,type:"number"},{key:"height",label:"高度",value:nodeData.size.height,type:"number"},{key:"scaleX",label:"X缩放",value:nodeData.scale.x,type:"number"},{key:"scaleY",label:"Y缩放",value:nodeData.scale.y,type:"number"},{key:"rotation",label:"旋转",value:nodeData.rotation,type:"number"},{key:"opacity",label:"透明度",value:nodeData.color.a,type:"number"}];// 创建所有基本属性行
properties.forEach(prop=>{const rowDiv=this.createPropertyRow(prop.key,prop.label,prop.value,prop.type);this.propertiesContainer.appendChild(rowDiv);});// 创建颜色属性行
this.createColorPropertyRows(nodeData.color);}/**
   * 刷新节点属性
   */refreshNodeProperties(){if(!this.currentNode){console.warn("没有选中的节点");return;}if(!this.refreshNodeCallback){console.warn("没有设置刷新回调函数");return;}try{// 通过回调函数获取最新的节点数据
const refreshedNodeData=this.refreshNodeCallback(this.currentNode);// 更新currentNode引用
this.currentNode=refreshedNodeData;// 重新显示节点属性
this.displayNodeProperties(refreshedNodeData);console.log("节点属性已刷新");if(this.showTooltipCallback&&this.refreshButton){this.showTooltipCallback("刷新完成",this.refreshButton);}}catch(error){console.error("刷新节点属性失败:",error);}}/**
   * 输出节点调试信息
   */outputNodeDebugInfo(){if(!this.currentNode){console.warn("没有选中的节点");return;}const nodePath=this.getNodePath(this.currentNode);const nodeProperties=this.getNodeProperties(this.currentNode);console.log("=== 节点调试信息 ===");console.log("节点路径:",nodePath);console.log("节点属性:",nodeProperties);console.log("原始节点对象:",this.currentNode);console.log("==================");if(this.showTooltipCallback&&this.debugButton){this.showTooltipCallback("节点信息已输出到控制台",this.debugButton);}}/**
   * 获取节点路径（兼容Creator 2.x和3.x）
   */getNodePath(nodeData){const pathParts=[];let currentNode=nodeData;// 构建路径
pathParts.unshift(currentNode.name);// 通过ccNode获取父级路径
if(currentNode.ccNode){try{let ccNode=currentNode.ccNode.parent;let depth=0;const maxDepth=20;// 防止无限循环
while(ccNode&&depth<maxDepth){// 兼容不同版本的节点名称获取
const nodeName=this.getNodeName(ccNode);pathParts.unshift(nodeName);// 获取父级节点
ccNode=ccNode.parent;depth++;}}catch(error){console.warn("获取节点路径时出错:",error);pathParts.unshift("[路径获取失败]");}}return pathParts.join("/");}/**
   * 获取节点名称（兼容Creator 2.x和3.x）
   */getNodeName(ccNode){var _a,_b;if(!ccNode)return"UnknownNode";// 尝试多种方式获取节点名称
return ccNode.name||ccNode._name||((_a=ccNode.node)===null||_a===void 0?void 0:_a.name)||`Node_${((_b=ccNode.uuid)===null||_b===void 0?void 0:_b.substring(0,8))||"Unknown"}`;}/**
   * 获取节点属性对象（兼容Creator 2.x和3.x）
   */getNodeProperties(nodeData){const properties={基本信息:{ID:nodeData.id,名称:nodeData.name,激活状态:nodeData.active},位置信息:{坐标:`(${nodeData.position.x}, ${nodeData.position.y})`,锚点:`(${nodeData.anchor.x}, ${nodeData.anchor.y})`},尺寸信息:{大小:`${nodeData.size.width} x ${nodeData.size.height}`,缩放:`(${nodeData.scale.x}, ${nodeData.scale.y})`},变换信息:{旋转角度:`${nodeData.rotation}°`},外观信息:{颜色:`RGB(${nodeData.color.r}, ${nodeData.color.g}, ${nodeData.color.b})`,透明度:nodeData.color.a},层级信息:{子节点数量:nodeData.children.length,子节点:nodeData.children.map(child=>child.name)}};// 添加Creator版本相关信息
if(nodeData.ccNode){properties.Creator信息=this.getCreatorSpecificInfo(nodeData.ccNode);}return properties;}/**
   * 获取Creator版本特定信息
   */getCreatorSpecificInfo(ccNode){var _a,_b,_c,_d,_e;const info={版本检测:this.detectCreatorVersion(),节点类型:((_a=ccNode.constructor)===null||_a===void 0?void 0:_a.name)||"Unknown"};try{// Creator 2.x特有属性
if(ccNode.color!==undefined){info.Creator版本="2.x";info.原始属性={uuid:ccNode.uuid,x:ccNode.x,y:ccNode.y,width:ccNode.width,height:ccNode.height,scaleX:ccNode.scaleX,scaleY:ccNode.scaleY,angle:ccNode.angle,opacity:ccNode.opacity,anchorX:ccNode.anchorX,anchorY:ccNode.anchorY};}// Creator 3.x特有属性
else{info.Creator版本="3.x+";const transform=(_b=ccNode.getComponent)===null||_b===void 0?void 0:_b.call(ccNode,"cc.UITransform");const renderer=((_c=ccNode.getComponent)===null||_c===void 0?void 0:_c.call(ccNode,"cc.UIRenderer"))||((_d=ccNode.getComponent)===null||_d===void 0?void 0:_d.call(ccNode,"cc.Renderable2D"));info.原始属性={uuid:ccNode.uuid,position:ccNode.position,scale:ccNode.scale,angle:ccNode.angle};if(transform){info.UITransform组件={width:transform.width,height:transform.height,anchorX:transform.anchorX,anchorY:transform.anchorY};}if(renderer){info.渲染组件={类型:((_e=renderer.constructor)===null||_e===void 0?void 0:_e.name)||"Unknown",颜色:renderer.color};}}// 通用信息
info.父节点=ccNode.parent?this.getNodeName(ccNode.parent):"无";info.组件列表=this.getNodeComponents(ccNode);}catch(error){info.错误=`获取Creator信息时出错: ${error instanceof Error?error.message:String(error)}`;}return info;}/**
   * 检测Creator版本
   */detectCreatorVersion(){try{const cc=window.cc;const version=(cc===null||cc===void 0?void 0:cc.ENGINE_VERSION)||"未知";if(version.startsWith("2."))return`Creator 2.x (${version})`;if(version.startsWith("3."))return`Creator 3.x (${version})`;if(version.startsWith("4."))return`Creator 4.x (${version})`;return`未知版本 (${version})`;}catch(error){return"版本检测失败";}}/**
   * 获取节点组件列表
   */getNodeComponents(ccNode){try{if(ccNode._components&&Array.isArray(ccNode._components)){return ccNode._components.map(comp=>{var _a;return((_a=comp.constructor)===null||_a===void 0?void 0:_a.name)||comp.__classname__||"Unknown";});}return["组件信息不可用"];}catch(error){return["获取组件失败"];}}/**
   * 创建颜色属性行
   */createColorPropertyRows(color){if(!this.propertiesContainer)return;// RGB输入行
const rgbRowDiv=document.createElement("div");rgbRowDiv.className="property-row";const rgbLabelDiv=document.createElement("div");rgbLabelDiv.className="property-label";rgbLabelDiv.textContent="RGB";rgbRowDiv.appendChild(rgbLabelDiv);const colorInputGroup=document.createElement("div");colorInputGroup.className="color-input-group";// R值
const rInput=document.createElement("input");rInput.type="number";rInput.className="property-input";rInput.value=color.r.toString();rInput.min="0";rInput.max="255";rInput.addEventListener("change",()=>{let value=Math.max(0,Math.min(255,Math.round(parseInt(rInput.value)||0)));rInput.value=value.toString();this.handlePropertyChange("color.r",value);});// G值
const gInput=document.createElement("input");gInput.type="number";gInput.className="property-input";gInput.value=color.g.toString();gInput.min="0";gInput.max="255";gInput.addEventListener("change",()=>{let value=Math.max(0,Math.min(255,Math.round(parseInt(gInput.value)||0)));gInput.value=value.toString();this.handlePropertyChange("color.g",value);});// B值
const bInput=document.createElement("input");bInput.type="number";bInput.className="property-input";bInput.value=color.b.toString();bInput.min="0";bInput.max="255";bInput.addEventListener("change",()=>{let value=Math.max(0,Math.min(255,Math.round(parseInt(bInput.value)||0)));bInput.value=value.toString();this.handlePropertyChange("color.b",value);});// 颜色选择器
const colorPicker=document.createElement("input");colorPicker.type="color";colorPicker.className="property-input";colorPicker.value=this.rgbToHex(color.r,color.g,color.b);colorPicker.addEventListener("change",()=>{const rgb=this.hexToRgb(colorPicker.value);if(rgb){// 确保RGB值都在0-255范围内
const r=Math.max(0,Math.min(255,Math.round(rgb.r)));const g=Math.max(0,Math.min(255,Math.round(rgb.g)));const b=Math.max(0,Math.min(255,Math.round(rgb.b)));rInput.value=r.toString();gInput.value=g.toString();bInput.value=b.toString();this.handlePropertyChange("color.r",r);this.handlePropertyChange("color.g",g);this.handlePropertyChange("color.b",b);}});colorInputGroup.appendChild(rInput);colorInputGroup.appendChild(gInput);colorInputGroup.appendChild(bInput);colorInputGroup.appendChild(colorPicker);rgbRowDiv.appendChild(colorInputGroup);this.propertiesContainer.appendChild(rgbRowDiv);}/**
   * 创建属性行
   */createPropertyRow(key,label,value,type){const rowDiv=document.createElement("div");rowDiv.className="property-row";const labelDiv=document.createElement("div");labelDiv.className="property-label";labelDiv.textContent=label;rowDiv.appendChild(labelDiv);const input=document.createElement("input");input.className="property-input";switch(type){case"boolean":input.type="checkbox";input.checked=Boolean(value);input.addEventListener("change",()=>{this.handlePropertyChange(key,input.checked);});break;case"number":input.type="number";input.value=value.toString();input.step=key.includes("scale")?"0.1":"1";// 为透明度设置范围限制
if(key==="opacity"){input.min="0";input.max="255";}input.addEventListener("change",()=>{let newValue=parseFloat(input.value);// 透明度值限制在0-255之间
if(key==="opacity"){newValue=Math.max(0,Math.min(255,Math.round(newValue)));input.value=newValue.toString();}this.handlePropertyChange(key,newValue);});break;case"string":input.type="text";input.value=value.toString();input.addEventListener("change",()=>{this.handlePropertyChange(key,input.value);});break;}rowDiv.appendChild(input);return rowDiv;}/**
   * 处理属性变化
   */handlePropertyChange(property,value){if(this.propertyChangeCallback){this.propertyChangeCallback(property,value);}}/**
   * 显示空状态
   */showEmptyState(){if(!this.propertiesContainer)return;this.currentNode=null;// 禁用调试按钮
if(this.debugButton){this.debugButton.disabled=true;}// 隐藏刷新按钮
if(this.refreshButton){this.refreshButton.style.display="none";}this.propertiesContainer.innerHTML="";const emptyDiv=document.createElement("div");emptyDiv.className="property-empty";emptyDiv.textContent="请选择一个节点";this.propertiesContainer.appendChild(emptyDiv);}/**
   * RGB转十六进制
   */rgbToHex(r,g,b){return"#"+((1<<24)+(r<<16)+(g<<8)+b).toString(16).slice(1);}/**
   * 十六进制转RGB
   */hexToRgb(hex){const result=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);return result?{r:parseInt(result[1],16),g:parseInt(result[2],16),b:parseInt(result[3],16)}:null;}/**
   * 设置属性变化回调
   */onPropertyChange(callback){this.propertyChangeCallback=callback;}/**
   * 设置节点刷新回调
   */onRefreshNode(callback){this.refreshNodeCallback=callback;}/**
   * 设置显示工具提示的回调
   */onShowTooltip(callback){this.showTooltipCallback=callback;}}/**
 * 节点计算器抽象基类
 * 定义了节点数据转换、属性更新、坐标计算等通用接口
 */class BaseNodeCalculator{/**
   * 递归处理子节点 - 通用方法
   */processChildren(ccNode){const children=[];if(ccNode.children&&ccNode.children.length>0){ccNode.children.forEach(child=>{children.push(this.convertCCNodeToNodeData(child));});}return children;}/**
   * 生成节点ID - 通用方法
   */generateNodeId(ccNode){return ccNode.uuid||ccNode._id||Math.random().toString(36);}/**
   * 获取节点名称 - 通用方法
   */getNodeName(ccNode){return ccNode.name||"未命名节点";}/**
   * 获取节点激活状态 - 通用方法
   */getNodeActive(ccNode){return ccNode.active!==undefined?ccNode.active:true;}/**
   * 创建DOM高亮层 - 通用方法
   */createDOMHighlight(rect){try{const existingHighlight=document.getElementById("node-inspector-highlight");if(existingHighlight){existingHighlight.remove();}const highlight=document.createElement("div");highlight.id="node-inspector-highlight";highlight.style.cssText=`
                position: fixed;
                left: ${rect.x}px;
                top: ${rect.y}px;
                width: ${rect.width}px;
                height: ${rect.height}px;
                border: 4px solid #ffff00;
                background: rgba(255, 255, 0, 0.65);
                pointer-events: none;
                z-index: 999999;
                box-sizing: border-box;
                border-radius: 4px;
                animation: nodeInspectorHighlightBlink 1.5s ease-in-out forwards;
            `;this.ensureHighlightStyles();document.body.appendChild(highlight);setTimeout(()=>{if(highlight&&highlight.parentNode){highlight.remove();}},1500);}catch(error){console.error("创建DOM高亮层失败:",error);}}/**
   * 确保高亮样式表存在 - 通用方法
   */ensureHighlightStyles(){const styleId="node-inspector-highlight-styles";if(!document.getElementById(styleId)){const style=document.createElement("style");style.id=styleId;style.textContent=`
                @keyframes nodeInspectorHighlightBlink {
                    0% { 
                        opacity: 1; 
                        transform: scale(1);
                        box-shadow: 0 0 20px rgba(255, 255, 0, 0.8);
                    }
                    25% { 
                        opacity: 0.4; 
                        transform: scale(1.02);
                        box-shadow: 0 0 30px rgba(255, 255, 0, 0.6);
                    }
                    50% { 
                        opacity: 1; 
                        transform: scale(1);
                        box-shadow: 0 0 20px rgba(255, 255, 0, 0.8);
                    }
                    75% { 
                        opacity: 0.4; 
                        transform: scale(1.02);
                        box-shadow: 0 0 30px rgba(255, 255, 0, 0.6);
                    }
                    100% { 
                        opacity: 0; 
                        transform: scale(1);
                        box-shadow: 0 0 10px rgba(255, 255, 0, 0.3);
                    }
                }
            `;document.head.appendChild(style);}}}/**
 * Creator 2.x 节点计算器
 * 处理Creator 2.x版本的节点数据转换、属性更新、坐标计算等
 */class NodeCalculator2x extends BaseNodeCalculator{constructor(){super();// 获取Canvas在页面中的位置和尺寸
// 获取GameCanvas元素
console.log("*********** 项目设置 ***********");console.log("creator版本",(cc===null||cc===void 0?void 0:cc.ENGINE_VERSION)||"");const gameCanvas=cc.game.canvas;if(!gameCanvas){console.log("❌ 未找到GameCanvas元素");return;}const canvasRect=gameCanvas.getBoundingClientRect();const designSize=cc.view.getDesignResolutionSize();let ratio=cc.view.getDevicePixelRatio();console.log("dom画布rect",canvasRect.x,canvasRect.y,canvasRect.width,canvasRect.height);console.log("设计尺寸",designSize.width,designSize.height);console.log("像素比",ratio);const canvasComponent=cc.Canvas.instance;if(canvasComponent){// 适配规则
if(canvasComponent.fitWidth&&canvasComponent.fitHeight){console.log("适配策略: 宽高适配");}else if(canvasComponent.fitHeight&&!canvasComponent.fitWidth){console.log("适配策略: 高度适配");}else if(!canvasComponent.fitHeight&&canvasComponent.fitWidth){console.log("适配策略: 宽度适配");}else{console.log("适配策略: 无适配");}}console.log("********************************");}/**
   * 将Creator 2.x节点转换为NodeData
   */convertCCNodeToNodeData(ccNode){const nodeData={id:this.generateNodeId(ccNode),name:this.getNodeName(ccNode),active:this.getNodeActive(ccNode),position:{x:ccNode.x||0,y:ccNode.y||0},anchor:{x:ccNode.anchorX||0.5,y:ccNode.anchorY||0.5},size:{width:ccNode.width||0,height:ccNode.height||0},scale:{x:ccNode.scaleX||1,y:ccNode.scaleY||1},rotation:ccNode.angle||0,color:{r:ccNode.color.r||255,g:ccNode.color.g||255,b:ccNode.color.b||255,a:ccNode.opacity||255},children:this.processChildren(ccNode),ccNode:ccNode// 保存原始节点引用
};return nodeData;}/**
   * 更新Creator 2.x节点属性
   */updateNodeProperty(nodeData,property,value){const ccNode=nodeData.ccNode;if(!ccNode)return;try{switch(property){case"active":ccNode.active=Boolean(value);break;case"x":nodeData.position.x=Number(value);ccNode.setPosition(Number(value),nodeData.position.y);break;case"y":nodeData.position.y=Number(value);ccNode.setPosition(nodeData.position.x,Number(value));break;case"anchorX":nodeData.anchor.x=Number(value);ccNode.setAnchorPoint(Number(value),nodeData.anchor.y);break;case"anchorY":nodeData.anchor.y=Number(value);ccNode.setAnchorPoint(nodeData.anchor.x,Number(value));break;case"width":nodeData.size.width=Number(value);ccNode.setContentSize(Number(value),nodeData.size.height);break;case"height":nodeData.size.height=Number(value);ccNode.setContentSize(nodeData.size.width,Number(value));break;case"scaleX":nodeData.scale.x=Number(value);ccNode.setScale(Number(value),nodeData.scale.y);break;case"scaleY":nodeData.scale.y=Number(value);ccNode.setScale(nodeData.scale.x,Number(value));break;case"rotation":ccNode.angle=Number(value);break;case"opacity":ccNode.opacity=Number(value);break;case"color.r":nodeData.color.r=Number(value);ccNode.color=nodeData.color;break;case"color.g":nodeData.color.g=Number(value);ccNode.color=nodeData.color;break;case"color.b":nodeData.color.b=Number(value);ccNode.color=nodeData.color;break;}}catch(error){console.error("更新Creator 2.x节点属性失败:",error);}}/**
   * 获取Creator 2.x节点在屏幕上的矩形区域
   * 直接基于GameCanvas进行计算
   */getNodeScreenRect(ccNode){try{// 1. 获取节点所用的摄像机
let camera=cc.Camera.findCamera(ccNode)||cc.Camera.main;if(!camera){console.error("❌ 未找到节点专属摄像机");return null;}// 2. 获取节点在creator屏幕坐标系中的位置
let aabb=this.getNodeScreenAABB(ccNode,camera);// 3. 转换到DOM坐标系中
const leftTop=this.getDomPosition(aabb.lt);const rirghtBottom=this.getDomPosition(aabb.rb);let width=Math.abs(leftTop.x-rirghtBottom.x);let height=Math.abs(leftTop.y-rirghtBottom.y);let x=leftTop.x;let y=Math.min(leftTop.y,rirghtBottom.y);return{x,y,width,height};}catch(error){console.error("获取Creator 2.x节点屏幕位置失败:",error);return null;}}getNodeScreenAABB(ccNode,camera){// 获取节点的包围盒
let rect=ccNode.getBoundingBoxToWorld();// 获取节点相机
return{lt:camera.getWorldToScreenPoint(cc.v2(rect.x,rect.y+rect.height)),rb:camera.getWorldToScreenPoint(cc.v2(rect.x+rect.width,rect.y))};}/** creator屏幕坐标系转换为dom坐标系 */getDomPosition(pos){try{// 获取GameCanvas元素
const gameCanvas=cc.game.canvas;if(!gameCanvas){Tool.log("❌ 未找到GameCanvas元素");return{x:0,y:0};}// 获取Canvas在页面中的位置和尺寸
const canvasRect=gameCanvas.getBoundingClientRect();const designSize=cc.view.getDesignResolutionSize();let ratio=cc.view.getDevicePixelRatio();Tool.log("像素比",ratio);Tool.log("dom画布rect",canvasRect.x,canvasRect.y,canvasRect.width,canvasRect.height);Tool.log("屏幕位置",pos.x,pos.y);Tool.log("设计尺寸",designSize.width,designSize.height);let canvasWidth=canvasRect.width;let canvasHeight=canvasRect.height;let domX=0;let domY=0;const canvasComponent=cc.Canvas.instance;if(canvasComponent){// 适配规则
if(canvasComponent.fitWidth&&canvasComponent.fitHeight){// creator画布缩放
let scale=Math.min(canvasWidth/designSize.width,canvasHeight/designSize.height);// Tool.log("宽高适配 缩放", scale, " canvas:", canvasWidth, canvasHeight, " design:", designSize.width, designSize.height);
let blackWidth=(canvasWidth-designSize.width*scale)*0.5;domX=canvasRect.x+blackWidth+pos.x/designSize.width*(canvasWidth-blackWidth*2);// 上下黑边高度
let blackHeight=(canvasHeight-designSize.height*scale)*0.5;// 上下有黑边，转换时需要多减去黑边高度
domY=canvasRect.y+canvasHeight-blackHeight-pos.y/designSize.height*(canvasHeight-blackHeight*2);}else if(canvasComponent.fitHeight&&!canvasComponent.fitWidth){let scale=canvasHeight/designSize.height;// Tool.log("高度适配 缩放", scale);
let blackWidth=(canvasWidth-designSize.width*scale)*0.5;// Tool.log("宽度超出部分", blackWidth);
domX=canvasRect.x+pos.x/designSize.width*(canvasRect.width-blackWidth*2);let blackHeight=(canvasHeight-designSize.height*scale)*0.5;domY=canvasRect.y+canvasHeight-blackHeight-pos.y/designSize.height*(canvasHeight-blackHeight*2);}else if(!canvasComponent.fitHeight&&canvasComponent.fitWidth){let scale=canvasWidth/designSize.width;// Tool.log("宽度适配 缩放");
let blackWidth=(canvasWidth-designSize.width*scale)*0.5;let blackHeight=(canvasHeight-designSize.height*scale)*0.5;domX=canvasRect.x+blackWidth+pos.x/designSize.width*(canvasWidth-blackWidth*2);domY=canvasRect.y+canvasHeight-pos.y/designSize.height*(canvasHeight-blackHeight*2);}else{// 无适配规则  填充满
let scale=Math.max(canvasWidth/designSize.width,canvasHeight/designSize.height);// Tool.log("无适配规则 缩放", scale);
let blackWidth=(canvasWidth-designSize.width*scale)*0.5;let blackHeight=(canvasHeight-designSize.height*scale)*0.5;domX=canvasRect.x+blackWidth+pos.x/designSize.width*(canvasWidth-blackWidth*2);domY=canvasRect.y+canvasHeight-pos.y/designSize.height*(canvasHeight-blackHeight*2);}}return{x:domX,y:domY};}catch(error){return{x:0,y:0};}}}/**
 * Creator 3.x 节点计算器
 * 处理Creator 3.x版本的节点数据转换、属性更新、坐标计算等
 */class NodeCalculator3x extends BaseNodeCalculator{constructor(){super();// 获取Canvas在页面中的位置和尺寸
// 获取GameCanvas元素
console.log("*********** 项目设置 ***********");console.log("creator版本",(cc===null||cc===void 0?void 0:cc.ENGINE_VERSION)||"");const gameCanvas=cc.game.canvas;if(!gameCanvas){console.log("❌ 未找到GameCanvas元素");return;}const canvasRect=gameCanvas.getBoundingClientRect();const designSize=cc.view.getDesignResolutionSize();let ratio=cc.screen.devicePixelRatio;console.log("dom画布rect",canvasRect.x,canvasRect.y,canvasRect.width,canvasRect.height);console.log("设计尺寸",designSize.width,designSize.height);console.log("像素比",ratio);//let adjustStrategy = cc.view.getResolutionPolicy().getContentStrategy().strategy;
//console.log("当前策略值=", adjustStrategy);
// 适配规则
// if (adjustStrategy === cc.ResolutionPolicy.SHOW_ALL) {
//     console.log("适配策略: 宽高适配");
// }
// else if (adjustStrategy === cc.ResolutionPolicy.FIXED_HEIGHT) {
//     console.log("适配策略: 高度适配");
// }
// else if (adjustStrategy === cc.ResolutionPolicy.FIXED_WIDTH) {
//     console.log("适配策略: 宽度适配");
// }
// else {
//     console.log("适配策略: 无适配");
// }
console.log("********************************");}/**
   * 将Creator 3.x节点转换为NodeData
   */convertCCNodeToNodeData(ccNode){const transform=ccNode.getComponent("cc.UITransform");const render=ccNode.getComponent("cc.UIRenderer")||ccNode.getComponent("cc.Renderable2D");const nodeData={id:this.generateNodeId(ccNode),name:this.getNodeName(ccNode),active:this.getNodeActive(ccNode),position:{x:ccNode.position.x||ccNode.x||0,y:ccNode.position.y||ccNode.y||0},anchor:{x:(transform===null||transform===void 0?void 0:transform.anchorX)||0.5,y:(transform===null||transform===void 0?void 0:transform.anchorY)||0.5},size:{width:(transform===null||transform===void 0?void 0:transform.width)||0,height:(transform===null||transform===void 0?void 0:transform.height)||0},scale:{x:ccNode.scale.x||1,y:ccNode.scale.y||1},rotation:ccNode.angle||0,color:render?{r:render.color.r||255,g:render.color.g||255,b:render.color.b||255,a:render.color.a||255}:{r:255,g:255,b:255,a:255},children:this.processChildren(ccNode),ccNode:ccNode// 保存原始节点引用
};return nodeData;}/**
   * 更新Creator 3.x节点属性
   */updateNodeProperty(nodeData,property,value){const ccNode=nodeData.ccNode;if(!ccNode)return;try{switch(property){case"active":ccNode.active=Boolean(value);break;case"x":nodeData.position.x=Number(value);ccNode.setPosition(Number(value),nodeData.position.y);break;case"y":nodeData.position.y=Number(value);ccNode.setPosition(nodeData.position.x,Number(value));break;case"anchorX":{nodeData.anchor.x=Number(value);const transform=ccNode.getComponent("cc.UITransform");if(transform){transform.setAnchorPoint(Number(value),nodeData.anchor.y);}break;}case"anchorY":{nodeData.anchor.y=Number(value);const transform=ccNode.getComponent("cc.UITransform");if(transform){transform.setAnchorPoint(nodeData.anchor.x,Number(value));}break;}case"width":{nodeData.size.width=Number(value);const transform=ccNode.getComponent("cc.UITransform");if(transform){transform.setContentSize(Number(value),nodeData.size.height);}break;}case"height":{nodeData.size.height=Number(value);const transform=ccNode.getComponent("cc.UITransform");if(transform){transform.setContentSize(nodeData.size.width,Number(value));}break;}case"scaleX":nodeData.scale.x=Number(value);ccNode.setScale(Number(value),nodeData.scale.y);break;case"scaleY":nodeData.scale.y=Number(value);ccNode.setScale(nodeData.scale.x,Number(value));break;case"rotation":ccNode.angle=Number(value);break;case"opacity":{nodeData.color.a=Number(value);const render=ccNode.getComponent("cc.UIRenderer")||ccNode.getComponent("cc.Renderable2D");if(render){render.color=nodeData.color;}break;}case"color.r":{nodeData.color.r=Number(value);const render=ccNode.getComponent("cc.UIRenderer")||ccNode.getComponent("cc.Renderable2D");if(render){render.color=nodeData.color;}break;}case"color.g":{nodeData.color.g=Number(value);const render=ccNode.getComponent("cc.UIRenderer")||ccNode.getComponent("cc.Renderable2D");if(render){render.color=nodeData.color;}break;}case"color.b":{nodeData.color.b=Number(value);const render=ccNode.getComponent("cc.UIRenderer")||ccNode.getComponent("cc.Renderable2D");if(render){render.color=nodeData.color;}break;}}}catch(error){console.error("更新Creator 3.x节点属性失败:",error);}}/**
   * 获取Creator 3.x节点在屏幕上的矩形区域
   */getNodeScreenRect(ccNode){try{// 1. 获取节点所用的摄像机
const camera=this.findCamera(ccNode);if(!camera){console.error("❌ 未找到节点专属摄像机");return null;}// 节点必须包含UITransform组件
const transform=ccNode.getComponent("cc.UITransform");if(!transform){return null;}// 2. 获取节点在creator屏幕坐标系中的位置
let aabb=this.getNodeScreenAABB(transform,camera);// 3. 转换到DOM坐标系中
const leftTop=this.getDomPosition(aabb.lt);const rirghtBottom=this.getDomPosition(aabb.rb);let width=Math.abs(leftTop.x-rirghtBottom.x);let height=Math.abs(leftTop.y-rirghtBottom.y);let x=leftTop.x;let y=Math.min(leftTop.y,rirghtBottom.y);return{x,y,width,height};}catch(error){console.error("获取Creator 3.x节点屏幕位置失败:",error);return null;}}findCamera(ccNode){if(ccNode.scene&&ccNode.scene.renderScene){const cameras=ccNode.scene.renderScene.cameras;for(let i=0;i<cameras.length;i++){const camera=cameras[i];if(camera.visibility&ccNode.layer){return camera;}}}return null;}/**
   * 获取节点在creator屏幕坐标系中的位置
   * @param ccNode 节点
   * @param camera 摄像机
   * @returns 节点在creator屏幕坐标系中的位置
   */getScreenPosition(transform,camera){const worldPos=transform.convertToWorldSpaceAR(cc.v3(0,0,1));Tool.log(`世界坐标 x:${worldPos.x} y:${worldPos.y} z:${worldPos.z}`);let out=cc.v3();camera.worldToScreen(out,worldPos);Tool.log(`屏幕坐标 x:${out.x} y:${out.y} z:${out.z}`);return out;}getNodeScreenAABB(transform,camera){// 获取节点的包围盒
let rect=transform.getBoundingBoxToWorld();// 获取节点相机
return{lt:camera.worldToScreen(cc.v3(),cc.v3(rect.x,rect.y+rect.height,0)),rb:camera.worldToScreen(cc.v3(),cc.v3(rect.x+rect.width,rect.y,0))};}/** creator屏幕坐标系转换为dom坐标系 */getDomPosition(pos){try{// 获取GameCanvas元素
const gameCanvas=cc.game.canvas;if(!gameCanvas){Tool.log("❌ 未找到GameCanvas元素");return{x:0,y:0};}// 获取Canvas在页面中的位置和尺寸
const canvasRect=gameCanvas.getBoundingClientRect();const designSize=cc.view.getDesignResolutionSize();let ratio=cc.screen.devicePixelRatio;Tool.log("像素比",ratio);Tool.log("dom画布rect",canvasRect.x,canvasRect.y,canvasRect.width,canvasRect.height);Tool.log("屏幕位置",pos.x,pos.y);Tool.log("设计尺寸",designSize.width,designSize.height);// let adjustStrategy = cc.view.getResolutionPolicy().getContentStrategy().strategy;
// Tool.log("当前策略", adjustStrategy);
let domX=0;let domY=0;domX=canvasRect.x+pos.x/ratio;domY=canvasRect.y+canvasRect.height-pos.y/ratio;return{x:domX,y:domY};}catch(error){return{x:0,y:0};}}}/**
 * 节点管理器类
 * 负责处理所有节点相关的逻辑，包括节点监控、数据转换、属性更新等
 */class NodeManager{constructor(){this.selectedNode=null;this.nodeUpdateInterval=null;this.nodeTreePanel=new NodeTreePanel();this.propertyPanel=new PropertyPanel();// 初始化计算器
if(isCreator3x()){this.calculator=new NodeCalculator3x();}else{this.calculator=new NodeCalculator2x();}}/**
   * 初始化节点管理器
   */initialize(treeContainer,showTooltip){this.createUI(treeContainer);this.bindEvents();// 将 showTooltip 回调传递给 propertyPanel
this.propertyPanel.onShowTooltip(showTooltip);}/**
   * 创建节点相关的UI
   */createUI(treeContainer){// 创建节点树面板
this.nodeTreePanel.create(treeContainer);// 创建属性面板
this.propertyPanel.create(treeContainer);}/**
   * 绑定事件
   */bindEvents(){// 监听节点选择事件
this.nodeTreePanel.onNodeSelect(nodeData=>{this.selectedNode=nodeData;this.propertyPanel.displayNodeProperties(nodeData);// 添加DOM高亮效果
this.highlightNodeInScene(nodeData);});// 监听属性修改事件
this.propertyPanel.onPropertyChange((property,value)=>{if(this.selectedNode){this.updateNodeProperty(this.selectedNode,property,value);}});// 监听节点刷新事件
this.propertyPanel.onRefreshNode(nodeData=>{if(nodeData.ccNode){// 使用计算器重新从ccNode读取最新数据
return this.convertCCNodeToNodeData(nodeData.ccNode);}else{console.warn("无法刷新节点：ccNode引用不存在");return nodeData;}});}/**
   * 开始监控节点变化
   */startNodeMonitoring(){// 如果已经在监控，先停止
if(this.nodeUpdateInterval){this.stopNodeMonitoring();}this.nodeUpdateInterval=window.setInterval(()=>{this.updateNodeTree();},100);// 每100ms更新一次
console.log("节点监控已开始");}/**
   * 停止监控节点变化
   */stopNodeMonitoring(){if(this.nodeUpdateInterval){clearInterval(this.nodeUpdateInterval);this.nodeUpdateInterval=null;console.log("节点监控已停止");}}/**
   * 更新节点树
   */updateNodeTree(){const rootNodes=this.getAllRootNodes();this.nodeTreePanel.updateTree(rootNodes);}/**
   * 获取所有根节点
   */getAllRootNodes(){var _a;const nodes=[];// 尝试获取Creator的场景节点
try{// @ts-ignore - Creator全局对象
const director=(_a=window.cc)===null||_a===void 0?void 0:_a.director;if(director){const scene=director.getScene();if(scene){// 递归处理子节点
if(scene.children&&scene.children.length>0){scene.children.forEach(child=>{nodes.push(this.convertCCNodeToNodeData(child));});}}}}catch(error){console.warn("无法获取Creator场景节点:",error);}return nodes;}/**
   * 将Creator节点转换为NodeData
   * 自动选择合适的计算器进行转换
   */convertCCNodeToNodeData(ccNode){return this.calculator.convertCCNodeToNodeData(ccNode);}/**
   * 更新节点属性
   * 自动选择合适的计算器进行更新
   */updateNodeProperty(nodeData,property,value){const ccNode=nodeData.ccNode;if(!ccNode)return;try{this.calculator.updateNodeProperty(nodeData,property,value);}catch(error){console.error("更新节点属性失败:",error);}}/**
   * 使用DOM方式在屏幕上高亮显示选中的节点
   */highlightNodeInScene(nodeData){const ccNode=nodeData.ccNode;if(!ccNode){console.warn("无法高亮节点：ccNode引用不存在");return;}// 检查浏览器环境
if(typeof document==="undefined"){console.warn("非浏览器环境，无法显示DOM高亮效果");return;}try{// 获取节点在屏幕上的位置和尺寸
const rect=this.calculator.getNodeScreenRect(ccNode);if(!rect){console.warn("无法获取节点屏幕位置");return;}// 创建DOM高亮层（使用基类的通用方法）
Tool.log("显示到dom上的rect",rect.x,rect.y,rect.width,rect.height);this.calculator.createDOMHighlight(rect);}catch(error){console.error("高亮节点时出错:",error);}}/**
   * 销毁节点管理器
   */destroy(){this.stopNodeMonitoring();}}/**
 * Creator节点调试器主类
 * 负责协调UI组件和抽屉管理
 */class NodeInspector{constructor(){this.drawerUI=new DrawerUI();this.rightDrawerUI=new RightDrawerUI();this.resourceMemoryPanel=new ResourceMemoryPanel();this.nodeManager=new NodeManager();}/**
   * 初始化插件
   */initialize(){this.createUI();this.bindEvents();}/**
   * 创建UI界面
   */createUI(){// 创建左侧抽屉容器
this.drawerUI.create();// 创建右侧抽屉容器
this.rightDrawerUI.create();// 创建资源内存面板
const resourceContainer=this.rightDrawerUI.getContentContainer();this.resourceMemoryPanel.create(resourceContainer);// 初始化节点管理器（传入左侧抽屉容器）
const treeContainer=this.drawerUI.getContentContainer();this.nodeManager.initialize(treeContainer,this.drawerUI.showTooltip.bind(this.drawerUI));}/**
   * 绑定事件
   */bindEvents(){// 监听左侧抽屉状态变化
this.drawerUI.onToggle(isOpen=>{this.handleDrawerToggle(isOpen);});// 监听右侧抽屉状态变化
this.rightDrawerUI.onToggle(isOpen=>{this.handleRightDrawerToggle(isOpen);});}/**
   * 处理左侧抽屉状态变化
   */handleDrawerToggle(isOpen){if(isOpen){// 抽屉展开时开始监控节点变化
this.nodeManager.startNodeMonitoring();}else{// 抽屉收起时停止监控节点变化
this.nodeManager.stopNodeMonitoring();// 抽屉收起时重置抽屉宽度
this.drawerUI.resetWidth();}}/**
   * 处理右侧抽屉状态变化
   */handleRightDrawerToggle(isOpen){if(isOpen){// 右侧抽屉展开时，刷新资源内存面板
this.resourceMemoryPanel.refreshData();console.log("<!----右侧抽屉已展开，资源内存监控已激活---->");}else{// 右侧抽屉收起时重置宽度
this.rightDrawerUI.resetWidth();console.log("<!----右侧抽屉已收起---->");}}/**
   * 销毁插件
   */destroy(){this.nodeManager.destroy();this.drawerUI.destroy();this.rightDrawerUI.destroy();this.resourceMemoryPanel.destroy();}}/**
 * Creator节点调试插件主入口
 * 在Creator项目的TypeScript文件中调用此函数来初始化插件
 */function initNodeInspector(){const inspector=new NodeInspector();inspector.initialize();return inspector;}/**
 * 获取Creator环境变量的兼容函数
 */function getCreatorEnvVars(){var _a,_b,_c,_d,_e,_f,_g;let DEBUG=false;let EDITOR=false;let isBrowser=false;try{// 获取Creator版本信息
const cc=window.cc;if(isCreator3x()){// Creator 3.x+ 环境，尝试动态导入
try{// 动态获取ESM模块
const ccEnv=cc===null||cc===void 0?void 0:cc.env;if(ccEnv){DEBUG=ccEnv.DEBUG||false;EDITOR=ccEnv.EDITOR||false;}else{// 如果cc.env不存在，尝试其他方式
DEBUG=(cc===null||cc===void 0?void 0:cc.DEBUG)||window.DEBUG||window.CC_DEBUG||false;EDITOR=(cc===null||cc===void 0?void 0:cc.EDITOR)||window.EDITOR||window.CC_EDITOR||false;}// 获取sys信息
const ccSys=(_a=window.cc)===null||_a===void 0?void 0:_a.sys;if(ccSys){isBrowser=ccSys.isBrowser||false;}}catch(e){// 如果动态获取失败，尝试全局变量
DEBUG=window.DEBUG||false;EDITOR=window.EDITOR||false;isBrowser=((_c=(_b=window.cc)===null||_b===void 0?void 0:_b.sys)===null||_c===void 0?void 0:_c.isBrowser)||false;}}else{// Creator 2.x 环境，使用全局变量
DEBUG=window.CC_DEBUG||false;EDITOR=window.CC_EDITOR||false;isBrowser=((_e=(_d=window.cc)===null||_d===void 0?void 0:_d.sys)===null||_e===void 0?void 0:_e.isBrowser)||false;}}catch(error){console.warn("检测Creator环境变量失败:",error);// 降级方案：尝试所有可能的全局变量
DEBUG=window.DEBUG||window.CC_DEBUG||false;EDITOR=window.EDITOR||window.CC_EDITOR||false;isBrowser=((_g=(_f=window.cc)===null||_f===void 0?void 0:_f.sys)===null||_g===void 0?void 0:_g.isBrowser)||false;}return{DEBUG,EDITOR,isBrowser};}// 自动初始化逻辑
try{const{DEBUG,EDITOR,isBrowser}=getCreatorEnvVars();if(DEBUG&&!EDITOR&&isBrowser){initNodeInspector();console.log("Node Inspector已自动初始化");}}catch(error){console.warn("Node Inspector自动初始化失败，请手动调用 window?.initNodeInspector?.():",error);}// 全局导出，方便在Creator项目中使用
window.initNodeInspector=initNodeInspector;// // CommonJS导出格式，兼容Creator 3.8.6
// module.exports = {
//     initNodeInspector,
//     NodeInspector,
//     NodeTreePanel,
//     PropertyPanel
// };
// // 同时支持ES模块导入（向后兼容）
// module.exports.default = initNodeInspector;
}).call(root);})(// The environment-specific global.
function(){if(typeof globalThis!=='undefined')return globalThis;if(typeof self!=='undefined')return self;if(typeof window!=='undefined')return window;if(typeof global!=='undefined')return global;if(typeof this!=='undefined')return this;return{};}.call(this));