const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/game-d1c71da1-BXgfYYqc.js","assets/game-fd4578d2-CJjC28Jv.js","assets/game-1db2353a-OPVs455c.js","assets/contextTrackingHelper-nB-OPn1T.css","assets/game-d8b296a6-D6-XlEtG.js","assets/game-a361386b-BqmJhnkd.js","assets/game-6f09ab72-Bi-UxEEh.js","assets/game-74aa2de1-B4nljWTS.js"])))=>i.map(i=>d[i]);
import{_ as Io,a as Of,i as Lf,d as kf}from"./game-1db2353a-OPVs455c.js";import{aM as me,aN as eo,aO as Gf,g as G,aP as Df,aQ as _t,aR as Ff,X as it,k as ue,i as Tr,aS as Ec,aT as _c,z as Pc,J as fi,S as to,m as Cr,o as ro,H as Nf,aU as jf,s as di,aV as Vf,aW as Wf,aX as qf,aY as pi,aZ as Hf,a_ as Bf,a$ as Tc,b0 as Uf,b1 as Yf,b2 as $f,b3 as Oo,b4 as Cc,b5 as Pt,b6 as Lo,b7 as Zf,b8 as ko,Y as Xf,b9 as Kf,Z as st,ba as zf,bb as Ac,bc as Ar,bd as Zt,be as Rc,bf as Jf,bg as xc,bh as Mc,bi as Qf,bj as Ic,bk as ed,bl as Go,_ as Do,b as Ue,bm as Oc,bn as td,W as Tt,V as Ct,bo as Lc,bp as rd,bq as nd,br as Fo,bs as No,bt as kc,bu as ad,bv as od,bw as id,bx as sd,by as $,bz as jr,a1 as Gc,bA as ud,bB as cd,bC as ld,a7 as no,bD as jo,bE as Ee,K as nt,ap as fd,j as Dc,bF as Fc,bG as Nc,bH as Lt,bI as kt,ab as dd,n as pd,bJ as md,bK as hd,L as vd,bL as gd,bM as yd,a6 as mi,bN as wd,M as hi,bO as bd,bP as Sd,F as Ed,bQ as vi,bR as gi,bS as _d}from"./game-fd4578d2-CJjC28Jv.js";import{g as ye}from"./game-d8b296a6-D6-XlEtG.js";function Rr(e,t){if(t.length<e)throw new TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function ao(e){Rr(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||me(e)==="object"&&t==="[object Date]"?new Date(e.getTime()):typeof e=="number"||t==="[object Number]"?new Date(e):((typeof e=="string"||t==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function yi(e){var t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),e.getTime()-t.getTime()}function wi(e){Rr(1,arguments);var t=ao(e);return t.setHours(0,0,0,0),t}var Pd=864e5;function Td(e,t){Rr(2,arguments);var r=wi(e),n=wi(t),a=r.getTime()-yi(r),o=n.getTime()-yi(n);return Math.round((a-o)/Pd)}function bi(e,t){var r=e.getFullYear()-t.getFullYear()||e.getMonth()-t.getMonth()||e.getDate()-t.getDate()||e.getHours()-t.getHours()||e.getMinutes()-t.getMinutes()||e.getSeconds()-t.getSeconds()||e.getMilliseconds()-t.getMilliseconds();return r<0?-1:r>0?1:r}function Cd(e,t){Rr(2,arguments);var r=ao(e),n=ao(t),a=bi(r,n),o=Math.abs(Td(r,n));r.setDate(r.getDate()-a*o);var i=+(bi(r,n)===-a),s=a*(o-i);return s===0?0:s}function Vo(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Vo=function(){return!!e})()}function Ad(e){var t=Vo();return function(){var r,n=eo(e);if(t){var a=eo(this).constructor;r=Reflect.construct(n,arguments,a)}else r=n.apply(this,arguments);return Gf(this,r)}}function Rd(e){G.dispatch(Df.actions.setCookieEnabled({cookieEnabled:e}))}function Xt(e){const t=document.createElement("img");return t.height=1,t.width=1,t.src=e,t}const xd=_t(async(e,t,r,n)=>{if(e!=="g_register"&&e!=="g_login"&&e!=="g_createrole"){console.error(new Error(`Invalid event type: ${e}`));return}if(!t){console.error(new Error(`Invalid appId: ${t}`));return}Xt(`/stats?k=g_event&t=${e}&a=${t}&img=1`),await Ff(),await it.post("/api/v1/session_log",{kind:"succeed",gameServerId:n.gameServerId,gameUserId:n.gameUserId,roleId:n.roleId,roleName:n.roleName},{headers:{Authorization:`Bearer ${r}`},withCredentials:!0})}),Md=["g_init","g_register","g_login","g_createrole","g_tutorial","g_logout","g_obtain","g_consumption","g_transaction","g_comsumption","g_view","g_heartbeat","g_active","g_click","g_levelup","g_payrequest","g_payend","g_milestone","misc_flashlaunch_*"],jc={ChannelRpcRequest:"@channel-rpc/REQUEST",ChannelRpcResponse:"@channel-rpc/RESPONSE"};function Id(e){return e&&e.type===jc.ChannelRpcRequest}function Od(e){return e&&e.jsonrpc==="2.0"&&typeof e.method=="string"}let Vc=!1;try{Vc=!!localStorage.getItem("channel-rpc-debug")}catch{}function Ve(...e){Vc&&console.log(...e)}const Vr={InvalidRequest:{code:-32600,message:"Invalid Request"},MethodNotFound:{code:-32601,message:"Method not found"},InternalError:{code:-32603,message:"Internal error"}};function Wr(e,t){return{jsonrpc:"2.0",error:{code:e.code,message:e.message},id:t}}class Ld{constructor(t){this._unlisten=void 0,this._handleMessage=i=>{if(!(!Id(i.data)||i.data.channelId!==this.channelId)){if(this.allowOrigins.length>0&&this.allowOrigins.indexOf(i.origin)===-1)throw new Error(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] Invalid origin: ${i.origin}`);if(!i.source){Ve(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] event.source is null`,i);return}Ve(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] RECEIVE_REQUEST`,i.data),this._handleRpcRequest(i.source,i.data.payload)}};const{allowOrigins:r,channelId:n,handler:a}=t;if(!n)throw new Error("id is required");this.channelId=n,this.allowOrigins=r&&r.indexOf("*")===-1?r:[],this._handlers={};const o=a||{};Object.keys(o).forEach(i=>{const s=o[i];typeof s=="function"&&(this._handlers[i]=s.bind(o))})}start(){if(this._unlisten)return;const t=typeof globalThis=="object"?globalThis:window;t.addEventListener("message",this._handleMessage),this._unlisten=()=>{t.removeEventListener("message",this._handleMessage)}}stop(){this._unlisten&&(this._unlisten(),this._unlisten=void 0)}async _sendResponse(t,r){const n={type:jc.ChannelRpcResponse,channelId:this.channelId,payload:r};t.postMessage(n,{targetOrigin:"*"})}async _handleRpcRequest(t,r){if(Ve(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] HANDLE_REQUEST_RPC`,r),!Od(r)){const a=Wr(Vr.InvalidRequest,r.id||null);Ve(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] reply`,a),this._sendResponse(t,a);return}Ve(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] HANDLE_REQUEST_RPC method[${r.method}]`,this._handlers,r);const n=this._handlers[r.method];if(!n){const a=Wr(Vr.MethodNotFound,r.id||null);Ve(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] SEND_RESPONSE`,a),this._sendResponse(t,a);return}try{const o={jsonrpc:"2.0",result:await n(...r.params||[]),id:r.id};Ve(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] SEND_RESPONSE`,o),this._sendResponse(t,o)}catch{const o=Wr(Vr.InternalError,r.id||null);Ve(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] SEND_RESPONSE`,o),this._sendResponse(t,o)}}}const qt=typeof Date.now=="function"?Date.now:()=>new Date().getTime();class kd extends Error{constructor(t){super(t.statusText),this.name="HTTPError",this.response=t}}function Wc(e){if(!e.ok)throw new kd(e);return e}async function aw(e){return(await fetch(`${ue.SHD_G123_GAME_URL}/api/share`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e),credentials:"include",mode:"same-origin"}).then(Wc)).text()}async function Gd(e){try{return await(await fetch(`${ue.SHD_G123_GAME_URL}/api/share?code=${e}`).then(Wc)).json()}catch(t){return console.error(t),null}}const Wo="iframe-game",Dd=["platform","bid","ver","ctw_share_id","ctw_share_extra","app","utm_medium","utm_campaign","game_mode"],Fd="sdk_mode",qc={CHANNEL_ONLY:"2"};let ne=document.getElementById(Wo),fr="0";function Nd(e){const t=e.searchParams.get(Fd);if(t&&["0","1","2"].indexOf(t)!==-1){fr=t;return}const{sdkMode:r}=window.option;if(r&&["0","1","2"].indexOf(r)!==-1){fr=r;return}fr="0"}function Hc(){return fr}let oo="";function Si(){return oo}function jd(e){if(oo||!e)return;oo=new URL(e).origin}let qr;function Bc(e=!1){return(!qr||e)&&(qr=new URL(window.location.href)),qr}async function Vd(){const t=await Tr().currentSession();if(!t)throw new Error("Session is unavailable");const{code:r}=t;if(!r)throw new Error("Code is unavailable");const n={platform:"ctw"},a=Bc(),o=a.searchParams;a.searchParams.forEach((c,l)=>{const f=l.toLowerCase();Dd.includes(f)&&(n[f]=c)});const i=o.get("ctw_share_code");if(i)try{const c=await Gd(i);c&&(c.ctw_share_id&&(n.ctw_share_id=c.ctw_share_id),c.ctw_share_extra&&(n.ctw_share_extra=c.ctw_share_extra))}catch(c){console.error(c)}const s=await Cr(),u={region:s.region?s.region.toLowerCase():"ja",country:s.country,lang:s.lang};return{gameParams:{code:r,params:{...n,lang:u.lang||"ja"},env:window.option.runEnv},entryParams:{__gp_region:u.region,sdk_mode:Hc()}}}let It;async function Uc(){return It||(It=Vd(),It.catch(()=>{It=void 0})),It}async function Wd(e){const{entryParams:t,gameParams:r}=await Uc(),n=new URL(e);return t.sdk_mode===qc.CHANNEL_ONLY?(Object.entries(t).forEach(([o,i])=>{n.searchParams.set(o,i)}),n.searchParams.sort(),n.href):(n.searchParams.set("code",r.code),Object.entries({...r.params,...t}).forEach(([o,i])=>{n.searchParams.set(o,i)}),n.searchParams.sort(),n.href)}const qd=_t(()=>{!window.perf.start||!window.perf.app_start||!window.perf.game_loading||(Xt(`/stats?k=perf&t=game_loading&a=${window.option.appId}&d=${window.perf.game_loading-window.perf.app_start}&img=1`),console.info("[Perf] Loading",window.perf.game_loading-window.perf.start))}),Hd=_t(()=>{!window.perf.game_loaded||!window.perf.game_loading||(Xt(`/stats?k=perf&t=game_loaded&a=${window.option.appId}&d=${window.perf.game_loaded-window.perf.game_loading}&img=1`),console.info("[Perf] Loaded",window.perf.game_loaded-window.perf.game_loading))}),Ei=_t(e=>{const t=e||ro();Xt(`/stats?k=pwa&t=pwa_first_launch&a=${window.option.appId}&d=${t}&img=1`)});async function Hr(e=!1){const t=await Tr().currentSession();if(!t)throw new Error("currentSession is unavailable");const{idCallback:r}=window.option,{code:n,gameMode:a,webClient:o}=t;if(!r)throw new Error(`Invalid idCallback[${r}] or code [${n}]`);if(n===Ec){console.error(new Error(`user: ${n} has been blocked!`));return}const i=!ne;i&&(ne=document.createElement("iframe"),ne.id=Wo,ne.allow="autoplay; screen-wake-lock");const s=e&&window.perf&&window.perf.app_start;s&&(window.perf.game_loading=window.perf.game_loading||qt(),setTimeout(qd));let u=await Wd(r);if(a==="cloud"&&o){const l=new URL(o.url,u);for(const[f,p]of Object.entries(o.query||{}))l.searchParams.set(f,p);l.searchParams.set("app_id",window.option.appId),l.searchParams.set("lang",window.option.lang||"ja"),l.searchParams.set("game_url",u),l.searchParams.set("page_view_id",window.__cloud_game_page_view_id__||""),l.searchParams.set("ts",(window.__platform_init_time__||Date.now()).toString()),u=l.toString()}if(!ne)throw new Error(`gameIframe is ${ne}`);if(ne.src===u)return;const c=_c();ne.onload=function(){c.resolve()},ne.onerror=(l,f,p,v,m)=>{console.error("gameIframe.onerror",l,f,p,v,m),m&&m instanceof Error&&c.reject(m)},jd(u),ne.src=u,i&&ne&&document.body.insertBefore(ne,document.getElementById("float-icon")),await c.promise.then(()=>{window.perf.game_loaded=window.perf.game_loaded||qt(),s&&setTimeout(Hd),Pc({action:"p_game_loaded",data:{display_name:window.option.userId,providers:window.option.providers}})})}function Yc(e){ne=document.getElementById(Wo),ne?.contentWindow?.postMessage(e,"*")}let Br;async function Bd(e){if(Br)throw new Error("ChannelServer has been started");const t=`${to.gameStoragePrefix}:${e}`,r={sendPayment:n=>{window.postMessage({type:"PspCommand",action:"EnterPayment",...n})},saveData:n=>{fi().setItem(t,n)},restoreData:()=>fi().getItem(t)||"",getPlatformParams:async()=>(await Uc()).gameParams};Br=new Ld({channelId:"cp-channel",handler:r}),Br.start()}function Ud({data:e}){if(!e){console.error("Empty in-game account",e);return}G.dispatch(Tc.actions.updateByInformEvent(e))}function Yd({data:e,action:t}){if(Hf(e)||Bf(e))G.dispatch(Tc.actions.updateByGEvent(e));else{if(t==="g_init")return;const r=new Error(`INVALID_GEVENT[${JSON.stringify({data:e,action:t})}]`);throw window.captureGlobalException?.(r),r}}function $d(e){Rd(!e.data.disabled)}function Zd(e){setTimeout(()=>{Wf()})}function Xd(e){if(!qf(e)){const r=new Error("Invalid PSP Monitor Event");throw console.error(r,JSON.stringify(e)),r}const{payload:t}=e;t.type==="sdk"?G.dispatch(pi.actions.updateSdkStatus(t)):G.dispatch(pi.actions.updateAppStatus(t))}function Kd(e){let t;if(e.data){if(typeof e.data=="object"&&(t=e.data),!t&&typeof e.data=="string")try{t=JSON.parse(e.data)}catch{}if(t){if(t.event==="CreateRole"){Zd();return}if(t.event==="PaymentSdkMonitor"){Xd(t);return}switch(t.action){case"inform":Ud(t);break;case"cookie_disabled_check":$d(t);break;case"EnterPayment":G.dispatch(Vf(t.orderNo)),di({version:"v2",action:"g_payinit",data:{custom:{type:t.type,order_no:t.orderNo}}});break;default:if(t.action?.startsWith("g_")&&Yd(t),(t.action==="g_createrole"||t.action==="g_login")&&Nf().start(),(t.action==="g_register"||t.action==="g_login"||t.action==="g_createrole")&&(G.dispatch(jf({gameLoginOrRegisterAt:Date.now()})),Io(()=>import("./game-d1c71da1-BXgfYYqc.js"),__vite__mapDeps([0,1,2,3,4])),xd(t.action,window.option.appId,window.option.code,{gameServerId:`${t.data?.game_server_id||""}`,gameUserId:`${t.data?.game_user_id||""}`,roleId:`${t.data?.role?.id||t.data?.role_id||""}`,roleName:`${t.data?.role?.name||t.data?.role_name||""}`}),window.option.gameServerId=t.data?.game_server_id,window.option.gameUserId=t.data?.game_user_id),Md.findIndex(r=>{const n=t.action||"";return!!(r===n||r.endsWith("*")&&n.startsWith(r.slice(0,-1)))})!==-1)if(Si()!==e.origin&&window.option.appId!=="seirei"&&window.option.appId!=="got"){const r=new Error(`Game origin mismatch, gameOrigin: ${Si()}, eventOrigin: ${e.origin}`);window.captureGlobalException?.(r)}else di(t);break}}}}let _i=!1;function zd(){_i||(_i=!0,window.addEventListener("message",Kd,!1))}const Jd="#000000";function Qd(){const e=document.head.querySelector('[name="theme-color"]');e&&e.setAttribute("content",Jd)}function Pi(e,t,r,n,a,o,i){try{var s=e[o](i),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,a)}function N(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var o=e.apply(t,r);function i(u){Pi(o,n,a,i,s,"next",u)}function s(u){Pi(o,n,a,i,s,"throw",u)}i(void 0)})}}var Ur,Ti;function ep(){if(Ti)return Ur;Ti=1;function e(){}return Ur=e,Ur}var tp=ep();const gr=ye(tp);function rp(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function np(e,t){if(e==null)return{};var r,n,a=rp(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function ap(e){if(Array.isArray(e))return Uf(e)}function op(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function $e(e){return ap(e)||Yf(e)||$f(e)||op()}var Yr={exports:{}},$r={exports:{}},Ci;function $c(){return Ci||(Ci=1,function(e){function t(r,n){this.v=r,this.k=n}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}($r)),$r.exports}var Zr={exports:{}},Xr={exports:{}},Ai;function Zc(){return Ai||(Ai=1,function(e){function t(r,n,a,o){var i=Object.defineProperty;try{i({},"",{})}catch{i=0}e.exports=t=function(u,c,l,f){if(c)i?i(u,c,{value:l,enumerable:!f,configurable:!f,writable:!f}):u[c]=l;else{var p=function(m,g){t(u,m,function(d){return this._invoke(m,g,d)})};p("next",0),p("throw",1),p("return",2)}},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,n,a,o)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(Xr)),Xr.exports}var Ri;function Xc(){return Ri||(Ri=1,function(e){var t=Zc();function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,a,o=typeof Symbol=="function"?Symbol:{},i=o.iterator||"@@iterator",s=o.toStringTag||"@@toStringTag";function u(d,h,y,E){var _=h&&h.prototype instanceof l?h:l,T=Object.create(_.prototype);return t(T,"_invoke",function(w,A,b){var P,C,M,O=0,z=b||[],D=!1,U={p:0,n:0,v:n,a:X,f:X.bind(n,4),d:function(V,q){return P=V,C=0,M=n,U.n=q,c}};function X(Y,V){for(C=Y,M=V,a=0;!D&&O&&!q&&a<z.length;a++){var q,j=z[a],te=U.p,re=j[2];Y>3?(q=re===V)&&(M=j[(C=j[4])?5:(C=3,3)],j[4]=j[5]=n):j[0]<=te&&((q=Y<2&&te<j[1])?(C=0,U.v=V,U.n=j[1]):te<re&&(q=Y<3||j[0]>V||V>re)&&(j[4]=Y,j[5]=V,U.n=re,C=0))}if(q||Y>1)return c;throw D=!0,V}return function(Y,V,q){if(O>1)throw TypeError("Generator is already running");for(D&&V===1&&X(V,q),C=V,M=q;(a=C<2?n:M)||!D;){P||(C?C<3?(C>1&&(U.n=-1),X(C,M)):U.n=M:U.v=M);try{if(O=2,P){if(C||(Y="next"),a=P[Y]){if(!(a=a.call(P,M)))throw TypeError("iterator result is not an object");if(!a.done)return a;M=a.value,C<2&&(C=0)}else C===1&&(a=P.return)&&a.call(P),C<2&&(M=TypeError("The iterator does not provide a '"+Y+"' method"),C=1);P=n}else if((a=(D=U.n<0)?M:w.call(A,U))!==c)break}catch(j){P=n,C=1,M=j}finally{O=1}}return{value:a,done:D}}}(d,y,E),!0),T}var c={};function l(){}function f(){}function p(){}a=Object.getPrototypeOf;var v=[][i]?a(a([][i]())):(t(a={},i,function(){return this}),a),m=p.prototype=l.prototype=Object.create(v);function g(d){return Object.setPrototypeOf?Object.setPrototypeOf(d,p):(d.__proto__=p,t(d,s,"GeneratorFunction")),d.prototype=Object.create(m),d}return f.prototype=p,t(m,"constructor",p),t(p,"constructor",f),f.displayName="GeneratorFunction",t(p,s,"GeneratorFunction"),t(m),t(m,s,"Generator"),t(m,i,function(){return this}),t(m,"toString",function(){return"[object Generator]"}),(e.exports=r=function(){return{w:u,m:g}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports}(Zr)),Zr.exports}var Kr={exports:{}},zr={exports:{}},Jr={exports:{}},xi;function Kc(){return xi||(xi=1,function(e){var t=$c(),r=Zc();function n(a,o){function i(u,c,l,f){try{var p=a[u](c),v=p.value;return v instanceof t?o.resolve(v.v).then(function(m){i("next",m,l,f)},function(m){i("throw",m,l,f)}):o.resolve(v).then(function(m){p.value=m,l(p)},function(m){return i("throw",m,l,f)})}catch(m){f(m)}}var s;this.next||(r(n.prototype),r(n.prototype,typeof Symbol=="function"&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),r(this,"_invoke",function(u,c,l){function f(){return new o(function(p,v){i(u,l,p,v)})}return s=s?s.then(f,f):f()},!0)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports}(Jr)),Jr.exports}var Mi;function zc(){return Mi||(Mi=1,function(e){var t=Xc(),r=Kc();function n(a,o,i,s,u){return new r(t().w(a,o,i,s),u||Promise)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports}(zr)),zr.exports}var Ii;function ip(){return Ii||(Ii=1,function(e){var t=zc();function r(n,a,o,i,s){var u=t(n,a,o,i,s);return u.next().then(function(c){return c.done?c.value:u.next()})}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports}(Kr)),Kr.exports}var Qr={exports:{}},Oi;function sp(){return Oi||(Oi=1,function(e){function t(r){var n=Object(r),a=[];for(var o in n)a.unshift(o);return function i(){for(;a.length;)if((o=a.pop())in n)return i.value=o,i.done=!1,i;return i.done=!0,i}}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(Qr)),Qr.exports}var en={exports:{}},tn={exports:{}},Li;function up(){return Li||(Li=1,function(e){function t(r){"@babel/helpers - typeof";return e.exports=t=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(tn)),tn.exports}var ki;function cp(){return ki||(ki=1,function(e){var t=up().default;function r(n){if(n!=null){var a=n[typeof Symbol=="function"&&Symbol.iterator||"@@iterator"],o=0;if(a)return a.call(n);if(typeof n.next=="function")return n;if(!isNaN(n.length))return{next:function(){return n&&o>=n.length&&(n=void 0),{value:n&&n[o++],done:!n}}}}throw new TypeError(t(n)+" is not iterable")}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports}(en)),en.exports}var Gi;function lp(){return Gi||(Gi=1,function(e){var t=$c(),r=Xc(),n=ip(),a=zc(),o=Kc(),i=sp(),s=cp();function u(){var c=r(),l=c.m(u),f=(Object.getPrototypeOf?Object.getPrototypeOf(l):l.__proto__).constructor;function p(g){var d=typeof g=="function"&&g.constructor;return!!d&&(d===f||(d.displayName||d.name)==="GeneratorFunction")}var v={throw:1,return:2,break:3,continue:3};function m(g){var d,h;return function(y){d||(d={stop:function(){return h(y.a,2)},catch:function(){return y.v},abrupt:function(_,T){return h(y.a,v[_],T)},delegateYield:function(_,T,w){return d.resultName=T,h(y.d,s(_),w)},finish:function(_){return h(y.f,_)}},h=function(_,T,w){y.p=d.prev,y.n=d.next;try{return _(T,w)}finally{d.next=y.n}}),d.resultName&&(d[d.resultName]=y.v,d.resultName=void 0),d.sent=y.v,d.next=y.n;try{return g.call(this,d)}finally{y.p=d.prev,y.n=d.next}}}return(e.exports=u=function(){return{wrap:function(h,y,E,_){return c.w(m(h),y,E,_&&_.reverse())},isGeneratorFunction:p,mark:c.m,awrap:function(h,y){return new t(h,y)},AsyncIterator:o,async:function(h,y,E,_,T){return(p(y)?a:n)(m(h),y,E,_,T)},keys:i,values:s}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=u,e.exports.__esModule=!0,e.exports.default=e.exports}(Yr)),Yr.exports}var rn,Di;function fp(){if(Di)return rn;Di=1;var e=lp()();rn=e;try{regeneratorRuntime=e}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=e:Function("r","regeneratorRuntime = r")(e)}return rn}var dp=fp();const R=ye(dp);var pp=Object.freeze({__proto__:null,get start(){return gl},get ensureJQuerySupport(){return ul},get setBootstrapMaxTime(){return bp},get setMountMaxTime(){return Sp},get setUnmountMaxTime(){return Ep},get setUnloadMaxTime(){return _p},get registerApplication(){return Rp},get unregisterApplication(){return ml},get getMountedApps(){return ll},get getAppStatus(){return dl},get unloadApplication(){return hl},get checkActivityFunctions(){return pl},get getAppNames(){return fl},get pathToActiveWhen(){return vl},get navigateToUrl(){return Ho},get triggerAppChange(){return xp},get addErrorHandler(){return mp},get removeErrorHandler(){return hp},get mountRootParcel(){return nl},get NOT_LOADED(){return Le},get LOADING_SOURCE_CODE(){return xr},get NOT_BOOTSTRAPPED(){return wt},get BOOTSTRAPPING(){return Jc},get NOT_MOUNTED(){return ke},get MOUNTING(){return vp},get UPDATING(){return Qc},get LOAD_ERROR(){return bt},get MOUNTED(){return he},get UNLOADING(){return io},get UNMOUNTING(){return el},get SKIP_BECAUSE_BROKEN(){return J}});function Se(e){return(Se=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function or(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Fi=(typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{}).CustomEvent,qe=function(){try{var e=new Fi("cat",{detail:{foo:"bar"}});return e.type==="cat"&&e.detail.foo==="bar"}catch{}return!1}()?Fi:typeof document<"u"&&typeof document.createEvent=="function"?function(e,t){var r=document.createEvent("CustomEvent");return t?r.initCustomEvent(e,t.bubbles,t.cancelable,t.detail):r.initCustomEvent(e,!1,!1,void 0),r}:function(e,t){var r=document.createEventObject();return r.type=e,t?(r.bubbles=!!t.bubbles,r.cancelable=!!t.cancelable,r.detail=t.detail):(r.bubbles=!1,r.cancelable=!1,r.detail=void 0),r},Ht=[];function Ze(e,t,r){var n=yt(e,t,r);Ht.length?Ht.forEach(function(a){return a(n)}):setTimeout(function(){throw n})}function mp(e){if(typeof e!="function")throw Error(x(28,!1));Ht.push(e)}function hp(e){if(typeof e!="function")throw Error(x(29,!1));var t=!1;return Ht=Ht.filter(function(r){var n=r===e;return t=t||n,!n}),t}function x(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),a=2;a<r;a++)n[a-2]=arguments[a];return"single-spa minified message #".concat(e,": ").concat("","See https://single-spa.js.org/error/?code=").concat(e).concat(n.length?"&arg=".concat(n.join("&arg=")):"")}function yt(e,t,r){var n,a="".concat(Mr(t)," '").concat(W(t),"' died in status ").concat(t.status,": ");if(e instanceof Error){try{e.message=a+e.message}catch{}n=e}else{console.warn(x(30,!1,t.status,W(t)));try{n=Error(a+JSON.stringify(e))}catch{n=e}}return n.appOrParcelName=W(t),t.status=r,n}var Le="NOT_LOADED",xr="LOADING_SOURCE_CODE",wt="NOT_BOOTSTRAPPED",Jc="BOOTSTRAPPING",ke="NOT_MOUNTED",vp="MOUNTING",he="MOUNTED",Qc="UPDATING",el="UNMOUNTING",io="UNLOADING",bt="LOAD_ERROR",J="SKIP_BECAUSE_BROKEN";function gp(e){return e.status===he}function so(e){try{return e.activeWhen(window.location)}catch(t){return Ze(t,e,J),!1}}function W(e){return e.name}function tl(e){return!!e.unmountThisParcel}function Mr(e){return tl(e)?"parcel":"application"}function Kt(){for(var e=arguments.length-1;e>0;e--)for(var t in arguments[e])t!=="__proto__"&&(arguments[e-1][t]=arguments[e][t]);return arguments[0]}function Ir(e,t){for(var r=0;r<e.length;r++)if(t(e[r]))return e[r];return null}function tt(e){return e&&(typeof e=="function"||(t=e,Array.isArray(t)&&!Ir(t,function(r){return typeof r!="function"})));var t}function He(e,t){var r=e[t]||[];(r=Array.isArray(r)?r:[r]).length===0&&(r=[function(){return Promise.resolve()}]);var n=Mr(e),a=W(e);return function(o){return r.reduce(function(i,s,u){return i.then(function(){var c=s(o);return rl(c)?c:Promise.reject(x(15,!1,n,a,t,u))})},Promise.resolve())}}function rl(e){return e&&typeof e.then=="function"&&typeof e.catch=="function"}function qo(e,t){return Promise.resolve().then(function(){return e.status!==wt?e:(e.status=Jc,e.bootstrap?zt(e,"bootstrap").then(r).catch(function(n){if(t)throw yt(n,e,J);return Ze(n,e,J),e}):Promise.resolve().then(r))});function r(){return e.status=ke,e}}function Or(e,t){return Promise.resolve().then(function(){if(e.status!==he)return e;e.status=el;var r=Object.keys(e.parcels).map(function(a){return e.parcels[a].unmountThisParcel()});return Promise.all(r).then(n,function(a){return n().then(function(){var o=Error(a.message);if(t)throw yt(o,e,J);Ze(o,e,J)})}).then(function(){return e});function n(){return zt(e,"unmount").then(function(){e.status=ke}).catch(function(a){if(t)throw yt(a,e,J);Ze(a,e,J)})}})}var Ni=!1,ji=!1;function uo(e,t){return Promise.resolve().then(function(){return e.status!==ke?e:(Ni||(window.dispatchEvent(new qe("single-spa:before-first-mount")),Ni=!0),zt(e,"mount").then(function(){return e.status=he,ji||(window.dispatchEvent(new qe("single-spa:first-mount")),ji=!0),e}).catch(function(r){return e.status=he,Or(e,!0).then(n,n);function n(){if(t)throw yt(r,e,J);return Ze(r,e,J),e}}))})}var yp=0,wp={parcels:{}};function nl(){return al.apply(wp,arguments)}function al(e,t){var r=this;if(!e||Se(e)!=="object"&&typeof e!="function")throw Error(x(2,!1));if(e.name&&typeof e.name!="string")throw Error(x(3,!1,Se(e.name)));if(Se(t)!=="object")throw Error(x(4,!1,name,Se(t)));if(!t.domElement)throw Error(x(5,!1,name));var n,a=yp++,o=typeof e=="function",i=o?e:function(){return Promise.resolve(e)},s={id:a,parcels:{},status:o?xr:wt,customProps:t,parentName:W(r),unmountThisParcel:function(){return p.then(function(){if(s.status!==he)throw Error(x(6,!1,name,s.status));return Or(s,!0)}).then(function(m){return s.parentName&&delete r.parcels[s.id],m}).then(function(m){return c(m),m}).catch(function(m){throw s.status=J,l(m),m})}};r.parcels[a]=s;var u=i();if(!u||typeof u.then!="function")throw Error(x(7,!1));var c,l,f=(u=u.then(function(m){if(!m)throw Error(x(8,!1));var g=m.name||"parcel-".concat(a);if(Object.prototype.hasOwnProperty.call(m,"bootstrap")&&!tt(m.bootstrap))throw Error(x(9,!1,g));if(!tt(m.mount))throw Error(x(10,!1,g));if(!tt(m.unmount))throw Error(x(11,!1,g));if(m.update&&!tt(m.update))throw Error(x(12,!1,g));var d=He(m,"bootstrap"),h=He(m,"mount"),y=He(m,"unmount");s.status=wt,s.name=g,s.bootstrap=d,s.mount=h,s.unmount=y,s.timeouts=il(m.timeouts),m.update&&(s.update=He(m,"update"),n.update=function(E){return s.customProps=E,Je(function(_){return Promise.resolve().then(function(){if(_.status!==he)throw Error(x(32,!1,W(_)));return _.status=Qc,zt(_,"update").then(function(){return _.status=he,_}).catch(function(T){throw yt(T,_,J)})})}(s))})})).then(function(){return qo(s,!0)}),p=f.then(function(){return uo(s,!0)}),v=new Promise(function(m,g){c=m,l=g});return n={mount:function(){return Je(Promise.resolve().then(function(){if(s.status!==ke)throw Error(x(13,!1,name,s.status));return r.parcels[a]=s,uo(s)}))},unmount:function(){return Je(s.unmountThisParcel())},getStatus:function(){return s.status},loadPromise:Je(u),bootstrapPromise:Je(f),mountPromise:Je(p),unmountPromise:Je(v)}}function Je(e){return e.then(function(){return null})}function ol(e){var t=W(e),r=typeof e.customProps=="function"?e.customProps(t,window.location):e.customProps;(Se(r)!=="object"||r===null||Array.isArray(r))&&(r={},console.warn(x(40,!1),t,r));var n=Kt({},r,{name:t,mountParcel:al.bind(e),singleSpa:pp});return tl(e)&&(n.unmountSelf=e.unmountThisParcel),n}var St={bootstrap:{millis:4e3,dieOnTimeout:!1,warningMillis:1e3},mount:{millis:3e3,dieOnTimeout:!1,warningMillis:1e3},unmount:{millis:3e3,dieOnTimeout:!1,warningMillis:1e3},unload:{millis:3e3,dieOnTimeout:!1,warningMillis:1e3},update:{millis:3e3,dieOnTimeout:!1,warningMillis:1e3}};function bp(e,t,r){if(typeof e!="number"||e<=0)throw Error(x(16,!1));St.bootstrap={millis:e,dieOnTimeout:t,warningMillis:r||1e3}}function Sp(e,t,r){if(typeof e!="number"||e<=0)throw Error(x(17,!1));St.mount={millis:e,dieOnTimeout:t,warningMillis:r||1e3}}function Ep(e,t,r){if(typeof e!="number"||e<=0)throw Error(x(18,!1));St.unmount={millis:e,dieOnTimeout:t,warningMillis:r||1e3}}function _p(e,t,r){if(typeof e!="number"||e<=0)throw Error(x(19,!1));St.unload={millis:e,dieOnTimeout:t,warningMillis:r||1e3}}function zt(e,t){var r=e.timeouts[t],n=r.warningMillis,a=Mr(e);return new Promise(function(o,i){var s=!1,u=!1;e[t](ol(e)).then(function(f){s=!0,o(f)}).catch(function(f){s=!0,i(f)}),setTimeout(function(){return l(1)},n),setTimeout(function(){return l(!0)},r.millis);var c=x(31,!1,t,a,W(e),r.millis);function l(f){if(!s){if(f===!0)u=!0,r.dieOnTimeout?i(Error(c)):console.error(c);else if(!u){var p=f,v=p*n;console.warn(c),v+n<r.millis&&setTimeout(function(){return l(p+1)},n)}}}})}function il(e){var t={};for(var r in St)t[r]=Kt({},St[r],e&&e[r]||{});return t}function co(e){return Promise.resolve().then(function(){return e.loadPromise?e.loadPromise:e.status!==Le&&e.status!==bt?e:(e.status=xr,e.loadPromise=Promise.resolve().then(function(){var n=e.loadApp(ol(e));if(!rl(n))throw r=!0,Error(x(33,!1,W(e)));return n.then(function(a){var o;e.loadErrorTime=null,Se(t=a)!=="object"&&(o=34),Object.prototype.hasOwnProperty.call(t,"bootstrap")&&!tt(t.bootstrap)&&(o=35),tt(t.mount)||(o=36),tt(t.unmount)||(o=37);var i=Mr(t);if(o){var s;try{s=JSON.stringify(t)}catch{}return console.error(x(o,!1,i,W(e),s),t),Ze(void 0,e,J),e}return t.devtools&&t.devtools.overlays&&(e.devtools.overlays=Kt({},e.devtools.overlays,t.devtools.overlays)),e.status=wt,e.bootstrap=He(t,"bootstrap"),e.mount=He(t,"mount"),e.unmount=He(t,"unmount"),e.unload=He(t,"unload"),e.timeouts=il(t.timeouts),delete e.loadPromise,e})}).catch(function(n){var a;return delete e.loadPromise,r?a=J:(a=bt,e.loadErrorTime=new Date().getTime()),Ze(n,e,a),e}));var t,r})}var sl,At=typeof window<"u",Gt={hashchange:[],popstate:[]},yr=["hashchange","popstate"];function Ho(e){var t;if(typeof e=="string")t=e;else if(this&&this.href)t=this.href;else{if(!(e&&e.currentTarget&&e.currentTarget.href&&e.preventDefault))throw Error(x(14,!1));t=e.currentTarget.href,e.preventDefault()}var r=Hi(window.location.href),n=Hi(t);t.indexOf("#")===0?window.location.hash=n.hash:r.host!==n.host&&n.host?window.location.href=t:n.pathname===r.pathname&&n.search===r.search?window.location.hash=n.hash:window.history.pushState(null,null,t)}function Vi(e){var t=this;if(e){var r=e[0].type;yr.indexOf(r)>=0&&Gt[r].forEach(function(n){try{n.apply(t,e)}catch(a){setTimeout(function(){throw a})}})}}function Wi(){Xe([],arguments)}function qi(e,t){return function(){var r=window.location.href,n=e.apply(this,arguments),a=window.location.href;return sl&&r===a||(yl()?window.dispatchEvent(Pp(window.history.state,t)):Xe([])),n}}function Pp(e,t){var r;try{r=new PopStateEvent("popstate",{state:e})}catch{(r=document.createEvent("PopStateEvent")).initPopStateEvent("popstate",!1,!1,e)}return r.singleSpa=!0,r.singleSpaTrigger=t,r}if(At){window.addEventListener("hashchange",Wi),window.addEventListener("popstate",Wi);var Tp=window.addEventListener,Cp=window.removeEventListener;window.addEventListener=function(e,t){if(!(typeof t=="function"&&yr.indexOf(e)>=0)||Ir(Gt[e],function(r){return r===t}))return Tp.apply(this,arguments);Gt[e].push(t)},window.removeEventListener=function(e,t){if(!(typeof t=="function"&&yr.indexOf(e)>=0))return Cp.apply(this,arguments);Gt[e]=Gt[e].filter(function(r){return r!==t})},window.history.pushState=qi(window.history.pushState,"pushState"),window.history.replaceState=qi(window.history.replaceState,"replaceState"),window.singleSpaNavigate?console.warn(x(41,!1)):window.singleSpaNavigate=Ho}function Hi(e){var t=document.createElement("a");return t.href=e,t}var Bi=!1;function ul(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.jQuery;if(e||window.$&&window.$.fn&&window.$.fn.jquery&&(e=window.$),e&&!Bi){var t=e.fn.on,r=e.fn.off;e.fn.on=function(n,a){return Ui.call(this,t,window.addEventListener,n,a,arguments)},e.fn.off=function(n,a){return Ui.call(this,r,window.removeEventListener,n,a,arguments)},Bi=!0}}function Ui(e,t,r,n,a){return typeof r!="string"?e.apply(this,a):(r.split(/\s+/).forEach(function(o){yr.indexOf(o)>=0&&(t(o,n),r=r.replace(o,""))}),r.trim()===""?this:e.apply(this,a))}var Et={};function lo(e){return Promise.resolve().then(function(){var t=Et[W(e)];if(!t)return e;if(e.status===Le)return Yi(e,t),e;if(e.status===io)return t.promise.then(function(){return e});if(e.status!==ke&&e.status!==bt)return e;var r=e.status===bt?Promise.resolve():zt(e,"unload");return e.status=io,r.then(function(){return Yi(e,t),e}).catch(function(n){return function(a,o,i){delete Et[W(a)],delete a.bootstrap,delete a.mount,delete a.unmount,delete a.unload,Ze(i,a,J),o.reject(i)}(e,t,n),e})})}function Yi(e,t){delete Et[W(e)],delete e.bootstrap,delete e.mount,delete e.unmount,delete e.unload,e.status=Le,t.resolve()}function $i(e,t,r,n){Et[W(e)]={app:e,resolve:r,reject:n},Object.defineProperty(Et[W(e)],"promise",{get:t})}function cl(e){return Et[e]}var ve=[];function Ap(){var e=[],t=[],r=[],n=[],a=new Date().getTime();return ve.forEach(function(o){var i=o.status!==J&&so(o);switch(o.status){case bt:i&&a-o.loadErrorTime>=200&&r.push(o);break;case Le:case xr:i&&r.push(o);break;case wt:case ke:!i&&cl(W(o))?e.push(o):i&&n.push(o);break;case he:i||t.push(o)}}),{appsToUnload:e,appsToUnmount:t,appsToLoad:r,appsToMount:n}}function ll(){return ve.filter(gp).map(W)}function fl(){return ve.map(W)}function dl(e){var t=Ir(ve,function(r){return W(r)===e});return t?t.status:null}function Rp(e,t,r,n){var a=function(o,i,s,u){var c,l={name:null,loadApp:null,activeWhen:null,customProps:null};return Se(o)==="object"?(function(f){if(Array.isArray(f)||f===null)throw Error(x(39,!1));var p=["name","app","activeWhen","customProps"],v=Object.keys(f).reduce(function(g,d){return p.indexOf(d)>=0?g:g.concat(d)},[]);if(v.length!==0)throw Error(x(38,!1,p.join(", "),v.join(", ")));if(typeof f.name!="string"||f.name.length===0||Se(f.app)!=="object"&&typeof f.app!="function")throw Error(x(20,!1));var m=function(g){return typeof g=="string"||typeof g=="function"};if(!(m(f.activeWhen)||Array.isArray(f.activeWhen)&&f.activeWhen.every(m)))throw Error(x(24,!1));if(!Xi(f.customProps))throw Error(x(22,!1))}(o),l.name=o.name,l.loadApp=o.app,l.activeWhen=o.activeWhen,l.customProps=o.customProps):(function(f,p,v,m){if(typeof f!="string"||f.length===0)throw Error(x(20,!1));if(!p)throw Error(x(23,!1));if(typeof v!="function")throw Error(x(24,!1));if(!Xi(m))throw Error(x(22,!1))}(o,i,s,u),l.name=o,l.loadApp=i,l.activeWhen=s,l.customProps=u),l.loadApp=typeof(c=l.loadApp)!="function"?function(){return Promise.resolve(c)}:c,l.customProps=function(f){return f||{}}(l.customProps),l.activeWhen=function(f){var p=Array.isArray(f)?f:[f];return p=p.map(function(v){return typeof v=="function"?v:vl(v)}),function(v){return p.some(function(m){return m(v)})}}(l.activeWhen),l}(e,t,r,n);if(fl().indexOf(a.name)!==-1)throw Error(x(21,!1,a.name));ve.push(Kt({loadErrorTime:null,status:Le,parcels:{},devtools:{overlays:{options:{},selectors:[]}}},a)),At&&(ul(),Xe())}function pl(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.location;return ve.filter(function(t){return t.activeWhen(e)}).map(W)}function ml(e){if(ve.filter(function(t){return W(t)===e}).length===0)throw Error(x(25,!1,e));return hl(e).then(function(){var t=ve.map(W).indexOf(e);ve.splice(t,1)})}function hl(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{waitForUnmount:!1};if(typeof e!="string")throw Error(x(26,!1));var r=Ir(ve,function(i){return W(i)===e});if(!r)throw Error(x(27,!1,e));var n,a=cl(W(r));if(t&&t.waitForUnmount){if(a)return a.promise;var o=new Promise(function(i,s){$i(r,function(){return o},i,s)});return o}return a?(n=a.promise,Zi(r,a.resolve,a.reject)):n=new Promise(function(i,s){$i(r,function(){return n},i,s),Zi(r,i,s)}),n}function Zi(e,t,r){Or(e).then(lo).then(function(){t(),setTimeout(function(){Xe()})}).catch(r)}function Xi(e){return!e||typeof e=="function"||Se(e)==="object"&&e!==null&&!Array.isArray(e)}function vl(e,t){var r=function(n,a){var o=0,i=!1,s="^";n[0]!=="/"&&(n="/"+n);for(var u=0;u<n.length;u++){var c=n[u];(!i&&c===":"||i&&c==="/")&&l(u)}return l(n.length),new RegExp(s,"i");function l(f){var p=n.slice(o,f).replace(/[|\\{}()[\]^$+*?.]/g,"\\$&");if(s+=i?"[^/]+/?":p,f===n.length)if(i)a&&(s+="$");else{var v=a?"":".*";s=s.charAt(s.length-1)==="/"?"".concat(s).concat(v,"$"):"".concat(s,"(/").concat(v,")?(#.*)?$")}i=!i,o=f}}(e,t);return function(n){var a=n.origin;a||(a="".concat(n.protocol,"//").concat(n.host));var o=n.href.replace(a,"").replace(n.search,"").split("?")[0];return r.test(o)}}var nn=!1,ir=[],Ki=At&&window.location.href;function xp(){return Xe()}function Xe(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0;if(nn)return new Promise(function(h,y){ir.push({resolve:h,reject:y,eventArguments:t})});var r,n=Ap(),a=n.appsToUnload,o=n.appsToUnmount,i=n.appsToLoad,s=n.appsToMount,u=!1,c=Ki,l=Ki=window.location.href;return yl()?(nn=!0,r=a.concat(i,o,s),v()):(r=i,p());function f(){u=!0}function p(){return Promise.resolve().then(function(){var h=i.map(co);return Promise.all(h).then(g).then(function(){return[]}).catch(function(y){throw g(),y})})}function v(){return Promise.resolve().then(function(){if(window.dispatchEvent(new qe(r.length===0?"single-spa:before-no-app-change":"single-spa:before-app-change",d(!0))),window.dispatchEvent(new qe("single-spa:before-routing-event",d(!0,{cancelNavigation:f}))),u)return window.dispatchEvent(new qe("single-spa:before-mount-routing-event",d(!0))),m(),void Ho(c);var h=a.map(lo),y=o.map(Or).map(function(w){return w.then(lo)}).concat(h),E=Promise.all(y);E.then(function(){window.dispatchEvent(new qe("single-spa:before-mount-routing-event",d(!0)))});var _=i.map(function(w){return co(w).then(function(A){return zi(A,E)})}),T=s.filter(function(w){return i.indexOf(w)<0}).map(function(w){return zi(w,E)});return E.catch(function(w){throw g(),w}).then(function(){return g(),Promise.all(_.concat(T)).catch(function(w){throw e.forEach(function(A){return A.reject(w)}),w}).then(m)})})}function m(){var h=ll();e.forEach(function(_){return _.resolve(h)});try{var y=r.length===0?"single-spa:no-app-change":"single-spa:app-change";window.dispatchEvent(new qe(y,d())),window.dispatchEvent(new qe("single-spa:routing-event",d()))}catch(_){setTimeout(function(){throw _})}if(nn=!1,ir.length>0){var E=ir;ir=[],Xe(E)}return h}function g(){e.forEach(function(h){Vi(h.eventArguments)}),Vi(t)}function d(){var h,y=arguments.length>0&&arguments[0]!==void 0&&arguments[0],E=arguments.length>1?arguments[1]:void 0,_={},T=(or(h={},he,[]),or(h,ke,[]),or(h,Le,[]),or(h,J,[]),h);y?(i.concat(s).forEach(function(b,P){A(b,he)}),a.forEach(function(b){A(b,Le)}),o.forEach(function(b){A(b,ke)})):r.forEach(function(b){A(b)});var w={detail:{newAppStatuses:_,appsByNewStatus:T,totalAppChanges:r.length,originalEvent:t?.[0],oldUrl:c,newUrl:l,navigationIsCanceled:u}};return E&&Kt(w.detail,E),w;function A(b,P){var C=W(b);P=P||dl(C),_[C]=P,(T[P]=T[P]||[]).push(C)}}}function zi(e,t){return so(e)?qo(e).then(function(r){return t.then(function(){return so(r)?uo(r):r})}):t.then(function(){return e})}var Bo=!1;function gl(e){var t;Bo=!0,e&&e.urlRerouteOnly&&(t=e.urlRerouteOnly,sl=t),At&&Xe()}function yl(){return Bo}At&&setTimeout(function(){Bo||console.warn(x(1,!1))},5e3);var Mp={getRawAppData:function(){return[].concat(ve)},reroute:Xe,NOT_LOADED:Le,toLoadPromise:co,toBootstrapPromise:qo,unregisterApplication:ml};At&&window.__SINGLE_SPA_DEVTOOLS__&&(window.__SINGLE_SPA_DEVTOOLS__.exposedMethods=Mp);var an,Ji;function Ip(){if(Ji)return an;Ji=1;var e=Oo(),t=Cc(),r=Pt(),n=e?e.isConcatSpreadable:void 0;function a(o){return r(o)||t(o)||!!(n&&o&&o[n])}return an=a,an}var on,Qi;function Op(){if(Qi)return on;Qi=1;var e=Lo(),t=Ip();function r(n,a,o,i,s){var u=-1,c=n.length;for(o||(o=t),s||(s=[]);++u<c;){var l=n[u];a>0&&o(l)?a>1?r(l,a-1,o,i,s):e(s,l):i||(s[s.length]=l)}return s}return on=r,on}var sn,es;function Uo(){if(es)return sn;es=1;function e(t,r){var n=-1,a=t.length;for(r||(r=Array(a));++n<a;)r[n]=t[n];return r}return sn=e,sn}var un,ts;function Lp(){if(ts)return un;ts=1;var e=Lo(),t=Op(),r=Uo(),n=Pt();function a(){var o=arguments.length;if(!o)return[];for(var i=Array(o-1),s=arguments[0],u=o;u--;)i[u-1]=arguments[u];return e(n(s)?r(s):[s],t(i,1))}return un=a,un}var kp=Lp();const wl=ye(kp);var cn,rs;function bl(){if(rs)return cn;rs=1;var e=Zf(),t=function(){try{var r=e(Object,"defineProperty");return r({},"",{}),r}catch{}}();return cn=t,cn}var ln,ns;function Yo(){if(ns)return ln;ns=1;var e=bl();function t(r,n,a){n=="__proto__"&&e?e(r,n,{configurable:!0,enumerable:!0,value:a,writable:!0}):r[n]=a}return ln=t,ln}var fn,as;function Sl(){if(as)return fn;as=1;var e=Yo(),t=ko();function r(n,a,o){(o!==void 0&&!t(n[a],o)||o===void 0&&!(a in n))&&e(n,a,o)}return fn=r,fn}var dn,os;function Gp(){if(os)return dn;os=1;function e(t){return function(r,n,a){for(var o=-1,i=Object(r),s=a(r),u=s.length;u--;){var c=s[t?u:++o];if(n(i[c],c,i)===!1)break}return r}}return dn=e,dn}var pn,is;function El(){if(is)return pn;is=1;var e=Gp(),t=e();return pn=t,pn}var Dt={exports:{}};Dt.exports;var ss;function _l(){return ss||(ss=1,function(e,t){var r=Xf(),n=t&&!t.nodeType&&t,a=n&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===n,i=o?r.Buffer:void 0,s=i?i.allocUnsafe:void 0;function u(c,l){if(l)return c.slice();var f=c.length,p=s?s(f):new c.constructor(f);return c.copy(p),p}e.exports=u}(Dt,Dt.exports)),Dt.exports}var mn,us;function $o(){if(us)return mn;us=1;var e=Kf();function t(r){var n=new r.constructor(r.byteLength);return new e(n).set(new e(r)),n}return mn=t,mn}var hn,cs;function Pl(){if(cs)return hn;cs=1;var e=$o();function t(r,n){var a=n?e(r.buffer):r.buffer;return new r.constructor(a,r.byteOffset,r.length)}return hn=t,hn}var vn,ls;function Dp(){if(ls)return vn;ls=1;var e=st(),t=Object.create,r=function(){function n(){}return function(a){if(!e(a))return{};if(t)return t(a);n.prototype=a;var o=new n;return n.prototype=void 0,o}}();return vn=r,vn}var gn,fs;function Zo(){if(fs)return gn;fs=1;var e=zf(),t=e(Object.getPrototypeOf,Object);return gn=t,gn}var yn,ds;function Tl(){if(ds)return yn;ds=1;var e=Dp(),t=Zo(),r=Ac();function n(a){return typeof a.constructor=="function"&&!r(a)?e(t(a)):{}}return yn=n,yn}var wn,ps;function Cl(){if(ps)return wn;ps=1;var e=Ar(),t=Zt();function r(n){return t(n)&&e(n)}return wn=r,wn}var bn,ms;function Fp(){if(ms)return bn;ms=1;var e=Rc(),t=Zo(),r=Zt(),n="[object Object]",a=Function.prototype,o=Object.prototype,i=a.toString,s=o.hasOwnProperty,u=i.call(Object);function c(l){if(!r(l)||e(l)!=n)return!1;var f=t(l);if(f===null)return!0;var p=s.call(f,"constructor")&&f.constructor;return typeof p=="function"&&p instanceof p&&i.call(p)==u}return bn=c,bn}var Sn,hs;function Al(){if(hs)return Sn;hs=1;function e(t,r){if(!(r==="constructor"&&typeof t[r]=="function")&&r!="__proto__")return t[r]}return Sn=e,Sn}var En,vs;function Rl(){if(vs)return En;vs=1;var e=Yo(),t=ko(),r=Object.prototype,n=r.hasOwnProperty;function a(o,i,s){var u=o[i];(!(n.call(o,i)&&t(u,s))||s===void 0&&!(i in o))&&e(o,i,s)}return En=a,En}var _n,gs;function Jt(){if(gs)return _n;gs=1;var e=Rl(),t=Yo();function r(n,a,o,i){var s=!o;o||(o={});for(var u=-1,c=a.length;++u<c;){var l=a[u],f=i?i(o[l],n[l],l,o,n):void 0;f===void 0&&(f=n[l]),s?t(o,l,f):e(o,l,f)}return o}return _n=r,_n}var Pn,ys;function Np(){if(ys)return Pn;ys=1;function e(t){var r=[];if(t!=null)for(var n in Object(t))r.push(n);return r}return Pn=e,Pn}var Tn,ws;function jp(){if(ws)return Tn;ws=1;var e=st(),t=Ac(),r=Np(),n=Object.prototype,a=n.hasOwnProperty;function o(i){if(!e(i))return r(i);var s=t(i),u=[];for(var c in i)c=="constructor"&&(s||!a.call(i,c))||u.push(c);return u}return Tn=o,Tn}var Cn,bs;function Qt(){if(bs)return Cn;bs=1;var e=Jf(),t=jp(),r=Ar();function n(a){return r(a)?e(a,!0):t(a)}return Cn=n,Cn}var An,Ss;function Vp(){if(Ss)return An;Ss=1;var e=Jt(),t=Qt();function r(n){return e(n,t(n))}return An=r,An}var Rn,Es;function Wp(){if(Es)return Rn;Es=1;var e=Sl(),t=_l(),r=Pl(),n=Uo(),a=Tl(),o=Cc(),i=Pt(),s=Cl(),u=xc(),c=Mc(),l=st(),f=Fp(),p=Qf(),v=Al(),m=Vp();function g(d,h,y,E,_,T,w){var A=v(d,y),b=v(h,y),P=w.get(b);if(P){e(d,y,P);return}var C=T?T(A,b,y+"",d,h,w):void 0,M=C===void 0;if(M){var O=i(b),z=!O&&u(b),D=!O&&!z&&p(b);C=b,O||z||D?i(A)?C=A:s(A)?C=n(A):z?(M=!1,C=t(b,!0)):D?(M=!1,C=r(b,!0)):C=[]:f(b)||o(b)?(C=A,o(A)?C=m(A):(!l(A)||c(A))&&(C=a(b))):M=!1}M&&(w.set(b,C),_(C,b,E,T,w),w.delete(b)),e(d,y,C)}return Rn=g,Rn}var xn,_s;function qp(){if(_s)return xn;_s=1;var e=Ic(),t=Sl(),r=El(),n=Wp(),a=st(),o=Qt(),i=Al();function s(u,c,l,f,p){u!==c&&r(c,function(v,m){if(p||(p=new e),a(v))n(u,c,m,l,s,f,p);else{var g=f?f(i(u,m),v,m+"",u,c,p):void 0;g===void 0&&(g=v),t(u,m,g)}},o)}return xn=s,xn}var Mn,Ps;function Xo(){if(Ps)return Mn;Ps=1;function e(t){return t}return Mn=e,Mn}var In,Ts;function Hp(){if(Ts)return In;Ts=1;function e(t,r,n){switch(n.length){case 0:return t.call(r);case 1:return t.call(r,n[0]);case 2:return t.call(r,n[0],n[1]);case 3:return t.call(r,n[0],n[1],n[2])}return t.apply(r,n)}return In=e,In}var On,Cs;function Bp(){if(Cs)return On;Cs=1;var e=Hp(),t=Math.max;function r(n,a,o){return a=t(a===void 0?n.length-1:a,0),function(){for(var i=arguments,s=-1,u=t(i.length-a,0),c=Array(u);++s<u;)c[s]=i[a+s];s=-1;for(var l=Array(a+1);++s<a;)l[s]=i[s];return l[a]=o(c),e(n,this,l)}}return On=r,On}var Ln,As;function Up(){if(As)return Ln;As=1;function e(t){return function(){return t}}return Ln=e,Ln}var kn,Rs;function Yp(){if(Rs)return kn;Rs=1;var e=Up(),t=bl(),r=Xo(),n=t?function(a,o){return t(a,"toString",{configurable:!0,enumerable:!1,value:e(o),writable:!0})}:r;return kn=n,kn}var Gn,xs;function $p(){if(xs)return Gn;xs=1;var e=800,t=16,r=Date.now;function n(a){var o=0,i=0;return function(){var s=r(),u=t-(s-i);if(i=s,u>0){if(++o>=e)return arguments[0]}else o=0;return a.apply(void 0,arguments)}}return Gn=n,Gn}var Dn,Ms;function Zp(){if(Ms)return Dn;Ms=1;var e=Yp(),t=$p(),r=t(e);return Dn=r,Dn}var Fn,Is;function xl(){if(Is)return Fn;Is=1;var e=Xo(),t=Bp(),r=Zp();function n(a,o){return r(t(a,o,e),a+"")}return Fn=n,Fn}var Nn,Os;function Xp(){if(Os)return Nn;Os=1;var e=ko(),t=Ar(),r=ed(),n=st();function a(o,i,s){if(!n(s))return!1;var u=typeof i;return(u=="number"?t(s)&&r(i,s.length):u=="string"&&i in s)?e(s[i],o):!1}return Nn=a,Nn}var jn,Ls;function Kp(){if(Ls)return jn;Ls=1;var e=xl(),t=Xp();function r(n){return e(function(a,o){var i=-1,s=o.length,u=s>1?o[s-1]:void 0,c=s>2?o[2]:void 0;for(u=n.length>3&&typeof u=="function"?(s--,u):void 0,c&&t(o[0],o[1],c)&&(u=s<3?void 0:u,s=1),a=Object(a);++i<s;){var l=o[i];l&&n(a,l,i,u)}return a})}return jn=r,jn}var Vn,ks;function zp(){if(ks)return Vn;ks=1;var e=qp(),t=Kp(),r=t(function(n,a,o,i){e(n,a,o,i)});return Vn=r,Vn}var Jp=zp();const Ml=ye(Jp);var Wn,Gs;function Il(){if(Gs)return Wn;Gs=1;function e(t,r){for(var n=-1,a=t==null?0:t.length;++n<a&&r(t[n],n,t)!==!1;);return t}return Wn=e,Wn}var qn,Ds;function Qp(){if(Ds)return qn;Ds=1;var e=El(),t=Go();function r(n,a){return n&&e(n,a,t)}return qn=r,qn}var Hn,Fs;function em(){if(Fs)return Hn;Fs=1;var e=Ar();function t(r,n){return function(a,o){if(a==null)return a;if(!e(a))return r(a,o);for(var i=a.length,s=n?i:-1,u=Object(a);(n?s--:++s<i)&&o(u[s],s,u)!==!1;);return a}}return Hn=t,Hn}var Bn,Ns;function tm(){if(Ns)return Bn;Ns=1;var e=Qp(),t=em(),r=t(e);return Bn=r,Bn}var Un,js;function rm(){if(js)return Un;js=1;var e=Xo();function t(r){return typeof r=="function"?r:e}return Un=t,Un}var Yn,Vs;function nm(){if(Vs)return Yn;Vs=1;var e=Il(),t=tm(),r=rm(),n=Pt();function a(o,i){var s=n(o)?e:t;return s(o,r(i))}return Yn=a,Yn}var am=nm();const om=ye(am);function Ol(e,t){return Promise.all(e.map(function(r,n){return r.then(function(a){return{status:"fulfilled",value:a}}).catch(function(a){if(t!=null&&t(n))throw a;return{status:"rejected",reason:a}})}))}var im=typeof navigator<"u"&&navigator.userAgent.indexOf("Trident")!==-1;function Ll(e,t){if(!e.hasOwnProperty(t)||!isNaN(t)&&t<e.length)return!0;if(im)try{return e[t]&&typeof window<"u"&&e[t].parent===window}catch{return!0}else return!1}var dr,pr,fo;function sm(e){var t=0,r,n=!1;for(var a in e)if(!Ll(e,a)){for(var o=0;o<window.frames.length&&!n;o++){var i=window.frames[o];if(i===e[a]){n=!0;break}}if(!n&&(t===0&&a!==dr||t===1&&a!==pr))return a;t++,r=a}if(r!==fo)return r}function um(e){dr=pr=void 0;for(var t in e)Ll(e,t)||(dr?pr||(pr=t):dr=t,fo=t);return fo}function Ko(e){var t=e.indexOf(">")+1,r=e.lastIndexOf("<");return e.substring(t,r)}function po(e){if(me(e)==="object")return"/";try{var t=new URL(e,location.href),r=t.origin,n=t.pathname,a=n.split("/");return a.pop(),"".concat(r).concat(a.join("/"),"/")}catch(o){return console.warn(o),""}}function cm(){var e=document.createElement("script");return"noModule"in e}var lm=window.requestIdleCallback||function(t){var r=Date.now();return setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-r))}})},1)};function fm(e,t){if(!t||!e.headers)return e.text();var r=e.headers.get("Content-Type");if(!r)return e.text();var n="utf-8",a=r.split(";");if(a.length===2){var o=a[1].split("="),i=Do(o,2),s=i[1],u=s&&s.trim();u&&(n=u)}return n.toUpperCase()==="UTF-8"?e.text():e.blob().then(function(c){return new Promise(function(l,f){var p=new window.FileReader;p.onload=function(){l(p.result)},p.onerror=f,p.readAsText(c,n)})})}var $n={};function dm(e,t){var r=e;if(!$n[r]){var n="(function(){".concat(t,"})");$n[r]=(0,eval)(n)}var a=$n[r];a.call(window)}function Ws(e){var t=new DOMParser,r='<script src="'.concat(e,'"><\/script>'),n=t.parseFromString(r,"text/html");return n.scripts[0].src}var pm=/(<script[\s\S]*?>)[\s\S]*?<\/script>/gi,mm=/<(script)\s+((?!type=('|")text\/ng\x2Dtemplate\3)[\s\S])*?>[\s\S]*?<\/\1>/i,qs=/.*\ssrc=('|")?([^>'"\s]+)/,hm=/.*\stype=('|")?([^>'"\s]+)/,vm=/.*\sentry\s*.*/,gm=/.*\sasync\s*.*/,ym=/.*\scrossorigin=('|")?use-credentials\1/,wm=/.*\snomodule\s*.*/,bm=/.*\stype=('|")?module('|")?\s*.*/,Sm=/<(link)\s+[\s\S]*?>/ig,Em=/\srel=('|")?(preload|prefetch)\1/,Hs=/.*\shref=('|")?([^>'"\s]+)/,_m=/.*\sas=('|")?font\1.*/,Pm=/<style[^>]*>[\s\S]*?<\/style>/gi,Tm=/\s+rel=('|")?stylesheet\1.*/,Cm=/.*\shref=('|")?([^>'"\s]+)/,Am=/<!--([\s\S]*?)-->/g,Rm=/<link(\s+|\s+[\s\S]+\s+)ignore(\s*|\s+[\s\S]*|=[\s\S]*)>/i,xm=/<style(\s+|\s+[\s\S]+\s+)ignore(\s*|\s+[\s\S]*|=[\s\S]*)>/i,Mm=/<script(\s+|\s+[\s\S]+\s+)ignore(\s*|\s+[\s\S]*|=[\s\S]*)>/i;function Bs(e){return e.startsWith("http://")||e.startsWith("https://")}function Us(e,t){return new URL(e,t).toString()}function Im(e){var t=["text/javascript","module","application/javascript","text/ecmascript","application/ecmascript"];return!e||t.indexOf(e)!==-1}var wr=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return"<!-- ".concat(r?"prefetch/preload":""," link ").concat(t," replaced by import-html-entry -->")},kl=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return"<!-- ".concat(n?"cors":""," ").concat(r?"async":""," script ").concat(t," replaced by import-html-entry -->")},Om="<!-- inline scripts replaced by import-html-entry -->",sr=function(t){return"<!-- ignore asset ".concat(t||"file"," replaced by import-html-entry -->")},Ys=function(t,r){return"<!-- ".concat(r?"nomodule":"module"," script ").concat(t," ignored by import-html-entry -->")};function Lm(e,t,r){var n=[],a=[],o=null,i=cm(),s=e.replace(Am,"").replace(Sm,function(c){var l=!!c.match(Tm);if(l){var f=c.match(Cm),p=c.match(Rm);if(f){var v=f&&f[2],m=v;return v&&!Bs(v)&&(m=Us(v,t)),p?sr(m):(m=Ws(m),a.push(m),wr(m))}}var g=c.match(Em)&&c.match(Hs)&&!c.match(_m);if(g){var d=c.match(Hs),h=Do(d,3),y=h[2];return wr(y,!0)}return c}).replace(Pm,function(c){return xm.test(c)?sr("style file"):c}).replace(pm,function(c,l){var f=l.match(Mm),p=i&&!!l.match(wm)||!i&&!!l.match(bm),v=l.match(hm),m=v&&v[2];if(!Im(m))return c;if(mm.test(c)&&l.match(qs)){var g=l.match(vm),d=l.match(qs),h=d&&d[2];if(o&&g)throw new SyntaxError("You should not set multiply entry script!");if(h&&(Bs(h)||(h=Us(h,t)),h=Ws(h)),o=o||g&&h,f)return sr(h||"js file");if(p)return Ys(h||"js file",i);if(h){var y=!!l.match(gm),E=!!l.match(ym);return n.push(y||E?{async:y,src:h,crossOrigin:E}:h),kl(h,y,E)}return c}else{if(f)return sr("js file");if(p)return Ys("js file",i);var _=Ko(c),T=_.split(/[\r\n]+/).every(function(w){return!w.trim()||w.trim().startsWith("//")});return T||n.push(c),Om}});n=n.filter(function(c){return!!c});var u={template:s,scripts:n,styles:a,entry:o||n[n.length-1]};return typeof r=="function"&&(u=r(u)),u}function $s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Gl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$s(Object(r),!0).forEach(function(n){Ue(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$s(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}var Zs={},Xs={},Ks={};if(!window.fetch)throw new Error('[import-html-entry] Here is no "fetch" on the window env, you need to polyfill it');var at=window.fetch.bind(window);function mo(e){return e}function Dl(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=r.fetch,a=n===void 0?at:n,o=e;return zo(t,a).then(function(i){return o=i.reduce(function(s,u){var c=u.src,l=u.value;return s=s.replace(wr(c),Lr(c)?"".concat(c):"<style>/* ".concat(c," */").concat(l,"</style>")),s},o),o})}var Lr=function(t){return t.startsWith("<")};function km(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=r.proxy,a=r.strictGlobal,o=r.scopedGlobalVariables,i=o===void 0?[]:o,s=Lr(e)?"":"//# sourceURL=".concat(e,`
`),u=i.length?"const {".concat(i.join(","),"}=this;"):"",c=(0,eval)("window");return c.proxy=n,a?u?";(function(){with(this){".concat(u).concat(t,`
`).concat(s,"}}).bind(window.proxy)();"):";(function(window, self, globalThis){with(window){;".concat(t,`
`).concat(s,"}}).bind(window.proxy)(window.proxy, window.proxy, window.proxy);"):";(function(window, self, globalThis){;".concat(t,`
`).concat(s,"}).bind(window.proxy)(window.proxy, window.proxy, window.proxy);")}function zo(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:at;return Ol(e.map(function(){var r=N(R.mark(function n(a){return R.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:if(!Lr(a)){i.next=4;break}return i.abrupt("return",Ko(a));case 4:return i.abrupt("return",Zs[a]||(Zs[a]=t(a).then(function(s){if(s.status>=400)throw new Error("".concat(a," load failed with status ").concat(s.status));return s.text()}).catch(function(s){try{s.message.indexOf(a)===-1&&(s.message="".concat(a," ").concat(s.message))}catch{}throw s})));case 5:case"end":return i.stop()}},n)}));return function(n){return r.apply(this,arguments)}}())).then(function(r){return r.map(function(n,a){return n.status==="fulfilled"&&(n.value={src:e[a],value:n.value}),n}).filter(function(n){return n.status==="rejected"&&Promise.reject(n.reason),n.status==="fulfilled"}).map(function(n){return n.value})})}function Jo(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:at,r=arguments.length>2?arguments[2]:void 0,n=function(i,s){return Xs[i]||(Xs[i]=t(i,s).then(function(u){if(u.status>=400)throw new Error("".concat(i," load failed with status ").concat(u.status));return u.text()}).catch(function(u){try{u.message.indexOf(i)===-1&&(u.message="".concat(i," ").concat(u.message))}catch{}throw u}))},a=function(i){return e[i]===r};return Ol(e.map(function(){var o=N(R.mark(function i(s){var u,c,l,f;return R.wrap(function(v){for(;;)switch(v.prev=v.next){case 0:if(typeof s!="string"){v.next=8;break}if(!Lr(s)){v.next=5;break}return v.abrupt("return",Ko(s));case 5:return v.abrupt("return",n(s));case 6:v.next=13;break;case 8:if(u=s.src,c=s.async,l=s.crossOrigin,f=l?{credentials:"include"}:{},!c){v.next=12;break}return v.abrupt("return",{src:u,async:!0,content:new Promise(function(m,g){return lm(function(){return n(u,f).then(m,g)})})});case 12:return v.abrupt("return",n(u,f));case 13:case"end":return v.stop()}},i)}));return function(i){return o.apply(this,arguments)}}()),a).then(function(o){return o.map(function(i,s){return i.status==="fulfilled"&&(i.value={src:e[s],value:i.value}),i}).filter(function(i){return i.status==="rejected"&&Promise.reject(i.reason),i.status==="fulfilled"}).map(function(i){return i.value})})}function zs(e,t){setTimeout(function(){throw console.error(t),e})}function br(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:window,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=n.fetch,o=a===void 0?at:a,i=n.strictGlobal,s=i===void 0?!1:i,u=n.success,c=n.error,l=c===void 0?function(){}:c,f=n.beforeExec,p=f===void 0?function(){}:f,v=n.afterExec,m=v===void 0?function(){}:v,g=n.scopedGlobalVariables,d=g===void 0?[]:g;return Jo(t,o,e).then(function(h){var y=function(w,A){var b=p(A,w)||A,P=km(w,b,{proxy:r,strictGlobal:s,scopedGlobalVariables:d});dm(w,P),m(A,w)};function E(T,w,A){if(T===e){um(s?r:window);try{y(T,w);var b=r[sm(s?r:window)]||{};A(b)}catch(P){throw console.error("[import-html-entry]: error occurs while executing entry script ".concat(T)),P}}else if(typeof w=="string")try{T!=null&&T.src?y(T.src,w):y(T,w)}catch(P){zs(P,"[import-html-entry]: error occurs while executing normal script ".concat(T))}else w.async&&w?.content.then(function(P){return y(w.src,P)}).catch(function(P){zs(P,"[import-html-entry]: error occurs while executing async script ".concat(w.src))})}function _(T,w){if(T<h.length){var A=h[T],b=A.src,P=A.value;E(b,P,w),!e&&T===h.length-1?w():_(T+1,w)}}return new Promise(function(T){return _(0,u||T)})}).catch(function(h){throw l(),h})}function Gm(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=at,n=!1,a=po,o=mo,i=t.postProcessTemplate;return typeof t=="function"?r=t:(t.fetch&&(typeof t.fetch=="function"?r=t.fetch:(r=t.fetch.fn||at,n=!!t.fetch.autoDecodeResponse)),a=t.getPublicPath||t.getDomain||po,o=t.getTemplate||mo),Ks[e]||(Ks[e]=r(e).then(function(s){return fm(s,n)}).then(function(s){var u=a(e),c=Lm(o(s),u,i),l=c.template,f=c.scripts,p=c.entry,v=c.styles;return Dl(l,v,{fetch:r}).then(function(m){return{template:m,assetPublicPath:u,getExternalScripts:function(){return Jo(f,r)},getExternalStyleSheets:function(){return zo(v,r)},execScripts:function(d,h){var y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return f.length?br(p,f,d,Gl({fetch:r,strictGlobal:h},y)):Promise.resolve()}}})}))}function Dm(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.fetch,n=r===void 0?at:r,a=t.getTemplate,o=a===void 0?mo:a,i=t.postProcessTemplate,s=t.getPublicPath||t.getDomain||po;if(!e)throw new SyntaxError("entry should not be empty!");if(typeof e=="string")return Gm(e,{fetch:n,getPublicPath:s,getTemplate:o,postProcessTemplate:i});if(Array.isArray(e.scripts)||Array.isArray(e.styles)){var u=e.scripts,c=u===void 0?[]:u,l=e.styles,f=l===void 0?[]:l,p=e.html,v=p===void 0?"":p,m=function(h){return f.reduceRight(function(y,E){return"".concat(wr(E)).concat(y)},h)},g=function(h){return c.reduce(function(y,E){return"".concat(y).concat(kl(E))},h)};return Dl(o(g(m(v))),f,{fetch:n}).then(function(d){return{template:d,assetPublicPath:s(e),getExternalScripts:function(){return Jo(c,n)},getExternalStyleSheets:function(){return zo(f,n)},execScripts:function(y,E){var _=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return c.length?br(c[c.length-1],c,y,Gl({fetch:n,strictGlobal:E},_)):Promise.resolve()}}})}else throw new SyntaxError("entry scripts or styles should be array!")}function Fm(e){return{beforeLoad:function(){return N(R.mark(function r(){return R.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:e.__POWERED_BY_QIANKUN__=!0;case 1:case"end":return a.stop()}},r)}))()},beforeMount:function(){return N(R.mark(function r(){return R.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:e.__POWERED_BY_QIANKUN__=!0;case 1:case"end":return a.stop()}},r)}))()},beforeUnmount:function(){return N(R.mark(function r(){return R.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:delete e.__POWERED_BY_QIANKUN__;case 1:case"end":return a.stop()}},r)}))()}}}var Js=window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;function Nm(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"/",r=!1;return{beforeLoad:function(){return N(R.mark(function a(){return R.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:e.__INJECTED_PUBLIC_PATH_BY_QIANKUN__=t;case 1:case"end":return i.stop()}},a)}))()},beforeMount:function(){return N(R.mark(function a(){return R.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:r&&(e.__INJECTED_PUBLIC_PATH_BY_QIANKUN__=t);case 1:case"end":return i.stop()}},a)}))()},beforeUnmount:function(){return N(R.mark(function a(){return R.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:Js===void 0?delete e.__INJECTED_PUBLIC_PATH_BY_QIANKUN__:e.__INJECTED_PUBLIC_PATH_BY_QIANKUN__=Js,r=!0;case 2:case"end":return i.stop()}},a)}))()}}}function jm(e,t){return Ml({},Fm(e),Nm(e,t),function(r,n){return wl(r??[],n??[])})}function Vm(e){try{return Function.toString.call(e).indexOf("[native code]")!==-1}catch{return typeof e=="function"}}function Wm(e,t,r){if(Vo())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,t);var a=new(e.bind.apply(e,n));return r&&Oc(a,r.prototype),a}function ho(e){var t=typeof Map=="function"?new Map:void 0;return ho=function(n){if(n===null||!Vm(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(t!==void 0){if(t.has(n))return t.get(n);t.set(n,a)}function a(){return Wm(n,arguments,eo(this).constructor)}return a.prototype=Object.create(n.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),Oc(a,n)},ho(e)}var Bt=function(e){td(r,e);var t=Ad(r);function r(n){return Tt(this,r),t.call(this,"[qiankun]: ".concat(n))}return Ct(r)}(ho(Error)),Zn,Qs;function qm(){if(Qs)return Zn;Qs=1;var e=Jt(),t=Go();function r(n,a){return n&&e(a,t(a),n)}return Zn=r,Zn}var Xn,eu;function Hm(){if(eu)return Xn;eu=1;var e=Jt(),t=Qt();function r(n,a){return n&&e(a,t(a),n)}return Xn=r,Xn}var Kn,tu;function Bm(){if(tu)return Kn;tu=1;var e=Jt(),t=Lc();function r(n,a){return e(n,t(n),a)}return Kn=r,Kn}var zn,ru;function Fl(){if(ru)return zn;ru=1;var e=Lo(),t=Zo(),r=Lc(),n=rd(),a=Object.getOwnPropertySymbols,o=a?function(i){for(var s=[];i;)e(s,r(i)),i=t(i);return s}:n;return zn=o,zn}var Jn,nu;function Um(){if(nu)return Jn;nu=1;var e=Jt(),t=Fl();function r(n,a){return e(n,t(n),a)}return Jn=r,Jn}var Qn,au;function Ym(){if(au)return Qn;au=1;var e=nd(),t=Fl(),r=Qt();function n(a){return e(a,r,t)}return Qn=n,Qn}var ea,ou;function $m(){if(ou)return ea;ou=1;var e=Object.prototype,t=e.hasOwnProperty;function r(n){var a=n.length,o=new n.constructor(a);return a&&typeof n[0]=="string"&&t.call(n,"index")&&(o.index=n.index,o.input=n.input),o}return ea=r,ea}var ta,iu;function Zm(){if(iu)return ta;iu=1;var e=$o();function t(r,n){var a=n?e(r.buffer):r.buffer;return new r.constructor(a,r.byteOffset,r.byteLength)}return ta=t,ta}var ra,su;function Xm(){if(su)return ra;su=1;var e=/\w*$/;function t(r){var n=new r.constructor(r.source,e.exec(r));return n.lastIndex=r.lastIndex,n}return ra=t,ra}var na,uu;function Km(){if(uu)return na;uu=1;var e=Oo(),t=e?e.prototype:void 0,r=t?t.valueOf:void 0;function n(a){return r?Object(r.call(a)):{}}return na=n,na}var aa,cu;function zm(){if(cu)return aa;cu=1;var e=$o(),t=Zm(),r=Xm(),n=Km(),a=Pl(),o="[object Boolean]",i="[object Date]",s="[object Map]",u="[object Number]",c="[object RegExp]",l="[object Set]",f="[object String]",p="[object Symbol]",v="[object ArrayBuffer]",m="[object DataView]",g="[object Float32Array]",d="[object Float64Array]",h="[object Int8Array]",y="[object Int16Array]",E="[object Int32Array]",_="[object Uint8Array]",T="[object Uint8ClampedArray]",w="[object Uint16Array]",A="[object Uint32Array]";function b(P,C,M){var O=P.constructor;switch(C){case v:return e(P);case o:case i:return new O(+P);case m:return t(P,M);case g:case d:case h:case y:case E:case _:case T:case w:case A:return a(P,M);case s:return new O;case u:case f:return new O(P);case c:return r(P);case l:return new O;case p:return n(P)}}return aa=b,aa}var oa,lu;function Jm(){if(lu)return oa;lu=1;var e=Fo(),t=Zt(),r="[object Map]";function n(a){return t(a)&&e(a)==r}return oa=n,oa}var ia,fu;function Qm(){if(fu)return ia;fu=1;var e=Jm(),t=No(),r=kc(),n=r&&r.isMap,a=n?t(n):e;return ia=a,ia}var sa,du;function eh(){if(du)return sa;du=1;var e=Fo(),t=Zt(),r="[object Set]";function n(a){return t(a)&&e(a)==r}return sa=n,sa}var ua,pu;function th(){if(pu)return ua;pu=1;var e=eh(),t=No(),r=kc(),n=r&&r.isSet,a=n?t(n):e;return ua=a,ua}var ca,mu;function rh(){if(mu)return ca;mu=1;var e=Ic(),t=Il(),r=Rl(),n=qm(),a=Hm(),o=_l(),i=Uo(),s=Bm(),u=Um(),c=ad(),l=Ym(),f=Fo(),p=$m(),v=zm(),m=Tl(),g=Pt(),d=xc(),h=Qm(),y=st(),E=th(),_=Go(),T=Qt(),w=1,A=2,b=4,P="[object Arguments]",C="[object Array]",M="[object Boolean]",O="[object Date]",z="[object Error]",D="[object Function]",U="[object GeneratorFunction]",X="[object Map]",Y="[object Number]",V="[object Object]",q="[object RegExp]",j="[object Set]",te="[object String]",re="[object Symbol]",fe="[object WeakMap]",_e="[object ArrayBuffer]",be="[object DataView]",Pe="[object Float32Array]",Ke="[object Float64Array]",ut="[object Int8Array]",Fe="[object Int16Array]",er="[object Int32Array]",tr="[object Uint8Array]",ze="[object Uint8ClampedArray]",rr="[object Uint16Array]",nr="[object Uint32Array]",F={};F[P]=F[C]=F[_e]=F[be]=F[M]=F[O]=F[Pe]=F[Ke]=F[ut]=F[Fe]=F[er]=F[X]=F[Y]=F[V]=F[q]=F[j]=F[te]=F[re]=F[tr]=F[ze]=F[rr]=F[nr]=!0,F[z]=F[D]=F[fe]=!1;function Ne(I,Te,Ce,ar,je,de){var Z,xt=Te&w,K=Te&A,Ae=Te&b;if(Ce&&(Z=je?Ce(I,ar,je,de):Ce(I)),Z!==void 0)return Z;if(!y(I))return I;var oe=g(I);if(oe){if(Z=p(I),!xt)return i(I,Z)}else{var Q=f(I),ct=Q==D||Q==U;if(d(I))return o(I,xt);if(Q==V||Q==P||ct&&!je){if(Z=K||ct?{}:m(I),!xt)return K?u(I,a(Z,I)):s(I,n(Z,I))}else{if(!F[Q])return je?I:{};Z=v(I,Q,xt)}}de||(de=new e);var Mt=de.get(I);if(Mt)return Mt;de.set(I,Z),E(I)?I.forEach(function(S){Z.add(Ne(S,Te,Ce,S,I,de))}):h(I)&&I.forEach(function(S,ie){Z.set(ie,Ne(S,Te,Ce,ie,I,de))});var L=Ae?K?l:c:K?T:_,B=oe?void 0:L(I);return t(B||I,function(S,ie){B&&(ie=S,S=I[ie]),r(Z,ie,Ne(S,Te,Ce,ie,I,de))}),Z}return ca=Ne,ca}var la,hu;function nh(){if(hu)return la;hu=1;var e=rh(),t=1,r=4;function n(a){return e(a,t|r)}return la=n,la}var ah=nh();const Nt=ye(ah);var lt={},gt={};function oh(e,t){Object.keys(gt).forEach(function(r){gt[r]instanceof Function&&gt[r](Nt(e),Nt(t))})}function ih(e,t){return{onGlobalStateChange:function(n,a){if(!(n instanceof Function)){console.error("[qiankun] callback must be function!");return}if(gt[e]&&console.warn("[qiankun] '".concat(e,"' global listener already exists before this, new listener will overwrite it.")),gt[e]=n,a){var o=Nt(lt);n(o,o)}},setGlobalState:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(n===lt)return console.warn("[qiankun] state has not changed！"),!1;var a=[],o=Nt(lt);return lt=Nt(Object.keys(n).reduce(function(i,s){return i.hasOwnProperty(s)?(a.push(s),Object.assign(i,Ue({},s,n[s]))):(console.warn("[qiankun] '".concat(s,"' not declared when init state！")),i)},lt)),a.length===0?(console.warn("[qiankun] state has not changed！"),!1):(oh(lt,o),!0)},offGlobalStateChange:function(){return delete gt[e],!0}}}var ge;(function(e){e.Proxy="Proxy",e.Snapshot="Snapshot",e.LegacyProxy="LegacyProxy"})(ge||(ge={}));var sh=Mc();const rt=ye(sh);var fa,vu;function uh(){if(vu)return fa;vu=1;var e=/\s/;function t(r){for(var n=r.length;n--&&e.test(r.charAt(n)););return n}return fa=t,fa}var da,gu;function ch(){if(gu)return da;gu=1;var e=uh(),t=/^\s+/;function r(n){return n&&n.slice(0,e(n)+1).replace(t,"")}return da=r,da}var pa,yu;function Nl(){if(yu)return pa;yu=1;var e=Rc(),t=Zt(),r="[object Symbol]";function n(a){return typeof a=="symbol"||t(a)&&e(a)==r}return pa=n,pa}var ma,wu;function lh(){if(wu)return ma;wu=1;var e=ch(),t=st(),r=Nl(),n=NaN,a=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,i=/^0o[0-7]+$/i,s=parseInt;function u(c){if(typeof c=="number")return c;if(r(c))return n;if(t(c)){var l=typeof c.valueOf=="function"?c.valueOf():c;c=t(l)?l+"":l}if(typeof c!="string")return c===0?c:+c;c=e(c);var f=o.test(c);return f||i.test(c)?s(c.slice(2),f?2:8):a.test(c)?n:+c}return ma=u,ma}var ha,bu;function fh(){if(bu)return ha;bu=1;var e=lh(),t=1/0,r=17976931348623157e292;function n(a){if(!a)return a===0?a:0;if(a=e(a),a===t||a===-t){var o=a<0?-1:1;return o*r}return a===a?a:0}return ha=n,ha}var va,Su;function dh(){if(Su)return va;Su=1;var e=fh();function t(r){var n=e(r),a=n%1;return n===n?a?n-a:n:0}return va=t,va}var ga,Eu;function ph(){if(Eu)return ga;Eu=1;var e=dh(),t="Expected a function";function r(n,a){var o;if(typeof a!="function")throw new TypeError(t);return n=e(n),function(){return--n>0&&(o=a.apply(this,arguments)),n<=1&&(a=void 0),o}}return ga=r,ga}var ya,_u;function mh(){if(_u)return ya;_u=1;var e=ph();function t(r){return e(2,r)}return ya=t,ya}var hh=mh();const vh=ye(hh);var wa,Pu;function gh(){if(Pu)return wa;Pu=1;function e(t,r,n,a){var o=-1,i=t==null?0:t.length;for(a&&i&&(n=t[++o]);++o<i;)n=r(n,t[o],o,t);return n}return wa=e,wa}var ba,Tu;function yh(){if(Tu)return ba;Tu=1;function e(t){return function(r){return t?.[r]}}return ba=e,ba}var Sa,Cu;function wh(){if(Cu)return Sa;Cu=1;var e=yh(),t={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},r=e(t);return Sa=r,Sa}var Ea,Au;function jl(){if(Au)return Ea;Au=1;function e(t,r){for(var n=-1,a=t==null?0:t.length,o=Array(a);++n<a;)o[n]=r(t[n],n,t);return o}return Ea=e,Ea}var _a,Ru;function bh(){if(Ru)return _a;Ru=1;var e=Oo(),t=jl(),r=Pt(),n=Nl(),a=e?e.prototype:void 0,o=a?a.toString:void 0;function i(s){if(typeof s=="string")return s;if(r(s))return t(s,i)+"";if(n(s))return o?o.call(s):"";var u=s+"";return u=="0"&&1/s==-1/0?"-0":u}return _a=i,_a}var Pa,xu;function Vl(){if(xu)return Pa;xu=1;var e=bh();function t(r){return r==null?"":e(r)}return Pa=t,Pa}var Ta,Mu;function Sh(){if(Mu)return Ta;Mu=1;var e=wh(),t=Vl(),r=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,n="\\u0300-\\u036f",a="\\ufe20-\\ufe2f",o="\\u20d0-\\u20ff",i=n+a+o,s="["+i+"]",u=RegExp(s,"g");function c(l){return l=t(l),l&&l.replace(r,e).replace(u,"")}return Ta=c,Ta}var Ca,Iu;function Eh(){if(Iu)return Ca;Iu=1;var e=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;function t(r){return r.match(e)||[]}return Ca=t,Ca}var Aa,Ou;function _h(){if(Ou)return Aa;Ou=1;var e=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;function t(r){return e.test(r)}return Aa=t,Aa}var Ra,Lu;function Ph(){if(Lu)return Ra;Lu=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",a=t+r+n,o="\\u2700-\\u27bf",i="a-z\\xdf-\\xf6\\xf8-\\xff",s="\\xac\\xb1\\xd7\\xf7",u="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",c="\\u2000-\\u206f",l=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",f="A-Z\\xc0-\\xd6\\xd8-\\xde",p="\\ufe0e\\ufe0f",v=s+u+c+l,m="['’]",g="["+v+"]",d="["+a+"]",h="\\d+",y="["+o+"]",E="["+i+"]",_="[^"+e+v+h+o+i+f+"]",T="\\ud83c[\\udffb-\\udfff]",w="(?:"+d+"|"+T+")",A="[^"+e+"]",b="(?:\\ud83c[\\udde6-\\uddff]){2}",P="[\\ud800-\\udbff][\\udc00-\\udfff]",C="["+f+"]",M="\\u200d",O="(?:"+E+"|"+_+")",z="(?:"+C+"|"+_+")",D="(?:"+m+"(?:d|ll|m|re|s|t|ve))?",U="(?:"+m+"(?:D|LL|M|RE|S|T|VE))?",X=w+"?",Y="["+p+"]?",V="(?:"+M+"(?:"+[A,b,P].join("|")+")"+Y+X+")*",q="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",j="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",te=Y+X+V,re="(?:"+[y,b,P].join("|")+")"+te,fe=RegExp([C+"?"+E+"+"+D+"(?="+[g,C,"$"].join("|")+")",z+"+"+U+"(?="+[g,C+O,"$"].join("|")+")",C+"?"+O+"+"+D,C+"+"+U,j,q,h,re].join("|"),"g");function _e(be){return be.match(fe)||[]}return Ra=_e,Ra}var xa,ku;function Th(){if(ku)return xa;ku=1;var e=Eh(),t=_h(),r=Vl(),n=Ph();function a(o,i,s){return o=r(o),i=s?void 0:i,i===void 0?t(o)?n(o):e(o):o.match(i)||[]}return xa=a,xa}var Ma,Gu;function Ch(){if(Gu)return Ma;Gu=1;var e=gh(),t=Sh(),r=Th(),n="['’]",a=RegExp(n,"g");function o(i){return function(s){return e(r(t(s).replace(a,"")),i,"")}}return Ma=o,Ma}var Ia,Du;function Ah(){if(Du)return Ia;Du=1;var e=Ch(),t=e(function(r,n,a){return r+(a?"_":"")+n.toLowerCase()});return Ia=t,Ia}var Rh=Ah();const xh=ye(Rh);var Mh="2.10.16";function mt(e){return Array.isArray(e)?e:[e]}var Ih=typeof window.__zone_symbol__setTimeout=="function"?window.__zone_symbol__setTimeout:function(e){return Promise.resolve().then(e)},Oa=!1;function Oh(e){Oa||(Oa=!0,Ih(function(){e(),Oa=!1}))}var La=new WeakMap;function Lh(e){var t=e.prototype&&e.prototype.constructor===e&&Object.getOwnPropertyNames(e.prototype).length>1;if(t)return!0;if(La.has(e))return La.get(e);var r=t;if(!r){var n=e.toString(),a=/^function\b\s[A-Z].*/,o=/^class\b/;r=a.test(n)||o.test(n)}return La.set(e,r),r}var Fu=new WeakMap;function Wl(e){if(Fu.has(e))return!0;var t=typeof e=="function"&&e instanceof Function;return t&&Fu.set(e,t),t}var Nu=new WeakMap;function kh(e,t){if(!e||!t)return!1;var r=Nu.get(e)||{};if(r[t])return r[t];var n=Object.getOwnPropertyDescriptor(e,t),a=!!(n&&n.configurable===!1&&(n.writable===!1||n.get&&!n.set));return r[t]=a,Nu.set(e,r),a}var ka=new WeakMap;function ql(e){if(ka.has(e))return ka.get(e);var t=e.name.indexOf("bound ")===0&&!e.hasOwnProperty("prototype");return ka.set(e,t),t}var Gh=od(function(){try{return new Function("const { a } = { a: 1 }")(),!0}catch{return!1}}),Ft="qiankun-head";function Dh(e,t){return function(r){var n;return r.indexOf("<head>")!==-1?n=r.replace("<head>","<".concat(Ft,">")).replace("</head>","</".concat(Ft,">")):n="<".concat(Ft,"></").concat(Ft,">").concat(r),'<div id="'.concat(Hl(e),'" data-name="').concat(e,'" data-version="').concat(Mh,'" data-sandbox-cfg=').concat(JSON.stringify(t),">").concat(n,"</div>")}}function Hl(e){return"__qiankun_microapp_wrapper_for_".concat(xh(e),"__")}var ae=new Function("return this")(),ju=new Function("return document")(),Fh=vh(function(){return ae.hasOwnProperty("__app_instance_name_map__")||Object.defineProperty(ae,"__app_instance_name_map__",{enumerable:!1,configurable:!0,writable:!0,value:{}}),ae.__app_instance_name_map__}),Nh=function(t){var r=Fh();return t in r?(r[t]++,"".concat(t,"_").concat(r[t])):(ae.__app_instance_name_map__[t]=0,t)};function Ga(e){var t=e??{},r=t.bootstrap,n=t.mount,a=t.unmount;return rt(r)&&rt(n)&&rt(a)}var Bl=Ct(function e(){var t=this;Tt(this,e),this.promise=void 0,this.resolve=void 0,this.reject=void 0,this.promise=new Promise(function(r,n){t.resolve=r,t.reject=n})});function jh(e){return me(e)!=="object"||e.strictStyleIsolation?!1:!!e.experimentalStyleIsolation}function Vh(e,t){if(t.body.contains(e)){for(var r="",n,a,o=e;o!==t.documentElement;){for(n=0,a=o;a;)a.nodeType===1&&a.nodeName===o.nodeName&&(n+=1),a=a.previousSibling;r="*[name()='".concat(o.nodeName,"'][").concat(n,"]/").concat(r),o=o.parentNode}return r="/*[name()='".concat(t.documentElement.nodeName,"']/").concat(r),r=r.replace(/\/$/,""),r}}function Ul(e){return typeof e=="string"?document.querySelector(e):e}function Wh(e){if(e){var t=Ul(e);if(t)return Vh(t,document)}}var Qo=null;function vo(){return Qo}function qh(e){Qo=e}function Hh(){Qo=null}var Vu=new WeakMap;function Yl(e,t){if(Wl(t)&&!ql(t)&&!Lh(t)){var r=Vu.get(t);if(r)return r;var n=Function.prototype.bind.call(t,e);if(Object.getOwnPropertyNames(t).forEach(function(s){n.hasOwnProperty(s)||Object.defineProperty(n,s,Object.getOwnPropertyDescriptor(t,s))}),t.hasOwnProperty("prototype")&&!n.hasOwnProperty("prototype")&&Object.defineProperty(n,"prototype",{value:t.prototype,enumerable:!1,writable:!0}),typeof t.toString=="function"){var a=t.hasOwnProperty("toString")&&!n.hasOwnProperty("toString"),o=n.toString===Function.prototype.toString;if(a||o){var i=Object.getOwnPropertyDescriptor(a?t:Function.prototype,"toString");Object.defineProperty(n,"toString",Object.assign({},i,i?.get?null:{value:function(){return t.toString()}}))}}return Vu.set(t,n),n}return t}function Bh(e,t){var r=Object.getOwnPropertyDescriptor(e,t);return r?r.configurable:!0}var Uh=function(){function e(t){var r=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:window;Tt(this,e),this.addedPropsMapInSandbox=new Map,this.modifiedPropsOriginalValueMapInSandbox=new Map,this.currentUpdatedPropsValueMap=new Map,this.name=void 0,this.proxy=void 0,this.globalContext=void 0,this.type=void 0,this.sandboxRunning=!0,this.latestSetProp=null,this.name=t,this.globalContext=n,this.type=ge.LegacyProxy;var a=this.addedPropsMapInSandbox,o=this.modifiedPropsOriginalValueMapInSandbox,i=this.currentUpdatedPropsValueMap,s=n,u=Object.create(null),c=function(p,v,m){var g=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0;return r.sandboxRunning&&(s.hasOwnProperty(p)?o.has(p)||o.set(p,m):a.set(p,v),i.set(p,v),g&&(s[p]=v),r.latestSetProp=p),!0},l=new Proxy(u,{set:function(p,v,m){var g=s[v];return c(v,m,g,!0)},get:function(p,v){if(v==="top"||v==="parent"||v==="window"||v==="self")return l;var m=s[v];return Yl(s,m)},has:function(p,v){return v in s},getOwnPropertyDescriptor:function(p,v){var m=Object.getOwnPropertyDescriptor(s,v);return m&&!m.configurable&&(m.configurable=!0),m},defineProperty:function(p,v,m){var g=s[v],d=Reflect.defineProperty(s,v,m),h=s[v];return c(v,h,g,!1),d}});this.proxy=l}return Ct(e,[{key:"setWindowProp",value:function(r,n,a){n===void 0&&a?delete this.globalContext[r]:Bh(this.globalContext,r)&&me(r)!=="symbol"&&(Object.defineProperty(this.globalContext,r,{writable:!0,configurable:!0}),this.globalContext[r]=n)}},{key:"active",value:function(){var r=this;this.sandboxRunning||this.currentUpdatedPropsValueMap.forEach(function(n,a){return r.setWindowProp(a,n)}),this.sandboxRunning=!0}},{key:"inactive",value:function(){var r=this;this.modifiedPropsOriginalValueMapInSandbox.forEach(function(n,a){return r.setWindowProp(a,n)}),this.addedPropsMapInSandbox.forEach(function(n,a){return r.setWindowProp(a,void 0,!0)}),this.sandboxRunning=!1}},{key:"patchDocument",value:function(){}}]),e}(),jt;(function(e){e[e.STYLE=1]="STYLE",e[e.MEDIA=4]="MEDIA",e[e.SUPPORTS=12]="SUPPORTS",e[e.IMPORT=3]="IMPORT",e[e.FONT_FACE=5]="FONT_FACE",e[e.PAGE=6]="PAGE",e[e.KEYFRAMES=7]="KEYFRAMES",e[e.KEYFRAME=8]="KEYFRAME"})(jt||(jt={}));var ur=function(t){return[].slice.call(t,0)},Yh=HTMLBodyElement.prototype.appendChild,$l=function(){function e(){Tt(this,e),this.sheet=void 0,this.swapNode=void 0;var t=document.createElement("style");Yh.call(document.body,t),this.swapNode=t,this.sheet=t.sheet,this.sheet.disabled=!0}return Ct(e,[{key:"process",value:function(r){var n=this,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";if(!(e.ModifiedTag in r)){if(r.textContent!==""){var o,i=document.createTextNode(r.textContent||"");this.swapNode.appendChild(i);var s=this.swapNode.sheet,u=ur((o=s?.cssRules)!==null&&o!==void 0?o:[]),c=this.rewrite(u,a);r.textContent=c,this.swapNode.removeChild(i),r[e.ModifiedTag]=!0;return}var l=new MutationObserver(function(f){for(var p=0;p<f.length;p+=1){var v=f[p];if(e.ModifiedTag in r)return;if(v.type==="childList"){var m,g=r.sheet,d=ur((m=g?.cssRules)!==null&&m!==void 0?m:[]),h=n.rewrite(d,a);r.textContent=h,r[e.ModifiedTag]=!0}}});l.observe(r,{childList:!0})}}},{key:"rewrite",value:function(r){var n=this,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",o="";return r.forEach(function(i){switch(i.type){case jt.STYLE:o+=n.ruleStyle(i,a);break;case jt.MEDIA:o+=n.ruleMedia(i,a);break;case jt.SUPPORTS:o+=n.ruleSupport(i,a);break;default:typeof i.cssText=="string"&&(o+="".concat(i.cssText));break}}),o}},{key:"ruleStyle",value:function(r,n){var a=/((?:[^\w\-.#]|^)(body|html|:root))/gm,o=/(html[^\w{[]+)/gm,i=r.selectorText.trim(),s="";if(typeof r.cssText=="string"&&(s=r.cssText),i==="html"||i==="body"||i===":root")return s.replace(a,n);if(o.test(r.selectorText)){var u=/(html[^\w{]+)(\+|~)/gm;u.test(r.selectorText)||(s=s.replace(o,""))}return s=s.replace(/^[\s\S]+{/,function(c){return c.replace(/(^|,\n?)([^,]+)/g,function(l,f,p){return a.test(l)?l.replace(a,function(v){var m=[",","("];return v&&m.includes(v[0])?"".concat(v[0]).concat(n):n}):"".concat(f).concat(n," ").concat(p.replace(/^ */,""))})}),s}},{key:"ruleMedia",value:function(r,n){var a=this.rewrite(ur(r.cssRules),n);return"@media ".concat(r.conditionText||r.media.mediaText," {").concat(a,"}")}},{key:"ruleSupport",value:function(r,n){var a=this.rewrite(ur(r.cssRules),n);return"@supports ".concat(r.conditionText||r.cssText.split("{")[0]," {").concat(a,"}")}}]),e}();$l.ModifiedTag="Symbol(style-modified-qiankun)";var Da,go="data-qiankun",yo=function(t,r,n){Da||(Da=new $l),r.tagName==="LINK"&&console.warn("Feature: sandbox.experimentalStyleIsolation is not support for link element yet.");var a=t;if(a){var o=(a.tagName||"").toLowerCase();if(o&&r.tagName==="STYLE"){var i="".concat(o,"[").concat(go,'="').concat(n,'"]');Da.process(r,i)}}},Fa,Wu;function $h(){if(Wu)return Fa;Wu=1;function e(t,r,n,a){for(var o=t.length,i=n+(a?1:-1);a?i--:++i<o;)if(r(t[i],i,t))return i;return-1}return Fa=e,Fa}var Na,qu;function Zh(){if(qu)return Na;qu=1;function e(t){return t!==t}return Na=e,Na}var ja,Hu;function Xh(){if(Hu)return ja;Hu=1;function e(t,r,n){for(var a=n-1,o=t.length;++a<o;)if(t[a]===r)return a;return-1}return ja=e,ja}var Va,Bu;function Kh(){if(Bu)return Va;Bu=1;var e=$h(),t=Zh(),r=Xh();function n(a,o,i){return o===o?r(a,o,i):e(a,t,i)}return Va=n,Va}var Wa,Uu;function zh(){if(Uu)return Wa;Uu=1;var e=Kh();function t(r,n){var a=r==null?0:r.length;return!!a&&e(r,n,0)>-1}return Wa=t,Wa}var qa,Yu;function Jh(){if(Yu)return qa;Yu=1;function e(t,r,n){for(var a=-1,o=t==null?0:t.length;++a<o;)if(n(r,t[a]))return!0;return!1}return qa=e,qa}var Ha,$u;function Qh(){if($u)return Ha;$u=1;var e=id(),t=zh(),r=Jh(),n=jl(),a=No(),o=sd(),i=200;function s(u,c,l,f){var p=-1,v=t,m=!0,g=u.length,d=[],h=c.length;if(!g)return d;l&&(c=n(c,a(l))),f?(v=r,m=!1):c.length>=i&&(v=o,m=!1,c=new e(c));e:for(;++p<g;){var y=u[p],E=l==null?y:l(y);if(y=f||y!==0?y:0,m&&E===E){for(var _=h;_--;)if(c[_]===E)continue e;d.push(y)}else v(c,E,f)||d.push(y)}return d}return Ha=s,Ha}var Ba,Zu;function ev(){if(Zu)return Ba;Zu=1;var e=Qh(),t=xl(),r=Cl(),n=t(function(a,o){return r(a)?e(a,o):[]});return Ba=n,Ba}var tv=ev();const Zl=ye(tv);var rv=window.Proxy?["Array","ArrayBuffer","Boolean","constructor","DataView","Date","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","Error","escape","eval","EvalError","Float32Array","Float64Array","Function","hasOwnProperty","Infinity","Int16Array","Int32Array","Int8Array","isFinite","isNaN","isPrototypeOf","JSON","Map","Math","NaN","Number","Object","parseFloat","parseInt","Promise","propertyIsEnumerable","Proxy","RangeError","ReferenceError","Reflect","RegExp","Set","String","Symbol","SyntaxError","toLocaleString","toString","TypeError","Uint16Array","Uint32Array","Uint8Array","Uint8ClampedArray","undefined","unescape","URIError","valueOf","WeakMap","WeakSet"].filter(function(e){return e in window}):[],nv=["AbortController","AbortSignal","addEventListener","alert","AnalyserNode","Animation","AnimationEffectReadOnly","AnimationEffectTiming","AnimationEffectTimingReadOnly","AnimationEvent","AnimationPlaybackEvent","AnimationTimeline","applicationCache","ApplicationCache","ApplicationCacheErrorEvent","atob","Attr","Audio","AudioBuffer","AudioBufferSourceNode","AudioContext","AudioDestinationNode","AudioListener","AudioNode","AudioParam","AudioProcessingEvent","AudioScheduledSourceNode","AudioWorkletGlobalScope","AudioWorkletNode","AudioWorkletProcessor","BarProp","BaseAudioContext","BatteryManager","BeforeUnloadEvent","BiquadFilterNode","Blob","BlobEvent","blur","BroadcastChannel","btoa","BudgetService","ByteLengthQueuingStrategy","Cache","caches","CacheStorage","cancelAnimationFrame","cancelIdleCallback","CanvasCaptureMediaStreamTrack","CanvasGradient","CanvasPattern","CanvasRenderingContext2D","ChannelMergerNode","ChannelSplitterNode","CharacterData","clearInterval","clearTimeout","clientInformation","ClipboardEvent","ClipboardItem","close","closed","CloseEvent","Comment","CompositionEvent","CompressionStream","confirm","console","ConstantSourceNode","ConvolverNode","CountQueuingStrategy","createImageBitmap","Credential","CredentialsContainer","crypto","Crypto","CryptoKey","CSS","CSSConditionRule","CSSFontFaceRule","CSSGroupingRule","CSSImportRule","CSSKeyframeRule","CSSKeyframesRule","CSSMatrixComponent","CSSMediaRule","CSSNamespaceRule","CSSPageRule","CSSPerspective","CSSRotate","CSSRule","CSSRuleList","CSSScale","CSSSkew","CSSSkewX","CSSSkewY","CSSStyleDeclaration","CSSStyleRule","CSSStyleSheet","CSSSupportsRule","CSSTransformValue","CSSTranslate","CustomElementRegistry","customElements","CustomEvent","DataTransfer","DataTransferItem","DataTransferItemList","DecompressionStream","defaultstatus","defaultStatus","DelayNode","DeviceMotionEvent","DeviceOrientationEvent","devicePixelRatio","dispatchEvent","document","Document","DocumentFragment","DocumentType","DOMError","DOMException","DOMImplementation","DOMMatrix","DOMMatrixReadOnly","DOMParser","DOMPoint","DOMPointReadOnly","DOMQuad","DOMRect","DOMRectList","DOMRectReadOnly","DOMStringList","DOMStringMap","DOMTokenList","DragEvent","DynamicsCompressorNode","Element","ErrorEvent","event","Event","EventSource","EventTarget","external","fetch","File","FileList","FileReader","find","focus","FocusEvent","FontFace","FontFaceSetLoadEvent","FormData","FormDataEvent","frameElement","frames","GainNode","Gamepad","GamepadButton","GamepadEvent","getComputedStyle","getSelection","HashChangeEvent","Headers","history","History","HTMLAllCollection","HTMLAnchorElement","HTMLAreaElement","HTMLAudioElement","HTMLBaseElement","HTMLBodyElement","HTMLBRElement","HTMLButtonElement","HTMLCanvasElement","HTMLCollection","HTMLContentElement","HTMLDataElement","HTMLDataListElement","HTMLDetailsElement","HTMLDialogElement","HTMLDirectoryElement","HTMLDivElement","HTMLDListElement","HTMLDocument","HTMLElement","HTMLEmbedElement","HTMLFieldSetElement","HTMLFontElement","HTMLFormControlsCollection","HTMLFormElement","HTMLFrameElement","HTMLFrameSetElement","HTMLHeadElement","HTMLHeadingElement","HTMLHRElement","HTMLHtmlElement","HTMLIFrameElement","HTMLImageElement","HTMLInputElement","HTMLLabelElement","HTMLLegendElement","HTMLLIElement","HTMLLinkElement","HTMLMapElement","HTMLMarqueeElement","HTMLMediaElement","HTMLMenuElement","HTMLMetaElement","HTMLMeterElement","HTMLModElement","HTMLObjectElement","HTMLOListElement","HTMLOptGroupElement","HTMLOptionElement","HTMLOptionsCollection","HTMLOutputElement","HTMLParagraphElement","HTMLParamElement","HTMLPictureElement","HTMLPreElement","HTMLProgressElement","HTMLQuoteElement","HTMLScriptElement","HTMLSelectElement","HTMLShadowElement","HTMLSlotElement","HTMLSourceElement","HTMLSpanElement","HTMLStyleElement","HTMLTableCaptionElement","HTMLTableCellElement","HTMLTableColElement","HTMLTableElement","HTMLTableRowElement","HTMLTableSectionElement","HTMLTemplateElement","HTMLTextAreaElement","HTMLTimeElement","HTMLTitleElement","HTMLTrackElement","HTMLUListElement","HTMLUnknownElement","HTMLVideoElement","IDBCursor","IDBCursorWithValue","IDBDatabase","IDBFactory","IDBIndex","IDBKeyRange","IDBObjectStore","IDBOpenDBRequest","IDBRequest","IDBTransaction","IDBVersionChangeEvent","IdleDeadline","IIRFilterNode","Image","ImageBitmap","ImageBitmapRenderingContext","ImageCapture","ImageData","indexedDB","innerHeight","innerWidth","InputEvent","IntersectionObserver","IntersectionObserverEntry","Intl","isSecureContext","KeyboardEvent","KeyframeEffect","KeyframeEffectReadOnly","length","localStorage","location","Location","locationbar","matchMedia","MediaDeviceInfo","MediaDevices","MediaElementAudioSourceNode","MediaEncryptedEvent","MediaError","MediaKeyMessageEvent","MediaKeySession","MediaKeyStatusMap","MediaKeySystemAccess","MediaList","MediaMetadata","MediaQueryList","MediaQueryListEvent","MediaRecorder","MediaSettingsRange","MediaSource","MediaStream","MediaStreamAudioDestinationNode","MediaStreamAudioSourceNode","MediaStreamConstraints","MediaStreamEvent","MediaStreamTrack","MediaStreamTrackEvent","menubar","MessageChannel","MessageEvent","MessagePort","MIDIAccess","MIDIConnectionEvent","MIDIInput","MIDIInputMap","MIDIMessageEvent","MIDIOutput","MIDIOutputMap","MIDIPort","MimeType","MimeTypeArray","MouseEvent","moveBy","moveTo","MutationEvent","MutationObserver","MutationRecord","name","NamedNodeMap","NavigationPreloadManager","navigator","Navigator","NavigatorUAData","NetworkInformation","Node","NodeFilter","NodeIterator","NodeList","Notification","OfflineAudioCompletionEvent","OfflineAudioContext","offscreenBuffering","OffscreenCanvas","OffscreenCanvasRenderingContext2D","onabort","onafterprint","onanimationend","onanimationiteration","onanimationstart","onappinstalled","onauxclick","onbeforeinstallprompt","onbeforeprint","onbeforeunload","onblur","oncancel","oncanplay","oncanplaythrough","onchange","onclick","onclose","oncontextmenu","oncuechange","ondblclick","ondevicemotion","ondeviceorientation","ondeviceorientationabsolute","ondrag","ondragend","ondragenter","ondragleave","ondragover","ondragstart","ondrop","ondurationchange","onemptied","onended","onerror","onfocus","ongotpointercapture","onhashchange","oninput","oninvalid","onkeydown","onkeypress","onkeyup","onlanguagechange","onload","onloadeddata","onloadedmetadata","onloadstart","onlostpointercapture","onmessage","onmessageerror","onmousedown","onmouseenter","onmouseleave","onmousemove","onmouseout","onmouseover","onmouseup","onmousewheel","onoffline","ononline","onpagehide","onpageshow","onpause","onplay","onplaying","onpointercancel","onpointerdown","onpointerenter","onpointerleave","onpointermove","onpointerout","onpointerover","onpointerup","onpopstate","onprogress","onratechange","onrejectionhandled","onreset","onresize","onscroll","onsearch","onseeked","onseeking","onselect","onstalled","onstorage","onsubmit","onsuspend","ontimeupdate","ontoggle","ontransitionend","onunhandledrejection","onunload","onvolumechange","onwaiting","onwheel","open","openDatabase","opener","Option","origin","OscillatorNode","outerHeight","outerWidth","OverconstrainedError","PageTransitionEvent","pageXOffset","pageYOffset","PannerNode","parent","Path2D","PaymentAddress","PaymentRequest","PaymentRequestUpdateEvent","PaymentResponse","performance","Performance","PerformanceEntry","PerformanceLongTaskTiming","PerformanceMark","PerformanceMeasure","PerformanceNavigation","PerformanceNavigationTiming","PerformanceObserver","PerformanceObserverEntryList","PerformancePaintTiming","PerformanceResourceTiming","PerformanceTiming","PeriodicWave","Permissions","PermissionStatus","personalbar","PhotoCapabilities","Plugin","PluginArray","PointerEvent","PopStateEvent","postMessage","Presentation","PresentationAvailability","PresentationConnection","PresentationConnectionAvailableEvent","PresentationConnectionCloseEvent","PresentationConnectionList","PresentationReceiver","PresentationRequest","print","ProcessingInstruction","ProgressEvent","PromiseRejectionEvent","prompt","PushManager","PushSubscription","PushSubscriptionOptions","queueMicrotask","RadioNodeList","Range","ReadableByteStreamController","ReadableStream","ReadableStreamBYOBReader","ReadableStreamBYOBRequest","ReadableStreamDefaultController","ReadableStreamDefaultReader","registerProcessor","RemotePlayback","removeEventListener","reportError","Request","requestAnimationFrame","requestIdleCallback","resizeBy","ResizeObserver","ResizeObserverEntry","resizeTo","Response","RTCCertificate","RTCDataChannel","RTCDataChannelEvent","RTCDtlsTransport","RTCIceCandidate","RTCIceGatherer","RTCIceTransport","RTCPeerConnection","RTCPeerConnectionIceEvent","RTCRtpContributingSource","RTCRtpReceiver","RTCRtpSender","RTCSctpTransport","RTCSessionDescription","RTCStatsReport","RTCTrackEvent","screen","Screen","screenLeft","ScreenOrientation","screenTop","screenX","screenY","ScriptProcessorNode","scroll","scrollbars","scrollBy","scrollTo","scrollX","scrollY","SecurityPolicyViolationEvent","Selection","self","ServiceWorker","ServiceWorkerContainer","ServiceWorkerRegistration","sessionStorage","setInterval","setTimeout","ShadowRoot","SharedWorker","SourceBuffer","SourceBufferList","speechSynthesis","SpeechSynthesisEvent","SpeechSynthesisUtterance","StaticRange","status","statusbar","StereoPannerNode","stop","Storage","StorageEvent","StorageManager","structuredClone","styleMedia","StyleSheet","StyleSheetList","SubmitEvent","SubtleCrypto","SVGAElement","SVGAngle","SVGAnimatedAngle","SVGAnimatedBoolean","SVGAnimatedEnumeration","SVGAnimatedInteger","SVGAnimatedLength","SVGAnimatedLengthList","SVGAnimatedNumber","SVGAnimatedNumberList","SVGAnimatedPreserveAspectRatio","SVGAnimatedRect","SVGAnimatedString","SVGAnimatedTransformList","SVGAnimateElement","SVGAnimateMotionElement","SVGAnimateTransformElement","SVGAnimationElement","SVGCircleElement","SVGClipPathElement","SVGComponentTransferFunctionElement","SVGDefsElement","SVGDescElement","SVGDiscardElement","SVGElement","SVGEllipseElement","SVGFEBlendElement","SVGFEColorMatrixElement","SVGFEComponentTransferElement","SVGFECompositeElement","SVGFEConvolveMatrixElement","SVGFEDiffuseLightingElement","SVGFEDisplacementMapElement","SVGFEDistantLightElement","SVGFEDropShadowElement","SVGFEFloodElement","SVGFEFuncAElement","SVGFEFuncBElement","SVGFEFuncGElement","SVGFEFuncRElement","SVGFEGaussianBlurElement","SVGFEImageElement","SVGFEMergeElement","SVGFEMergeNodeElement","SVGFEMorphologyElement","SVGFEOffsetElement","SVGFEPointLightElement","SVGFESpecularLightingElement","SVGFESpotLightElement","SVGFETileElement","SVGFETurbulenceElement","SVGFilterElement","SVGForeignObjectElement","SVGGElement","SVGGeometryElement","SVGGradientElement","SVGGraphicsElement","SVGImageElement","SVGLength","SVGLengthList","SVGLinearGradientElement","SVGLineElement","SVGMarkerElement","SVGMaskElement","SVGMatrix","SVGMetadataElement","SVGMPathElement","SVGNumber","SVGNumberList","SVGPathElement","SVGPatternElement","SVGPoint","SVGPointList","SVGPolygonElement","SVGPolylineElement","SVGPreserveAspectRatio","SVGRadialGradientElement","SVGRect","SVGRectElement","SVGScriptElement","SVGSetElement","SVGStopElement","SVGStringList","SVGStyleElement","SVGSVGElement","SVGSwitchElement","SVGSymbolElement","SVGTextContentElement","SVGTextElement","SVGTextPathElement","SVGTextPositioningElement","SVGTitleElement","SVGTransform","SVGTransformList","SVGTSpanElement","SVGUnitTypes","SVGUseElement","SVGViewElement","TaskAttributionTiming","Text","TextDecoder","TextDecoderStream","TextEncoder","TextEncoderStream","TextEvent","TextMetrics","TextTrack","TextTrackCue","TextTrackCueList","TextTrackList","TimeRanges","ToggleEvent","toolbar","top","Touch","TouchEvent","TouchList","TrackEvent","TransformStream","TransformStreamDefaultController","TransitionEvent","TreeWalker","UIEvent","URL","URLSearchParams","ValidityState","visualViewport","VisualViewport","VTTCue","WaveShaperNode","WebAssembly","WebGL2RenderingContext","WebGLActiveInfo","WebGLBuffer","WebGLContextEvent","WebGLFramebuffer","WebGLProgram","WebGLQuery","WebGLRenderbuffer","WebGLRenderingContext","WebGLSampler","WebGLShader","WebGLShaderPrecisionFormat","WebGLSync","WebGLTexture","WebGLTransformFeedback","WebGLUniformLocation","WebGLVertexArrayObject","WebSocket","WheelEvent","window","Window","Worker","WritableStream","WritableStreamDefaultController","WritableStreamDefaultWriter","XMLDocument","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload","XMLSerializer","XPathEvaluator","XPathExpression","XPathResult","XSLTProcessor"];function av(e){return e.filter(function(r){return r in this?!1:this[r]=!0},Object.create(null))}function ei(e){return e.reduce(function(t,r){return t[r]=!0,t},Object.create(null))}var ov=ei(nv.concat([]));function iv(e){return e in ov}var sv=Object.defineProperty,uv=window.__QIANKUN_DEVELOPMENT__?["__REACT_ERROR_OVERLAY_GLOBAL_HOOK__","event"]:[],Xu=["System","__cjsWrapper"].concat(uv),wo=!1,Xl=["document","top","parent","eval"],Kl=["window","self","globalThis","hasOwnProperty"].concat([]),kr=Array.from(new Set(Zl.apply(void 0,[rv.concat(Kl).concat("requestAnimationFrame")].concat(Xl)))),cv=ei(kr),lv=ei(Zl.apply(void 0,[kr].concat($e(Xl.concat(Kl))))),Ku=new Map([["fetch",!0],["mockDomAPIInBlackList",!1]]);function fv(e,t){var r=new Map,n={};return Object.getOwnPropertyNames(e).filter(function(a){var o=Object.getOwnPropertyDescriptor(e,a);return!o?.configurable}).forEach(function(a){var o=Object.getOwnPropertyDescriptor(e,a);if(o){var i=Object.prototype.hasOwnProperty.call(o,"get");(a==="top"||a==="parent"||a==="self"||a==="window"||a==="document"&&t||wo)&&(o.configurable=!0,i||(o.writable=!0)),i&&r.set(a,!0),sv(n,a,Object.freeze(o))}}),{fakeWindow:n,propertiesWithGetter:r}}var Ua=0,dv=function(){function e(t){var r=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:window,a=arguments.length>2?arguments[2]:void 0;Tt(this,e),this.updatedValueSet=new Set,this.document=document,this.name=void 0,this.type=void 0,this.proxy=void 0,this.sandboxRunning=!0,this.latestSetProp=null,this.globalWhitelistPrevDescriptor={},this.globalContext=void 0,this.name=t,this.globalContext=n,this.type=ge.Proxy;var o=this.updatedValueSet,i=a||{},s=i.speedy,u=fv(n,!!s),c=u.fakeWindow,l=u.propertiesWithGetter,f=new Map,p=new Proxy(c,{set:function(g,d,h){if(r.sandboxRunning){if(r.registerRunningApp(t,p),typeof d=="string"&&Xu.indexOf(d)!==-1)r.globalWhitelistPrevDescriptor[d]=Object.getOwnPropertyDescriptor(n,d),n[d]=h;else if(!g.hasOwnProperty(d)&&n.hasOwnProperty(d)){var y=Object.getOwnPropertyDescriptor(n,d),E=y.writable,_=y.configurable,T=y.enumerable,w=y.set;(E||w)&&Object.defineProperty(g,d,{configurable:_,enumerable:T,writable:!0,value:h})}else g[d]=h;return o.add(d),r.latestSetProp=d,!0}return!0},get:function(g,d){if(r.registerRunningApp(t,p),d===Symbol.unscopables)return lv;if(d==="window"||d==="self"||d==="globalThis"||wo)return p;if(d==="top"||d==="parent"||wo)return n===n.parent?p:n[d];if(d==="hasOwnProperty")return v;if(d==="document")return r.document;if(d==="eval")return eval;if(d==="string"&&Xu.indexOf(d)!==-1)return n[d];var h=l.has(d)?n:d in g?g:n,y=h[d];if(kh(h,d)||!iv(d)&&!Ku.has(d))return y;var E=Ku.get(d)?ae:n;return Yl(E,y)},has:function(g,d){return d in cv||d in g||d in n},getOwnPropertyDescriptor:function(g,d){if(g.hasOwnProperty(d)){var h=Object.getOwnPropertyDescriptor(g,d);return f.set(d,"target"),h}if(n.hasOwnProperty(d)){var y=Object.getOwnPropertyDescriptor(n,d);return f.set(d,"globalContext"),y&&!y.configurable&&(y.configurable=!0),y}},ownKeys:function(g){return av(Reflect.ownKeys(n).concat(Reflect.ownKeys(g)))},defineProperty:function(g,d,h){var y=f.get(d);switch(y){case"globalContext":return Reflect.defineProperty(n,d,h);default:return Reflect.defineProperty(g,d,h)}},deleteProperty:function(g,d){return r.registerRunningApp(t,p),g.hasOwnProperty(d)&&(delete g[d],o.delete(d)),!0},getPrototypeOf:function(){return Reflect.getPrototypeOf(n)}});this.proxy=p,Ua++;function v(m){return this!==p&&this!==null&&me(this)==="object"?Object.prototype.hasOwnProperty.call(this,m):c.hasOwnProperty(m)||n.hasOwnProperty(m)}}return Ct(e,[{key:"active",value:function(){this.sandboxRunning||Ua++,this.sandboxRunning=!0}},{key:"inactive",value:function(){var r=this;--Ua===0&&Object.keys(this.globalWhitelistPrevDescriptor).forEach(function(n){var a=r.globalWhitelistPrevDescriptor[n];a?Object.defineProperty(r.globalContext,n,a):delete r.globalContext[n]}),this.sandboxRunning=!1}},{key:"patchDocument",value:function(r){this.document=r}},{key:"registerRunningApp",value:function(r,n){if(this.sandboxRunning){var a=vo();(!a||a.name!==r)&&qh({name:r,window:n}),Oh(Hh)}}}]),e}(),ti="SCRIPT",Sr="LINK",ri="STYLE",zl=Symbol("target"),Jl=Symbol("refNodeNo"),et=Symbol("qiankun-overwritten"),Ut=function(t){return t.querySelector(Ft)};function pv(e){return!e.type||["text/javascript","module","application/javascript","text/ecmascript","application/ecmascript"].indexOf(e.type)!==-1}function ni(e){return e?.toUpperCase()===Sr||e?.toUpperCase()===ri||e?.toUpperCase()===ti}function Ql(e){var t,r;return!e.textContent&&(((t=e.sheet)===null||t===void 0?void 0:t.cssRules.length)||((r=uf(e))===null||r===void 0?void 0:r.length))}var bo=new Map;function Be(e,t,r){var n=bo.get(e)||{bootstrappingPatchCount:0,mountingPatchCount:0};switch(t){case"increase":n["".concat(r,"PatchCount")]+=1;break;case"decrease":n["".concat(r,"PatchCount")]>0&&(n["".concat(r,"PatchCount")]-=1);break}bo.set(e,n)}function ef(){return Array.from(bo.entries()).every(function(e){var t=Do(e,2),r=t[1],n=r.bootstrappingPatchCount,a=r.mountingPatchCount;return n===0&&a===0})}function tf(e,t){return Object.defineProperties(e,{srcElement:{get:t},target:{get:t}}),e}function rf(e){var t=new CustomEvent("load"),r=tf(t,function(){return e});rt(e.onload)?e.onload(r):e.dispatchEvent(r)}function nf(e){var t=new CustomEvent("error"),r=tf(t,function(){return e});rt(e.onerror)?e.onerror(r):e.dispatchEvent(r)}function mv(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:fetch,n=document.createElement("style"),a=e.href;return n.dataset.qiankunHref=a,r(a).then(function(o){return o.text()}).then(function(o){n.appendChild(document.createTextNode(o)),t(n),rf(e)}).catch(function(){return nf(e)}),n}var zu=function(t,r,n){Object.defineProperty(t,r,{configurable:!0,enumerable:!1,writable:!0,value:n})},af=new WeakMap,So=new WeakMap,of=new WeakMap;function sf(e){e.forEach(function(t){t instanceof HTMLStyleElement&&Ql(t)&&t.sheet&&af.set(t,t.sheet.cssRules)})}function uf(e){return af.get(e)}function Ya(e){function t(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,a=r,o=e.rawDOMAppendOrInsertBefore,i=e.isInvokedByMicroApp,s=e.containerConfigGetter,u=e.target,c=u===void 0?"body":u;if(!ni(a.tagName)||!i(a))return o.call(this,a,n);if(a.tagName){var l=s(a),f=l.appName,p=l.appWrapperGetter,v=l.proxy,m=l.strictGlobal,g=l.speedySandbox,d=l.dynamicStyleSheetElements,h=l.scopedCSS,y=l.excludeAssetFilter;switch(a.tagName){case Sr:case ri:{var E=r,_=E,T=_.href;if(y&&T&&y(T))return o.call(this,a,n);zu(E,zl,c);var w=p();if(h){var A,b=((A=a.tagName)===null||A===void 0?void 0:A.toUpperCase())===Sr&&a.rel==="stylesheet"&&a.href;if(b){var P,C=typeof ht.fetch=="function"?ht.fetch:(P=ht.fetch)===null||P===void 0?void 0:P.fn;E=mv(a,function(Pe){return yo(w,Pe,f)},C),of.set(a,E)}else yo(w,E,f)}var M=c==="head"?Ut(w):w,O=M.contains(n)?n:null,z;O&&(z=Array.from(M.childNodes).indexOf(O));var D=o.call(M,E,O);return typeof z=="number"&&z!==-1&&zu(E,Jl,z),d.push(E),D}case ti:{var U=a,X=U.src,Y=U.text;if(y&&X&&y(X)||!pv(a))return o.call(this,a,n);var V=p(),q=c==="head"?Ut(V):V,j=ht.fetch,te=q.contains(n)?n:null,re=g?kr:[];if(X){var fe=!1;br(null,[X],v,{fetch:j,strictGlobal:m,scopedGlobalVariables:re,beforeExec:function(){var Ke=function(){var Fe=Object.getOwnPropertyDescriptor(document,"currentScript");return!Fe||Fe.configurable};Ke()&&(Object.defineProperty(document,"currentScript",{get:function(){return a},configurable:!0}),fe=!0)},success:function(){rf(a),fe&&delete document.currentScript,a=null},error:function(){nf(a),fe&&delete document.currentScript,a=null}});var _e=document.createComment("dynamic script ".concat(X," replaced by qiankun"));return So.set(a,_e),o.call(q,_e,te)}br(null,["<script>".concat(Y,"<\/script>")],v,{strictGlobal:m,scopedGlobalVariables:re});var be=document.createComment("dynamic inline script replaced by qiankun");return So.set(a,be),o.call(q,be,te)}}}return o.call(this,a,n)}return t[et]=!0,t}function Ju(e,t,r,n){function a(o){var i=o.tagName;if(!ni(i)||!n(o))return e.call(this,o);try{var s,u=t(o),c=u.appWrapperGetter,l=u.dynamicStyleSheetElements;switch(i){case ri:case Sr:{s=of.get(o)||o;var f=l.indexOf(s);f!==-1&&l.splice(f,1);break}case ti:{s=So.get(o)||o;break}default:s=o}var p=c(),v=r==="head"?Ut(p):p;if(v.contains(s))return e.call(s.parentNode,s)}catch(m){console.warn(m)}return e.call(this,o)}return a[et]=!0,a}function cf(e,t){var r=HTMLHeadElement.prototype.appendChild,n=HTMLBodyElement.prototype.appendChild,a=HTMLHeadElement.prototype.insertBefore;r[et]!==!0&&n[et]!==!0&&a[et]!==!0&&(HTMLHeadElement.prototype.appendChild=Ya({rawDOMAppendOrInsertBefore:r,containerConfigGetter:t,isInvokedByMicroApp:e,target:"head"}),HTMLBodyElement.prototype.appendChild=Ya({rawDOMAppendOrInsertBefore:n,containerConfigGetter:t,isInvokedByMicroApp:e,target:"body"}),HTMLHeadElement.prototype.insertBefore=Ya({rawDOMAppendOrInsertBefore:a,containerConfigGetter:t,isInvokedByMicroApp:e,target:"head"}));var o=HTMLHeadElement.prototype.removeChild,i=HTMLBodyElement.prototype.removeChild;return o[et]!==!0&&i[et]!==!0&&(HTMLHeadElement.prototype.removeChild=Ju(o,t,"head",e),HTMLBodyElement.prototype.removeChild=Ju(i,t,"body",e)),function(){HTMLHeadElement.prototype.appendChild=r,HTMLHeadElement.prototype.removeChild=o,HTMLBodyElement.prototype.appendChild=n,HTMLBodyElement.prototype.removeChild=i,HTMLHeadElement.prototype.insertBefore=a}}function lf(e,t){e.forEach(function(r){var n=t(r);if(n&&r instanceof HTMLStyleElement&&Ql(r)){var a=uf(r);if(a)for(var o=0;o<a.length;o++){var i=a[o],s=r.sheet;s.insertRule(i.cssText,s.cssRules.length)}}})}function Er(e,t,r){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,a=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1,o=arguments.length>5?arguments[5]:void 0,i=r.proxy,s=[],u=cf(function(){return pl(window.location).some(function(c){return c===e})},function(){return{appName:e,appWrapperGetter:t,proxy:i,strictGlobal:!1,speedySandbox:!1,scopedCSS:a,dynamicStyleSheetElements:s,excludeAssetFilter:o}});return n||Be(e,"increase","bootstrapping"),n&&Be(e,"increase","mounting"),function(){return n||Be(e,"decrease","bootstrapping"),n&&Be(e,"decrease","mounting"),ef()&&u(),sf(s),function(){lf(s,function(f){var p=t();return p.contains(f)?!1:(document.head.appendChild.call(p,f),!0)}),n&&(s=[])}}}Object.defineProperty(ae,"__proxyAttachContainerConfigMap__",{enumerable:!1,writable:!0});Object.defineProperty(ae,"__currentLockingSandbox__",{enumerable:!1,writable:!0,configurable:!0});var hv=HTMLHeadElement.prototype.appendChild,vv=HTMLHeadElement.prototype.insertBefore;ae.__proxyAttachContainerConfigMap__=ae.__proxyAttachContainerConfigMap__||new WeakMap;var _r=ae.__proxyAttachContainerConfigMap__,Eo=new WeakMap,Qu=new WeakMap,xe=new WeakMap;function gv(e){var t=e.sandbox,r=e.speedy,n=function(d,h){var y=_r.get(h);y&&Eo.set(d,y)};if(r){var a={},o=new Proxy(document,{set:function(d,h,y){switch(h){case"createElement":{a.createElement=y;break}case"querySelector":{a.querySelector=y;break}default:d[h]=y;break}return!0},get:function(d,h,y){switch(h){case"createElement":{var E=a.createElement||d.createElement;return function(){ae.__currentLockingSandbox__||(ae.__currentLockingSandbox__=t.name);for(var A=arguments.length,b=new Array(A),P=0;P<A;P++)b[P]=arguments[P];var C=E.call.apply(E,[d].concat(b));return ae.__currentLockingSandbox__===t.name&&(n(C,t.proxy),delete ae.__currentLockingSandbox__),C}}case"querySelector":{var _=a.querySelector||d.querySelector;return function(){for(var A=arguments.length,b=new Array(A),P=0;P<A;P++)b[P]=arguments[P];var C=b[0];switch(C){case"head":{var M=_r.get(t.proxy);if(M){var O=Ut(M.appWrapperGetter());return O.appendChild=HTMLHeadElement.prototype.appendChild,O.insertBefore=HTMLHeadElement.prototype.insertBefore,O.removeChild=HTMLHeadElement.prototype.removeChild,O}break}}return _.call.apply(_,[d].concat(b))}}}var T=d[h];return Wl(T)&&!ql(T)?function(){for(var A=arguments.length,b=new Array(A),P=0;P<A;P++)b[P]=arguments[P];return T.call.apply(T,[d].concat($e(b.map(function(C){return C===y?d:C}))))}:T}});t.patchDocument(o);var i=MutationObserver.prototype.observe;if(!xe.has(i)){var s=function(d,h){var y=d instanceof Document?ju:d;return i.call(this,y,h)};MutationObserver.prototype.observe=s,xe.set(i,s)}var u=Node.prototype.compareDocumentPosition;xe.has(u)||(Node.prototype.compareDocumentPosition=function(d){var h=d instanceof Document?ju:d;return u.call(this,h)},xe.set(u,Node.prototype.compareDocumentPosition));var c=Object.getOwnPropertyDescriptor(Node.prototype,"parentNode");if(c&&!xe.has(c)){var l=c.get,f=c.configurable;if(l&&f){var p=$($({},c),{},{get:function(){var d=l.call(this);if(d instanceof Document){var h,y=(h=vo())===null||h===void 0?void 0:h.window;if(y)return y.document}return d}});Object.defineProperty(Node.prototype,"parentNode",p),xe.set(c,p)}}return function(){MutationObserver.prototype.observe=i,xe.delete(i),Node.prototype.compareDocumentPosition=u,xe.delete(u),c&&(Object.defineProperty(Node.prototype,"parentNode",c),xe.delete(c))}}var v=Qu.get(document.createElement);if(!v){var m=document.createElement;Document.prototype.createElement=function(d,h){var y=m.call(this,d,h);if(ni(d)){var E=vo()||{},_=E.window;_&&n(y,_)}return y},document.hasOwnProperty("createElement")&&(document.createElement=Document.prototype.createElement),Qu.set(Document.prototype.createElement,m)}return function(){v&&(Document.prototype.createElement=v,document.createElement=v)}}function ff(e,t,r){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,a=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1,o=arguments.length>5?arguments[5]:void 0,i=arguments.length>6&&arguments[6]!==void 0?arguments[6]:!1,s=r.proxy,u=_r.get(s);u||(u={appName:e,proxy:s,appWrapperGetter:t,dynamicStyleSheetElements:[],strictGlobal:!0,speedySandbox:i,excludeAssetFilter:o,scopedCSS:a},_r.set(s,u));var c=u,l=c.dynamicStyleSheetElements,f=cf(function(v){return Eo.has(v)},function(v){return Eo.get(v)}),p=gv({sandbox:r,speedy:i});return n||Be(e,"increase","bootstrapping"),n&&Be(e,"increase","mounting"),function(){return n||Be(e,"decrease","bootstrapping"),n&&Be(e,"decrease","mounting"),ef()&&(f(),p()),sf(l),function(){lf(l,function(g){var d=t();if(!d.contains(g)){var h=g[zl]==="head"?Ut(d):d,y=g[Jl];if(typeof y=="number"&&y!==-1){var E=h.childNodes[y]||null;return vv.call(h,g,E),!0}else return hv.call(h,g),!0}return!1})}}}function yv(){var e=function(a){return gr},t=[],r=[];return window.g_history&&rt(window.g_history.listen)&&(e=window.g_history.listen.bind(window.g_history),window.g_history.listen=function(n){t.push(n);var a=e(n);return r.push(a),function(){a(),r.splice(r.indexOf(a),1),t.splice(t.indexOf(n),1)}}),function(){var a=gr;return t.length&&(a=function(){t.forEach(function(i){return window.g_history.listen(i)})}),r.forEach(function(o){return o()}),window.g_history&&rt(window.g_history.listen)&&(window.g_history.listen=e),a}}var ec=window.setInterval,tc=window.clearInterval;function wv(e){var t=[];return e.clearInterval=function(r){return t=t.filter(function(n){return n!==r}),tc.call(window,r)},e.setInterval=function(r,n){for(var a=arguments.length,o=new Array(a>2?a-2:0),i=2;i<a;i++)o[i-2]=arguments[i];var s=ec.apply(void 0,[r,n].concat(o));return t=[].concat($e(t),[s]),s},function(){return t.forEach(function(n){return e.clearInterval(n)}),e.setInterval=ec,e.clearInterval=tc,gr}}var rc=window.addEventListener,nc=window.removeEventListener;function bv(e){var t=new Map;return e.addEventListener=function(r,n,a){var o=t.get(r)||[];return t.set(r,[].concat($e(o),[n])),rc.call(window,r,n,a)},e.removeEventListener=function(r,n,a){var o=t.get(r);return o&&o.length&&o.indexOf(n)!==-1&&o.splice(o.indexOf(n),1),nc.call(window,r,n,a)},function(){return t.forEach(function(n,a){return $e(n).forEach(function(o){return e.removeEventListener(a,o)})}),e.addEventListener=rc,e.removeEventListener=nc,gr}}function Sv(e,t,r,n,a,o){var i,s=[function(){return wv(r.proxy)},function(){return bv(r.proxy)},function(){return yv()}],u=Ue(Ue(Ue({},ge.LegacyProxy,[].concat(s,[function(){return Er(e,t,r,!0,n,a)}])),ge.Proxy,[].concat(s,[function(){return ff(e,t,r,!0,n,a,o)}])),ge.Snapshot,[].concat(s,[function(){return Er(e,t,r,!0,n,a)}]));return(i=u[r.type])===null||i===void 0?void 0:i.map(function(c){return c()})}function Ev(e,t,r,n,a,o){var i,s=Ue(Ue(Ue({},ge.LegacyProxy,[function(){return Er(e,t,r,!1,n,a)}]),ge.Proxy,[function(){return ff(e,t,r,!1,n,a,o)}]),ge.Snapshot,[function(){return Er(e,t,r,!1,n,a)}]);return(i=s[r.type])===null||i===void 0?void 0:i.map(function(u){return u()})}function ac(e,t){for(var r in e)(e.hasOwnProperty(r)||r==="clearInterval")&&t(r)}var _v=function(){function e(t){Tt(this,e),this.proxy=void 0,this.name=void 0,this.type=void 0,this.sandboxRunning=!0,this.windowSnapshot=void 0,this.modifyPropsMap={},this.name=t,this.proxy=window,this.type=ge.Snapshot}return Ct(e,[{key:"active",value:function(){var r=this;this.windowSnapshot={},ac(window,function(n){r.windowSnapshot[n]=window[n]}),Object.keys(this.modifyPropsMap).forEach(function(n){window[n]=r.modifyPropsMap[n]}),this.sandboxRunning=!0}},{key:"inactive",value:function(){var r=this;this.modifyPropsMap={},ac(window,function(n){window[n]!==r.windowSnapshot[n]&&(r.modifyPropsMap[n]=window[n],window[n]=r.windowSnapshot[n])}),this.sandboxRunning=!1}},{key:"patchDocument",value:function(){}}]),e}();function Pv(e,t,r,n,a,o,i){var s;window.Proxy?s=n?new Uh(e,o):new dv(e,o,{speedy:!!i}):s=new _v(e);var u=Ev(e,t,s,r,a,i),c=[],l=[];return{instance:s,mount:function(){return N(R.mark(function p(){var v,m;return R.wrap(function(d){for(;;)switch(d.prev=d.next){case 0:s.active(),v=l.slice(0,u.length),m=l.slice(u.length),v.length&&v.forEach(function(h){return h()}),c=Sv(e,t,s,r,a,i),m.length&&m.forEach(function(h){return h()}),l=[];case 7:case"end":return d.stop()}},p)}))()},unmount:function(){return N(R.mark(function p(){return R.wrap(function(m){for(;;)switch(m.prev=m.next){case 0:l=[].concat($e(u),$e(c)).map(function(g){return g()}),s.inactive();case 2:case"end":return m.stop()}},p)}))()}}}var Tv=["singular","sandbox","excludeAssetFilter","globalContext"];function _o(e,t){if(!e)throw t?new Bt(t):new Bt("element not existed!")}function Ot(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:window;return e.length?e.reduce(function(n,a){return n.then(function(){return a(t,r)})},Promise.resolve()):Promise.resolve()}function cr(e,t){return Po.apply(this,arguments)}function Po(){return Po=N(R.mark(function e(t,r){return R.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.abrupt("return",typeof t=="function"?t(r):!!t);case 1:case"end":return a.stop()}},e)})),Po.apply(this,arguments)}var df=!!document.head.attachShadow||!!document.head.createShadowRoot;function oc(e,t,r,n){var a=document.createElement("div");a.innerHTML=e;var o=a.firstChild;if(t)if(!df)console.warn("[qiankun]: As current browser not support shadow dom, your strictStyleIsolation configuration will be ignored!");else{var i=o.innerHTML;o.innerHTML="";var s;o.attachShadow?s=o.attachShadow({mode:"open"}):s=o.createShadowRoot(),s.innerHTML=i}if(r){var u=o.getAttribute(go);u||o.setAttribute(go,n);var c=o.querySelectorAll("style")||[];om(c,function(l){yo(o,l,n)})}return o}function ic(e,t,r,n,a){return function(){if(t){if(r)throw new Bt("strictStyleIsolation can not be used with legacy render!");if(n)throw new Bt("experimentalStyleIsolation can not be used with legacy render!");var o=document.getElementById(Hl(e));return _o(o,"Wrapper element for ".concat(e," is not existed!")),o}var i=a();return _o(i,"Wrapper element for ".concat(e," is not existed!")),r&&df?i.shadowRoot:i}}var Cv=HTMLElement.prototype.appendChild,Av=HTMLElement.prototype.removeChild;function Rv(e,t,r){var n=function(o,i){var s=o.element,u=o.loading,c=o.container;if(r)return r({loading:u,appContent:s?t:""});var l=Ul(c);if(i!=="unmounted"){var f=function(){switch(i){case"loading":case"mounting":return"Target container with ".concat(c," not existed while ").concat(e," ").concat(i,"!");case"mounted":return"Target container with ".concat(c," not existed after ").concat(e," ").concat(i,"!");default:return"Target container with ".concat(c," not existed while ").concat(e," rendering!")}}();_o(l,f)}if(l&&!l.contains(s)){for(;l.firstChild;)Av.call(l,l.firstChild);s&&Cv.call(l,s)}};return n}function xv(e,t,r,n){if(Ga(e))return e;if(n){var a=r[n];if(Ga(a))return a}var o=r[t];if(Ga(o))return o;throw new Bt("You need to export lifecycle functions in ".concat(t," entry"))}var Qe;function Mv(e){return To.apply(this,arguments)}function To(){return To=N(R.mark(function e(t){var r,n,a,o,i,s,u,c,l,f,p,v,m,g,d,h,y,E,_,T,w,A,b,P,C,M,O,z,D,U,X,Y,V,q,j,te,re,fe,_e,be,Pe,Ke,ut,Fe,er,tr,ze,rr,nr,F,Ne,I,Te,Ce,ar,je,de,Z=arguments;return R.wrap(function(K){for(;;)switch(K.prev=K.next){case 0:return a=Z.length>1&&Z[1]!==void 0?Z[1]:{},o=Z.length>2?Z[2]:void 0,i=t.entry,s=t.name,u=Nh(s),c=a.singular,l=c===void 0?!1:c,f=a.sandbox,p=f===void 0?!0:f,v=a.excludeAssetFilter,m=a.globalContext,g=m===void 0?window:m,d=np(a,Tv),K.next=9,Dm(i,d);case 9:return h=K.sent,y=h.template,E=h.execScripts,_=h.assetPublicPath,T=h.getExternalScripts,K.next=16,T();case 16:return K.next=18,cr(l,t);case 18:if(!K.sent){K.next=21;break}return K.next=21,Qe&&Qe.promise;case 21:return w=Dh(u,p)(y),A=me(p)==="object"&&!!p.strictStyleIsolation,b=jh(p),P=oc(w,A,b,u),C="container"in t?t.container:void 0,M="render"in t?t.render:void 0,O=Rv(u,w,M),O({element:P,loading:!0,container:C},"loading"),z=ic(u,!!M,A,b,function(){return P}),D=g,U=function(){return Promise.resolve()},X=function(){return Promise.resolve()},Y=me(p)==="object"&&!!p.loose,V=me(p)==="object"?p.speedy!==!1:!0,p&&(q=Pv(u,z,b,Y,v,D,V),D=q.instance.proxy,U=q.mount,X=q.unmount),j=Ml({},jm(D,_),o,function(Ae,oe){return wl(Ae??[],oe??[])}),te=j.beforeUnmount,re=te===void 0?[]:te,fe=j.afterUnmount,_e=fe===void 0?[]:fe,be=j.afterMount,Pe=be===void 0?[]:be,Ke=j.beforeMount,ut=Ke===void 0?[]:Ke,Fe=j.beforeLoad,er=Fe===void 0?[]:Fe,K.next=40,Ot(mt(er),t,D);case 40:return K.next=42,E(D,p&&!Y,{scopedGlobalVariables:V?kr:[]});case 42:return tr=K.sent,ze=xv(tr,s,D,(r=q)===null||r===void 0||(n=r.instance)===null||n===void 0?void 0:n.latestSetProp),rr=ze.bootstrap,nr=ze.mount,F=ze.unmount,Ne=ze.update,I=ih(u),Te=I.onGlobalStateChange,Ce=I.setGlobalState,ar=I.offGlobalStateChange,je=function(oe){return P=oe},de=function(){var oe=arguments.length>0&&arguments[0]!==void 0?arguments[0]:C,Q,ct,Mt={name:u,bootstrap:rr,mount:[N(R.mark(function L(){return R.wrap(function(S){for(;;)switch(S.prev=S.next){case 0:case 1:case"end":return S.stop()}},L)})),N(R.mark(function L(){return R.wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return S.next=2,cr(l,t);case 2:if(S.t0=S.sent,!S.t0){S.next=5;break}S.t0=Qe;case 5:if(!S.t0){S.next=7;break}return S.abrupt("return",Qe.promise);case 7:return S.abrupt("return",void 0);case 8:case"end":return S.stop()}},L)})),N(R.mark(function L(){return R.wrap(function(S){for(;;)switch(S.prev=S.next){case 0:Q=P,ct=ic(u,!!M,A,b,function(){return Q});case 2:case"end":return S.stop()}},L)})),N(R.mark(function L(){var B;return R.wrap(function(ie){for(;;)switch(ie.prev=ie.next){case 0:B=oe!==C,(B||!Q)&&(Q=oc(w,A,b,u),je(Q)),O({element:Q,loading:!0,container:oe},"mounting");case 3:case"end":return ie.stop()}},L)})),U,N(R.mark(function L(){return R.wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return S.abrupt("return",Ot(mt(ut),t,D));case 1:case"end":return S.stop()}},L)})),function(){var L=N(R.mark(function B(S){return R.wrap(function(Re){for(;;)switch(Re.prev=Re.next){case 0:return Re.abrupt("return",nr($($({},S),{},{container:ct(),setGlobalState:Ce,onGlobalStateChange:Te})));case 1:case"end":return Re.stop()}},B)}));return function(B){return L.apply(this,arguments)}}(),N(R.mark(function L(){return R.wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return S.abrupt("return",O({element:Q,loading:!1,container:oe},"mounted"));case 1:case"end":return S.stop()}},L)})),N(R.mark(function L(){return R.wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return S.abrupt("return",Ot(mt(Pe),t,D));case 1:case"end":return S.stop()}},L)})),N(R.mark(function L(){return R.wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return S.next=2,cr(l,t);case 2:if(!S.sent){S.next=4;break}Qe=new Bl;case 4:case"end":return S.stop()}},L)})),N(R.mark(function L(){return R.wrap(function(S){for(;;)switch(S.prev=S.next){case 0:case 1:case"end":return S.stop()}},L)}))],unmount:[N(R.mark(function L(){return R.wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return S.abrupt("return",Ot(mt(re),t,D));case 1:case"end":return S.stop()}},L)})),function(){var L=N(R.mark(function B(S){return R.wrap(function(Re){for(;;)switch(Re.prev=Re.next){case 0:return Re.abrupt("return",F($($({},S),{},{container:ct()})));case 1:case"end":return Re.stop()}},B)}));return function(B){return L.apply(this,arguments)}}(),X,N(R.mark(function L(){return R.wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return S.abrupt("return",Ot(mt(_e),t,D));case 1:case"end":return S.stop()}},L)})),N(R.mark(function L(){return R.wrap(function(S){for(;;)switch(S.prev=S.next){case 0:O({element:null,loading:!1,container:oe},"unmounted"),ar(u),Q=null,je(Q);case 4:case"end":return S.stop()}},L)})),N(R.mark(function L(){return R.wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return S.next=2,cr(l,t);case 2:if(S.t0=S.sent,!S.t0){S.next=5;break}S.t0=Qe;case 5:if(!S.t0){S.next=7;break}Qe.resolve();case 7:case"end":return S.stop()}},L)}))]};return typeof Ne=="function"&&(Mt.update=Ne),Mt},K.abrupt("return",de);case 48:case"end":return K.stop()}},e)})),To.apply(this,arguments)}var ht={},Iv=!0;new Bl;var Ov=function(t){var r=t.sandbox,n=r===void 0?!0:r,a=t.singular;if(n){if(!window.Proxy)return console.warn("[qiankun] Missing window.Proxy, proxySandbox will degenerate into snapshotSandbox"),a===!1&&console.warn("[qiankun] Setting singular as false may cause unexpected behavior while your browser not support window.Proxy"),$($({},t),{},{sandbox:me(n)==="object"?$($({},n),{},{loose:!0}):{loose:!0}});if(!Gh()&&(n===!0||me(n)==="object"&&n.speedy!==!1))return console.warn("[qiankun] Speedy mode will turn off as const destruct assignment not supported in current browser!"),$($({},t),{},{sandbox:me(n)==="object"?$($({},n),{},{speedy:!1}):{speedy:!1}})}return t},lr=new Map,$a=new Map;function Lv(e,t,r){var n=e.props,a=e.name,o="container"in e?e.container:void 0,i=Wh(o),s="".concat(a,"-").concat(i),u,c=function(g){var d=g;if(o&&i){var h=$a.get(s);if(h?.length){var y=[N(R.mark(function E(){var _,T;return R.wrap(function(A){for(;;)switch(A.prev=A.next){case 0:return _=h.slice(0,h.indexOf(u)),T=_.filter(function(b){return b.getStatus()!=="LOAD_ERROR"&&b.getStatus()!=="SKIP_BECAUSE_BROKEN"}),A.next=4,Promise.all(T.map(function(b){return b.unmountPromise}));case 4:case"end":return A.stop()}},E)}))].concat($e(mt(d.mount)));d=$($({},g),{},{mount:y})}}return $($({},d),{},{bootstrap:function(){return Promise.resolve()}})},l=function(){var m=N(R.mark(function g(){var d,h,y,E,_;return R.wrap(function(w){for(;;)switch(w.prev=w.next){case 0:if(d=Ov(t??$($({},ht),{},{singular:!1})),h=d.$$cacheLifecycleByAppName,!o){w.next=21;break}if(!h){w.next=12;break}if(y=lr.get(a),!y){w.next=12;break}return w.t0=c,w.next=9,y;case 9:return w.t1=w.sent,w.t2=(0,w.t1)(o),w.abrupt("return",(0,w.t0)(w.t2));case 12:if(!i){w.next=21;break}if(E=lr.get(s),!E){w.next=21;break}return w.t3=c,w.next=18,E;case 18:return w.t4=w.sent,w.t5=(0,w.t4)(o),w.abrupt("return",(0,w.t3)(w.t5));case 21:return _=Mv(e,d,r),o&&(h?lr.set(a,_):i&&lr.set(s,_)),w.next=25,_;case 25:return w.t6=w.sent,w.abrupt("return",(0,w.t6)(o));case 27:case"end":return w.stop()}},g)}));return function(){return m.apply(this,arguments)}}();if(t?.autoStart!==!1){var f;gl({urlRerouteOnly:(f=ht.urlRerouteOnly)!==null&&f!==void 0?f:Iv})}if(u=nl(l,$({domElement:document.createElement("div")},n)),o&&i){var p=$a.get(s)||[];p.push(u),$a.set(s,p);var v=function(){var g=p.indexOf(u);p.splice(g,1),u=null};u.unmountPromise.then(v).catch(v)}return u}const Co="friday-app",kv=ue.SHD_G123_CUSTOMER_SVC,pf="customer-service-close";let vt,Ie,mf;async function Gv(){const e=Dr();if(e!=="MOUNTED"){if(e==="LOADING_SOURCE_CODE"||e==="BOOTSTRAPPING"||e==="MOUNTING"){Gr();return}try{vt=Lv({name:"g123-friday",entry:kv,container:`#${Co}`,props:mf},{sandbox:{experimentalStyleIsolation:!0}}),Ie.style.display="block",await jr(vt.loadPromise),console.log("[CS_APP]: fridayapp loaded successfully."),await jr(vt.bootstrapPromise),console.log("[CS_APP]: fridayapp bootstrapped successfully."),await jr(vt.mountPromise),console.log("[CS_APP]: fridayapp mounted successfully.")}catch(t){qv("[CS_APP]: failed to load or mount fridayapp",t)}}}const Dv=e=>{e.addEventListener("click",t=>{t.stopPropagation(),t.target===e.querySelector("#jarvis")&&Gr()})};function Fv(){document.getElementById(Co)?.remove(),Ie=document.createElement("div"),Ie.setAttribute("id",Co),Ie.setAttribute("class","CustomerSupportBox"),Ie.style.display="none",document.body.append(Ie),Dv(Ie)}const Gr=()=>{Ie&&(Ie.style.display="none",vt?.unmount())},Nv=e=>{if(!e){console.error("[CS_APP]: csPropsData is empty");return}mf=e,Gv()};function jv(e){return typeof e=="object"&&e?.data?.type===pf}function Vv(e){if(!jv(e))return;const{data:t}=e;t.type===pf&&Gr()}const Wv=_t(async()=>{console.log("[CS_APP]: init fridayapp"),Fv(),window.addEventListener("message",Vv,!1)}),Dr=()=>vt?.getStatus();function qv(e,t){Gr(),console.error(e,t),window.captureGlobalException?.(t)}const ow=Object.freeze(Object.defineProperty({__proto__:null,getFridayAppStatus:Dr,initFridayApp:Wv,showFridayApp:Nv},Symbol.toStringTag,{value:"Module"})),Hv=async()=>{const t=await Tr().currentSession();if(!t)throw new Error("Session is unavailable");const{code:r}=t,n=new URLSearchParams({appid:window.option.appId,code:r}).toString();return`${ue.SHD_G123_WEB_SOCKET_ENDPOINT}?${n}`},Me={socket:null,interval:void 0,currConnId:""};function Bv(e){return typeof e=="object"&&e!==null&&"message_type"in e}const Ye={lastConfirmedAt:Date.now(),interval:void 0};function ai(e,t){const r=Date.now();(t||r-Ye.lastConfirmedAt>=60*1e3)&&(Ye.lastConfirmedAt=r,G.dispatch(ud(e)))}function Uv(){Ye.interval||(Ye.interval=setInterval(()=>{ai("websocket_disconnected",!1)},10*1e3))}function Yv(){Ye.interval&&(clearInterval(Ye.interval),Ye.interval=void 0)}const hf=async()=>{const e=await Hv();Me.socket&&(Me.socket.close(),Me.socket=null),Me.socket=new WebSocket(e);const{socket:t}=Me;t&&(t.addEventListener("open",()=>{console.log("websocket open"),Yv()}),t.addEventListener("close",()=>{console.log("websocket close"),Uv(),window.setTimeout(()=>{hf()},10*1e3)}),t.addEventListener("error",()=>{console.log("websocket error"),t.close()}),t.addEventListener("message",r=>{if(r.data==="+h"){Ye.lastConfirmedAt=Date.now();return}console.log("websocket message: ",r.data);try{const n=JSON.parse(r.data);if(n===null||!Bv(n)){console.error("[websocket] message is not a CourierEvent",r.data);return}if(n.message_type==="CONN_INFO"){const{conn_id:a,timeout:o}=n.data;Me.currConnId=a,Me.interval&&window.clearInterval(Me.interval),Me.interval=window.setInterval(()=>{t.send("h")},o*1e3),ai("websocket_connected",!1);return}if(n.message_type==="INMAIL")return;if(n.message_type==="CS_REPLIED"){if(Dr()==="MOUNTED")return;G.dispatch(Gc({isUnread:!0}));return}if(n.message_type==="AUTH_EVENT"){const a=JSON.parse(n.data);if(!a||typeof a!="object"){console.error("[websocket] Invalid message",n);return}if(a.targetAppCode&&a.targetAppCode!==window.option.appId)return;window.postMessage(a,window.location.origin);return}if(n.message_type==="AUXIN"){const a={type:"auxin_ws_msg",data:n.data};window.postMessage(a,window.location.origin);return}if(n.message_type==="PUBLISHER_EVENT"){const a=JSON.parse(n.data);if(!a||typeof a!="object"){console.error("[websocket] Invalid message",n);return}window.postMessage(a,window.location.origin);return}}catch(n){console.log("websocket message error: ",n)}}))},$v=async()=>{try{await hf()}catch(e){console.error("[websocket] init failed",e)}setTimeout(()=>{ai("force_check",!0)})};window.addCSMessage=()=>{Dr()!=="MOUNTED"&&G.dispatch(Gc({isUnread:!0}))};const vf={platform:!0,pwa:!0,psp:!0,auxin:!0,gameErrorTrace:!0,cs:!0,jobConsole:!0,vconsole:!0,mainApp:!0,gtm:!0,websocket:!0,favorite:!0},Zv="platform";function Xv(e){const t={};try{const n=(new URL(e).searchParams.get("disableFeatures")||"").split(",").map(a=>a.trim()).filter(Boolean);if(n.length<1)return t;if(n.indexOf(Zv)!==-1)for(const a of Object.keys(vf))t[a]=!1;else for(const a of n)t[a]=!1}catch(r){console.log("getStorage error",r)}return t}const Kv=typeof window<"u"&&window.location?Xv(window.location.href):{},mr={...vf,...Kv};var Ao=function(){return Ao=Object.assign||function(t){for(var r,n=1,a=arguments.length;n<a;n++){r=arguments[n];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o])}return t},Ao.apply(this,arguments)};function Ge(e,t,r,n){function a(o){return o instanceof r?o:new r(function(i){i(o)})}return new(r||(r=Promise))(function(o,i){function s(l){try{c(n.next(l))}catch(f){i(f)}}function u(l){try{c(n.throw(l))}catch(f){i(f)}}function c(l){l.done?o(l.value):a(l.value).then(s,u)}c((n=n.apply(e,t||[])).next())})}function De(e,t){var r={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},n,a,o,i=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),typeof Symbol=="function"&&(i[Symbol.iterator]=function(){return this}),i;function s(c){return function(l){return u([c,l])}}function u(c){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(r=0)),r;)try{if(n=1,a&&(o=c[0]&2?a.return:c[0]?a.throw||((o=a.return)&&o.call(a),0):a.next)&&!(o=o.call(a,c[1])).done)return o;switch(a=0,o&&(c=[c[0]&2,o.value]),c[0]){case 0:case 1:o=c;break;case 4:return r.label++,{value:c[1],done:!1};case 5:r.label++,a=c[1],c=[0];continue;case 7:c=r.ops.pop(),r.trys.pop();continue;default:if(o=r.trys,!(o=o.length>0&&o[o.length-1])&&(c[0]===6||c[0]===2)){r=0;continue}if(c[0]===3&&(!o||c[1]>o[0]&&c[1]<o[3])){r.label=c[1];break}if(c[0]===6&&r.label<o[1]){r.label=o[1],o=c;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(c);break}o[2]&&r.ops.pop(),r.trys.pop();continue}c=t.call(e,r)}catch(l){c=[6,l],a=0}finally{n=o=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function gf(e,t,r){if(r||arguments.length===2)for(var n=0,a=t.length,o;n<a;n++)(o||!(n in t))&&(o||(o=Array.prototype.slice.call(t,0,n)),o[n]=t[n]);return e.concat(o||Array.prototype.slice.call(t))}var yf="3.4.2";function Yt(e,t){return new Promise(function(r){return setTimeout(r,e,t)})}function zv(e,t){t===void 0&&(t=1/0);var r=window.requestIdleCallback;return r?new Promise(function(n){return r.call(window,function(){return n()},{timeout:t})}):Yt(Math.min(e,t))}function wf(e){return!!e&&typeof e.then=="function"}function sc(e,t){try{var r=e();wf(r)?r.then(function(n){return t(!0,n)},function(n){return t(!1,n)}):t(!0,r)}catch(n){t(!1,n)}}function uc(e,t,r){return r===void 0&&(r=16),Ge(this,void 0,void 0,function(){var n,a,o,i;return De(this,function(s){switch(s.label){case 0:n=Array(e.length),a=Date.now(),o=0,s.label=1;case 1:return o<e.length?(n[o]=t(e[o],o),i=Date.now(),i>=a+r?(a=i,[4,Yt(0)]):[3,3]):[3,4];case 2:s.sent(),s.label=3;case 3:return++o,[3,1];case 4:return[2,n]}})})}function $t(e){e.then(void 0,function(){})}function We(e,t){e=[e[0]>>>16,e[0]&65535,e[1]>>>16,e[1]&65535],t=[t[0]>>>16,t[0]&65535,t[1]>>>16,t[1]&65535];var r=[0,0,0,0];return r[3]+=e[3]+t[3],r[2]+=r[3]>>>16,r[3]&=65535,r[2]+=e[2]+t[2],r[1]+=r[2]>>>16,r[2]&=65535,r[1]+=e[1]+t[1],r[0]+=r[1]>>>16,r[1]&=65535,r[0]+=e[0]+t[0],r[0]&=65535,[r[0]<<16|r[1],r[2]<<16|r[3]]}function ce(e,t){e=[e[0]>>>16,e[0]&65535,e[1]>>>16,e[1]&65535],t=[t[0]>>>16,t[0]&65535,t[1]>>>16,t[1]&65535];var r=[0,0,0,0];return r[3]+=e[3]*t[3],r[2]+=r[3]>>>16,r[3]&=65535,r[2]+=e[2]*t[3],r[1]+=r[2]>>>16,r[2]&=65535,r[2]+=e[3]*t[2],r[1]+=r[2]>>>16,r[2]&=65535,r[1]+=e[1]*t[3],r[0]+=r[1]>>>16,r[1]&=65535,r[1]+=e[2]*t[2],r[0]+=r[1]>>>16,r[1]&=65535,r[1]+=e[3]*t[1],r[0]+=r[1]>>>16,r[1]&=65535,r[0]+=e[0]*t[3]+e[1]*t[2]+e[2]*t[1]+e[3]*t[0],r[0]&=65535,[r[0]<<16|r[1],r[2]<<16|r[3]]}function ft(e,t){return t%=64,t===32?[e[1],e[0]]:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t|e[0]>>>32-t]:(t-=32,[e[1]<<t|e[0]>>>32-t,e[0]<<t|e[1]>>>32-t])}function se(e,t){return t%=64,t===0?e:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t]:[e[1]<<t-32,0]}function H(e,t){return[e[0]^t[0],e[1]^t[1]]}function cc(e){return e=H(e,[0,e[0]>>>1]),e=ce(e,[4283543511,3981806797]),e=H(e,[0,e[0]>>>1]),e=ce(e,[3301882366,444984403]),e=H(e,[0,e[0]>>>1]),e}function Jv(e,t){e=e||"",t=t||0;var r=e.length%16,n=e.length-r,a=[0,t],o=[0,t],i=[0,0],s=[0,0],u=[2277735313,289559509],c=[1291169091,658871167],l;for(l=0;l<n;l=l+16)i=[e.charCodeAt(l+4)&255|(e.charCodeAt(l+5)&255)<<8|(e.charCodeAt(l+6)&255)<<16|(e.charCodeAt(l+7)&255)<<24,e.charCodeAt(l)&255|(e.charCodeAt(l+1)&255)<<8|(e.charCodeAt(l+2)&255)<<16|(e.charCodeAt(l+3)&255)<<24],s=[e.charCodeAt(l+12)&255|(e.charCodeAt(l+13)&255)<<8|(e.charCodeAt(l+14)&255)<<16|(e.charCodeAt(l+15)&255)<<24,e.charCodeAt(l+8)&255|(e.charCodeAt(l+9)&255)<<8|(e.charCodeAt(l+10)&255)<<16|(e.charCodeAt(l+11)&255)<<24],i=ce(i,u),i=ft(i,31),i=ce(i,c),a=H(a,i),a=ft(a,27),a=We(a,o),a=We(ce(a,[0,5]),[0,1390208809]),s=ce(s,c),s=ft(s,33),s=ce(s,u),o=H(o,s),o=ft(o,31),o=We(o,a),o=We(ce(o,[0,5]),[0,944331445]);switch(i=[0,0],s=[0,0],r){case 15:s=H(s,se([0,e.charCodeAt(l+14)],48));case 14:s=H(s,se([0,e.charCodeAt(l+13)],40));case 13:s=H(s,se([0,e.charCodeAt(l+12)],32));case 12:s=H(s,se([0,e.charCodeAt(l+11)],24));case 11:s=H(s,se([0,e.charCodeAt(l+10)],16));case 10:s=H(s,se([0,e.charCodeAt(l+9)],8));case 9:s=H(s,[0,e.charCodeAt(l+8)]),s=ce(s,c),s=ft(s,33),s=ce(s,u),o=H(o,s);case 8:i=H(i,se([0,e.charCodeAt(l+7)],56));case 7:i=H(i,se([0,e.charCodeAt(l+6)],48));case 6:i=H(i,se([0,e.charCodeAt(l+5)],40));case 5:i=H(i,se([0,e.charCodeAt(l+4)],32));case 4:i=H(i,se([0,e.charCodeAt(l+3)],24));case 3:i=H(i,se([0,e.charCodeAt(l+2)],16));case 2:i=H(i,se([0,e.charCodeAt(l+1)],8));case 1:i=H(i,[0,e.charCodeAt(l)]),i=ce(i,u),i=ft(i,31),i=ce(i,c),a=H(a,i)}return a=H(a,[0,e.length]),o=H(o,[0,e.length]),a=We(a,o),o=We(o,a),a=cc(a),o=cc(o),a=We(a,o),o=We(o,a),("00000000"+(a[0]>>>0).toString(16)).slice(-8)+("00000000"+(a[1]>>>0).toString(16)).slice(-8)+("00000000"+(o[0]>>>0).toString(16)).slice(-8)+("00000000"+(o[1]>>>0).toString(16)).slice(-8)}function Qv(e){var t;return Ao({name:e.name,message:e.message,stack:(t=e.stack)===null||t===void 0?void 0:t.split(`
`)},e)}function eg(e,t){for(var r=0,n=e.length;r<n;++r)if(e[r]===t)return!0;return!1}function tg(e,t){return!eg(e,t)}function oi(e){return parseInt(e)}function pe(e){return parseFloat(e)}function Oe(e,t){return typeof e=="number"&&isNaN(e)?t:e}function we(e){return e.reduce(function(t,r){return t+(r?1:0)},0)}function bf(e,t){if(t===void 0&&(t=1),Math.abs(t)>=1)return Math.round(e/t)*t;var r=1/t;return Math.round(e*r)/r}function rg(e){for(var t,r,n="Unexpected syntax '".concat(e,"'"),a=/^\s*([a-z-]*)(.*)$/i.exec(e),o=a[1]||void 0,i={},s=/([.:#][\w-]+|\[.+?\])/gi,u=function(p,v){i[p]=i[p]||[],i[p].push(v)};;){var c=s.exec(a[2]);if(!c)break;var l=c[0];switch(l[0]){case".":u("class",l.slice(1));break;case"#":u("id",l.slice(1));break;case"[":{var f=/^\[([\w-]+)([~|^$*]?=("(.*?)"|([\w-]+)))?(\s+[is])?\]$/.exec(l);if(f)u(f[1],(r=(t=f[4])!==null&&t!==void 0?t:f[5])!==null&&r!==void 0?r:"");else throw new Error(n);break}default:throw new Error(n)}}return[o,i]}function lc(e){return e&&typeof e=="object"&&"message"in e?e:{message:e}}function ng(e){return typeof e!="function"}function ag(e,t){var r=new Promise(function(n){var a=Date.now();sc(e.bind(null,t),function(){for(var o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];var s=Date.now()-a;if(!o[0])return n(function(){return{error:lc(o[1]),duration:s}});var u=o[1];if(ng(u))return n(function(){return{value:u,duration:s}});n(function(){return new Promise(function(c){var l=Date.now();sc(u,function(){for(var f=[],p=0;p<arguments.length;p++)f[p]=arguments[p];var v=s+Date.now()-l;if(!f[0])return c({error:lc(f[1]),duration:v});c({value:f[1],duration:v})})})})})});return $t(r),function(){return r.then(function(a){return a()})}}function Sf(e,t,r){var n=Object.keys(e).filter(function(o){return tg(r,o)}),a=uc(n,function(o){return ag(e[o],t)});return $t(a),function(){return Ge(this,void 0,void 0,function(){var i,s,u,c,l;return De(this,function(f){switch(f.label){case 0:return[4,a];case 1:return i=f.sent(),[4,uc(i,function(p){var v=p();return $t(v),v})];case 2:return s=f.sent(),[4,Promise.all(s)];case 3:for(u=f.sent(),c={},l=0;l<n.length;++l)c[n[l]]=u[l];return[2,c]}})})}}function ii(){var e=window,t=navigator;return we(["MSCSSMatrix"in e,"msSetImmediate"in e,"msIndexedDB"in e,"msMaxTouchPoints"in t,"msPointerEnabled"in t])>=4}function Ef(){var e=window,t=navigator;return we(["msWriteProfilerMark"in e,"MSStream"in e,"msLaunchUri"in t,"msSaveBlob"in t])>=3&&!ii()}function Fr(){var e=window,t=navigator;return we(["webkitPersistentStorage"in t,"webkitTemporaryStorage"in t,t.vendor.indexOf("Google")===0,"webkitResolveLocalFileSystemURL"in e,"BatteryManager"in e,"webkitMediaStream"in e,"webkitSpeechGrammar"in e])>=5}function Rt(){var e=window,t=navigator;return we(["ApplePayError"in e,"CSSPrimitiveValue"in e,"Counter"in e,t.vendor.indexOf("Apple")===0,"getStorageUpdates"in t,"WebKitMediaKeys"in e])>=4}function Nr(){var e=window;return we(["safari"in e,!("DeviceMotionEvent"in e),!("ongestureend"in e),!("standalone"in navigator)])>=3}function _f(){var e,t,r=window;return we(["buildID"in navigator,"MozAppearance"in((t=(e=document.documentElement)===null||e===void 0?void 0:e.style)!==null&&t!==void 0?t:{}),"onmozfullscreenchange"in r,"mozInnerScreenX"in r,"CSSMozDocumentRule"in r,"CanvasCaptureMediaStream"in r])>=4}function og(){var e=window;return we([!("MediaSettingsRange"in e),"RTCEncodedAudioFrame"in e,""+e.Intl=="[object Intl]",""+e.Reflect=="[object Reflect]"])>=3}function ig(){var e=window;return we(["DOMRectList"in e,"RTCPeerConnectionIceEvent"in e,"SVGGeometryElement"in e,"ontransitioncancel"in e])>=3}function sg(){if(navigator.platform==="iPad")return!0;var e=screen,t=e.width/e.height;return we(["MediaSource"in window,!!Element.prototype.webkitRequestFullscreen,t>.65&&t<1.53])>=2}function Pf(){var e=document;return e.fullscreenElement||e.msFullscreenElement||e.mozFullScreenElement||e.webkitFullscreenElement||null}function ug(){var e=document;return(e.exitFullscreen||e.msExitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen).call(e)}function si(){var e=Fr(),t=_f();if(!e&&!t)return!1;var r=window;return we(["onorientationchange"in r,"orientation"in r,e&&!("SharedWorker"in r),t&&/android/i.test(navigator.appVersion)])>=2}function cg(){var e=window,t=e.OfflineAudioContext||e.webkitOfflineAudioContext;if(!t)return-2;if(lg())return-1;var r=4500,n=5e3,a=new t(1,n,44100),o=a.createOscillator();o.type="triangle",o.frequency.value=1e4;var i=a.createDynamicsCompressor();i.threshold.value=-50,i.knee.value=40,i.ratio.value=12,i.attack.value=0,i.release.value=.25,o.connect(i),i.connect(a.destination),o.start(0);var s=fg(a),u=s[0],c=s[1],l=u.then(function(f){return dg(f.getChannelData(0).subarray(r))},function(f){if(f.name==="timeout"||f.name==="suspended")return-3;throw f});return $t(l),function(){return c(),l}}function lg(){return Rt()&&!Nr()&&!ig()}function fg(e){var t=3,r=500,n=500,a=5e3,o=function(){},i=new Promise(function(s,u){var c=!1,l=0,f=0;e.oncomplete=function(m){return s(m.renderedBuffer)};var p=function(){setTimeout(function(){return u(fc("timeout"))},Math.min(n,f+a-Date.now()))},v=function(){try{var m=e.startRendering();switch(wf(m)&&$t(m),e.state){case"running":f=Date.now(),c&&p();break;case"suspended":document.hidden||l++,c&&l>=t?u(fc("suspended")):setTimeout(v,r);break}}catch(g){u(g)}};v(),o=function(){c||(c=!0,f>0&&p())}});return[i,o]}function dg(e){for(var t=0,r=0;r<e.length;++r)t+=Math.abs(e[r]);return t}function fc(e){var t=new Error(e);return t.name=e,t}function ui(e,t,r){var n,a,o;return r===void 0&&(r=50),Ge(this,void 0,void 0,function(){var i,s;return De(this,function(u){switch(u.label){case 0:i=document,u.label=1;case 1:return i.body?[3,3]:[4,Yt(r)];case 2:return u.sent(),[3,1];case 3:s=i.createElement("iframe"),u.label=4;case 4:return u.trys.push([4,,10,11]),[4,new Promise(function(c,l){var f=!1,p=function(){f=!0,c()},v=function(d){f=!0,l(d)};s.onload=p,s.onerror=v;var m=s.style;m.setProperty("display","block","important"),m.position="absolute",m.top="0",m.left="0",m.visibility="hidden",t&&"srcdoc"in s?s.srcdoc=t:s.src="about:blank",i.body.appendChild(s);var g=function(){var d,h;f||(((h=(d=s.contentWindow)===null||d===void 0?void 0:d.document)===null||h===void 0?void 0:h.readyState)==="complete"?p():setTimeout(g,10))};g()})];case 5:u.sent(),u.label=6;case 6:return!((a=(n=s.contentWindow)===null||n===void 0?void 0:n.document)===null||a===void 0)&&a.body?[3,8]:[4,Yt(r)];case 7:return u.sent(),[3,6];case 8:return[4,e(s,s.contentWindow)];case 9:return[2,u.sent()];case 10:return(o=s.parentNode)===null||o===void 0||o.removeChild(s),[7];case 11:return[2]}})})}function pg(e){for(var t=rg(e),r=t[0],n=t[1],a=document.createElement(r??"div"),o=0,i=Object.keys(n);o<i.length;o++){var s=i[o],u=n[s].join(" ");s==="style"?mg(a.style,u):a.setAttribute(s,u)}return a}function mg(e,t){for(var r=0,n=t.split(";");r<n.length;r++){var a=n[r],o=/^\s*([\w-]+)\s*:\s*(.+?)(\s*!([\w-]+))?\s*$/.exec(a);if(o){var i=o[1],s=o[2],u=o[4];e.setProperty(i,s,u||"")}}}var hg="mmMwWLliI0O&1",vg="48px",dt=["monospace","sans-serif","serif"],dc=["sans-serif-thin","ARNO PRO","Agency FB","Arabic Typesetting","Arial Unicode MS","AvantGarde Bk BT","BankGothic Md BT","Batang","Bitstream Vera Sans Mono","Calibri","Century","Century Gothic","Clarendon","EUROSTILE","Franklin Gothic","Futura Bk BT","Futura Md BT","GOTHAM","Gill Sans","HELV","Haettenschweiler","Helvetica Neue","Humanst521 BT","Leelawadee","Letter Gothic","Levenim MT","Lucida Bright","Lucida Sans","Menlo","MS Mincho","MS Outlook","MS Reference Specialty","MS UI Gothic","MT Extra","MYRIAD PRO","Marlett","Meiryo UI","Microsoft Uighur","Minion Pro","Monotype Corsiva","PMingLiU","Pristina","SCRIPTINA","Segoe UI Light","Serifa","SimHei","Small Fonts","Staccato222 BT","TRAJAN PRO","Univers CE 55 Medium","Vrinda","ZWAdobeF"];function gg(){return ui(function(e,t){var r=t.document,n=r.body;n.style.fontSize=vg;var a=r.createElement("div"),o={},i={},s=function(g){var d=r.createElement("span"),h=d.style;return h.position="absolute",h.top="0",h.left="0",h.fontFamily=g,d.textContent=hg,a.appendChild(d),d},u=function(g,d){return s("'".concat(g,"',").concat(d))},c=function(){return dt.map(s)},l=function(){for(var g={},d=function(_){g[_]=dt.map(function(T){return u(_,T)})},h=0,y=dc;h<y.length;h++){var E=y[h];d(E)}return g},f=function(g){return dt.some(function(d,h){return g[h].offsetWidth!==o[d]||g[h].offsetHeight!==i[d]})},p=c(),v=l();n.appendChild(a);for(var m=0;m<dt.length;m++)o[dt[m]]=p[m].offsetWidth,i[dt[m]]=p[m].offsetHeight;return dc.filter(function(g){return f(v[g])})})}function yg(){var e=navigator.plugins;if(e){for(var t=[],r=0;r<e.length;++r){var n=e[r];if(n){for(var a=[],o=0;o<n.length;++o){var i=n[o];a.push({type:i.type,suffixes:i.suffixes})}t.push({name:n.name,description:n.description,mimeTypes:a})}}return t}}function wg(){var e=!1,t,r,n=bg(),a=n[0],o=n[1];if(!Sg(a,o))t=r="";else{e=Eg(o),_g(a,o);var i=Za(a),s=Za(a);i!==s?t=r="unstable":(r=i,Pg(a,o),t=Za(a))}return{winding:e,geometry:t,text:r}}function bg(){var e=document.createElement("canvas");return e.width=1,e.height=1,[e,e.getContext("2d")]}function Sg(e,t){return!!(t&&e.toDataURL)}function Eg(e){return e.rect(0,0,10,10),e.rect(2,2,6,6),!e.isPointInPath(5,5,"evenodd")}function _g(e,t){e.width=240,e.height=60,t.textBaseline="alphabetic",t.fillStyle="#f60",t.fillRect(100,1,62,20),t.fillStyle="#069",t.font='11pt "Times New Roman"';var r="Cwm fjordbank gly ".concat("😃");t.fillText(r,2,15),t.fillStyle="rgba(102, 204, 0, 0.2)",t.font="18pt Arial",t.fillText(r,4,45)}function Pg(e,t){e.width=122,e.height=110,t.globalCompositeOperation="multiply";for(var r=0,n=[["#f2f",40,40],["#2ff",80,40],["#ff2",60,80]];r<n.length;r++){var a=n[r],o=a[0],i=a[1],s=a[2];t.fillStyle=o,t.beginPath(),t.arc(i,s,40,0,Math.PI*2,!0),t.closePath(),t.fill()}t.fillStyle="#f9c",t.arc(60,60,60,0,Math.PI*2,!0),t.arc(60,60,20,0,Math.PI*2,!0),t.fill("evenodd")}function Za(e){return e.toDataURL()}function Tg(){var e=navigator,t=0,r;e.maxTouchPoints!==void 0?t=oi(e.maxTouchPoints):e.msMaxTouchPoints!==void 0&&(t=e.msMaxTouchPoints);try{document.createEvent("TouchEvent"),r=!0}catch{r=!1}var n="ontouchstart"in window;return{maxTouchPoints:t,touchEvent:r,touchStart:n}}function Cg(){return navigator.oscpu}function Ag(){var e=navigator,t=[],r=e.language||e.userLanguage||e.browserLanguage||e.systemLanguage;if(r!==void 0&&t.push([r]),Array.isArray(e.languages))Fr()&&og()||t.push(e.languages);else if(typeof e.languages=="string"){var n=e.languages;n&&t.push(n.split(","))}return t}function Rg(){return window.screen.colorDepth}function xg(){return Oe(pe(navigator.deviceMemory),void 0)}function Mg(){var e=screen,t=function(n){return Oe(oi(n),null)},r=[t(e.width),t(e.height)];return r.sort().reverse(),r}var Ig=2500,Og=10,hr,Xa;function Lg(){if(Xa===void 0){var e=function(){var t=Ro();xo(t)?Xa=setTimeout(e,Ig):(hr=t,Xa=void 0)};e()}}function Tf(){var e=this;return Lg(),function(){return Ge(e,void 0,void 0,function(){var t;return De(this,function(r){switch(r.label){case 0:return t=Ro(),xo(t)?hr?[2,gf([],hr,!0)]:Pf()?[4,ug()]:[3,2]:[3,2];case 1:r.sent(),t=Ro(),r.label=2;case 2:return xo(t)||(hr=t),[2,t]}})})}}function kg(){var e=this,t=Tf();return function(){return Ge(e,void 0,void 0,function(){var r,n;return De(this,function(a){switch(a.label){case 0:return[4,t()];case 1:return r=a.sent(),n=function(o){return o===null?null:bf(o,Og)},[2,[n(r[0]),n(r[1]),n(r[2]),n(r[3])]]}})})}}function Ro(){var e=screen;return[Oe(pe(e.availTop),null),Oe(pe(e.width)-pe(e.availWidth)-Oe(pe(e.availLeft),0),null),Oe(pe(e.height)-pe(e.availHeight)-Oe(pe(e.availTop),0),null),Oe(pe(e.availLeft),null)]}function xo(e){for(var t=0;t<4;++t)if(e[t])return!1;return!0}function Gg(){return Oe(oi(navigator.hardwareConcurrency),void 0)}function Dg(){var e,t=(e=window.Intl)===null||e===void 0?void 0:e.DateTimeFormat;if(t){var r=new t().resolvedOptions().timeZone;if(r)return r}var n=-Fg();return"UTC".concat(n>=0?"+":"").concat(Math.abs(n))}function Fg(){var e=new Date().getFullYear();return Math.max(pe(new Date(e,0,1).getTimezoneOffset()),pe(new Date(e,6,1).getTimezoneOffset()))}function Ng(){try{return!!window.sessionStorage}catch{return!0}}function jg(){try{return!!window.localStorage}catch{return!0}}function Vg(){if(!(ii()||Ef()))try{return!!window.indexedDB}catch{return!0}}function Wg(){return!!window.openDatabase}function qg(){return navigator.cpuClass}function Hg(){var e=navigator.platform;return e==="MacIntel"&&Rt()&&!Nr()?sg()?"iPad":"iPhone":e}function Bg(){return navigator.vendor||""}function Ug(){for(var e=[],t=0,r=["chrome","safari","__crWeb","__gCrWeb","yandex","__yb","__ybro","__firefox__","__edgeTrackingPreventionStatistics","webkit","oprt","samsungAr","ucweb","UCShellJava","puffinDevice"];t<r.length;t++){var n=r[t],a=window[n];a&&typeof a=="object"&&e.push(n)}return e.sort()}function Yg(){var e=document;try{e.cookie="cookietest=1; SameSite=Strict;";var t=e.cookie.indexOf("cookietest=")!==-1;return e.cookie="cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT",t}catch{return!1}}function $g(){var e=atob;return{abpIndo:["#Iklan-Melayang","#Kolom-Iklan-728","#SidebarIklan-wrapper",'[title="ALIENBOLA" i]',e("I0JveC1CYW5uZXItYWRz")],abpvn:[".quangcao","#mobileCatfish",e("LmNsb3NlLWFkcw=="),'[id^="bn_bottom_fixed_"]',"#pmadv"],adBlockFinland:[".mainostila",e("LnNwb25zb3JpdA=="),".ylamainos",e("YVtocmVmKj0iL2NsaWNrdGhyZ2guYXNwPyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hcHAucmVhZHBlYWsuY29tL2FkcyJd")],adBlockPersian:["#navbar_notice_50",".kadr",'TABLE[width="140px"]',"#divAgahi",e("YVtocmVmXj0iaHR0cDovL2cxLnYuZndtcm0ubmV0L2FkLyJd")],adBlockWarningRemoval:["#adblock-honeypot",".adblocker-root",".wp_adblock_detect",e("LmhlYWRlci1ibG9ja2VkLWFk"),e("I2FkX2Jsb2NrZXI=")],adGuardAnnoyances:[".hs-sosyal","#cookieconsentdiv",'div[class^="app_gdpr"]',".as-oil",'[data-cypress="soft-push-notification-modal"]'],adGuardBase:[".BetterJsPopOverlay",e("I2FkXzMwMFgyNTA="),e("I2Jhbm5lcmZsb2F0MjI="),e("I2NhbXBhaWduLWJhbm5lcg=="),e("I0FkLUNvbnRlbnQ=")],adGuardChinese:[e("LlppX2FkX2FfSA=="),e("YVtocmVmKj0iLmh0aGJldDM0LmNvbSJd"),"#widget-quan",e("YVtocmVmKj0iLzg0OTkyMDIwLnh5eiJd"),e("YVtocmVmKj0iLjE5NTZobC5jb20vIl0=")],adGuardFrench:["#pavePub",e("LmFkLWRlc2t0b3AtcmVjdGFuZ2xl"),".mobile_adhesion",".widgetadv",e("LmFkc19iYW4=")],adGuardGerman:['aside[data-portal-id="leaderboard"]'],adGuardJapanese:["#kauli_yad_1",e("YVtocmVmXj0iaHR0cDovL2FkMi50cmFmZmljZ2F0ZS5uZXQvIl0="),e("Ll9wb3BJbl9pbmZpbml0ZV9hZA=="),e("LmFkZ29vZ2xl"),e("Ll9faXNib29zdFJldHVybkFk")],adGuardMobile:[e("YW1wLWF1dG8tYWRz"),e("LmFtcF9hZA=="),'amp-embed[type="24smi"]',"#mgid_iframe1",e("I2FkX2ludmlld19hcmVh")],adGuardRussian:[e("YVtocmVmXj0iaHR0cHM6Ly9hZC5sZXRtZWFkcy5jb20vIl0="),e("LnJlY2xhbWE="),'div[id^="smi2adblock"]',e("ZGl2W2lkXj0iQWRGb3hfYmFubmVyXyJd"),"#psyduckpockeball"],adGuardSocial:[e("YVtocmVmXj0iLy93d3cuc3R1bWJsZXVwb24uY29tL3N1Ym1pdD91cmw9Il0="),e("YVtocmVmXj0iLy90ZWxlZ3JhbS5tZS9zaGFyZS91cmw/Il0="),".etsy-tweet","#inlineShare",".popup-social"],adGuardSpanishPortuguese:["#barraPublicidade","#Publicidade","#publiEspecial","#queTooltip",".cnt-publi"],adGuardTrackingProtection:["#qoo-counter",e("YVtocmVmXj0iaHR0cDovL2NsaWNrLmhvdGxvZy5ydS8iXQ=="),e("YVtocmVmXj0iaHR0cDovL2hpdGNvdW50ZXIucnUvdG9wL3N0YXQucGhwIl0="),e("YVtocmVmXj0iaHR0cDovL3RvcC5tYWlsLnJ1L2p1bXAiXQ=="),"#top100counter"],adGuardTurkish:["#backkapat",e("I3Jla2xhbWk="),e("YVtocmVmXj0iaHR0cDovL2Fkc2Vydi5vbnRlay5jb20udHIvIl0="),e("YVtocmVmXj0iaHR0cDovL2l6bGVuemkuY29tL2NhbXBhaWduLyJd"),e("YVtocmVmXj0iaHR0cDovL3d3dy5pbnN0YWxsYWRzLm5ldC8iXQ==")],bulgarian:[e("dGQjZnJlZW5ldF90YWJsZV9hZHM="),"#ea_intext_div",".lapni-pop-over","#xenium_hot_offers"],easyList:[".yb-floorad",e("LndpZGdldF9wb19hZHNfd2lkZ2V0"),e("LnRyYWZmaWNqdW5reS1hZA=="),".textad_headline",e("LnNwb25zb3JlZC10ZXh0LWxpbmtz")],easyListChina:[e("LmFwcGd1aWRlLXdyYXBbb25jbGljayo9ImJjZWJvcy5jb20iXQ=="),e("LmZyb250cGFnZUFkdk0="),"#taotaole","#aafoot.top_box",".cfa_popup"],easyListCookie:[".ezmob-footer",".cc-CookieWarning","[data-cookie-number]",e("LmF3LWNvb2tpZS1iYW5uZXI="),".sygnal24-gdpr-modal-wrap"],easyListCzechSlovak:["#onlajny-stickers",e("I3Jla2xhbW5pLWJveA=="),e("LnJla2xhbWEtbWVnYWJvYXJk"),".sklik",e("W2lkXj0ic2tsaWtSZWtsYW1hIl0=")],easyListDutch:[e("I2FkdmVydGVudGll"),e("I3ZpcEFkbWFya3RCYW5uZXJCbG9jaw=="),".adstekst",e("YVtocmVmXj0iaHR0cHM6Ly94bHR1YmUubmwvY2xpY2svIl0="),"#semilo-lrectangle"],easyListGermany:["#SSpotIMPopSlider",e("LnNwb25zb3JsaW5rZ3J1ZW4="),e("I3dlcmJ1bmdza3k="),e("I3Jla2xhbWUtcmVjaHRzLW1pdHRl"),e("YVtocmVmXj0iaHR0cHM6Ly9iZDc0Mi5jb20vIl0=")],easyListItaly:[e("LmJveF9hZHZfYW5udW5jaQ=="),".sb-box-pubbliredazionale",e("YVtocmVmXj0iaHR0cDovL2FmZmlsaWF6aW9uaWFkcy5zbmFpLml0LyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hZHNlcnZlci5odG1sLml0LyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hZmZpbGlhemlvbmlhZHMuc25haS5pdC8iXQ==")],easyListLithuania:[e("LnJla2xhbW9zX3RhcnBhcw=="),e("LnJla2xhbW9zX251b3JvZG9z"),e("aW1nW2FsdD0iUmVrbGFtaW5pcyBza3lkZWxpcyJd"),e("aW1nW2FsdD0iRGVkaWt1b3RpLmx0IHNlcnZlcmlhaSJd"),e("aW1nW2FsdD0iSG9zdGluZ2FzIFNlcnZlcmlhaS5sdCJd")],estonian:[e("QVtocmVmKj0iaHR0cDovL3BheTRyZXN1bHRzMjQuZXUiXQ==")],fanboyAnnoyances:["#ac-lre-player",".navigate-to-top","#subscribe_popup",".newsletter_holder","#back-top"],fanboyAntiFacebook:[".util-bar-module-firefly-visible"],fanboyEnhancedTrackers:[".open.pushModal","#issuem-leaky-paywall-articles-zero-remaining-nag","#sovrn_container",'div[class$="-hide"][zoompage-fontsize][style="display: block;"]',".BlockNag__Card"],fanboySocial:["#FollowUs","#meteored_share","#social_follow",".article-sharer",".community__social-desc"],frellwitSwedish:[e("YVtocmVmKj0iY2FzaW5vcHJvLnNlIl1bdGFyZ2V0PSJfYmxhbmsiXQ=="),e("YVtocmVmKj0iZG9rdG9yLXNlLm9uZWxpbmsubWUiXQ=="),"article.category-samarbete",e("ZGl2LmhvbGlkQWRz"),"ul.adsmodern"],greekAdBlock:[e("QVtocmVmKj0iYWRtYW4ub3RlbmV0LmdyL2NsaWNrPyJd"),e("QVtocmVmKj0iaHR0cDovL2F4aWFiYW5uZXJzLmV4b2R1cy5nci8iXQ=="),e("QVtocmVmKj0iaHR0cDovL2ludGVyYWN0aXZlLmZvcnRobmV0LmdyL2NsaWNrPyJd"),"DIV.agores300","TABLE.advright"],hungarian:["#cemp_doboz",".optimonk-iframe-container",e("LmFkX19tYWlu"),e("W2NsYXNzKj0iR29vZ2xlQWRzIl0="),"#hirdetesek_box"],iDontCareAboutCookies:['.alert-info[data-block-track*="CookieNotice"]',".ModuleTemplateCookieIndicator",".o--cookies--container","#cookies-policy-sticky","#stickyCookieBar"],icelandicAbp:[e("QVtocmVmXj0iL2ZyYW1ld29yay9yZXNvdXJjZXMvZm9ybXMvYWRzLmFzcHgiXQ==")],latvian:[e("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiAxMjBweDsgaGVpZ2h0OiA0MHB4OyBvdmVyZmxvdzogaGlkZGVuOyBwb3NpdGlvbjogcmVsYXRpdmU7Il0="),e("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiA4OHB4OyBoZWlnaHQ6IDMxcHg7IG92ZXJmbG93OiBoaWRkZW47IHBvc2l0aW9uOiByZWxhdGl2ZTsiXQ==")],listKr:[e("YVtocmVmKj0iLy9hZC5wbGFuYnBsdXMuY28ua3IvIl0="),e("I2xpdmVyZUFkV3JhcHBlcg=="),e("YVtocmVmKj0iLy9hZHYuaW1hZHJlcC5jby5rci8iXQ=="),e("aW5zLmZhc3R2aWV3LWFk"),".revenue_unit_item.dable"],listeAr:[e("LmdlbWluaUxCMUFk"),".right-and-left-sponsers",e("YVtocmVmKj0iLmFmbGFtLmluZm8iXQ=="),e("YVtocmVmKj0iYm9vcmFxLm9yZyJd"),e("YVtocmVmKj0iZHViaXp6bGUuY29tL2FyLz91dG1fc291cmNlPSJd")],listeFr:[e("YVtocmVmXj0iaHR0cDovL3Byb21vLnZhZG9yLmNvbS8iXQ=="),e("I2FkY29udGFpbmVyX3JlY2hlcmNoZQ=="),e("YVtocmVmKj0id2Vib3JhbWEuZnIvZmNnaS1iaW4vIl0="),".site-pub-interstitiel",'div[id^="crt-"][data-criteo-id]'],officialPolish:["#ceneo-placeholder-ceneo-12",e("W2hyZWZePSJodHRwczovL2FmZi5zZW5kaHViLnBsLyJd"),e("YVtocmVmXj0iaHR0cDovL2Fkdm1hbmFnZXIudGVjaGZ1bi5wbC9yZWRpcmVjdC8iXQ=="),e("YVtocmVmXj0iaHR0cDovL3d3dy50cml6ZXIucGwvP3V0bV9zb3VyY2UiXQ=="),e("ZGl2I3NrYXBpZWNfYWQ=")],ro:[e("YVtocmVmXj0iLy9hZmZ0cmsuYWx0ZXgucm8vQ291bnRlci9DbGljayJd"),e("YVtocmVmXj0iaHR0cHM6Ly9ibGFja2ZyaWRheXNhbGVzLnJvL3Ryay9zaG9wLyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9ldmVudC4ycGVyZm9ybWFudC5jb20vZXZlbnRzL2NsaWNrIl0="),e("YVtocmVmXj0iaHR0cHM6Ly9sLnByb2ZpdHNoYXJlLnJvLyJd"),'a[href^="/url/"]'],ruAd:[e("YVtocmVmKj0iLy9mZWJyYXJlLnJ1LyJd"),e("YVtocmVmKj0iLy91dGltZy5ydS8iXQ=="),e("YVtocmVmKj0iOi8vY2hpa2lkaWtpLnJ1Il0="),"#pgeldiz",".yandex-rtb-block"],thaiAds:["a[href*=macau-uta-popup]",e("I2Fkcy1nb29nbGUtbWlkZGxlX3JlY3RhbmdsZS1ncm91cA=="),e("LmFkczMwMHM="),".bumq",".img-kosana"],webAnnoyancesUltralist:["#mod-social-share-2","#social-tools",e("LmN0cGwtZnVsbGJhbm5lcg=="),".zergnet-recommend",".yt.btn-link.btn-md.btn"]}}function Zg(e){var t=e===void 0?{}:e,r=t.debug;return Ge(this,void 0,void 0,function(){var n,a,o,i,s,u;return De(this,function(c){switch(c.label){case 0:return Xg()?(n=$g(),a=Object.keys(n),o=(u=[]).concat.apply(u,a.map(function(l){return n[l]})),[4,Kg(o)]):[2,void 0];case 1:return i=c.sent(),r&&zg(n,i),s=a.filter(function(l){var f=n[l],p=we(f.map(function(v){return i[v]}));return p>f.length*.6}),s.sort(),[2,s]}})})}function Xg(){return Rt()||si()}function Kg(e){var t;return Ge(this,void 0,void 0,function(){var r,n,a,o,u,i,s,u;return De(this,function(c){switch(c.label){case 0:for(r=document,n=r.createElement("div"),a=new Array(e.length),o={},pc(n),u=0;u<e.length;++u)i=pg(e[u]),i.tagName==="DIALOG"&&i.show(),s=r.createElement("div"),pc(s),s.appendChild(i),n.appendChild(s),a[u]=i;c.label=1;case 1:return r.body?[3,3]:[4,Yt(50)];case 2:return c.sent(),[3,1];case 3:r.body.appendChild(n);try{for(u=0;u<e.length;++u)a[u].offsetParent||(o[e[u]]=!0)}finally{(t=n.parentNode)===null||t===void 0||t.removeChild(n)}return[2,o]}})})}function pc(e){e.style.setProperty("display","block","important")}function zg(e,t){for(var r="DOM blockers debug:\n```",n=0,a=Object.keys(e);n<a.length;n++){var o=a[n];r+=`
`.concat(o,":");for(var i=0,s=e[o];i<s.length;i++){var u=s[i];r+=`
  `.concat(t[u]?"🚫":"➡️"," ").concat(u)}}console.log("".concat(r,"\n```"))}function Jg(){for(var e=0,t=["rec2020","p3","srgb"];e<t.length;e++){var r=t[e];if(matchMedia("(color-gamut: ".concat(r,")")).matches)return r}}function Qg(){if(mc("inverted"))return!0;if(mc("none"))return!1}function mc(e){return matchMedia("(inverted-colors: ".concat(e,")")).matches}function ey(){if(hc("active"))return!0;if(hc("none"))return!1}function hc(e){return matchMedia("(forced-colors: ".concat(e,")")).matches}var ty=100;function ry(){if(matchMedia("(min-monochrome: 0)").matches){for(var e=0;e<=ty;++e)if(matchMedia("(max-monochrome: ".concat(e,")")).matches)return e;throw new Error("Too high value")}}function ny(){if(pt("no-preference"))return 0;if(pt("high")||pt("more"))return 1;if(pt("low")||pt("less"))return-1;if(pt("forced"))return 10}function pt(e){return matchMedia("(prefers-contrast: ".concat(e,")")).matches}function ay(){if(vc("reduce"))return!0;if(vc("no-preference"))return!1}function vc(e){return matchMedia("(prefers-reduced-motion: ".concat(e,")")).matches}function oy(){if(gc("high"))return!0;if(gc("standard"))return!1}function gc(e){return matchMedia("(dynamic-range: ".concat(e,")")).matches}var k=Math,ee=function(){return 0};function iy(){var e=k.acos||ee,t=k.acosh||ee,r=k.asin||ee,n=k.asinh||ee,a=k.atanh||ee,o=k.atan||ee,i=k.sin||ee,s=k.sinh||ee,u=k.cos||ee,c=k.cosh||ee,l=k.tan||ee,f=k.tanh||ee,p=k.exp||ee,v=k.expm1||ee,m=k.log1p||ee,g=function(b){return k.pow(k.PI,b)},d=function(b){return k.log(b+k.sqrt(b*b-1))},h=function(b){return k.log(b+k.sqrt(b*b+1))},y=function(b){return k.log((1+b)/(1-b))/2},E=function(b){return k.exp(b)-1/k.exp(b)/2},_=function(b){return(k.exp(b)+1/k.exp(b))/2},T=function(b){return k.exp(b)-1},w=function(b){return(k.exp(2*b)-1)/(k.exp(2*b)+1)},A=function(b){return k.log(1+b)};return{acos:e(.12312423423423424),acosh:t(1e308),acoshPf:d(1e154),asin:r(.12312423423423424),asinh:n(1),asinhPf:h(1),atanh:a(.5),atanhPf:y(.5),atan:o(.5),sin:i(-1e300),sinh:s(1),sinhPf:E(1),cos:u(10.000000000123),cosh:c(1),coshPf:_(1),tan:l(-1e300),tanh:f(1),tanhPf:w(1),exp:p(1),expm1:v(1),expm1Pf:T(1),log1p:m(10),log1pPf:A(10),powPI:g(-100)}}var sy="mmMwWLliI0fiflO&1",Ka={default:[],apple:[{font:"-apple-system-body"}],serif:[{fontFamily:"serif"}],sans:[{fontFamily:"sans-serif"}],mono:[{fontFamily:"monospace"}],min:[{fontSize:"1px"}],system:[{fontFamily:"system-ui"}]};function uy(){return cy(function(e,t){for(var r={},n={},a=0,o=Object.keys(Ka);a<o.length;a++){var i=o[a],s=Ka[i],u=s[0],c=u===void 0?{}:u,l=s[1],f=l===void 0?sy:l,p=e.createElement("span");p.textContent=f,p.style.whiteSpace="nowrap";for(var v=0,m=Object.keys(c);v<m.length;v++){var g=m[v],d=c[g];d!==void 0&&(p.style[g]=d)}r[i]=p,t.appendChild(e.createElement("br")),t.appendChild(p)}for(var h=0,y=Object.keys(Ka);h<y.length;h++){var i=y[h];n[i]=r[i].getBoundingClientRect().width}return n})}function cy(e,t){return t===void 0&&(t=4e3),ui(function(r,n){var a=n.document,o=a.body,i=o.style;i.width="".concat(t,"px"),i.webkitTextSizeAdjust=i.textSizeAdjust="none",Fr()?o.style.zoom="".concat(1/n.devicePixelRatio):Rt()&&(o.style.zoom="reset");var s=a.createElement("div");return s.textContent=gf([],Array(t/20<<0),!0).map(function(){return"word"}).join(" "),o.appendChild(s),e(a,o)},'<!doctype html><html><head><meta name="viewport" content="width=device-width, initial-scale=1">')}function ly(){var e,t=document.createElement("canvas"),r=(e=t.getContext("webgl"))!==null&&e!==void 0?e:t.getContext("experimental-webgl");if(r&&"getExtension"in r){var n=r.getExtension("WEBGL_debug_renderer_info");if(n)return{vendor:(r.getParameter(n.UNMASKED_VENDOR_WEBGL)||"").toString(),renderer:(r.getParameter(n.UNMASKED_RENDERER_WEBGL)||"").toString()}}}function fy(){return navigator.pdfViewerEnabled}function dy(){var e=new Float32Array(1),t=new Uint8Array(e.buffer);return e[0]=1/0,e[0]=e[0]-e[0],t[3]}var Cf={fonts:gg,domBlockers:Zg,fontPreferences:uy,audio:cg,screenFrame:kg,osCpu:Cg,languages:Ag,colorDepth:Rg,deviceMemory:xg,screenResolution:Mg,hardwareConcurrency:Gg,timezone:Dg,sessionStorage:Ng,localStorage:jg,indexedDB:Vg,openDatabase:Wg,cpuClass:qg,platform:Hg,plugins:yg,canvas:wg,touchSupport:Tg,vendor:Bg,vendorFlavors:Ug,cookiesEnabled:Yg,colorGamut:Jg,invertedColors:Qg,forcedColors:ey,monochrome:ry,contrast:ny,reducedMotion:ay,hdr:oy,math:iy,videoCard:ly,pdfViewerEnabled:fy,architecture:dy};function py(e){return Sf(Cf,e,[])}var my="$ if upgrade to Pro: https://fpjs.dev/pro";function hy(e){var t=vy(e),r=gy(t);return{score:t,comment:my.replace(/\$/g,"".concat(r))}}function vy(e){if(si())return .4;if(Rt())return Nr()?.5:.3;var t=e.platform.value||"";return/^Win/.test(t)?.6:/^Mac/.test(t)?.5:.7}function gy(e){return bf(.99+.01*e,1e-4)}function yy(e){for(var t="",r=0,n=Object.keys(e).sort();r<n.length;r++){var a=n[r],o=e[a],i=o.error?"error":JSON.stringify(o.value);t+="".concat(t?"|":"").concat(a.replace(/([:|\\])/g,"\\$1"),":").concat(i)}return t}function ci(e){return JSON.stringify(e,function(t,r){return r instanceof Error?Qv(r):r},2)}function li(e){return Jv(yy(e))}function wy(e){var t,r=hy(e);return{get visitorId(){return t===void 0&&(t=li(this.components)),t},set visitorId(n){t=n},confidence:r,components:e,version:yf}}function Af(e){return e===void 0&&(e=50),zv(e,e*2)}function by(e,t){var r=Date.now();return{get:function(n){return Ge(this,void 0,void 0,function(){var a,o,i;return De(this,function(s){switch(s.label){case 0:return a=Date.now(),[4,e()];case 1:return o=s.sent(),i=wy(o),(t||n?.debug)&&console.log("Copy the text below to get the debug data:\n\n```\nversion: ".concat(i.version,`
userAgent: `).concat(navigator.userAgent,`
timeBetweenLoadAndGet: `).concat(a-r,`
visitorId: `).concat(i.visitorId,`
components: `).concat(ci(o),"\n```")),[2,i]}})})}}}function Sy(){if(!(window.__fpjs_d_m||Math.random()>=.001))try{var e=new XMLHttpRequest;e.open("get","https://m1.openfpcdn.io/fingerprintjs/v".concat(yf,"/npm-monitoring"),!0),e.send()}catch(t){console.error(t)}}function Rf(e){var t=e===void 0?{}:e,r=t.delayFallback,n=t.debug,a=t.monitoring,o=a===void 0?!0:a;return Ge(this,void 0,void 0,function(){var i;return De(this,function(s){switch(s.label){case 0:return o&&Sy(),[4,Af(r)];case 1:return s.sent(),i=py({debug:n}),[2,by(i,n)]}})})}var xf={load:Rf,hashComponents:li,componentsToDebugString:ci};const iw=Object.freeze(Object.defineProperty({__proto__:null,componentsToDebugString:ci,default:xf,getFullscreenElement:Pf,getScreenFrame:Tf,hashComponents:li,isAndroid:si,isChromium:Fr,isDesktopSafari:Nr,isEdgeHTML:Ef,isGecko:_f,isTrident:ii,isWebKit:Rt,load:Rf,loadSources:Sf,prepareForSources:Af,sources:Cf,withIframe:ui},Symbol.toStringTag,{value:"Module"}));function Ey(e){const t=()=>{if(!window.navigator.sendBeacon("/reports",JSON.stringify(e)))throw new Error("sendBeacon failed")},r=()=>{jo(()=>fetch("/reports",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),{retries:3})};if(typeof navigator<"u"&&typeof navigator.sendBeacon=="function")try{t()}catch(n){console.error(n),r()}else r()}async function _y(){const e=Of(),r=await(await xf.load()).get();return{uuid:e,time:`${Date.now()}`,ctwid:"G0000000",appid:window.option.appId,page:{referer:document.referrer||"",network_connection:ld(window,"navigator.network.connection.effectiveType",""),url:window.location.href,fp:r.visitorId||"",country:"",region:"",lang:window.option.lang||"",userAgent:window.navigator.userAgent,userAgentData:await cd(),isStandalone:no()?1:0}}}async function Py({action:e,service:t,payload:r}){const a={...await _y(),version:"v2",type:e,service:t,payload:r};Ey(a)}const Ty=e=>new Promise(t=>setTimeout(t,e)),Cy=(e,t)=>{let r;if(typeof AbortSignal=="function"&&typeof AbortSignal.timeout=="function")r=AbortSignal.timeout(t);else if(typeof AbortController=="function"){const n=new AbortController;r=n.signal,setTimeout(()=>n.abort(),t)}else r=void 0;return Promise.race([fetch(e,{cache:"no-store",signal:r}).catch(()=>null),Ty(t).then(()=>null)])};async function Ay(){let t=!1;try{const r=l=>Math.round(l*100)/100,n=Date.now(),a=new URL(window.option.idCallback);a.pathname="/cloud-game-web-client/ping.json",a.searchParams.set("t",n.toString());const o=await Cy(a.toString(),600);o&&(await o.text(),t=!0);const i=Date.now(),u=performance.getEntriesByType("resource").find(l=>l.name.endsWith(a.toString())),c={dnsLookup:null,tcpHandshake:null,sslHandshake:null,ttfb:null,contentDownload:null,totalTime:null,fetchTotalTime:i-n,waitTime:null};return u&&u instanceof PerformanceResourceTiming?(c.dnsLookup=r(u.domainLookupEnd-u.domainLookupStart),c.tcpHandshake=r(u.connectEnd-u.connectStart),c.sslHandshake=r(u.secureConnectionStart>0?u.connectEnd-u.secureConnectionStart:0),c.ttfb=r(u.responseStart-u.requestStart),c.contentDownload=r(u.responseEnd-u.responseStart),c.totalTime=r(u.responseEnd-u.fetchStart),c.waitTime=r(u.requestStart-u.startTime),console.log("[CloudGameWeb] speedTest results:",c)):console.log("[CloudGameWeb] speedTest: ping.json performance entry not found."),{dns_lookup:c.dnsLookup,tcp_handshake:c.tcpHandshake,ssl_handshake:c.sslHandshake,ttfb:c.ttfb,content_download:c.contentDownload,total_time:c.totalTime,wait_time:c.waitTime,fetch_total_time:c.fetchTotalTime,isSuccess:t}}catch(r){return console.log("[CloudGameWeb] speedTest error:",r),null}}function yc(e){const t=new URL(window.location.href);try{const r=t.searchParams.get("enable_flashlaunch");r?document.cookie=`gp_flashlaunch=${r}; path=/; Secure`:e?document.cookie=`gp_flashlaunch=${e}; path=/; Secure`:document.cookie="gp_flashlaunch=; path=/; Secure; expires=Thu, 01 Jan 1970 00:00:00 GMT"}catch(r){console.error("CLOUD_GAME_ENABLE_ERROR",r)}}async function Ry(){const e=await Ay();if(e?.isSuccess){const{isSuccess:t,...r}=e;Py({action:"misc_flashlaunch_cloud_game_speedtest",service:"flashlaunch",payload:{page_view_id:window.__cloud_game_page_view_id__,...r}}),yc()}else yc("0")}function vr(e,t){window.postMessage({type:"gp_global_actions/show_toast",payload:{type:e,content:t}})}function xy(){window.postMessage({type:"gp_global_actions/dismiss_toast"})}function My(){window.postMessage({type:"gp_global_actions/reload_cshelp"})}const Mf=G.getState();window.option.inGameAccount=Mf.inGameAccount;G.subscribe(()=>{const e=G.getState();Mf.inGameAccount!==e.inGameAccount&&(window.option.inGameAccount=e.inGameAccount,My())});/*! clipboard-copy. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */var za,wc;function Iy(){if(wc)return za;wc=1,za=n;function e(){return new DOMException("The request is not allowed","NotAllowedError")}async function t(a){if(!navigator.clipboard)throw e();return navigator.clipboard.writeText(a)}async function r(a){const o=document.createElement("span");o.textContent=a,o.style.whiteSpace="pre",o.style.webkitUserSelect="auto",o.style.userSelect="all",document.body.appendChild(o);const i=window.getSelection(),s=window.document.createRange();i.removeAllRanges(),s.selectNode(o),i.addRange(s);let u=!1;try{u=window.document.execCommand("copy")}finally{i.removeAllRanges(),window.document.body.removeChild(o)}if(!u)throw e()}async function n(a){try{await t(a)}catch(o){try{await r(a)}catch(i){throw i||o||e()}}}return za}var Oy=Iy();const Ly=ye(Oy);function ky(e){G.dispatch(Ee.actions.toggleMainPopup({isOpen:e})),!e&&G.getState().blockedUser.blocked&&G.dispatch(Dc.actions.showDialog())}function Gy(e){G.dispatch(Ee.actions.switchMainPopupRoute({tab:e}))}function sw(){ky(!1),Gy(fd.Recommends),xy()}function uw(e=!0){G.dispatch(Ee.actions.setIsGuestClicked({clicked:e}))}async function cw(e){const t=await Cr(),r=await it.get(`${ue.SHD_G123_WEB_URL}/api/v2/game/${e}`,{params:{lang:nt.language,region:t.region}});return G.dispatch(Ee.actions.updateCurrentAppInfo({appInfo:r.data})),r.data}async function lw(){try{const e=await Cr(),{data:{games:t}}=await it.get(`${ue.SHD_G123_GAME_URL}/api/recommends`,{params:{lang:nt.language,region:e.region,appCode:e.aud}}),r=await Fy(e),n=Dy(t,r,e.aud);return G.dispatch(Ee.actions.updateRecommendGames({recommendGames:n})),n}catch(e){return console.error("Failed to fetch recommendations:",e),window.captureGlobalException?.(e),[]}}function Dy(e,t,r){const n=e.filter(i=>i.appId!==r),a=t?.length>3?n.filter(i=>t.includes(i.appId)).sort((i,s)=>{const u=t.indexOf(i.appId),c=t.indexOf(s.appId);return u-c}):n;return Fc()?a.filter(i=>!Nc(i.appId)):a}async function Fy(e){if(!(nt.language&&e.sub&&e.aud)){const r=new Error("Language, ctwid, or appid is null or empty. Skipping axios post request.");return console.warn(r.message),window.captureGlobalException?.(r),[]}try{const{data:{appid_list:r}}=await it.post(`${ue.SHD_G123_GC3A_URL}/v1/recommendation/g-button/rank`,{metadata:{labels:"string",name:"gbutton"},payload:{abtest:!1,language:nt.language,ctwid:e.sub,appid:e.aud}});return r}catch(r){return console.error("Failed to fetch personalized recommendations:",r),window.captureGlobalException?.(r),[]}}async function If(e,t){try{const r=await it.get(`${ue.SHD_G123_WEB_URL}/api/v2/game_tag/${e}/game_codes`,{params:{lang:nt.language}});return t(r.data.content),r.data.content}catch(r){return console.error(`Failed to fetch ${e} game codes:`,r),window.captureGlobalException?.(r),[]}}async function fw(){return If("hotgames",e=>G.dispatch(Ee.actions.updateHotGameCodes({hotGameCodes:e})))}async function dw(){return If("newgames",e=>G.dispatch(Ee.actions.updateNewGameCodes({newGameCodes:e})))}async function pw(){const e=await it.get(`${ue.SHD_G123_WEB_URL}/api/v2/game_tag/pre-registration/games`,{params:{lang:nt.language,offset:0,limit:100}});return G.dispatch(Ee.actions.updatePreregists({preregists:e.data.content})),e.data.content}async function bc(e){G.dispatch(Ee.actions.updateAuthProviders({authProviders:e}))}function mw(){Ly(window.option.userId),vr("success",nt.t("common.actions.copy.success"))}async function hw(){const e=await it.get(`${ue.SHD_G123_GAME_URL}/api/v1/user_ranking/${window.option.userId}`);return G.dispatch(Ee.actions.updateVipRank({vipRank:e?.data?.ranking||0})),e?.data?.ranking||0}function Ny(e){if(!ue.SHD_PARTNER_TTD_ENDPOINT)return;const t=new URL(ue.SHD_PARTNER_TTD_ENDPOINT);t.searchParams.set("ttd_puid",e),t.searchParams.set("ttd_pid",ue.SHD_PARTNER_TTD_PID),t.searchParams.set("ttd_tpi","1"),t.searchParams.set("gdpr","0");const r=new Image;r.src=t.href}class jy{constructor(t,r){this.deferredPrompt=_c(),this.isFirstLaunch=!1,this.trackerProps=t,this.storage=r||this.createDefaultStorage();const n=new URLSearchParams(window.location.search);this.iid=n.get("_iid")||"",this.itm=n.get("_itm")||"",no()?this.promptType=n.get("_pt")||ro():this.promptType=ro(),this.checkFirstLaunch(),this.initInstallPrompt()}createDefaultStorage(){return{setItem:(t,r)=>{try{return localStorage.setItem(t,r),!0}catch(n){return console.error("Storage error:",n),!1}},getItem:t=>{try{return localStorage.getItem(t)}catch(r){return console.error("Storage error:",r),null}}}}initInstallPrompt(){const t=()=>{setTimeout(()=>{this.deferredPrompt.resolve(null)},1e3)};window.addEventListener("beforeinstallprompt",r=>{console.log("beforeinstallprompt event:",r),this.trackInstallPrompt(r),this.deferredPrompt.resolve(r)},{once:!0}),document.readyState==="complete"?t():window.addEventListener("load",t,{once:!0}),window.addEventListener("appinstalled",r=>{console.log("appinstalled event:",r),this.trackInstallSuccess(r)})}checkFirstLaunch(){if("storage"in navigator&&"estimate"in navigator.storage&&(window.matchMedia("(display-mode: standalone)").matches||window.matchMedia("(display-mode: fullscreen)").matches||"standalone"in window.navigator&&window.navigator.standalone===!0)){const r=this.getFirstLaunchStorageKey();this.storage.getItem(r)||(this.isFirstLaunch=!0,this.trackFirstLaunch(),this.storage.setItem(r,Date.now().toString()))}}getFirstLaunchStorageKey(){return`pwa_first_launch:${this.iid||"default"}`}trackInstallPrompt(t){this.trackerProps.onEvent({event:"pwa_install_prompt",platform:this.getPlatform(),promptType:this.promptType,originEvent:t})}trackInstallSuccess(t){this.trackerProps.onEvent({event:"pwa_install_success",platform:this.getPlatform(),promptType:this.promptType,originEvent:t})}trackFirstLaunch(){this.trackerProps.onEvent({event:"pwa_first_launch",platform:this.getPlatform(),promptType:this.promptType,iid:this.iid,itm:this.itm})}getPlatform(){return Lt()?"iOS":kt()?"Android":"Desktop"}async getTrackerStatus(){return{isInstalled:no(),isFirstLaunch:this.isFirstLaunch,isPwaPromptSupported:pd(),isPwaNativePromptSupported:dd(),beforeInstallPromptEvent:await this.deferredPrompt.promise,platform:this.getPlatform(),trackerProps:this.trackerProps}}}function Vy(e){window.pwaTracker||(window.pwaTracker=new jy(e))}const Ja=500,Wy=1e3,Sc=1e3;let Vt,Wt,Pr,le;function qy(){Vt||Wt||Pr||le||(Vt=document.getElementById("splash")||void 0,Wt=document.getElementById("iframe-game")||void 0,Pr=document.getElementById("splash-progress-container")||void 0,le=document.getElementById("splash-progress")?.querySelector("span")||void 0)}function Hy(){Vt&&(Vt.style.opacity="0",Vt.style.transition=`opacity ${Ja}ms ease-out`),Wt&&(Wt.style.opacity="1",Wt.style.transition=`opacity ${Ja}ms ease-in ${Ja*1.5}ms`)}function By(){le&&(le.style.display="none"),Pr&&(Pr.style.display="none")}function Uy(){return le?le.style.display==="block":!1}function Yy(){return le?Number.parseInt(le.style.width):0}function $y(e){if(le){if(e<Yy())return;le.style.width=`${e}%`}}function Zy(){le&&(le.style.transition=`width ${Sc/2}ms ease-in`,$y(100)),setTimeout(By,Sc*2)}function Xy(){qy(),window.SPLASH_TIMER&&(clearTimeout(window.SPLASH_TIMER),window.SPLASH_TIMER=void 0),Uy()&&Zy(),setTimeout(Hy,Wy)}zd();const ot=window.option.appId;Nc(ot)&&Fc()&&G.dispatch(md(!1));const Ky=()=>/line/i.test(navigator.userAgent);function zy(){if(!/iPad|iPhone|iPod/.test(navigator.userAgent))return;const e=t=>{const r=t.originalEvent||t;"scale"in r&&r.scale!==1&&(r.preventDefault(),document.body.style.transform="scale(1)")};document.addEventListener("touchmove",e,{passive:!1}),document.addEventListener("gesturestart",e,{passive:!1})}window.perf?.start&&(window.perf.app_start=qt(),setTimeout(()=>{window.perf.app_start===void 0||window.perf.start===void 0||Xt(`/stats?k=perf&t=app_start&a=${ot}&d=${window.perf.app_start-window.perf.start}&img=1`)}));Vy({onEvent:e=>{if(console.log("[PWA] ON_EVENT",e),e.event==="pwa_first_launch"){const t=e.iid,r=e.itm;if(!t||!r)return;try{const n=Number.parseInt(r,10);if(Number.isNaN(n)||Date.now()-n>1e3*60*60*24)return}catch(n){console.error("[PWA] Error",n);return}console.log("[PWA] FirstLaunch",e),Lt()||kt()?(console.log("[PWA][pwa_first_launch] Send FirstLaunch",e),vi(),Ei(e.promptType||"")):console.info("[PWA][pwa_install_success] Ignore",e,{isIOS:Lt(),isAndroid:kt()});return}if(e.event==="pwa_install_prompt"){console.log("[PWA] BeforeIntallPrompt",e),gi({event:"PWA",pwa_event:"prompt",appid:ot}),window.pwaInstallPrompt=e.originEvent,G.dispatch(_d({isPwaInstallPromptReady:!0})),e.originEvent.userChoice.then(t=>{console.log("[PWA] UserChoice",t.outcome)}).catch(t=>{console.error("[PWA] Error",t)});return}e.event==="pwa_install_success"&&(console.log("[PWA] AppInstalled",e),!Lt()&&!kt()?(console.log("[PWA][pwa_install_success] Send FirstLaunch",e),vi(),Ei(e.promptType||"")):console.info("[PWA][pwa_install_success] Ignore",e,{isIOS:Lt(),isAndroid:kt()}),gi({event:"PWA",pwa_event:"install",appid:ot}))}});const Mo=typeof requestIdleCallback=="function"?requestIdleCallback:setTimeout,Qa=_t(async()=>{if(!mr.platform)return;console.log("[PLATFORM] Waiting for idle",Date.now());try{typeof Promise.race=="function"&&await Promise.race([new Promise(t=>Mo(t)),new Promise(t=>setTimeout(t,1e4))])}catch(t){console.error(t)}console.log("[PLATFORM] Loading app",Date.now());let e;try{e=await Sd(ot)}catch(t){console.log(`[PLATFORM] No valid age verification status: ${t}`)}e?.verificationRequired&&!e?.verified&&G.dispatch(Ed.actions.showDialog()),jo(()=>Io(()=>import("./game-a361386b-BqmJhnkd.js").then(t=>t.d),__vite__mapDeps([5,2,3,1,4,6])),{retries:3}).then(t=>t.initApp()).catch(t=>{console.error("[PLATFORM] LOADING_APP_ERROR",t),window.captureGlobalException?.(t)})});function Jy(){try{if(window.innerHeight<document.documentElement.clientHeight){const e=Math.min(window.innerHeight,document.documentElement.clientHeight);document.documentElement.style.height=`${e}px`,window.addEventListener("orientationchange",()=>{document.documentElement.style.height=""})}}catch(e){console.error(e)}}async function Qy(){const t=await Tr().currentSession(),r=await Cr(),n={authCode:t.code,appCode:r.aud,userId:r.sub,country:r.country,region:r.region,lang:r.lang,appUrl:window.option.idCallback,onpaymentcompleted:a=>{console.info("[Payment] completed with",a),Yc({type:"PspCommand",action:"PaymentStatusChanged",orderNo:a})}};await bd(n)}async function ew(){await Lf();const e=Bc();Nd(e),Mo(()=>{hd()}),zy();const{prefetchSession:t}=window;t&&(window.prefetchSession=void 0),window.__cloud_game_enabled__&&await Ry();const r=vd({config:{appId:ot,zIndex:9997},onAuthStateChanged:(u,c)=>{if(!u){console.error(new Error("Fetch user error"),{user:u,prevUser:c});return}if(u.code===Ec&&G.dispatch(Dc.actions.showDialog()),G.dispatch(wd()),!c){window.option.userId=u.userId,window.option.providers=u.providers,bc(u.providers),window.option.code=u.code,kf(u.userId);return}if((u.providers.length!==c.providers.length||u.providers.slice().sort().join(",")!==c.providers.slice().sort().join(","))&&(console.info(`Providers changed [${c.providers}] -> [${u.providers}], re-render template`),bc(u.providers),Yc({category:"g123_auth_event",action:"p_sns_bind_changed"}),window.postMessage({type:"g123_auth_event",action:"sns_bind_changed",data:{userId:u.userId,providers:u.providers}})),u.userId!==c.userId){console.info(`User changed [${c.userId}] -> [${u.userId}], reload page`),hi("USER_ID_CHANGED",!0);return}u.code!==c.code&&(console.info(`Code changed [${c.code}] -> [${u.code}], reload game`),hi("USER_APP_CODE_CHANGED",!0))},onAuthResult:u=>{switch(u.level){case"success":vr("login",u.message);break;case"warning":vr("warning",u.message);break;case"error":vr("error",u.message);break}},prefetch:t});Bd(ot);const n=Hc()===qc.CHANNEL_ONLY?Hr(!0):void 0;try{await r.currentSession()}catch(u){console.error(u)}r.currentUser()||await jo(()=>r.reload(),{retries:5});const a=r.currentUser();if(!a)throw new Error("Fetching user error");const o=gd(r);if(!o)throw new Error("Fetching session error");const{isPlatformNewUser:i,isAppNewUser:s}=o;if(Pc({action:i?"p_register":"p_login",data:{display_name:a.userId,providers:a.providers,...i?{}:{custom:{is_app_new_user:s}}}}),console.info("[PLATFORM] reloadGame in game mode"),mr.psp&&Qy(),mr.auxin&&o.gameMode!=="cloud"&&Mo(()=>{Io(async()=>{const{initMicroApplications:u}=await import("./game-74aa2de1-B4nljWTS.js");return{initMicroApplications:u}},__vite__mapDeps([7,1,2,3,4])).then(({initMicroApplications:u})=>{u()})}),mr.websocket&&$v(),Ny(a.userId),window.option.mode!=="app"){if(yd()&&Ky())try{const c=mi.getItem(to.lineAppAuthRequestedAt),l=c?Number.parseInt(c,10):void 0;(!l||Cd(new Date,new Date(l))>7)&&(mi.setItem(to.lineAppAuthRequestedAt,`${Date.now()}`),r.login("line"))}catch(c){console.error(c)}setTimeout(Qa,3e3);const u=qt();await new Promise(c=>{const l=setTimeout(c,3e4);(n||Hr(!0)).then(()=>{clearTimeout(l),c()})}),Qa(),qt()-u>1e4&&await new Promise(c=>setTimeout(c,2e3))}else console.info("[PLATFORM] reloadGame in app mode"),document.body.style.backgroundColor="#fff",n||Hr(!0),Qa();Xy(),Jy(),setTimeout(Qd,1e3)}ew();const vw=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));export{mr as F,rp as _,Rr as a,ao as b,aw as c,Ly as d,sw as e,hw as f,mw as g,uw as h,cw as i,lw as j,fw as k,Lv as l,dw as m,pw as n,ow as o,iw as p,vw as q,lh as r,Gy as s,ky as t};
