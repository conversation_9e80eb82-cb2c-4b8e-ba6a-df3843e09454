[1, ["e7FAotE69J1KEBRuhMf5jy@f9941", "a7ZHpooe1A07RKUYPDmCm2", "99kCQpVCFH04FJAaKTXN8t@f9941", "91r+DCnc1D7L49HhrhpSJh@f9941", "03uLHjJ4NBSaBdMroakUwA@f9941", "5dRMB/7/1Ki4hTzroiNsxC@f9941", "63mRm0nWNLPYi/F6itN0Ki@f9941", "7eIdKJEElJLL893pJ69gq1@f9941", "34HJhSVXxLR6ZNyWM+4T1I@f9941", "32unDRUrVDM4AS9JGl+8MC@f9941", "b7uO3fkh9HGZUXk7brEGWf@f9941", "7eIdKJEElJLL893pJ69gq1@6c48a"], ["node", "_spriteFrame", "targetInfo", "_parent", "root", "asset", "value", "_content", "_target", "data", "_textureSource"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_lpos", "_children", "_lscale", "_lrot", "_euler"], -2, 4, 9, 1, 5, 2, 5, 5, 5], ["cc.Label", ["_string", "_actualFontSize", "_isBold", "_outlineWidth", "_enableOutline", "_horizontalAlign", "_enableWrapText", "_lineHeight", "_fontSize", "_overflow", "node", "__prefab", "_outlineColor", "_color"], -7, 1, 4, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Widget", ["_alignFlags", "_originalHeight", "_top", "_bottom", "_originalWidth", "_left", "_right", "node", "__prefab"], -4, 1, 4], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingY", "_spacingX", "_paddingTop", "_paddingBottom", "_affectedByScale", "node", "__prefab"], -4, 1, 4], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.<PERSON><PERSON>", ["_enabled", "_transition", "_zoomScale", "node", "__prefab", "_target"], 0, 1, 4, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["<PERSON><PERSON>", ["bounceDuration", "brake", "horizontal", "node", "__prefab", "_content"], 0, 1, 4, 1], ["ad253TrMIpHFZgL3Bp6KTbe", ["_batch", "node", "__prefab", "tmpNode", "pageChangeEvent", "renderEvent", "selectedEvent"], 2, 1, 4, 1, 4, 4, 4], ["cc.ClickEvent", [], 3], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.TargetInfo", ["localID"], 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "mountedComponents", "propertyOverrides", "removedComponents"], 2, 1, 9, 9, 9], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 6], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["cc.LabelOutline", ["node", "__prefab"], 3, 1, 4], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4]], [[10, 0, 2], [12, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [19, 0, 2], [22, 0, 1, 2, 3], [23, 0, 1, 2, 2], [0, 0, 1, 7, 6, 5, 8, 10, 3], [5, 0, 1, 2, 3, 1], [0, 0, 1, 7, 9, 6, 5, 8, 3], [25, 0, 1, 2, 2], [2, 0, 2, 3, 4, 2], [2, 1, 0, 2, 3, 4, 3], [0, 0, 1, 7, 6, 5, 8, 3], [0, 0, 2, 1, 7, 6, 5, 8, 11, 12, 4], [4, 0, 1, 6, 7, 8, 4], [2, 1, 0, 2, 3, 3], [2, 2, 3, 4, 1], [16, 1], [6, 0, 1, 2, 3, 4, 4], [28, 0, 1, 2, 2], [0, 0, 1, 9, 6, 5, 8, 3], [0, 0, 1, 9, 6, 5, 8, 10, 3], [0, 0, 1, 7, 9, 6, 5, 3], [0, 3, 4, 7, 5, 3], [0, 0, 2, 1, 7, 6, 5, 11, 12, 4], [9, 0, 1, 2, 3, 4, 5, 6, 3], [3, 0, 2, 3, 1, 7, 8, 5], [13, 0, 1, 2, 3, 4, 5, 4], [4, 7, 8, 1], [2, 2, 3, 1], [2, 0, 2, 3, 2], [20, 0, 1, 2, 3, 4, 2], [21, 0, 1, 1], [24, 0, 1, 2, 2], [1, 0, 1, 8, 7, 2, 4, 3, 10, 11, 12, 8], [8, 0, 2], [0, 0, 1, 9, 6, 5, 3], [3, 0, 5, 6, 2, 3, 4, 1, 7, 8, 8], [3, 0, 4, 1, 7, 8, 4], [3, 0, 7, 8, 2], [11, 0, 1, 2, 3, 4, 5, 4], [4, 0, 1, 3, 2, 7, 8, 5], [4, 0, 1, 4, 5, 2, 7, 8, 6], [2, 1, 2, 3, 4, 2], [14, 0, 1, 2, 3, 4, 5, 4], [15, 0, 1, 2, 3, 4, 5, 6, 2], [17, 0, 1, 1], [18, 0, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5, 4], [26, 0, 1, 2, 3], [1, 0, 1, 8, 2, 4, 3, 10, 11, 12, 7], [1, 0, 5, 1, 7, 6, 2, 3, 10, 11, 13, 12, 8], [1, 0, 5, 1, 7, 2, 10, 11, 6], [1, 0, 1, 9, 2, 10, 11, 13, 5], [1, 0, 5, 1, 6, 2, 4, 3, 10, 11, 12, 8], [1, 0, 5, 1, 9, 6, 2, 4, 3, 10, 11, 13, 12, 9], [1, 0, 1, 6, 2, 4, 3, 10, 11, 13, 12, 7], [27, 0, 1, 1]], [[[[35, "nongchang_pygw_wd"], [36, "nongchang_pygw_wd", 33554432, [-6], [[2, -4, [0, "6fUyudUT1IFo6FvRHKFGRJ"], [5, 527, 765.5]], [37, 21, -40, -40, 257.25, 257.25, 720, 765.5, -5, [0, "6b1cvTbIZFyJGZXVrsGtve"]]], [40, "95ptoTxBhATrAjGlSVUme9", null, null, -3, 0, [-1, -2]]], [20, "item1", 33554432, [-9, -10, -11, -12, -13, -14, -15, -16, -17], [[2, -7, [0, "ccRmbuDa9OILV7PEMW7RX6"], [5, 522, 201]], [28, -8, [0, "e3iF1NDBlDar2LL3ZVbMwM"]]], [1, "b9b8i6iZBHLIanFaDT8ecC", null, null, null, 1, 0], [1, 0, -106.5, 0]], [21, "xingxing1", 33554432, [-20, -21, -22, -23, -24], [[2, -18, [0, "ddndB1LdhP2YSqhoJEFCIk"], [5, 1.4, 88]], [41, 1, 1, -1.4, -14.7, -19, [0, "26l1mrT69IsJ1Wvi7wnVFG"]]], [1, "260Fk8jFpOiasANFzLKmaD", null, null, null, 1, 0], [1, -0.98, -22.807, 0], [1, 0.15, 0.15, 1]], [20, "ScrollView1", 33554432, [-31], [[2, -25, [0, "08HftT53NM/JZmACyBLbj8"], [5, 540, 636.4010000000001]], [15, 1, 0, -26, [0, "cdmfT+b7NK2I2ZsY40O65q"]], [44, 0.23, 0.75, false, -28, [0, "4definfZFLgJaxFE/yglEU"], -27], [45, false, -29, [0, "eeOQCv9Z9O4av8PPikpPCd"], 2, [17], [17], [17]], [26, 5, 14.783999999999855, -17.497, 617.5, -30, [0, "83lOl0WYFP95KuZwbAPRXk"]]], [1, "a2shbOAbZGfacBSfHmkfAw", null, null, null, 1, 0], [1, 0, -16.140499999999975, 0]], [8, "view1", 33554432, 4, [-36], [[7, -32, [0, "adr/uBRp9DV5yr7LNKaDFJ"], [5, 540, 636.4010000000001], [0, 0.5, 1]], [46, -33, [0, "2avtNFdZVF+Y3EQs1XwrhA"]], [47, -34, [0, "13gCeHkTdIZqMbcE25CjAU"], [4, 16777215]], [38, 45, 240, 250, -35, [0, "03w2AYL2BOZI8fCk78UcvF"]]], [1, "46jP0PFcFDLL7LK0+MbVfJ", null, null, null, 1, 0], [1, 0, 318.20050000000003, 0]], [22, "content1", 33554432, 5, [2], [[7, -37, [0, "efpE9GCDREYJR7NW2JpD/E"], [5, 508, 211], [0, 0.5, 1]], [39, 1, -38, [0, "c2+Uv+SMxIa7e2O0sm+AeP"]], [42, 1, 2, 6, 4, 5.1, -39, [0, "13BObobaJPGIbfvEIlMpAS"]]], [1, "ffQKq6ghBPpqlC3B/2MKM4", null, null, null, 1, 0]], [3, ["bc648ctydDD5l0O0o5vV7i"]], [3, ["bc648ctydDD5l0O0o5vV7i"]], [21, "icon_bg", 33554432, [-44], [[2, -40, [0, "fbQg6/0TNMyaeGp7Br6ph2"], [5, 71, 73]], [15, 1, 0, -41, [0, "44HexSi11DrLJZE6RdLMez"]], [48, false, 3, 0.9, -43, [0, "f7URolRq1IT6R1w7wVGV8y"], -42]], [1, "09MVS2qZFHEqGXOS5/HENf", null, null, null, 1, 0], [1, 0, 3.517, 0], [1, 0.6, 0.6, 1]], [8, "layout_sj", 33554432, 2, [-47, -48, -49], [[2, -45, [0, "dcRYb5EuVJbYf+SE/vcNcf"], [5, 160.6427734375, 36]], [14, 1, 1, true, -46, [0, "8bUJX1spVEObJ2uiKGK3NC"]]], [1, "95oURJ7jdCFJK4YU4buUlG", null, null, null, 1, 0], [1, 0, 24.841, 0]], [8, "img_pygw_di3", 33554432, 2, [-53, -54], [[7, -50, [0, "6cJgu41IZIt7hmA8pt5smS"], [5, 157.48828125, 34], [0, 1, 0.5]], [29, -51, [0, "87nBTzwApOlbXo0Q3/W/IW"]], [14, 1, 1, true, -52, [0, "02ff7vfDZIAJekI87ZIDvM"]]], [1, "e4ETjEk4lBm7qfn/WtsiL2", null, null, null, 1, 0], [1, 241.943, 84.232, 0]], [22, "shouyi", 33554432, 11, [-58, -59], [[7, -55, [0, "28H2a/jbFOuJACWQGCFvAo"], [5, 97.48828125, 36], [0, 1, 0.5]], [29, -56, [0, "begAcvattK16YrCvCNTR2e"]], [14, 1, 1, true, -57, [0, "3e7gWLkzdEFp9tkbqIHtKd"]]], [1, "61CEkm+adJDqKzgcGoZxFT", null, null, null, 1, 0]], [8, "neirong", 33554432, 1, [-62, 4], [[2, -60, [0, "0blN3mmM5Eur8q4yELitIz"], [5, 486, 633.688]], [28, -61, [0, "b7Nps+IyhJg7YvtzKt4HML"]]], [1, "58CcusHbdHmJiHiLVg5WNq", null, null, null, 1, 0], [1, 0, 31.686, 0]], [3, ["03XfDFaqNOdp6SCwdhIQpD"]], [3, ["03XfDFaqNOdp6SCwdhIQpD"]], [8, "icon_tx", 33554432, 2, [-65, -66], [[2, -63, [0, "d0erUqmxlHm7PoIXYQ/fnO"], [5, 80, 79]], [30, 0, -64, [0, "d8rSy4baBDoaxyhmdiSuYN"]]], [1, "ffF1e44gtPpYni4JNDol45", null, null, null, 1, 0], [1, -198.787, -14, 0]], [8, "img_pygw_di4", 33554432, 2, [9, 3], [[2, -67, [0, "93R9GeIKNC0ouDeSNkTb+i"], [5, 71, 60]], [16, -68, [0, "497yv0ZY9MmbpEcDfnktRu"], 12]], [1, "9cxZ0CiTVEb4tnfHWRLQog", null, null, null, 1, 0], [1, -139.348, 18.462, 0]], [8, "img_hy_jdt1", 33554432, 2, [-71, -72], [[2, -69, [0, "3dJzonnqlGdq31bt/lfwzh"], [5, 166, 16]], [11, 1, 0, -70, [0, "7fG5Vy0VhEzIsM+U+tkgDL"], 14]], [1, "0d+X6OW/BPZrhiBD4pgNLG", null, null, null, 1, 0], [1, 0, -6.649000000000001, 0]], [12, "bg_tips_3", 33554432, 13, [[2, -73, [0, "4dtgY8kdxPYr/81hu6TgBU"], [5, 542, 656.798]], [11, 1, 0, -74, [0, "8abrLlANpAjZupn+Y2g5ar"], 0], [26, 5, 6.889999999999991, -30, 745, -75, [0, "5feJwgRh9H55AqrovIwlGi"]]], [1, "feKfrakDNE1qJZlTRBVMwd", null, null, null, 1, 0], [1, 0, -18.444999999999993, 0]], [23, 0, {}, 2, [27, "03XfDFaqNOdp6SCwdhIQpD", null, null, -84, [31, "fcgLPNn3lG+osi+naVzCdP", 1, [[32, [3, ["5cvq8qCo5I7pXjdLcFwDsb"]], [-83]]], [[4, "btn_qwhy", ["_name"], 14], [5, ["_lpos"], 14, [1, -0.972, -66, 0]], [5, ["_lrot"], 14, [3, 0, 0, 0, 1]], [5, ["_euler"], 14, [1, 0, 0, 0]], [4, "前往花園", ["_string"], 7], [33, ["_spriteFrame"], -76, 3], [4, 0, ["_overflow"], 7], [9, ["_contentSize"], [3, ["77Mt/A7RxGrJ0Q7zFDQxkY"]], [5, 150, 61.44]], [4, 36, ["_actualFontSize"], 7], [5, ["_lpos"], -77, [1, 0, 0, 0]], [9, ["_contentSize"], [3, ["1657nTijNEh7Bns5UYA0sV"]], [5, 128, 47]], [9, ["_contentSize"], [3, ["89Kmzp7M5JRJxKSraI3F2C"]], [5, 142, 50]], [4, 1, ["_sizeMode"], -78], [4, "txt_qwhy", ["_name"], -79], [4, "btn_tab_qwhy", ["_name"], -80], [4, 36, ["_fontSize"], 7], [4, 44, ["_lineHeight"], 7], [5, ["_lscale"], -81, [1, 1, 1, 1]], [5, ["_outlineColor"], 7, [4, 4282867204]], [5, ["_lscale"], 14, [1, 1, 1, 1]], [5, ["_lscale"], -82, [1, 0.5, 0.5, 1]]], [[3, ["2fM+Ep5oJCAbVEaTu9NhXO"]]]], 2]], [23, 0, {}, 2, [27, "03XfDFaqNOdp6SCwdhIQpD", null, null, -91, [31, "8eWwXd3MtGxqt+EAtn9Jex", 1, [[32, [3, ["5cvq8qCo5I7pXjdLcFwDsb"]], [-90]]], [[4, "btn_tzpy", ["_name"], 15], [5, ["_lpos"], 15, [1, 170.698, 3.44, 0]], [5, ["_lrot"], 15, [3, 0, 0, 0, 1]], [5, ["_euler"], 15, [1, 0, 0, 0]], [4, "停止培養", ["_string"], 8], [33, ["_spriteFrame"], -85, 5], [4, 0, ["_overflow"], 8], [9, ["_contentSize"], [3, ["77Mt/A7RxGrJ0Q7zFDQxkY"]], [5, 150, 61.44]], [4, 36, ["_actualFontSize"], 8], [5, ["_lpos"], -86, [1, 0, 0, 0]], [9, ["_contentSize"], [3, ["1657nTijNEh7Bns5UYA0sV"]], [5, 128, 47]], [9, ["_contentSize"], [3, ["89Kmzp7M5JRJxKSraI3F2C"]], [5, 142, 50]], [4, 0, ["_sizeMode"], -87], [49, "txt_tzpy", ["_name"], [3, ["5cvq8qCo5I7pXjdLcFwDsb"]]], [4, "btn_tab_tzpy", ["_name"], -88], [4, 36, ["_fontSize"], 8], [4, 44, ["_lineHeight"], 8], [5, ["_lscale"], -89, [1, 1, 1, 1]], [5, ["_lscale"], 15, [1, 1, 1, 1]], [5, ["_outlineColor"], 8, [4, 4282072208]]], [[3, ["2fM+Ep5oJCAbVEaTu9NhXO"]]]], 4]], [8, "bg_djs1", 33554432, 16, [-94], [[2, -92, [0, "a8h4cp5PBC1LXSYDAu+kNU"], [5, 136, 41]], [11, 1, 0, -93, [0, "10UjMAeANNRZTR42Imu/mU"], 6]], [1, "70yzum1sVBkpkJ9M8sPEKa", null, null, null, 1, 0], [1, 28.363, -27.964, 0]], [6, "icon_shu", 33554432, 9, [[2, -95, [0, "80EJHU8jVEl7e5/XaPSK/S"], [5, 71, 73]], [15, 1, 0, -96, [0, "d4c8CoZR9CTJ0SmJzIZUzh"]], [18, false, 3, 0.9, -97, [0, "91QxAwnx1JMKMLNTJKMJt2"]]], [1, "f9s6UTryZP+Lxd4SRdB+H+", null, null, null, 1, 0], [1, 0, 7, 0], [1, 0.4, 0.4, 1]], [6, "txt_time", 33554432, 18, [[2, -98, [0, "06brIqi4VKgYDCxq7Sdkk2"], [5, 150.10546875, 56.4]], [50, "00:00:03", 36, 36, true, true, 3, -99, [0, "7ae8aE/fBD8aqdvuFS/QwE"], [4, 4280098330]], [57, -100, [0, "0dJfLFVY5GWJwXUtwbctAk"]]], [1, "70X0ZZKhVGl74XBjEcC2NM", null, null, null, 1, 0], [1, -6.404, 0.049, 0], [1, 0.5, 0.5, 1]], [6, "icon_shouyi1", 33554432, 10, [[2, -101, [0, "61klTD3uhHcq3b+gjSDqR6"], [5, 37, 52]], [16, -102, [0, "1eMRW3LbVPG7ZsTrVg7fn+"], 15], [18, false, 3, 0.9, -103, [0, "83zi4koZxMlIgOsJtubGA2"]]], [1, "d6fNzw6ClETIpp2d1Id4yS", null, null, null, 1, 0], [1, -68.29638671875, 0.09399999999993724, 0], [1, 0.65, 0.65, 1]], [6, "txt_sy1", 33554432, 11, [[7, -104, [0, "7bTMwiisRHw54cObCdLKvn"], [5, 120, 55.44], [0, 0, 0.5]], [51, "收益：", 0, 40, 44, false, true, 3, -105, [0, "d2GoBdJ79FNbK/W2LPrO0z"], [4, 4290117376], [4, 4280098330]], [19, "nongchang_71", -106, [0, "59T0wZ50xKv4XZZ8msc6aE"]]], [1, "63cbD6AElISKzFujW+CKye", null, null, null, 1, 0], [1, -157.48828125, 0.56, 0], [1, 0.5, 0.5, 1]], [6, "icon_shouyi2", 33554432, 12, [[2, -107, [0, "5bisDLTMxFLq7IxITElKc6"], [5, 37, 52]], [16, -108, [0, "5608iKdVdKqqZCB/7E636e"], 16], [18, false, 3, 0.9, -109, [0, "b8bVXYt6NIJK6nRZJMKBl4"]]], [1, "5fotrixUlE9YPoUJywAkLh", null, null, null, 1, 0], [1, -83.61328125, 0, 0], [1, 0.75, 0.75, 1]], [12, "bg_di4", 33554432, 2, [[2, -110, [0, "e2Vp5YbLNIqb0Oi5lvwrtB"], [5, 522, 201]], [43, 1, -111, [0, "7bd63MRwpJ6qMBvLNfri8Z"], 1]], [1, "6dj9cSvJZLDbjqk9KFMX62", null, null, null, 1, 0], [1, 0, -0.27750000000000075, 0]], [25, "txt_qwhy", 33554432, 20, [[[2, -112, [0, "77Mt/A7RxGrJ0Q7zFDQxkY"], [5, 150, 61.44]], [34, "前往花園", 36, 36, 44, true, true, 3, -113, [0, "bc648ctydDD5l0O0o5vV7i"], [4, 4282867204]], -114], 4, 4, 1], [1, "5cvq8qCo5I7pXjdLcFwDsb", null, null, null, 1, 0], [1, 0, 2, 0], [1, 0.5, 0.5, 1]], [3, ["5eZAD6dg1PkIthJGeTXCdA"]], [25, "txt_tzpy", 33554432, 21, [[[2, -115, [0, "77Mt/A7RxGrJ0Q7zFDQxkY"], [5, 150, 61.44]], [34, "停止培養", 36, 36, 44, true, true, 3, -116, [0, "bc648ctydDD5l0O0o5vV7i"], [4, 4282072208]], -117], 4, 4, 1], [1, "5cvq8qCo5I7pXjdLcFwDsb", null, null, null, 1, 0], [1, 0, 2, 0], [1, 0.5, 0.5, 1]], [3, ["5eZAD6dg1PkIthJGeTXCdA"]], [6, "txt_mzzl1", 33554432, 2, [[7, -118, [0, "5fy+7uPaRP1qOCrDidMW05"], [5, 251.11328125, 63], [0, 0, 0.5]], [52, "巴啦啦小魔仙 ", 0, 40, 50, true, -119, [0, "ddwrl9bBhB5LCyIqpHKmf/"]]], [1, "326HINcPJOurJv/prer/eT", null, null, null, 1, 0], [1, -235.725, 84.15, 0], [1, 0.5, 0.5, 1]], [6, "txt_jishi1", 33554432, 22, [[2, -120, [0, "993ZJwRnJB3auR2zBbU3ly"], [5, 223.217188, 50.4]], [53, "00:29:59", 41, 2, true, -121, [0, "4d3JDE08tLXLJsoeZvvrPd"], [4, 4281742902]]], [1, "1f2eNntyVEgpB6Xa5uok87", null, null, null, 1, 0], [1, 0, -3.5, 0], [1, 0.5, 0.5, 1]], [6, "hy_item1", 33554432, 16, [[2, -122, [0, "d4RR/sKCdFVpOaqcTMR/eP"], [5, 80, 80]], [30, 0, -123, [0, "1baqrmRgZGIr79K/N8cKPP"]]], [1, "5c/eOx9RtNErggufZkpWcf", null, null, null, 1, 0], [1, -12.59, 31.37, 0], [1, 0.85, 0.85, 1]], [13, "icon_xx_1", false, 33554432, 3, [[2, -124, [0, "91akewlfVNErgMFsnO7d6b"], [5, 78, 80]], [10, 0, -125, [0, "d6WVdowDZIjLJN72syS+R0"], 7]], [1, "61AaMVaEpFa4BJgE0dlAEt", null, null, null, 1, 0], [1, -153.2, 0, 0], [3, 0, 0, 0.15643446504023087, 0.9876883405951378], [1, 0, 0, 18]], [13, "icon_xx_2", false, 33554432, 3, [[2, -126, [0, "b3euiolWpInrjFiqk7UQ+1"], [5, 78, 80]], [10, 0, -127, [0, "2drYa7SF5IQIAf10eJbB00"], 8]], [1, "93jjAfKxVFcYjbIvfzx5Lu", null, null, null, 1, 0], [1, -76.6, 0, 0], [3, 0, 0, 0.15643446504023087, 0.9876883405951378], [1, 0, 0, 18]], [24, "icon_xx_3", false, 33554432, 3, [[2, -128, [0, "7a6+pW3OVEq4hIDNAqGJoP"], [5, 78, 80]], [10, 0, -129, [0, "8dqKx+HxNGlpxuXCj+RJbp"], 9]], [1, "0dH85v821OwbV1uaL+X4gn", null, null, null, 1, 0], [3, 0, 0, 0.15643446504023087, 0.9876883405951378], [1, 0, 0, 18]], [13, "icon_xx_4", false, 33554432, 3, [[2, -130, [0, "26kkFB775JoLY66orHxt37"], [5, 78, 80]], [10, 0, -131, [0, "2cWA8/JO5HGLa/YAgQ2ICA"], 10]], [1, "09D90MKplNIqq/Ehc8a2gR", null, null, null, 1, 0], [1, 76.60000000000001, 0, 0], [3, 0, 0, 0.15643446504023087, 0.9876883405951378], [1, 0, 0, 18]], [24, "icon_xx_5", false, 33554432, 3, [[2, -132, [0, "0aCpQkKLhNLI+Q/HNVKbPE"], [5, 78, 80]], [10, 0, -133, [0, "a7PDRZeaBB/5SDe+u1aexr"], 11]], [1, "ffEQJmU1dF74ItKeyRSiXm", null, null, null, 1, 0], [3, 0, 0, 0.15643446504023087, 0.9876883405951378], [1, 0, 0, 18]], [12, "Bar", 33554432, 18, [[7, -134, [0, "f3lJOAFb1JUbedQPlVAjJc"], [5, 151, 6], [0, 0, 0.5]], [11, 3, 0, -135, [0, "4ePwhjBF9ExpbMe8AvRSqt"], 13]], [1, "5d+QovDXFHPK+fIZk32LbX", null, null, null, 1, 0], [1, -75.265, 0, 0]], [6, "txt_sc1", 33554432, 10, [[7, -136, [0, "c5CrGI2WxJ6YRzhp2VLd1o"], [5, 186.09765625, 56.4], [0, 0, 0.5]], [54, "99/99分鐘", 0, 40, false, true, true, 3, -137, [0, "699OV+noBA4b9EYw4VzZQg"], [4, 4280098330]]], [1, "deU48edudOnZgtx56jzVWc", null, null, null, 1, 0], [1, -56.27138671875, 0.09399999999993724, 0], [1, 0.5, 0.5, 1]], [6, "txt_jia", 33554432, 10, [[7, -138, [0, "04q9xT4blKOq51NCi9SD6b"], [5, 87.087890625, 56.4], [0, 0, 0.5]], [55, "+20", 0, 40, 2, false, true, true, 3, -139, [0, "1fyPwLHTRPYbxyPjkeeD5i"], [4, 4289656106], [4, 4280098330]]], [1, "8eQPDfokNG/7MDiMPxFAwD", null, null, null, 1, 0], [1, 36.77744140625, 0.09399999999993724, 0], [1, 0.5, 0.5, 1]], [6, "txt_sc2", 33554432, 12, [[2, -140, [0, "a0ytpuDZBE/Zc6YN1SX8g0"], [5, 139.4765625, 56.4]], [56, "x10000", 40, false, true, true, 3, -141, [0, "beglsUxx1FVbL7lAC1prRn"], [4, 4290117376], [4, 4280098330]]], [1, "e1MiIT1iJMxq5+Ztj6hqzE", null, null, null, 1, 0], [1, -34.869140625, 0, 0], [1, 0.5, 0.5, 1]], [19, "nongchang_57", 29, [0, "feHYF1+XBPnal6VzQ/Ey9v"]], [3, ["43Z+zpMHNH95/fghPESQqh"]], [3, ["5cvq8qCo5I7pXjdLcFwDsb"]], [19, "nongchang_70", 31, [0, "46XO+tWCNOCLVQyYXpFZI2"]], [3, ["43Z+zpMHNH95/fghPESQqh"]]], 0, [0, -1, 21, 0, -2, 20, 0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 13, 0, 0, 2, 0, 0, 2, 0, -1, 28, 0, -2, 20, 0, -3, 21, 0, -4, 33, 0, -5, 16, 0, -6, 17, 0, -7, 18, 0, -8, 10, 0, -9, 11, 0, 0, 3, 0, 0, 3, 0, -1, 36, 0, -2, 37, 0, -3, 38, 0, -4, 39, 0, -5, 40, 0, 0, 4, 0, 0, 4, 0, 7, 6, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 9, 0, 0, 9, 0, 8, 9, 0, 0, 9, 0, -1, 23, 0, 0, 10, 0, 0, 10, 0, -1, 25, 0, -2, 42, 0, -3, 43, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, -1, 26, 0, -2, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, -1, 27, 0, -2, 44, 0, 0, 13, 0, 0, 13, 0, -1, 19, 0, 0, 16, 0, 0, 16, 0, -1, 22, 0, -2, 35, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, -1, 41, 0, -2, 24, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 2, 46, 0, 2, 30, 0, 2, 46, 0, 2, 47, 0, 2, 30, 0, 2, 30, 0, 2, 47, 0, -1, 45, 0, 4, 20, 0, 2, 49, 0, 2, 32, 0, 2, 49, 0, 2, 32, 0, 2, 32, 0, -1, 48, 0, 4, 21, 0, 0, 22, 0, 0, 22, 0, -1, 34, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, -3, 45, 0, 0, 31, 0, 0, 31, 0, -3, 48, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 9, 1, 2, 3, 6, 3, 3, 17, 4, 3, 13, 9, 3, 17, 141], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 5, 6, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [3, 4, 1, 5, 1, 6, 7, 0, 0, 0, 0, 0, 8, 9, 10, 2, 2]], [[{"name": "img_djs", "rect": {"x": 0, "y": 0, "width": 75, "height": 41}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 75, "height": 41}, "rotated": false, "capInsets": [35, 15, 20, 8], "vertices": {"rawPosition": [-37.5, -20.5, 0, 37.5, -20.5, 0, -37.5, 20.5, 0, 37.5, 20.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 41, 75, 41, 0, 0, 75, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -37.5, "y": -20.5, "z": 0}, "maxPos": {"x": 37.5, "y": 20.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [7], 0, [0], [10], [11]]]]