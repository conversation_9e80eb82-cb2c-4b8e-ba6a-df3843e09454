import{_ as d,B,u as p,E as Y,M as fe,y as k,q as j,x as me,d as se,Q as ge,P as ye,A as ve,r as z,J as be,L as we,e as oe,c as ie}from"./psp-f1fd60fd-CDYNsoiC.js";var K,Z;function J(){return J=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},J.apply(null,arguments)}var Ee=function(e){return d("svg",J({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"currentColor"},e),K||(K=d("path",{fill:"inherit",fillRule:"evenodd",d:"M12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12",clipRule:"evenodd"})),Z||(Z=d("path",{fill:"inherit",fillRule:"evenodd",d:"M17.707 7.793a1 1 0 0 1 0 1.414l-6 6a1 1 0 0 1-1.414 0l-3-3a1 1 0 1 1 1.414-1.414L11 13.086l5.293-5.293a1 1 0 0 1 1.414 0",clipRule:"evenodd"})))};const _e=B((e,t)=>p(Y,{ref:t,IconSvg:Ee,...e}));var X;function F(){return F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},F.apply(null,arguments)}var Re=function(e){return d("svg",F({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"currentColor"},e),X||(X=d("path",{fill:"inherit",fillRule:"evenodd",d:"M11.648 16a1 1 0 0 1-.729-.315l-5.647-6a1 1 0 0 1 1.456-1.37l4.92 5.226 4.919-5.226a1 1 0 0 1 1.456 1.37l-5.647 6a1 1 0 0 1-.728.315",clipRule:"evenodd"})))};const xe=B((e,t)=>p(Y,{ref:t,IconSvg:Re,...e}));var ee,te,ne;function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},W.apply(null,arguments)}var Ne=function(e){return d("svg",W({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"currentColor"},e),ee||(ee=d("path",{fill:"inherit",fillRule:"evenodd",d:"M12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12",clipRule:"evenodd"})),te||(te=d("path",{fill:"inherit",fillRule:"evenodd",d:"M8.293 8.293a1 1 0 0 1 1.414 0l6 6a1 1 0 0 1-1.414 1.414l-6-6a1 1 0 0 1 0-1.414",clipRule:"evenodd"})),ne||(ne=d("path",{fill:"inherit",fillRule:"evenodd",d:"M15.707 8.293a1 1 0 0 1 0 1.414l-6 6a1 1 0 0 1-1.414-1.414l6-6a1 1 0 0 1 1.414 0",clipRule:"evenodd"})))};const Ce=B((e,t)=>p(Y,{ref:t,IconSvg:Ne,...e}));let Pe={data:""},Se=e=>typeof window=="object"?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||Pe,$e=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Ae=/\/\*[^]*?\*\/|  +/g,re=/\n+/g,E=(e,t)=>{let n="",r="",a="";for(let s in e){let o=e[s];s[0]=="@"?s[1]=="i"?n=s+" "+o+";":r+=s[1]=="f"?E(o,s):s+"{"+E(o,s[1]=="k"?"":t)+"}":typeof o=="object"?r+=E(o,t?t.replace(/([^,])+/g,i=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,l=>/&/.test(l)?l.replace(/&/g,i):i?i+" "+l:l)):s):o!=null&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=E.p?E.p(s,o):s+":"+o+";")}return n+(t&&a?t+"{"+a+"}":a)+r},b={},le=e=>{if(typeof e=="object"){let t="";for(let n in e)t+=n+le(e[n]);return t}return e},Ie=(e,t,n,r,a)=>{let s=le(e),o=b[s]||(b[s]=(l=>{let c=0,u=11;for(;c<l.length;)u=101*u+l.charCodeAt(c++)>>>0;return"go"+u})(s));if(!b[o]){let l=s!==e?e:(c=>{let u,f,y=[{}];for(;u=$e.exec(c.replace(Ae,""));)u[4]?y.shift():u[3]?(f=u[3].replace(re," ").trim(),y.unshift(y[0][f]=y[0][f]||{})):y[0][u[1]]=u[2].replace(re," ").trim();return y[0]})(e);b[o]=E(a?{["@keyframes "+o]:l}:l,n?"":"."+o)}let i=n&&b.g?b.g:null;return n&&(b.g=b[o]),((l,c,u,f)=>{f?c.data=c.data.replace(f,l):c.data.indexOf(l)===-1&&(c.data=u?l+c.data:c.data+l)})(b[o],t,r,i),o},Oe=(e,t,n)=>e.reduce((r,a,s)=>{let o=t[s];if(o&&o.call){let i=o(n),l=i&&i.props&&i.props.className||/^go/.test(i)&&i;o=l?"."+l:i&&typeof i=="object"?i.props?"":E(i,""):i===!1?"":i}return r+a+(o??"")},"");function D(e){let t=this||{},n=e.call?e(t.p):e;return Ie(n.unshift?n.raw?Oe(n,[].slice.call(arguments,1),t.p):n.reduce((r,a)=>Object.assign(r,a&&a.call?a(t.p):a),{}):n,Se(t.target),t.g,t.o,t.k)}let ce,Q,G;D.bind({g:1});let w=D.bind({k:1});function Te(e,t,n,r){E.p=t,ce=e,Q=n,G=r}function _(e,t){let n=this||{};return function(){let r=arguments;function a(s,o){let i=Object.assign({},s),l=i.className||a.className;n.p=Object.assign({theme:Q&&Q()},i),n.o=/ *go\d+/.test(l),i.className=D.apply(n,r)+(l?" "+l:"");let c=e;return e[0]&&(c=i.as||e,delete i.as),G&&c[0]&&G(i),ce(c,i)}return t?t(a):a}}var Le=e=>typeof e=="function",M=(e,t)=>Le(e)?e(t):e,ke=(()=>{let e=0;return()=>(++e).toString()})(),de=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),je=20,ue=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,je)};case 1:return{...e,toasts:e.toasts.map(s=>s.id===t.toast.id?{...s,...t.toast}:s)};case 2:let{toast:n}=t;return ue(e,{type:e.toasts.find(s=>s.id===n.id)?1:0,toast:n});case 3:let{toastId:r}=t;return{...e,toasts:e.toasts.map(s=>s.id===r||r===void 0?{...s,dismissed:!0,visible:!1}:s)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(s=>s.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(s=>({...s,pauseDuration:s.pauseDuration+a}))}}},A=[],I={toasts:[],pausedAt:void 0},R=e=>{I=ue(I,e),A.forEach(t=>{t(I)})},Me={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},De=(e={})=>{let[t,n]=se(I);k(()=>(A.push(n),()=>{let a=A.indexOf(n);a>-1&&A.splice(a,1)}),[t]);let r=t.toasts.map(a=>{var s,o,i;return{...e,...e[a.type],...a,removeDelay:a.removeDelay||((s=e[a.type])==null?void 0:s.removeDelay)||e?.removeDelay,duration:a.duration||((o=e[a.type])==null?void 0:o.duration)||e?.duration||Me[a.type],style:{...e.style,...(i=e[a.type])==null?void 0:i.style,...a.style}}});return{...t,toasts:r}},He=(e,t="blank",n)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...n,id:n?.id||ke()}),S=e=>(t,n)=>{let r=He(t,e,n);return R({type:2,toast:r}),r.id},h=(e,t)=>S("blank")(e,t);h.error=S("error");h.success=S("success");h.loading=S("loading");h.custom=S("custom");h.dismiss=e=>{R({type:3,toastId:e})};h.remove=e=>R({type:4,toastId:e});h.promise=(e,t,n)=>{let r=h.loading(t.loading,{...n,...n?.loading});return typeof e=="function"&&(e=e()),e.then(a=>{let s=t.success?M(t.success,a):void 0;return s?h.success(s,{id:r,...n,...n?.success}):h.dismiss(r),a}).catch(a=>{let s=t.error?M(t.error,a):void 0;s?h.error(s,{id:r,...n,...n?.error}):h.dismiss(r)}),e};var Ue=(e,t)=>{R({type:1,toast:{id:e,height:t}})},qe=()=>{R({type:5,time:Date.now()})},P=new Map,Ve=1e3,ze=(e,t=Ve)=>{if(P.has(e))return;let n=setTimeout(()=>{P.delete(e),R({type:4,toastId:e})},t);P.set(e,n)},Je=e=>{let{toasts:t,pausedAt:n}=De(e);k(()=>{if(n)return;let s=Date.now(),o=t.map(i=>{if(i.duration===1/0)return;let l=(i.duration||0)+i.pauseDuration-(s-i.createdAt);if(l<0){i.visible&&h.dismiss(i.id);return}return setTimeout(()=>h.dismiss(i.id),l)});return()=>{o.forEach(i=>i&&clearTimeout(i))}},[t,n]);let r=j(()=>{n&&R({type:6,time:Date.now()})},[n]),a=j((s,o)=>{let{reverseOrder:i=!1,gutter:l=8,defaultPosition:c}=o||{},u=t.filter(v=>(v.position||c)===(s.position||c)&&v.height),f=u.findIndex(v=>v.id===s.id),y=u.filter((v,U)=>U<f&&v.visible).length;return u.filter(v=>v.visible).slice(...i?[y+1]:[0,y]).reduce((v,U)=>v+(U.height||0)+l,0)},[t]);return k(()=>{t.forEach(s=>{if(s.dismissed)ze(s.id,s.removeDelay);else{let o=P.get(s.id);o&&(clearTimeout(o),P.delete(s.id))}})},[t]),{toasts:t,handlers:{updateHeight:Ue,startPause:qe,endPause:r,calculateOffset:a}}},Fe=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,We=w`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Qe=w`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Ge=_("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Fe} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${We} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Qe} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Be=w`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Ye=_("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Be} 1s linear infinite;
`,Ke=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Ze=w`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Xe=_("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Ke} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Ze} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,et=_("div")`
  position: absolute;
`,tt=_("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,nt=w`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,rt=_("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${nt} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,at=({toast:e})=>{let{icon:t,type:n,iconTheme:r}=e;return t!==void 0?typeof t=="string"?d(rt,null,t):t:n==="blank"?null:d(tt,null,d(Ye,{...r}),n!=="loading"&&d(et,null,n==="error"?d(Ge,{...r}):d(Xe,{...r})))},st=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ot=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}
`,it="0%{opacity:0;} 100%{opacity:1;}",lt="0%{opacity:1;} 100%{opacity:0;}",ct=_("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,dt=_("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ut=(e,t)=>{let n=e.includes("top")?1:-1,[r,a]=de()?[it,lt]:[st(n),ot(n)];return{animation:t?`${w(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${w(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},pt=fe(({toast:e,position:t,style:n,children:r})=>{let a=e.height?ut(e.position||t||"top-center",e.visible):{opacity:0},s=d(at,{toast:e}),o=d(dt,{...e.ariaProps},M(e.message,e));return d(ct,{className:e.className,style:{...a,...n,...e.style}},typeof r=="function"?r({icon:s,message:o}):d(me,null,s,o))});Te(d);var ht=({id:e,className:t,style:n,onHeightUpdate:r,children:a})=>{let s=j(o=>{if(o){let i=()=>{let l=o.getBoundingClientRect().height;r(e,l)};i(),new MutationObserver(i).observe(o,{subtree:!0,childList:!0,characterData:!0})}},[e,r]);return d("div",{ref:s,className:t,style:n},a)},ft=(e,t)=>{let n=e.includes("top"),r=n?{top:0}:{bottom:0},a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:de()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(n?1:-1)}px)`,...r,...a}},mt=D`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,$=16,It=({reverseOrder:e,position:t="top-center",toastOptions:n,gutter:r,children:a,containerStyle:s,containerClassName:o})=>{let{toasts:i,handlers:l}=Je(n);return d("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:$,left:$,right:$,bottom:$,pointerEvents:"none",...s},className:o,onMouseEnter:l.startPause,onMouseLeave:l.endPause},i.map(c=>{let u=c.position||t,f=l.calculateOffset(c,{reverseOrder:e,gutter:r,defaultPosition:t}),y=ft(u,f);return d(ht,{id:c.id,key:c.id,onHeightUpdate:l.updateHeight,className:c.visible?mt:"",style:y},c.type==="custom"?M(c.message,c):a?a(c):d(pt,{toast:c,position:u}))}))},g=h;const gt=({message:e,currToast:t,options:n})=>{const{icon:r,extraContent:a,duration:s=2e3,variant:o}=n||{},[i,l]=se(!1),c=ve(null),u=j(f=>{f.stopPropagation(),f.preventDefault(),c.current&&clearTimeout(c.current),i||l(!0)},[i]);return k(()=>(c.current=window.setTimeout(()=>{g.dismiss(t.id)},s),()=>{c.current&&clearTimeout(c.current)}),[t.id,s]),p("div",{className:z("w-fit",{"cursor-default":i,"cursor-pointer":!i}),role:"button",tabIndex:0,onClick:u,children:[p("div",{className:"flex items-center gap-x-2",children:[r,p("span",{className:"flex-1",children:e}),p(be,i?{className:"!p-0",icon:p(we,{className:o==="dark"?"text-font-overlay":"text-brand-tertiary-base dark:text-font-overlay"}),type:"link",onClick:()=>g.dismiss(t.id)}:{className:"!p-0",icon:p(xe,{className:o==="dark"?"text-font-overlay":"text-brand-tertiary-base dark:text-font-overlay"}),type:"link"})]}),p("div",{className:z({"mt-4":i,"invisible h-0":!i}),children:a})]})};function yt(e){return"!py-2.5 !px-1.5 dark:!bg-neutral-7 dark:!text-font-overlay "+(e==="dark"?"bg-neutral-9/88! text-neutral-0!":"!bg-surface-primary !text-font-primary")}const N=({icon:e,message:t,variant:n,extraContent:r,originalOptions:a})=>g(s=>p(gt,{currToast:s,message:t,options:{icon:e,extraContent:r,...a}}),{className:yt(n),...a,duration:Number.POSITIVE_INFINITY});function x(e){return"py-3! !px-1.5 dark:!bg-neutral-7 dark:!text-font-overlay "+(e==="dark"?"bg-neutral-9/88! text-font-overlay!":"!bg-surface-primary !text-font-primary")}const ae={...g,default:(e,t)=>{const{extraContent:n,variant:r,...a}=t||{};return n?N({message:e,extraContent:n,variant:r,originalOptions:t}):g(e,{className:x(r),...a})},success:(e,t)=>{const n=p(_e,{className:"text-success-default"}),{extraContent:r,variant:a,...s}=t||{};return r?N({icon:n,message:e,extraContent:r,variant:a,originalOptions:t}):g.success(e,{icon:n,className:x(a),...s})},error:(e,t)=>{const n=p(Ce,{className:"text-error-default"}),{extraContent:r,variant:a,...s}=t||{};return r?N({icon:n,message:e,extraContent:r,variant:a,originalOptions:t}):g.error(e,{icon:n,className:x(a),...s})},warn:(e,t)=>{const n=p(ye,{className:"text-info-default"}),{extraContent:r,variant:a,...s}=t||{};return r?N({icon:n,message:e,variant:a,extraContent:r,originalOptions:t}):g.error(e,{icon:n,className:x(a),...s})},loading:(e,t)=>{const n=p(ge,{className:"scale-75 text-font-secondary dark:text-font-overlay",containerClassName:"animate-spin"}),{extraContent:r,variant:a,...s}=t||{};return r?N({icon:n,message:e,variant:a,extraContent:r,originalOptions:t}):g.loading(e,{icon:n,className:x(a),...s})},dismiss:e=>{g.dismiss(e)},remove:e=>{g.remove(e)},promise:(e,t,n)=>{const{variant:r,...a}=n||{};return g.promise(e,t,{className:x(r),...a})}};var Ot=Object.assign(ae,{default:ae.default});const H={ChannelRpcRequest:"@channel-rpc/REQUEST",ChannelRpcResponse:"@channel-rpc/RESPONSE"};function vt(e){return e&&e.type===H.ChannelRpcRequest}function bt(e){return e&&e.type===H.ChannelRpcResponse}function wt(e){return e&&e.jsonrpc==="2.0"&&typeof e.method=="string"}function Et(e){return e&&e.jsonrpc==="2.0"&&"result"in e}function _t(e){return e&&e.jsonrpc==="2.0"&&"error"in e}function Rt(){return typeof crypto<"u"&&typeof crypto?.randomUUID=="function"?crypto.randomUUID():new Array(4).fill(0).map(()=>Math.floor(Math.random()*Number.MAX_SAFE_INTEGER).toString(16)).join("-")}let pe=!1;try{pe=!!localStorage.getItem("channel-rpc-debug")}catch{}function m(...e){pe&&console.log(...e)}const he="timeout",O={InvalidRequest:{code:-32600,message:"Invalid Request"},MethodNotFound:{code:-32601,message:"Method not found"},InternalError:{code:-32603,message:"Internal error"},Timeout:{code:-32e3,message:"Timeout"}};function T(e,t){return{jsonrpc:"2.0",error:{code:e.code,message:e.message},id:t}}function xt(e){const t={resolve:n=>{},reject:n=>{},promise:void 0};return t.promise=new Promise((n,r)=>{const a=e?setTimeout(()=>{r(new Error(he))},e):void 0;t.resolve=s=>(clearTimeout(a),n(s)),t.reject=s=>(clearTimeout(a),r(s))}),t}class Tt{constructor(t){this._unlisten=void 0,this._handleMessage=o=>{if(!(!vt(o.data)||o.data.channelId!==this.channelId)){if(this.allowOrigins.length>0&&this.allowOrigins.indexOf(o.origin)===-1)throw new Error(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] Invalid origin: ${o.origin}`);if(!o.source){m(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] event.source is null`,o);return}m(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] RECEIVE_REQUEST`,o.data),this._handleRpcRequest(o.source,o.data.payload)}};const{allowOrigins:n,channelId:r,handler:a}=t;if(!r)throw new Error("id is required");this.channelId=r,this.allowOrigins=n&&n.indexOf("*")===-1?n:[],this._handlers={};const s=a||{};Object.keys(s).forEach(o=>{const i=s[o];typeof i=="function"&&(this._handlers[o]=i.bind(s))})}start(){if(this._unlisten)return;const t=typeof globalThis=="object"?globalThis:window;t.addEventListener("message",this._handleMessage),this._unlisten=()=>{t.removeEventListener("message",this._handleMessage)}}stop(){this._unlisten&&(this._unlisten(),this._unlisten=void 0)}async _sendResponse(t,n){const r={type:H.ChannelRpcResponse,channelId:this.channelId,payload:n};t.postMessage(r,{targetOrigin:"*"})}async _handleRpcRequest(t,n){if(m(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] HANDLE_REQUEST_RPC`,n),!wt(n)){const a=T(O.InvalidRequest,n.id||null);m(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] reply`,a),this._sendResponse(t,a);return}m(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] HANDLE_REQUEST_RPC method[${n.method}]`,this._handlers,n);const r=this._handlers[n.method];if(!r){const a=T(O.MethodNotFound,n.id||null);m(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] SEND_RESPONSE`,a),this._sendResponse(t,a);return}try{const s={jsonrpc:"2.0",result:await r(...n.params||[]),id:n.id};m(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] SEND_RESPONSE`,s),this._sendResponse(t,s)}catch{const s=T(O.InternalError,n.id||null);m(`[CHANNEL_RPC_SERVER][channel=${this.channelId}] SEND_RESPONSE`,s),this._sendResponse(t,s)}}}class Lt{constructor(t){const{target:n,channelId:r,timeout:a}=t;if(!n)throw new Error("target is required");if(!r)throw new Error("channelId is required");this.target=n,this.channelId=r,this._deferreds={},this._timeout=a||1e3,this.stub=new Proxy({},{get:(o,i)=>(...l)=>(m(`[CHANNEL_RPC_CLIENT][channel=${r}] INVOKE`,i,l),this._sendRequest(String(i),l))}),(typeof globalThis=="object"?globalThis:window).addEventListener("message",o=>{!bt(o.data)||o.data.channelId!==r||(m(`[CHANNEL_RPC_CLIENT][channel=${this.channelId}] HANDLE_RESPONSE`,o.data),this._handleRpcResponse(o.data.payload))})}async _sendRequest(t,n){const r=Rt(),a=xt(this._timeout),s=a.promise.then(l=>(delete this._deferreds[r],l)).catch(l=>{throw delete this._deferreds[r],l.message===he?T(O.Timeout,r).error:l});this._deferreds[r]=a;const o={jsonrpc:"2.0",method:t,params:n,id:r};m("[CHANNEL_RPC_CLIENT] SEND_REQUEST",o);const i={type:H.ChannelRpcRequest,channelId:this.channelId,payload:o};return this.target.postMessage(i,"*"),s}_handleRpcResponse(t){if(m("[CHANNEL_RPC_CLIENT] HANDLE_RESPONSE_RPC",t),Et(t)){const{id:n,result:r}=t;this._deferreds[n]?.resolve(r)}else if(_t(t)){const{id:n,error:r}=t;if(!n)throw r;this._deferreds[n]?.reject(r)}else{const n=new Error(`[CHANNEL_RPC_CLIENT][channel=${this.channelId}] UNKNOWN_RESPONSE: ${JSON.stringify(t)}`);throw m("[CHANNEL_RPC_CLIENT] HANDLE_RESPONSE_RPC, ERROR",n),n}}}const kt="psp-sdk-internal-channel",jt="psp-app-internal-channel",q=({color:e,delay:t})=>p("div",{className:"inline-block animate-pulse bg-primary",style:{backgroundColor:e||"#333",margin:"0 3px",width:"18px",height:"18px",borderRadius:"100%",animationDelay:`${t}s`}}),Mt=({color:e="#333",hidden:t=!1})=>p("div",{className:z("relative mx-auto my-0 flex size-full items-center justify-center",{hidden:t}),children:[p(q,{color:e,delay:0}),p(q,{color:e,delay:.14}),p(q,{color:e,delay:.28})]});async function Dt(e){return(await oe.get(`${ie.SHD_G123_PSP_URL}/api/third_party/adyen/paymentMethods`,{params:e})).data}async function Nt(e){return(await oe.post(`${ie.SHD_G123_PSP_URL}/api/third_party/adyen/applePaySession`,{domainName:window.location.hostname},{params:{currency:e.currency}})).data}const Ct=e=>decodeURIComponent(Array.prototype.map.call(window.atob(e),t=>`%${`00${t.charCodeAt(0).toString(16)}`.slice(-2)}`).join("")),Ht=e=>window.btoa(e);function C(e){if(typeof e!="object"||e===null)return JSON.stringify(e);const t={};for(const n in e)t[n]=e[n];return JSON.stringify(t)}const V=14;function Ut(){const e=[];for(let t=V;t>0;t-=1)e.push(t);try{return e.find(t=>t&&window.ApplePaySession&&ApplePaySession.supportsVersion(t))||V}catch(t){return console.warn(t),V}}function qt(e){const t={mc:"masterCard",amex:"amex",visa:"visa",elodebit:"elo",elo:"elo",interac:"interac",discover:"discover",jcb:"jcb",electron:"electron",maestro:"maestro",girocard:"girocard",cartebancaire:"cartesBancaires",eftpos_australia:"eftpos"};return e.reduce((n,r)=>{const a=t[r];return a&&!n.includes(a)?[...n,a]:n},[])}function Vt(){try{if("ApplePaySession"in window&&typeof window.ApplePaySession=="function"&&ApplePaySession.canMakePayments())return!0}catch(e){if(e instanceof DOMException&&e.name==="InvalidAccessError"&&e.message?.indexOf("different security origin")!==-1)return"InvalidAccessError";console.warn(e)}return!1}function zt(e,t,n){const r=new ApplePaySession(n.version,t);return r.onvalidatemerchant=async a=>{console.log(`[ApplePay][${e}][applePayHelper] onvalidatemerchant`,a,C(a));try{const s=await Nt({currency:t.currencyCode});s.data&&(r.completeMerchantValidation(JSON.parse(Ct(s.data))),console.info(`[ApplePay][${e}][applePayHelper] completeMerchantValidation success`))}catch{throw new Error("Merchant validation failed")}},r.onpaymentauthorized=async a=>{console.log(`[ApplePay][${e}][applePayHelper] onpaymentauthorized`,a,C(a));try{await n.onpaymentauthorized(a),r.completePayment(ApplePaySession.STATUS_SUCCESS)}catch(s){console.error(`[ApplePay][${e}][applePayHelper] onpaymentauthorized`,s),r.completePayment(ApplePaySession.STATUS_FAILURE)}},r.oncancel=a=>{console.log(`[ApplePay][${e}][applePayHelper] oncancel`,a,C(a)),n.oncancel(a),console.log(`[ApplePay][${e}][applePayHelper] oncancel abort`,a,C(a))},n.onpaymentmethodselected&&(r.onpaymentmethodselected=a=>{console.log(`[ApplePay][${e}][applePayHelper] onpaymentmethodselected`,a,C(a)),n.onpaymentmethodselected?.(a)}),r}function Jt(e){return typeof e=="object"&&e!==null&&"type"in e&&"event"in e&&typeof e.type=="string"&&e.type.startsWith("applepay:")}const Ft=e=>{window.postMessage(e)},L={};function Wt(e){L.current=e}function Qt(){return{country:L.current?.country||"JP",region:L.current?.region||"JP",lang:L.current?.lang||"ja"}}const Pt=e=>{let t,n=e;return(...r)=>(n&&(t=n(...r),n=void 0),t)},Gt={credituccHandleAction:"psp_app:show_sca_window",adyenHandleAction:"psp_app:adyen_handle_action"};function St(e,t){const n=Math.min(window.screen.width-20,798),r=Math.min(window.screen.height-20,798);return window.open(e,t,`toolbar=no,location=yes,directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,copyhistory=no,width=${n},height=${r},top=${(window.screen.height-r)/2}, left=${(window.screen.width-n)/2}`)}function Bt(e,t,n){const r=document.createElement("form");r.setAttribute("method","post"),r.setAttribute("action",e),r.setAttribute("target",t);const a=Object.keys(n);for(let o=0;o<a.length;o+=1){const i=document.createElement("input");i.type="hidden",i.name=a[o],i.value=n[a[o]],r.appendChild(i)}document.body.appendChild(r);const s=St("",t);return r.target=t,r.submit(),document.body.removeChild(r),s}function $t(e){return typeof e=="object"&&e!==null&&"type"in e&&e.type==="PspCommand"&&"action"in e&&e.action==="PaymentWindowCallback"&&"orderNo"in e&&typeof e.orderNo=="string"&&!!e.orderNo}const Yt=(e=>(t,n)=>{const r=()=>{e.handler&&window.removeEventListener("message",e.handler,!1),e.interval&&clearInterval(e.interval)};r();const a=Pt(()=>{r(),n()});e.handler=s=>{$t(s.data)&&(a(),t?.closed||t?.close())},window.addEventListener("message",e.handler,!1),e.interval=setInterval(()=>{t?.closed&&a()},1e3)})({handler:void 0,interval:void 0});export{Tt as C,It as D,Mt as L,kt as P,Gt as W,Bt as a,C as b,Lt as c,jt as d,Qt as e,Ot as f,zt as g,Dt as h,Vt as i,Ft as j,O as k,Jt as l,qt as m,Ht as n,St as o,Pt as p,xe as q,Ut as r,Wt as s,Yt as w};
