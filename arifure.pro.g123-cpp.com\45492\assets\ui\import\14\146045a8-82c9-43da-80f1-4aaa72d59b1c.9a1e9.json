[1, ["62RaD3pitHHZSEDAEOJUpM", "ec1Y+NM2xCaJ/RdrGerabP@f9941", "d9+eRv9ftMJYX2vX8I9h8Y@f9941", "11mh+wExVCP4gXZcP7NW5p@f9941", "2aJyXiuZ5Clq2H/5uu3BcT"], ["node", "_spriteFrame", "_defaultClip", "_skeletonData", "root", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_parent", "_lpos", "_children", "_lscale"], 1, 9, 4, 1, 5, 2, 5], ["cc.Sprite", ["_type", "_isTrimmedMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Widget", ["_alignFlags", "_bottom", "_left", "_horizontalCenter", "_verticalCenter", "_originalWidth", "_originalHeight", "node", "__prefab"], -4, 1, 4], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Animation", ["playOnLoad", "node", "__prefab", "_clips", "_defaultClip"], 2, 1, 4, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_overflow", "_isBold", "_enableOutline", "_outlineWidth", "node", "__prefab", "_outlineColor"], -5, 1, 4, 5], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Layout", ["node", "__prefab"], 3, 1, 4], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_premultipliedAlpha", "_preCacheMode", "node", "__prefab", "_skeletonData"], -1, 1, 4, 6]], [[7, 0, 2], [3, 0, 1, 2, 1], [4, 0, 1, 2, 3, 4, 5, 5], [0, 0, 1, 4, 6, 2, 3, 5, 3], [0, 0, 1, 4, 2, 3, 5, 7, 3], [2, 1, 7, 8, 2], [6, 0, 2], [0, 0, 1, 6, 2, 3, 3], [0, 0, 4, 6, 2, 3, 5, 2], [0, 0, 1, 4, 2, 3, 5, 3], [3, 0, 1, 2, 3, 1], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 9], [9, 0, 1, 2, 2], [4, 0, 1, 3, 2, 4, 5, 5], [1, 0, 2, 3, 4, 2], [1, 1, 2, 3, 4, 2], [1, 2, 3, 4, 1], [2, 0, 2, 1, 3, 4, 7, 8, 6], [2, 0, 5, 6, 7, 8, 4], [10, 0, 1, 2, 2], [11, 0, 1, 1], [5, 0, 1, 2, 3, 4, 2], [5, 1, 2, 3, 4, 1], [12, 0, 1, 2, 3, 4, 5, 6, 5]], [[6, "xinshou"], [7, "xinshou", 33554432, [-4, -5], [[1, -2, [0, "98uxGpqZhKArK2Ac6EtMoa"], [5, 640, 1280]], [18, 45, 720, 134, -3, [0, "2bkx+/WHxITKU6QCZLhzFu"]]], [13, "74gXne1LNBxbeD40dL+ibu", null, null, [], -1, 0]], [3, "tishi", 33554432, 1, [-10, -11], [[1, -6, [0, "e6YpB0Td9Eo50EHnkuXoCT"], [5, 390, 120]], [20, -7, [0, "c9lYrxSElBpor08H34h86l"]], [5, 505.16200000000003, -8, [0, "8ajiaPJOZDw4rjjwFDWwpD"]], [21, true, -9, [0, "acDlHxX7JNnIQMSWVTLocO"], [3], 4]], [2, "26MoJfP/NI3I/BG6StiGbU", null, null, null, 1, 0], [1, 0, -191.054, 0]], [8, "head1", 2, [-15], [[1, -12, [0, "9el0e1o1FMEryLyfhllg6y"], [5, 106, 85]], [19, 3, -13, [0, "f6VmJ1eqJMhqO4aZcnCK0U"]], [16, -14, [0, "d770aW1e5EoK5IsyfcqRYG"], 2]], [2, "48kZmdkQJJdZusSIqZwcub", null, null, null, 1, 0], [1, -139.236, -13.85, 0]], [9, "img_zhiying1", 33554432, 1, [[10, -16, [0, "74q5JejgxAxbBbl7d4p3P6"], [5, 640, 1280], [0, 0.5, 0.32001724243164065]], [23, "default", "animation1", false, 0, -17, [0, "9d51lgpzpAF5fewtj48tMT"], 5], [5, 1.3449296874999561, -18, [0, "83N5JLRoNJXbnOepKL6QLo"]], [22, -19, [0, "d9xbXdrBhO9IojyU/zvvbm"], [6], 7]], [2, "67Yu/qH4ZAbaQeS7Jo5FSv", null, null, null, 1, 0], [1, 0, -313.838, 0]], [3, "img_xs_di1", 33554432, 2, [-22], [[1, -20, [0, "9bLX40VA1NcZ+1yC59xwFb"], [5, 412, 96]], [14, 1, -21, [0, "697IvACKhJDqONKLSMQrHZ"], 0]], [2, "29mdDXiV1NF54WSGMwI0xJ", null, null, null, 1, 0], [1, 8.796, -15.367500000000001, 0]], [4, "txt_ts_ms1", 33554432, 5, [[1, -23, [0, "e1qNB86CNCEYVr81C7LcI5"], [5, 394.914, 76.56]], [11, "點擊荧幕開始攻擊", 44, 44, 56, 3, true, true, 3, -24, [0, "1ezX1ZceFOkJ4bfjqys7oM"], [4, 4280953386]], [12, "djpm", -25, [0, "1cz+jn6hBKv7f28JdrOIUC"]]], [2, "d9sxKVtfJMW5rTB7GJnESA", null, null, null, 1, 0], [1, 54.554, 1.273, 0], [1, 0.5, 0.5, 1]], [4, "img_xs_rw1", 33554432, 3, [[1, -26, [0, "a5PvT4f69JPp5Ev5LC0BTF"], [5, 315, 317]], [15, false, -27, [0, "b3VJh2FexHfYvqWBPHmp1E"], 1], [17, 18, -16.680999999999994, -76.35999999999999, 19.607, -24.955, -28, [0, "62/U7SNDJGS4+EuLLS3z+f"]]], [2, "aayQnuo8VCyqXivD9T/GNE", null, null, null, 1, 0], [1, 19.607, -24.955, 0], [1, 0.6, 0.6, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 4, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, -2, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 7, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 5, 1, 28], [0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, -1, 2, 3, -1, 2], [1, 2, 3, 0, 0, 4, 0, 0]]