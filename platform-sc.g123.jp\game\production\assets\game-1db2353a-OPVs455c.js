const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/game-09a670c7-CJwm0GIc.js","assets/game-731917b6-ChFGqukb.js","assets/game-19bbaba1-DXHcYjdT.js","assets/game-201d43b2-3D0n_qs2.js","assets/game-d8b296a6-D6-XlEtG.js","assets/game-687bafc7-DM5GjXir.js"])))=>i.map(i=>d[i]);
const P="modulepreload",N=function(t){return window.__dynamic_base__+"/"+t},_={},w=function(e,n,o){let r=Promise.resolve();if(n&&n.length>0){let c=function(a){return Promise.all(a.map(d=>Promise.resolve(d).then(g=>({status:"fulfilled",value:g}),g=>({status:"rejected",reason:g}))))};document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),f=s?.nonce||s?.getAttribute("nonce");r=c(n.map(a=>{if(a=N(a),a in _)return;_[a]=!0;const d=a.endsWith(".css"),g=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${g}`))return;const u=document.createElement("link");if(u.rel=d?"stylesheet":P,d||(u.as="script"),u.crossOrigin="",u.href=a,f&&u.setAttribute("nonce",f),document.head.appendChild(u),d)return new Promise(($,L)=>{u.addEventListener("load",$),u.addEventListener("error",()=>L(new Error(`Unable to preload CSS for ${a}`)))})}))}function i(c){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=c,window.dispatchEvent(s),!s.defaultPrevented)throw c}return r.then(c=>{for(const s of c||[])s.status==="rejected"&&i(s.reason);return e().catch(i)})},B="20250806-76521dd";let p;async function U(){return p||(p=(async()=>{if(!window.option?.datadogLogsOption)return;console.log("🚀 ~ [datadogLogs] initDatadogLogs ~");const{datadogLogs:t}=await w(async()=>{const{datadogLogs:e}=await import("./game-09a670c7-CJwm0GIc.js");return{datadogLogs:e}},__vite__mapDeps([0,1]));return t.init(window.option.datadogLogsOption),t})(),p)}let h;async function m(){return h||(h=(async()=>{if(console.log("🚀 ~ [datadogRum] initDatadogRum ~",window.option.datadogRumOption),!window.option?.datadogRumOption)return;console.log("🚀 ~ [datadogRum] initDatadogRum ~");const{datadogRum:t}=await w(async()=>{const{datadogRum:e}=await import("./game-19bbaba1-DXHcYjdT.js").then(n=>n.z);return{datadogRum:e}},__vite__mapDeps([2,1]));return t.init({...window.option.datadogRumOption,version:B,beforeSend(e,n){return!0}}),window.datadogRum=t,t})(),h)}async function rt(t){const e=await m();console.log("🚀 ~ [datadogRum] datadogRumSetUser ~ ",t,!!e),e?.setUser({id:t,name:t})}const M=()=>{U(),m()};M();const v=window?.option?.runEnv;(v==="staging"||v==="local")&&w(async()=>{const{default:t}=await import("./game-201d43b2-3D0n_qs2.js").then(e=>e.v);return{default:t}},__vite__mapDeps([3,4])).then(({default:t})=>{new t});w(()=>import("./game-687bafc7-DM5GjXir.js").then(t=>t.j),__vite__mapDeps([5,4])).then(t=>{window.$=t.default});window.captureGlobalException=t=>{m().then(e=>{e?.addError(t)})};function E(){if(typeof crypto<"u"&&typeof crypto.randomUUID=="function")return crypto.randomUUID();let t="",e,n;for(e=0;e<32;e+=1)n=Math.random()*16|0,(e===8||e===12||e===16||e===20)&&(t+="-"),t+=(e===12?4:e===16?n&3|8:n).toString(16);return t}const j=t=>{const e=`${t}=`,n=document.cookie.split(";");for(let o=0;o<n.length;o+=1){let r=n[o];for(;r.charAt(0)===" ";)r=r.substring(1);if(r.indexOf(e)===0)return r.substring(e.length,r.length)}},G=(t,e,n,o)=>{const r=new Date;r.setTime(r.getTime()+n*24*60*60*1e3);const i=`expires=${r.toUTCString()}`;document.cookie=`${t}=${e};${i}${o?`;domain=${o}`:""};path=/`};function it(t){document.cookie=`${t}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`}const b=7;function R(t){return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}async function K(t){navigator.hardwareConcurrency&&t.push({key:"cpu_cores",value:navigator.hardwareConcurrency,weight:8,stability:10}),navigator.deviceMemory&&t.push({key:"device_memory",value:navigator.deviceMemory,weight:8,stability:10}),navigator.maxTouchPoints!==void 0&&t.push({key:"touch_points",value:navigator.maxTouchPoints,weight:7,stability:9}),navigator.platform&&t.push({key:"platform",value:navigator.platform,weight:6,stability:8}),"oscpu"in navigator&&t.push({key:"os_cpu",value:navigator.oscpu,weight:5,stability:7})}async function V(t){try{const e=document.createElement("canvas"),n=e.getContext("webgl")||e.getContext("experimental-webgl");if(!n)return;const o=n.getExtension("WEBGL_debug_renderer_info");if(o){const c=n.getParameter(o.UNMASKED_VENDOR_WEBGL);t.push({key:"gpu_vendor",value:c,weight:9,stability:10});const s=n.getParameter(o.UNMASKED_RENDERER_WEBGL);t.push({key:"gpu_renderer",value:s,weight:10,stability:10})}t.push({key:"max_texture_size",value:n.getParameter(n.MAX_TEXTURE_SIZE),weight:6,stability:9}),t.push({key:"max_viewport_dims",value:n.getParameter(n.MAX_VIEWPORT_DIMS).join("x"),weight:6,stability:9}),e.width=50,e.height=20,n.viewport(0,0,50,20),n.clearColor(.15,.3,.45,1),n.clear(n.COLOR_BUFFER_BIT);const r=new Uint8Array(4);n.readPixels(25,10,1,1,n.RGBA,n.UNSIGNED_BYTE,r);const i=`${r[0]}_${r[1]}_${r[2]}`;t.push({key:"webgl_pixel_test",value:i,weight:7,stability:8})}catch{}}function F(t){if(!window.screen)return;const e=`${window.screen.width}x${window.screen.height}`;t.push({key:"screen_resolution",value:e,weight:8,stability:9}),t.push({key:"color_depth",value:window.screen.colorDepth,weight:6,stability:9}),t.push({key:"pixel_ratio",value:window.devicePixelRatio,weight:7,stability:8})}function H(t){try{const o=["audio/mp3","audio/ogg","audio/wav"],r=["video/mp4","video/webm","video/ogg"],i=document.createElement("audio"),c=document.createElement("video"),s=o.filter(a=>i.canPlayType(a)!=="").join("_"),f=r.filter(a=>c.canPlayType(a)!=="").join("_");t.push({key:"media_support",value:`${s}|${f}`,weight:4,stability:7})}catch{}const n=["Intl","WebAssembly","BigInt","SharedArrayBuffer","RTCPeerConnection","IntersectionObserver","ResizeObserver"].filter(o=>o in window).join("_");t.push({key:"supported_apis",value:n,weight:4,stability:7});try{const o=document.createElement("canvas");t.push({key:"canvas_support",value:!!o.getContext("2d"),weight:3,stability:8})}catch{}}function W(t){const e={chrome:"chrome"in window,msCredentials:"msCredentials"in window,MSInputMethodContext:"MSInputMethodContext"in window,InstallTrigger:"InstallTrigger"in window,safari:"safari"in window||"ApplePaySession"in window,Intl:typeof Intl<"u"?Object.keys(Intl).sort().join("_"):""};t.push({key:"browser_apis",value:JSON.stringify(e),weight:8,stability:7})}function X(t){const e=["MozAppearance","WebkitAppearance","msTextSizeAdjust","scrollbarWidth","webkitCoverflowAdjust","webkitLineSnap"],n=document.documentElement,o=window.getComputedStyle(n),r=e.filter(i=>i in o).join("_");t.push({key:"css_features",value:r,weight:7,stability:7})}async function z(t){try{const e=JSON.stringify(t),n=new TextEncoder().encode(e),o=await crypto.subtle.digest("SHA-256",n);return D(o)}catch{return O(JSON.stringify(t))}}async function Y(t){const e=document.createElement("canvas");e.width=100,e.height=40;const n=e.getContext("2d");if(n){n.textBaseline="alphabetic",n.fillStyle="#f60",n.fillRect(10,5,30,30),n.fillStyle="#069",n.font="15px Arial",n.fillText("BrowserID",15,25);const o=n.getImageData(0,0,100,40).data,r=[];for(let i=0;i<o.length;i+=41)r.push(o[i]);t.push({key:"browser_canvas",value:await z(r),weight:8,stability:7})}}async function J(t){W(t),X(t),await Y(t)}async function q(){const t=[];return await Promise.all([K(t),V(t),F(t),H(t),J(t)]),t}function Z(t){return typeof t=="number"?t%1!==0?Number(t.toFixed(1)):t:typeof t=="string"?t.replace(/\s+[\d.]+(?:\.\d+)*(?:\w*)/g,""):t}function y(t){return t===null||typeof t!="object"?String(t):Array.isArray(t)?`[${t.map(o=>y(o)).join(",")}]`:`{${Object.keys(t).sort().map(o=>{const r=t[o];return`"${o}":${y(r)}`}).join(",")}}`}function D(t){const e=new Uint8Array(t);let n="";for(let o=0;o<e.byteLength;o++)n+=String.fromCharCode(e[o]);return R(n)}function O(t){const e=new Uint8Array(32);for(let o=0;o<t.length;o++){const r=t.charCodeAt(o);if(e[o%32]=(e[o%32]+r)%256,o>0){const i=o*7%32;e[i]=(e[i]+r)%256}}for(let o=0;o<3;o++)for(let r=0;r<32;r++){const i=e[(r+31)%32],c=e[(r+1)%32];e[r]=(e[r]+i+c)%256}let n="";for(let o=0;o<e.byteLength;o++)n+=String.fromCharCode(e[o]);return R(n)}async function C(t){t.sort((n,o)=>n.key.localeCompare(o.key));const e=t.map(n=>({k:n.key,v:Z(n.value)}));try{console.info("stableData",e);const n=y(e),o=await crypto.subtle.digest("SHA-256",new TextEncoder().encode(n));return D(o)}catch{return O(y(e))}}async function Q(){const t=await q(),e=t.filter(n=>n.stability>=b);if(e.length<3){const n=t.filter(o=>o.stability>=b-2);return C(n)}return C(e)}const S="gp_dvid",I="gp_dvtm",A=(()=>{switch(window.location.host.replace(/^h5\.|(\.)?g123\.jp$/g,"")){case"local":return{suffix:"_local",domain:".local.g123.jp"};case"stg":return{suffix:"_staging",domain:".stg.g123.jp"};default:return{suffix:"",domain:".g123.jp"}}})(),x=`__gp_dvid${A.suffix}`,tt=A.domain;let l;const et=(()=>{try{const t="gp_test";return localStorage.setItem(t,t),localStorage.removeItem(t),!0}catch{return!1}})();function nt(t,e){if(!e)return!0;const n=Number.parseInt(e,10);return Number.isNaN(n)?!0:t-n>7*24*60*60*1e3}function T(t,e){try{localStorage.setItem(t,e)}catch(n){console.info("[CONTEXT] storageSetItem error",n)}}function k(t){try{return localStorage.getItem(t)}catch(e){return console.info("[CONTEXT] storageGetItem error",e),null}}async function ot(){if(l)return l;const t=j(x),e=k(S);l=t||e||"";const n=Date.now(),o=k(I),r=nt(n,o);if(!l||r)try{const i=await Q();l=i&&et?`df${i}`:`dr${E()}`}catch(i){console.error("[CONTEXT] initDeviceId error",i),l=`dr${E()}`}return G(x,l,365,tt),l&&(e!==l||r)&&(T(S,l),T(I,`${n}`)),l}async function at(){try{l=await ot()}catch(t){console.error("[CONTEXT] initContext error",t)}}export{w as _,E as a,rt as d,j as g,at as i,it as r,G as s};
