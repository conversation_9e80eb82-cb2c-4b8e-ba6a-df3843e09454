<!DOCTYPE html>

<html lang="zh-TW">
  <head
    prefix="og: http://ogp.me/ns# fb: http://ogp.me/ns/fb# website: http://ogp.me/ns/website#"
  >
    <meta
      id="viewport"
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />
    <title>平凡職業造就世界最強 反叛之魂 | 開始遊戲 - G123</title>
    <meta charset="utf-8" />
    
    <meta name="title" content="平凡職業造就世界最強 反叛之魂 | 開始遊戲 - G123" />
    <meta
      name="description"
      content="在這款新作《平凡職業造就世界最強 反叛之魂》中，玩家將扮演南雲始——世界最強的煉成師。隨著劇情推進，南雲將與月、希雅等眾多女主角相遇，並攜手對抗強大的敵人，煉成出威力無窮的神器！"
    />
    <link rel="canonical" href="https://h5.g123.jp/game/arifure?lang=zh-TW" />
    <link
      rel="manifest"
      href="https://h5.g123.jp/api/pwa/arifure-manifest.json?lang=zh-TW"
      crossorigin="use-credentials"
    />
    
    <link
      rel="alternate"
      href="https://h5.g123.jp/game/arifure?lang=en"
      hreflang="en"
    />
    
    <link
      rel="alternate"
      href="https://h5.g123.jp/game/arifure?lang=ja"
      hreflang="ja"
    />
    
    <link
      rel="alternate"
      href="https://h5.g123.jp/game/arifure?lang=ko"
      hreflang="ko"
    />
    
    <link
      rel="alternate"
      href="https://h5.g123.jp/game/arifure?lang=zh-TW"
      hreflang="zh-TW"
    />
    
    <link
      rel="alternate"
      href="https://h5.g123.jp/game/arifure?lang=zh-TW"
      hreflang="x-default"
    />
    
    <meta name="theme-color" content="#136C72" />
    <meta name="screen-orientation" content="portrait" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta
      name="apple-mobile-web-app-title"
      content="平凡職業RS"
    />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="application-name" content="平凡職業RS" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta
      name="twitter:card"
      content="summary_large_image"
    />
    <meta name="twitter:site" content="" />
    <meta
      name="twitter:title"
      content="平凡職業造就世界最強 反叛之魂 | 開始遊戲 - G123"
    />
    <meta name="twitter:url" content="https://h5.g123.jp/game/arifure?lang=zh-TW" />
    <meta
      name="twitter:description"
      content="在這款新作《平凡職業造就世界最強 反叛之魂》中，玩家將扮演南雲始——世界最強的煉成師。隨著劇情推進，南雲將與月、希雅等眾多女主角相遇，並攜手對抗強大的敵人，煉成出威力無窮的神器！"
    />
    <meta name="twitter:image" content="https://platform-sc.g123.jp/h5-g123/game/arifure/zh-TW/ogp-1729820091272.png" />
    <meta
      name="twitter:image:alt"
      content="平凡職業造就世界最強 反叛之魂 | 開始遊戲 - G123"
    />
    <meta property="og:url" content="https://h5.g123.jp/game/arifure?lang=zh-TW" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="平凡職業造就世界最強 反叛之魂 | 開始遊戲 - G123" />
    <meta property="og:site_name" content="G123.jp" />
    <meta
      property="og:description"
      content="在這款新作《平凡職業造就世界最強 反叛之魂》中，玩家將扮演南雲始——世界最強的煉成師。隨著劇情推進，南雲將與月、希雅等眾多女主角相遇，並攜手對抗強大的敵人，煉成出威力無窮的神器！"
    />
    <meta property="og:image" content="https://platform-sc.g123.jp/h5-g123/game/arifure/zh-TW/ogp-1729820091272.png" />
    <link rel="preconnect" href="https://arifure.pro.g123-cpp.com" crossorigin />
    <link rel="preconnect" href="https://psp.g123.jp" crossorigin />
    <link rel="preconnect" href="https://gw-ap-northeast-1-app-windows-public-cluster1.wuying.aliyuncs.com:8008" crossorigin />
    <link rel="shortcut icon" href="https://platform-ik.g123.jp/h5-g123/game/arifure/zh-TW/favicon.png?tr=dpr-1%2Cw-48" />
    <link rel="apple-touch-icon" sizes="180x180" href="https://platform-ik.g123.jp/h5-g123/game/arifure/zh-TW/favicon.png?tr=dpr-1%2Cw-180" />
    <link rel="icon" type="image/png" sizes="32x32" href="https://platform-ik.g123.jp/h5-g123/game/arifure/zh-TW/favicon.png?tr=dpr-1%2Cw-32" />
    <link rel="icon" type="image/png" sizes="16x16" href="https://platform-ik.g123.jp/h5-g123/game/arifure/zh-TW/favicon.png?tr=dpr-1%2Cw-16" />
    
      <script type="application/ld+json">
        {"@context":"https://schema.org","@type":"Organization","name":"CTW.inc","url":"https://g123.jp/","logo":"https://cdn-new.g123.jp/admin/2021/10/1635476153567.png","image":"https://platform-sc.g123.jp/admin/2024/10/1729479074917.png","sameAs":["https://twitter.com/ctw_inc","https://www.facebook.com/insidectw/","https://ctw.inc/"],"contactPoint":{"contactType":"CustomerSupport","url":"https://g123.jp/contact_global","email":"<EMAIL>","areaServed":"JP","availableLanguage":[{"@type":"Language","name":"Japanese","alternateName":"ja"},{"@type":"Language","name":"English","alternateName":"en"},{"@type":"Language","name":"Traditional Chinese","alternateName":"zh-TW"},{"@type":"Language","name":"Korean","alternateName":"ko"}],"address":[{"@type":"PostalAddress","@language":"ja","addressCountry":"JP","addressRegion":"東京都","addressLocality":"港区","postalCode":"106-0032","streetAddress":"六本木1-9-10仙石山森タワー29F"},{"@type":"PostalAddress","@language":"en","addressCountry":"JP","addressRegion":"Tokyo","addressLocality":"Minato-ku","postalCode":"106-0032","streetAddress":"1-9-10 Roppongi, Sengokuyama Mori Tower, 29F"}],"foundingDate":"2013-08-14","founder":{"@type":"Person","name":"Ryuichi Sasaki","jobRole":"CEO"},"description":"可以在手機・PC遊玩，不需註冊會員的全球遊戲平台。只有這裡才玩得到的高品質人氣動畫改編網頁遊戲・線上遊戲任君挑選。"}}
      </script>
    
    
      <script type="application/ld+json">
        {"@context":"http://schema.org","@type":"WebApplication","name":"平凡職業造就世界最強 反叛之魂","applicationCategory":"GameApplication","image":"https://platform-sc.g123.jp/h5-g123/game/arifure/zh-TW/ogp-1729820091272.png","operatingSystem":"Windows, macOS, Linux, Android, iOS","browserRequirements":"Google Chrome, Mozilla Firefox, Safari","url":"https://h5.g123.jp/game/arifure?lang=zh-TW","genre":"Browser Game","offers":{"@type":"Offer","price":"0","description":"Free (in-game items are purchased)"},"description":"在這款新作《平凡職業造就世界最強 反叛之魂》中，玩家將扮演南雲始——世界最強的煉成師。隨著劇情推進，南雲將與月、希雅等眾多女主角相遇，並攜手對抗強大的敵人，煉成出威力無窮的神器！","isAccessibleForFree":true,"screenshot":[{"url":"https://platform-sc.g123.jp/admin/2024/10/1729820672281.jpg","alt":""},{"url":"https://platform-sc.g123.jp/admin/2024/10/1729820667285.jpg","alt":""},{"url":"https://platform-sc.g123.jp/admin/2024/10/1729820663389.jpg","alt":""},{"url":"https://platform-sc.g123.jp/admin/2024/10/1729820659509.jpg","alt":""},{"url":"https://platform-sc.g123.jp/admin/2024/10/1729820655142.jpg","alt":""}],"publisher":{"@type":"Organization","name":"CTW Inc.","sameAs":"https://g123.jp"},"author":{"@type":"Organization","name":"CTW Inc."},"mainEntityOfPage":{"@type":"WebPage","@id":"https://h5.g123.jp/game/arifure?lang=zh-TW"},"datePublished":"2025-01-20T01:00:00.000Z"}
      </script>
    
    <script>
      window.__dynamic_base__ = 'https://platform-sc.g123.jp/game/production';
      window.__game_process_env__ = JSON.parse("{\"NODE_ENV\":\"production\",\"CLI_CSP_REPORT_URI\":\"https://sentry.io/api/1474275/security/?sentry_key=f2ffa43050e840bf92dac8578514eff3\",\"SHD_G123_MA_AUXIN\":\"https://platform-sc.g123.jp/micro-app/auxin/index.html\",\"SHD_G123_CUSTOMER_SVC\":\"https://cs-chatbox.g123.jp\",\"SHD_G123_PSP_URL\":\"https://psp.g123.jp\",\"SHD_G123_GAME_URL\":\"https://h5.g123.jp\",\"SHD_G123_WEB_URL\":\"https://g123.jp\",\"SHD_G123_AUXIN_URL\":\"https://auxin.g123.jp\",\"SHD_G123_GC3A_URL\":\" https://gc3a.g123.jp\",\"SHD_G123_GAME_CDN_URL\":\"https://platform-sc.g123.jp/game/production\",\"SHD_G123_PLATFORM_CDN_URL\":\"https://platform-sc.g123.jp\",\"SHD_G123_PSP_SDK_URL\":\"https://psp.g123.jp/static/psp_sdk.js\",\"SHD_GTM_ID\":\"GTM-PC66Z3W\",\"SHD_G123_DATA_ENDPOINT\":\"https://api.g123.jp\",\"SHD_G123_WEB_SOCKET_ENDPOINT\":\"wss://courier-comet.g123.jp/ws\",\"SHD_PARTNER_TTD_PID\":\"d7n982t\",\"SHD_PARTNER_TTD_ENDPOINT\":\"https://match.adsrvr.org/track/cmf/generic\",\"SHD_DD_RUM_APPLICATION_ID\":\"145024c1-4855-4f8e-ae01-f37a7b7f0f82\",\"SHD_DD_RUM_CLIENT_TOKEN\":\"pub5fdf46d7fe24757843b7fa01f1254abd\"}");
      window.option = JSON.parse("{\"__pspLazyInit\":true,\"mode\":\"game\",\"sdkMode\":\"0\",\"lang\":\"zh-TW\",\"runEnv\":\"production\",\"idCallback\":\"https://arifure.pro.g123-cpp.com/45492/index.html\",\"appVersion\":\"20250806-76521dd\",\"appId\":\"arifure\",\"stage\":\"PUBLISHED\",\"appTitle\":\"平凡職業造就世界最強 反叛之魂\",\"expflags\":{\"disableGtmMessageListener\":true},\"datadogRumOption\":{\"applicationId\":\"145024c1-4855-4f8e-ae01-f37a7b7f0f82\",\"clientToken\":\"pub5fdf46d7fe24757843b7fa01f1254abd\",\"site\":\"datadoghq.com\",\"service\":\"g123-game\",\"env\":\"production\",\"version\":\"20250806-76521dd\",\"sessionSampleRate\":0.01,\"sessionReplaySampleRate\":0,\"startSessionReplayRecordingManually\":true,\"defaultPrivacyLevel\":\"mask-user-input\",\"traceContextInjection\":\"sampled\"}}");
      try { document.cookie = 'gp_game_ver=20250806-76521dd;path=/'; } catch (e) {}
    </script>
    <!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl+ '&gtm_auth=WBqlAWBzcGUtd2GJM6EsTg&gtm_preview=env-2&gtm_cookies_win=x';f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-PC66Z3W');</script>
<!-- End Google Tag Manager -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-J4ZXKKX9VQ&l=dataLayer"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      window.gtag = gtag;
      gtag('js', new Date());
      gtag('config', 'G-J4ZXKKX9VQ', {
        'content_group': window.location.pathname
      });
    </script>
    <script>
      "use strict";(()=>{var Lr=Object.create;var Ve=Object.defineProperty,Or=Object.defineProperties,Mr=Object.getOwnPropertyDescriptor,Dr=Object.getOwnPropertyDescriptors,Nr=Object.getOwnPropertyNames,mt=Object.getOwnPropertySymbols,Vr=Object.getPrototypeOf,wt=Object.prototype.hasOwnProperty,Fr=Object.prototype.propertyIsEnumerable;var ht=(e,t,r)=>t in e?Ve(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Fe=(e,t)=>{for(var r in t||(t={}))wt.call(t,r)&&ht(e,r,t[r]);if(mt)for(var r of mt(t))Fr.call(t,r)&&ht(e,r,t[r]);return e},bt=(e,t)=>Or(e,Dr(t));var V=(e,t)=>()=>(e&&(t=e(e=0)),t);var J=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Gr=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Nr(t))!wt.call(e,o)&&o!==r&&Ve(e,o,{get:()=>t[o],enumerable:!(n=Mr(t,o))||n.enumerable});return e};var _e=(e,t,r)=>(r=e!=null?Lr(Vr(e)):{},Gr(t||!e||!e.__esModule?Ve(r,"default",{value:e,enumerable:!0}):r,e));var R=(e,t,r)=>new Promise((n,o)=>{var i=m=>{try{f(r.next(m))}catch(p){o(p)}},s=m=>{try{f(r.throw(m))}catch(p){o(p)}},f=m=>m.done?n(m.value):Promise.resolve(m.value).then(i,s);f((r=r.apply(e,t)).next())});function H(e,t,r,n){function o(i){return i instanceof r?i:new r(function(s){s(i)})}return new(r||(r=Promise))(function(i,s){function f(a){try{p(n.next(a))}catch(u){s(u)}}function m(a){try{p(n.throw(a))}catch(u){s(u)}}function p(a){a.done?i(a.value):o(a.value).then(f,m)}p((n=n.apply(e,t||[])).next())})}function B(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,s=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return s.next=f(0),s.throw=f(1),s.return=f(2),typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function f(p){return function(a){return m([p,a])}}function m(p){if(n)throw new TypeError("Generator is already executing.");for(;s&&(s=0,p[0]&&(r=0)),r;)try{if(n=1,o&&(i=p[0]&2?o.return:p[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,p[1])).done)return i;switch(o=0,i&&(p=[p[0]&2,i.value]),p[0]){case 0:case 1:i=p;break;case 4:return r.label++,{value:p[1],done:!1};case 5:r.label++,o=p[1],p=[0];continue;case 7:p=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(p[0]===6||p[0]===2)){r=0;continue}if(p[0]===3&&(!i||p[1]>i[0]&&p[1]<i[3])){r.label=p[1];break}if(p[0]===6&&r.label<i[1]){r.label=i[1],i=p;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(p);break}i[2]&&r.ops.pop(),r.trys.pop();continue}p=t.call(e,r)}catch(a){p=[6,a],o=0}finally{n=i=0}if(p[0]&5)throw p[1];return{value:p[0]?p[1]:void 0,done:!0}}}function Ge(e,t,r){if(r||arguments.length===2)for(var n=0,o=t.length,i;n<o;n++)(i||!(n in t))&&(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))}var xe,vt=V(()=>{xe=function(){return xe=Object.assign||function(t){for(var r,n=1,o=arguments.length;n<o;n++){r=arguments[n];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t},xe.apply(this,arguments)}});function ue(e,t){return new Promise(function(r){return setTimeout(r,e,t)})}function jr(e,t){t===void 0&&(t=1/0);var r=window.requestIdleCallback;return r?new Promise(function(n){return r.call(window,function(){return n()},{timeout:t})}):ue(Math.min(e,t))}function Ct(e){return!!e&&typeof e.then=="function"}function gt(e,t){try{var r=e();Ct(r)?r.then(function(n){return t(!0,n)},function(n){return t(!1,n)}):t(!0,r)}catch(n){t(!1,n)}}function yt(e,t,r){return r===void 0&&(r=16),H(this,void 0,void 0,function(){var n,o,i,s;return B(this,function(f){switch(f.label){case 0:n=Array(e.length),o=Date.now(),i=0,f.label=1;case 1:return i<e.length?(n[i]=t(e[i],i),s=Date.now(),s>=o+r?(o=s,[4,ue(0)]):[3,3]):[3,4];case 2:f.sent(),f.label=3;case 3:return++i,[3,1];case 4:return[2,n]}})})}function le(e){e.then(void 0,function(){})}function X(e,t){e=[e[0]>>>16,e[0]&65535,e[1]>>>16,e[1]&65535],t=[t[0]>>>16,t[0]&65535,t[1]>>>16,t[1]&65535];var r=[0,0,0,0];return r[3]+=e[3]+t[3],r[2]+=r[3]>>>16,r[3]&=65535,r[2]+=e[2]+t[2],r[1]+=r[2]>>>16,r[2]&=65535,r[1]+=e[1]+t[1],r[0]+=r[1]>>>16,r[1]&=65535,r[0]+=e[0]+t[0],r[0]&=65535,[r[0]<<16|r[1],r[2]<<16|r[3]]}function M(e,t){e=[e[0]>>>16,e[0]&65535,e[1]>>>16,e[1]&65535],t=[t[0]>>>16,t[0]&65535,t[1]>>>16,t[1]&65535];var r=[0,0,0,0];return r[3]+=e[3]*t[3],r[2]+=r[3]>>>16,r[3]&=65535,r[2]+=e[2]*t[3],r[1]+=r[2]>>>16,r[2]&=65535,r[2]+=e[3]*t[2],r[1]+=r[2]>>>16,r[2]&=65535,r[1]+=e[1]*t[3],r[0]+=r[1]>>>16,r[1]&=65535,r[1]+=e[2]*t[2],r[0]+=r[1]>>>16,r[1]&=65535,r[1]+=e[3]*t[1],r[0]+=r[1]>>>16,r[1]&=65535,r[0]+=e[0]*t[3]+e[1]*t[2]+e[2]*t[1]+e[3]*t[0],r[0]&=65535,[r[0]<<16|r[1],r[2]<<16|r[3]]}function te(e,t){return t%=64,t===32?[e[1],e[0]]:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t|e[0]>>>32-t]:(t-=32,[e[1]<<t|e[0]>>>32-t,e[0]<<t|e[1]>>>32-t])}function O(e,t){return t%=64,t===0?e:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t]:[e[1]<<t-32,0]}function E(e,t){return[e[0]^t[0],e[1]^t[1]]}function St(e){return e=E(e,[0,e[0]>>>1]),e=M(e,[4283543511,3981806797]),e=E(e,[0,e[0]>>>1]),e=M(e,[3301882366,444984403]),e=E(e,[0,e[0]>>>1]),e}function Wr(e,t){e=e||"",t=t||0;var r=e.length%16,n=e.length-r,o=[0,t],i=[0,t],s=[0,0],f=[0,0],m=[2277735313,289559509],p=[1291169091,658871167],a;for(a=0;a<n;a=a+16)s=[e.charCodeAt(a+4)&255|(e.charCodeAt(a+5)&255)<<8|(e.charCodeAt(a+6)&255)<<16|(e.charCodeAt(a+7)&255)<<24,e.charCodeAt(a)&255|(e.charCodeAt(a+1)&255)<<8|(e.charCodeAt(a+2)&255)<<16|(e.charCodeAt(a+3)&255)<<24],f=[e.charCodeAt(a+12)&255|(e.charCodeAt(a+13)&255)<<8|(e.charCodeAt(a+14)&255)<<16|(e.charCodeAt(a+15)&255)<<24,e.charCodeAt(a+8)&255|(e.charCodeAt(a+9)&255)<<8|(e.charCodeAt(a+10)&255)<<16|(e.charCodeAt(a+11)&255)<<24],s=M(s,m),s=te(s,31),s=M(s,p),o=E(o,s),o=te(o,27),o=X(o,i),o=X(M(o,[0,5]),[0,1390208809]),f=M(f,p),f=te(f,33),f=M(f,m),i=E(i,f),i=te(i,31),i=X(i,o),i=X(M(i,[0,5]),[0,944331445]);switch(s=[0,0],f=[0,0],r){case 15:f=E(f,O([0,e.charCodeAt(a+14)],48));case 14:f=E(f,O([0,e.charCodeAt(a+13)],40));case 13:f=E(f,O([0,e.charCodeAt(a+12)],32));case 12:f=E(f,O([0,e.charCodeAt(a+11)],24));case 11:f=E(f,O([0,e.charCodeAt(a+10)],16));case 10:f=E(f,O([0,e.charCodeAt(a+9)],8));case 9:f=E(f,[0,e.charCodeAt(a+8)]),f=M(f,p),f=te(f,33),f=M(f,m),i=E(i,f);case 8:s=E(s,O([0,e.charCodeAt(a+7)],56));case 7:s=E(s,O([0,e.charCodeAt(a+6)],48));case 6:s=E(s,O([0,e.charCodeAt(a+5)],40));case 5:s=E(s,O([0,e.charCodeAt(a+4)],32));case 4:s=E(s,O([0,e.charCodeAt(a+3)],24));case 3:s=E(s,O([0,e.charCodeAt(a+2)],16));case 2:s=E(s,O([0,e.charCodeAt(a+1)],8));case 1:s=E(s,[0,e.charCodeAt(a)]),s=M(s,m),s=te(s,31),s=M(s,p),o=E(o,s)}return o=E(o,[0,e.length]),i=E(i,[0,e.length]),o=X(o,i),i=X(i,o),o=St(o),i=St(i),o=X(o,i),i=X(i,o),("00000000"+(o[0]>>>0).toString(16)).slice(-8)+("00000000"+(o[1]>>>0).toString(16)).slice(-8)+("00000000"+(i[0]>>>0).toString(16)).slice(-8)+("00000000"+(i[1]>>>0).toString(16)).slice(-8)}function Hr(e){var t;return xe({name:e.name,message:e.message,stack:(t=e.stack)===null||t===void 0?void 0:t.split(`
`)},e)}function Br(e,t){for(var r=0,n=e.length;r<n;++r)if(e[r]===t)return!0;return!1}function Zr(e,t){return!Br(e,t)}function Ye(e){return parseInt(e)}function F(e){return parseFloat(e)}function z(e,t){return typeof e=="number"&&isNaN(e)?t:e}function G(e){return e.reduce(function(t,r){return t+(r?1:0)},0)}function Lt(e,t){if(t===void 0&&(t=1),Math.abs(t)>=1)return Math.round(e/t)*t;var r=1/t;return Math.round(e*r)/r}function Yr(e){for(var t,r,n="Unexpected syntax '".concat(e,"'"),o=/^\s*([a-z-]*)(.*)$/i.exec(e),i=o[1]||void 0,s={},f=/([.:#][\w-]+|\[.+?\])/gi,m=function(c,l){s[c]=s[c]||[],s[c].push(l)};;){var p=f.exec(o[2]);if(!p)break;var a=p[0];switch(a[0]){case".":m("class",a.slice(1));break;case"#":m("id",a.slice(1));break;case"[":{var u=/^\[([\w-]+)([~|^$*]?=("(.*?)"|([\w-]+)))?(\s+[is])?\]$/.exec(a);if(u)m(u[1],(r=(t=u[4])!==null&&t!==void 0?t:u[5])!==null&&r!==void 0?r:"");else throw new Error(n);break}default:throw new Error(n)}}return[i,s]}function _t(e){return e&&typeof e=="object"&&"message"in e?e:{message:e}}function zr(e){return typeof e!="function"}function Xr(e,t){var r=new Promise(function(n){var o=Date.now();gt(e.bind(null,t),function(){for(var i=[],s=0;s<arguments.length;s++)i[s]=arguments[s];var f=Date.now()-o;if(!i[0])return n(function(){return{error:_t(i[1]),duration:f}});var m=i[1];if(zr(m))return n(function(){return{value:m,duration:f}});n(function(){return new Promise(function(p){var a=Date.now();gt(m,function(){for(var u=[],c=0;c<arguments.length;c++)u[c]=arguments[c];var l=f+Date.now()-a;if(!u[0])return p({error:_t(u[1]),duration:l});p({value:u[1],duration:l})})})})})});return le(r),function(){return r.then(function(o){return o()})}}function Ur(e,t,r){var n=Object.keys(e).filter(function(i){return Zr(r,i)}),o=yt(n,function(i){return Xr(e[i],t)});return le(o),function(){return H(this,void 0,void 0,function(){var s,f,m,p,a;return B(this,function(u){switch(u.label){case 0:return[4,o];case 1:return s=u.sent(),[4,yt(s,function(c){var l=c();return le(l),l})];case 2:return f=u.sent(),[4,Promise.all(f)];case 3:for(m=u.sent(),p={},a=0;a<n.length;++a)p[n[a]]=m[a];return[2,p]}})})}}function Ot(){var e=window,t=navigator;return G(["MSCSSMatrix"in e,"msSetImmediate"in e,"msIndexedDB"in e,"msMaxTouchPoints"in t,"msPointerEnabled"in t])>=4}function Jr(){var e=window,t=navigator;return G(["msWriteProfilerMark"in e,"MSStream"in e,"msLaunchUri"in t,"msSaveBlob"in t])>=3&&!Ot()}function ze(){var e=window,t=navigator;return G(["webkitPersistentStorage"in t,"webkitTemporaryStorage"in t,t.vendor.indexOf("Google")===0,"webkitResolveLocalFileSystemURL"in e,"BatteryManager"in e,"webkitMediaStream"in e,"webkitSpeechGrammar"in e])>=5}function fe(){var e=window,t=navigator;return G(["ApplePayError"in e,"CSSPrimitiveValue"in e,"Counter"in e,t.vendor.indexOf("Apple")===0,"getStorageUpdates"in t,"WebKitMediaKeys"in e])>=4}function Xe(){var e=window;return G(["safari"in e,!("DeviceMotionEvent"in e),!("ongestureend"in e),!("standalone"in navigator)])>=3}function $r(){var e,t,r=window;return G(["buildID"in navigator,"MozAppearance"in((t=(e=document.documentElement)===null||e===void 0?void 0:e.style)!==null&&t!==void 0?t:{}),"onmozfullscreenchange"in r,"mozInnerScreenX"in r,"CSSMozDocumentRule"in r,"CanvasCaptureMediaStream"in r])>=4}function qr(){var e=window;return G([!("MediaSettingsRange"in e),"RTCEncodedAudioFrame"in e,""+e.Intl=="[object Intl]",""+e.Reflect=="[object Reflect]"])>=3}function Kr(){var e=window;return G(["DOMRectList"in e,"RTCPeerConnectionIceEvent"in e,"SVGGeometryElement"in e,"ontransitioncancel"in e])>=3}function Qr(){if(navigator.platform==="iPad")return!0;var e=screen,t=e.width/e.height;return G(["MediaSource"in window,!!Element.prototype.webkitRequestFullscreen,t>.65&&t<1.53])>=2}function en(){var e=document;return e.fullscreenElement||e.msFullscreenElement||e.mozFullScreenElement||e.webkitFullscreenElement||null}function tn(){var e=document;return(e.exitFullscreen||e.msExitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen).call(e)}function Mt(){var e=ze(),t=$r();if(!e&&!t)return!1;var r=window;return G(["onorientationchange"in r,"orientation"in r,e&&!("SharedWorker"in r),t&&/android/i.test(navigator.appVersion)])>=2}function rn(){var e=window,t=e.OfflineAudioContext||e.webkitOfflineAudioContext;if(!t)return-2;if(nn())return-1;var r=4500,n=5e3,o=new t(1,n,44100),i=o.createOscillator();i.type="triangle",i.frequency.value=1e4;var s=o.createDynamicsCompressor();s.threshold.value=-50,s.knee.value=40,s.ratio.value=12,s.attack.value=0,s.release.value=.25,i.connect(s),s.connect(o.destination),i.start(0);var f=on(o),m=f[0],p=f[1],a=m.then(function(u){return an(u.getChannelData(0).subarray(r))},function(u){if(u.name==="timeout"||u.name==="suspended")return-3;throw u});return le(a),function(){return p(),a}}function nn(){return fe()&&!Xe()&&!Kr()}function on(e){var t=3,r=500,n=500,o=5e3,i=function(){},s=new Promise(function(f,m){var p=!1,a=0,u=0;e.oncomplete=function(d){return f(d.renderedBuffer)};var c=function(){setTimeout(function(){return m(xt("timeout"))},Math.min(n,u+o-Date.now()))},l=function(){try{var d=e.startRendering();switch(Ct(d)&&le(d),e.state){case"running":u=Date.now(),p&&c();break;case"suspended":document.hidden||a++,p&&a>=t?m(xt("suspended")):setTimeout(l,r);break}}catch(y){m(y)}};l(),i=function(){p||(p=!0,u>0&&c())}});return[s,i]}function an(e){for(var t=0,r=0;r<e.length;++r)t+=Math.abs(e[r]);return t}function xt(e){var t=new Error(e);return t.name=e,t}function Dt(e,t,r){var n,o,i;return r===void 0&&(r=50),H(this,void 0,void 0,function(){var s,f;return B(this,function(m){switch(m.label){case 0:s=document,m.label=1;case 1:return s.body?[3,3]:[4,ue(r)];case 2:return m.sent(),[3,1];case 3:f=s.createElement("iframe"),m.label=4;case 4:return m.trys.push([4,,10,11]),[4,new Promise(function(p,a){var u=!1,c=function(){u=!0,p()},l=function(T){u=!0,a(T)};f.onload=c,f.onerror=l;var d=f.style;d.setProperty("display","block","important"),d.position="absolute",d.top="0",d.left="0",d.visibility="hidden",t&&"srcdoc"in f?f.srcdoc=t:f.src="about:blank",s.body.appendChild(f);var y=function(){var T,h;u||(((h=(T=f.contentWindow)===null||T===void 0?void 0:T.document)===null||h===void 0?void 0:h.readyState)==="complete"?c():setTimeout(y,10))};y()})];case 5:m.sent(),m.label=6;case 6:return!((o=(n=f.contentWindow)===null||n===void 0?void 0:n.document)===null||o===void 0)&&o.body?[3,8]:[4,ue(r)];case 7:return m.sent(),[3,6];case 8:return[4,e(f,f.contentWindow)];case 9:return[2,m.sent()];case 10:return(i=f.parentNode)===null||i===void 0||i.removeChild(f),[7];case 11:return[2]}})})}function sn(e){for(var t=Yr(e),r=t[0],n=t[1],o=document.createElement(r!=null?r:"div"),i=0,s=Object.keys(n);i<s.length;i++){var f=s[i],m=n[f].join(" ");f==="style"?cn(o.style,m):o.setAttribute(f,m)}return o}function cn(e,t){for(var r=0,n=t.split(";");r<n.length;r++){var o=n[r],i=/^\s*([\w-]+)\s*:\s*(.+?)(\s*!([\w-]+))?\s*$/.exec(o);if(i){var s=i[1],f=i[2],m=i[4];e.setProperty(s,f,m||"")}}}function fn(){return Dt(function(e,t){var r=t.document,n=r.body;n.style.fontSize=ln;var o=r.createElement("div"),i={},s={},f=function(y){var T=r.createElement("span"),h=T.style;return h.position="absolute",h.top="0",h.left="0",h.fontFamily=y,T.textContent=un,o.appendChild(T),T},m=function(y,T){return f("'".concat(y,"',").concat(T))},p=function(){return re.map(f)},a=function(){for(var y={},T=function(C){y[C]=re.map(function(U){return m(C,U)})},h=0,w=At;h<w.length;h++){var P=w[h];T(P)}return y},u=function(y){return re.some(function(T,h){return y[h].offsetWidth!==i[T]||y[h].offsetHeight!==s[T]})},c=p(),l=a();n.appendChild(o);for(var d=0;d<re.length;d++)i[re[d]]=c[d].offsetWidth,s[re[d]]=c[d].offsetHeight;return At.filter(function(y){return u(l[y])})})}function dn(){var e=navigator.plugins;if(e){for(var t=[],r=0;r<e.length;++r){var n=e[r];if(n){for(var o=[],i=0;i<n.length;++i){var s=n[i];o.push({type:s.type,suffixes:s.suffixes})}t.push({name:n.name,description:n.description,mimeTypes:o})}}return t}}function pn(){var e=!1,t,r,n=mn(),o=n[0],i=n[1];if(!hn(o,i))t=r="";else{e=wn(i),bn(o,i);var s=je(o),f=je(o);s!==f?t=r="unstable":(r=s,vn(o,i),t=je(o))}return{winding:e,geometry:t,text:r}}function mn(){var e=document.createElement("canvas");return e.width=1,e.height=1,[e,e.getContext("2d")]}function hn(e,t){return!!(t&&e.toDataURL)}function wn(e){return e.rect(0,0,10,10),e.rect(2,2,6,6),!e.isPointInPath(5,5,"evenodd")}function bn(e,t){e.width=240,e.height=60,t.textBaseline="alphabetic",t.fillStyle="#f60",t.fillRect(100,1,62,20),t.fillStyle="#069",t.font='11pt "Times New Roman"';var r="Cwm fjordbank gly ".concat("\u{1F603}");t.fillText(r,2,15),t.fillStyle="rgba(102, 204, 0, 0.2)",t.font="18pt Arial",t.fillText(r,4,45)}function vn(e,t){e.width=122,e.height=110,t.globalCompositeOperation="multiply";for(var r=0,n=[["#f2f",40,40],["#2ff",80,40],["#ff2",60,80]];r<n.length;r++){var o=n[r],i=o[0],s=o[1],f=o[2];t.fillStyle=i,t.beginPath(),t.arc(s,f,40,0,Math.PI*2,!0),t.closePath(),t.fill()}t.fillStyle="#f9c",t.arc(60,60,60,0,Math.PI*2,!0),t.arc(60,60,20,0,Math.PI*2,!0),t.fill("evenodd")}function je(e){return e.toDataURL()}function gn(){var e=navigator,t=0,r;e.maxTouchPoints!==void 0?t=Ye(e.maxTouchPoints):e.msMaxTouchPoints!==void 0&&(t=e.msMaxTouchPoints);try{document.createEvent("TouchEvent"),r=!0}catch(o){r=!1}var n="ontouchstart"in window;return{maxTouchPoints:t,touchEvent:r,touchStart:n}}function yn(){return navigator.oscpu}function Sn(){var e=navigator,t=[],r=e.language||e.userLanguage||e.browserLanguage||e.systemLanguage;if(r!==void 0&&t.push([r]),Array.isArray(e.languages))ze()&&qr()||t.push(e.languages);else if(typeof e.languages=="string"){var n=e.languages;n&&t.push(n.split(","))}return t}function _n(){return window.screen.colorDepth}function xn(){return z(F(navigator.deviceMemory),void 0)}function An(){var e=screen,t=function(n){return z(Ye(n),null)},r=[t(e.width),t(e.height)];return r.sort().reverse(),r}function En(){if(We===void 0){var e=function(){var t=Be();Ze(t)?We=setTimeout(e,Tn):(Ae=t,We=void 0)};e()}}function Rn(){var e=this;return En(),function(){return H(e,void 0,void 0,function(){var t;return B(this,function(r){switch(r.label){case 0:return t=Be(),Ze(t)?Ae?[2,Ge([],Ae,!0)]:en()?[4,tn()]:[3,2]:[3,2];case 1:r.sent(),t=Be(),r.label=2;case 2:return Ze(t)||(Ae=t),[2,t]}})})}}function Pn(){var e=this,t=Rn();return function(){return H(e,void 0,void 0,function(){var r,n;return B(this,function(o){switch(o.label){case 0:return[4,t()];case 1:return r=o.sent(),n=function(i){return i===null?null:Lt(i,kn)},[2,[n(r[0]),n(r[1]),n(r[2]),n(r[3])]]}})})}}function Be(){var e=screen;return[z(F(e.availTop),null),z(F(e.width)-F(e.availWidth)-z(F(e.availLeft),0),null),z(F(e.height)-F(e.availHeight)-z(F(e.availTop),0),null),z(F(e.availLeft),null)]}function Ze(e){for(var t=0;t<4;++t)if(e[t])return!1;return!0}function In(){return z(Ye(navigator.hardwareConcurrency),void 0)}function Cn(){var e,t=(e=window.Intl)===null||e===void 0?void 0:e.DateTimeFormat;if(t){var r=new t().resolvedOptions().timeZone;if(r)return r}var n=-Ln();return"UTC".concat(n>=0?"+":"").concat(Math.abs(n))}function Ln(){var e=new Date().getFullYear();return Math.max(F(new Date(e,0,1).getTimezoneOffset()),F(new Date(e,6,1).getTimezoneOffset()))}function On(){try{return!!window.sessionStorage}catch(e){return!0}}function Mn(){try{return!!window.localStorage}catch(e){return!0}}function Dn(){if(!(Ot()||Jr()))try{return!!window.indexedDB}catch(e){return!0}}function Nn(){return!!window.openDatabase}function Vn(){return navigator.cpuClass}function Fn(){var e=navigator.platform;return e==="MacIntel"&&fe()&&!Xe()?Qr()?"iPad":"iPhone":e}function Gn(){return navigator.vendor||""}function jn(){for(var e=[],t=0,r=["chrome","safari","__crWeb","__gCrWeb","yandex","__yb","__ybro","__firefox__","__edgeTrackingPreventionStatistics","webkit","oprt","samsungAr","ucweb","UCShellJava","puffinDevice"];t<r.length;t++){var n=r[t],o=window[n];o&&typeof o=="object"&&e.push(n)}return e.sort()}function Wn(){var e=document;try{e.cookie="cookietest=1; SameSite=Strict;";var t=e.cookie.indexOf("cookietest=")!==-1;return e.cookie="cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT",t}catch(r){return!1}}function Hn(){var e=atob;return{abpIndo:["#Iklan-Melayang","#Kolom-Iklan-728","#SidebarIklan-wrapper",'[title="ALIENBOLA" i]',e("I0JveC1CYW5uZXItYWRz")],abpvn:[".quangcao","#mobileCatfish",e("LmNsb3NlLWFkcw=="),'[id^="bn_bottom_fixed_"]',"#pmadv"],adBlockFinland:[".mainostila",e("LnNwb25zb3JpdA=="),".ylamainos",e("YVtocmVmKj0iL2NsaWNrdGhyZ2guYXNwPyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hcHAucmVhZHBlYWsuY29tL2FkcyJd")],adBlockPersian:["#navbar_notice_50",".kadr",'TABLE[width="140px"]',"#divAgahi",e("YVtocmVmXj0iaHR0cDovL2cxLnYuZndtcm0ubmV0L2FkLyJd")],adBlockWarningRemoval:["#adblock-honeypot",".adblocker-root",".wp_adblock_detect",e("LmhlYWRlci1ibG9ja2VkLWFk"),e("I2FkX2Jsb2NrZXI=")],adGuardAnnoyances:[".hs-sosyal","#cookieconsentdiv",'div[class^="app_gdpr"]',".as-oil",'[data-cypress="soft-push-notification-modal"]'],adGuardBase:[".BetterJsPopOverlay",e("I2FkXzMwMFgyNTA="),e("I2Jhbm5lcmZsb2F0MjI="),e("I2NhbXBhaWduLWJhbm5lcg=="),e("I0FkLUNvbnRlbnQ=")],adGuardChinese:[e("LlppX2FkX2FfSA=="),e("YVtocmVmKj0iLmh0aGJldDM0LmNvbSJd"),"#widget-quan",e("YVtocmVmKj0iLzg0OTkyMDIwLnh5eiJd"),e("YVtocmVmKj0iLjE5NTZobC5jb20vIl0=")],adGuardFrench:["#pavePub",e("LmFkLWRlc2t0b3AtcmVjdGFuZ2xl"),".mobile_adhesion",".widgetadv",e("LmFkc19iYW4=")],adGuardGerman:['aside[data-portal-id="leaderboard"]'],adGuardJapanese:["#kauli_yad_1",e("YVtocmVmXj0iaHR0cDovL2FkMi50cmFmZmljZ2F0ZS5uZXQvIl0="),e("Ll9wb3BJbl9pbmZpbml0ZV9hZA=="),e("LmFkZ29vZ2xl"),e("Ll9faXNib29zdFJldHVybkFk")],adGuardMobile:[e("YW1wLWF1dG8tYWRz"),e("LmFtcF9hZA=="),'amp-embed[type="24smi"]',"#mgid_iframe1",e("I2FkX2ludmlld19hcmVh")],adGuardRussian:[e("YVtocmVmXj0iaHR0cHM6Ly9hZC5sZXRtZWFkcy5jb20vIl0="),e("LnJlY2xhbWE="),'div[id^="smi2adblock"]',e("ZGl2W2lkXj0iQWRGb3hfYmFubmVyXyJd"),"#psyduckpockeball"],adGuardSocial:[e("YVtocmVmXj0iLy93d3cuc3R1bWJsZXVwb24uY29tL3N1Ym1pdD91cmw9Il0="),e("YVtocmVmXj0iLy90ZWxlZ3JhbS5tZS9zaGFyZS91cmw/Il0="),".etsy-tweet","#inlineShare",".popup-social"],adGuardSpanishPortuguese:["#barraPublicidade","#Publicidade","#publiEspecial","#queTooltip",".cnt-publi"],adGuardTrackingProtection:["#qoo-counter",e("YVtocmVmXj0iaHR0cDovL2NsaWNrLmhvdGxvZy5ydS8iXQ=="),e("YVtocmVmXj0iaHR0cDovL2hpdGNvdW50ZXIucnUvdG9wL3N0YXQucGhwIl0="),e("YVtocmVmXj0iaHR0cDovL3RvcC5tYWlsLnJ1L2p1bXAiXQ=="),"#top100counter"],adGuardTurkish:["#backkapat",e("I3Jla2xhbWk="),e("YVtocmVmXj0iaHR0cDovL2Fkc2Vydi5vbnRlay5jb20udHIvIl0="),e("YVtocmVmXj0iaHR0cDovL2l6bGVuemkuY29tL2NhbXBhaWduLyJd"),e("YVtocmVmXj0iaHR0cDovL3d3dy5pbnN0YWxsYWRzLm5ldC8iXQ==")],bulgarian:[e("dGQjZnJlZW5ldF90YWJsZV9hZHM="),"#ea_intext_div",".lapni-pop-over","#xenium_hot_offers"],easyList:[".yb-floorad",e("LndpZGdldF9wb19hZHNfd2lkZ2V0"),e("LnRyYWZmaWNqdW5reS1hZA=="),".textad_headline",e("LnNwb25zb3JlZC10ZXh0LWxpbmtz")],easyListChina:[e("LmFwcGd1aWRlLXdyYXBbb25jbGljayo9ImJjZWJvcy5jb20iXQ=="),e("LmZyb250cGFnZUFkdk0="),"#taotaole","#aafoot.top_box",".cfa_popup"],easyListCookie:[".ezmob-footer",".cc-CookieWarning","[data-cookie-number]",e("LmF3LWNvb2tpZS1iYW5uZXI="),".sygnal24-gdpr-modal-wrap"],easyListCzechSlovak:["#onlajny-stickers",e("I3Jla2xhbW5pLWJveA=="),e("LnJla2xhbWEtbWVnYWJvYXJk"),".sklik",e("W2lkXj0ic2tsaWtSZWtsYW1hIl0=")],easyListDutch:[e("I2FkdmVydGVudGll"),e("I3ZpcEFkbWFya3RCYW5uZXJCbG9jaw=="),".adstekst",e("YVtocmVmXj0iaHR0cHM6Ly94bHR1YmUubmwvY2xpY2svIl0="),"#semilo-lrectangle"],easyListGermany:["#SSpotIMPopSlider",e("LnNwb25zb3JsaW5rZ3J1ZW4="),e("I3dlcmJ1bmdza3k="),e("I3Jla2xhbWUtcmVjaHRzLW1pdHRl"),e("YVtocmVmXj0iaHR0cHM6Ly9iZDc0Mi5jb20vIl0=")],easyListItaly:[e("LmJveF9hZHZfYW5udW5jaQ=="),".sb-box-pubbliredazionale",e("YVtocmVmXj0iaHR0cDovL2FmZmlsaWF6aW9uaWFkcy5zbmFpLml0LyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hZHNlcnZlci5odG1sLml0LyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hZmZpbGlhemlvbmlhZHMuc25haS5pdC8iXQ==")],easyListLithuania:[e("LnJla2xhbW9zX3RhcnBhcw=="),e("LnJla2xhbW9zX251b3JvZG9z"),e("aW1nW2FsdD0iUmVrbGFtaW5pcyBza3lkZWxpcyJd"),e("aW1nW2FsdD0iRGVkaWt1b3RpLmx0IHNlcnZlcmlhaSJd"),e("aW1nW2FsdD0iSG9zdGluZ2FzIFNlcnZlcmlhaS5sdCJd")],estonian:[e("QVtocmVmKj0iaHR0cDovL3BheTRyZXN1bHRzMjQuZXUiXQ==")],fanboyAnnoyances:["#ac-lre-player",".navigate-to-top","#subscribe_popup",".newsletter_holder","#back-top"],fanboyAntiFacebook:[".util-bar-module-firefly-visible"],fanboyEnhancedTrackers:[".open.pushModal","#issuem-leaky-paywall-articles-zero-remaining-nag","#sovrn_container",'div[class$="-hide"][zoompage-fontsize][style="display: block;"]',".BlockNag__Card"],fanboySocial:["#FollowUs","#meteored_share","#social_follow",".article-sharer",".community__social-desc"],frellwitSwedish:[e("YVtocmVmKj0iY2FzaW5vcHJvLnNlIl1bdGFyZ2V0PSJfYmxhbmsiXQ=="),e("YVtocmVmKj0iZG9rdG9yLXNlLm9uZWxpbmsubWUiXQ=="),"article.category-samarbete",e("ZGl2LmhvbGlkQWRz"),"ul.adsmodern"],greekAdBlock:[e("QVtocmVmKj0iYWRtYW4ub3RlbmV0LmdyL2NsaWNrPyJd"),e("QVtocmVmKj0iaHR0cDovL2F4aWFiYW5uZXJzLmV4b2R1cy5nci8iXQ=="),e("QVtocmVmKj0iaHR0cDovL2ludGVyYWN0aXZlLmZvcnRobmV0LmdyL2NsaWNrPyJd"),"DIV.agores300","TABLE.advright"],hungarian:["#cemp_doboz",".optimonk-iframe-container",e("LmFkX19tYWlu"),e("W2NsYXNzKj0iR29vZ2xlQWRzIl0="),"#hirdetesek_box"],iDontCareAboutCookies:['.alert-info[data-block-track*="CookieNotice"]',".ModuleTemplateCookieIndicator",".o--cookies--container","#cookies-policy-sticky","#stickyCookieBar"],icelandicAbp:[e("QVtocmVmXj0iL2ZyYW1ld29yay9yZXNvdXJjZXMvZm9ybXMvYWRzLmFzcHgiXQ==")],latvian:[e("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiAxMjBweDsgaGVpZ2h0OiA0MHB4OyBvdmVyZmxvdzogaGlkZGVuOyBwb3NpdGlvbjogcmVsYXRpdmU7Il0="),e("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiA4OHB4OyBoZWlnaHQ6IDMxcHg7IG92ZXJmbG93OiBoaWRkZW47IHBvc2l0aW9uOiByZWxhdGl2ZTsiXQ==")],listKr:[e("YVtocmVmKj0iLy9hZC5wbGFuYnBsdXMuY28ua3IvIl0="),e("I2xpdmVyZUFkV3JhcHBlcg=="),e("YVtocmVmKj0iLy9hZHYuaW1hZHJlcC5jby5rci8iXQ=="),e("aW5zLmZhc3R2aWV3LWFk"),".revenue_unit_item.dable"],listeAr:[e("LmdlbWluaUxCMUFk"),".right-and-left-sponsers",e("YVtocmVmKj0iLmFmbGFtLmluZm8iXQ=="),e("YVtocmVmKj0iYm9vcmFxLm9yZyJd"),e("YVtocmVmKj0iZHViaXp6bGUuY29tL2FyLz91dG1fc291cmNlPSJd")],listeFr:[e("YVtocmVmXj0iaHR0cDovL3Byb21vLnZhZG9yLmNvbS8iXQ=="),e("I2FkY29udGFpbmVyX3JlY2hlcmNoZQ=="),e("YVtocmVmKj0id2Vib3JhbWEuZnIvZmNnaS1iaW4vIl0="),".site-pub-interstitiel",'div[id^="crt-"][data-criteo-id]'],officialPolish:["#ceneo-placeholder-ceneo-12",e("W2hyZWZePSJodHRwczovL2FmZi5zZW5kaHViLnBsLyJd"),e("YVtocmVmXj0iaHR0cDovL2Fkdm1hbmFnZXIudGVjaGZ1bi5wbC9yZWRpcmVjdC8iXQ=="),e("YVtocmVmXj0iaHR0cDovL3d3dy50cml6ZXIucGwvP3V0bV9zb3VyY2UiXQ=="),e("ZGl2I3NrYXBpZWNfYWQ=")],ro:[e("YVtocmVmXj0iLy9hZmZ0cmsuYWx0ZXgucm8vQ291bnRlci9DbGljayJd"),e("YVtocmVmXj0iaHR0cHM6Ly9ibGFja2ZyaWRheXNhbGVzLnJvL3Ryay9zaG9wLyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9ldmVudC4ycGVyZm9ybWFudC5jb20vZXZlbnRzL2NsaWNrIl0="),e("YVtocmVmXj0iaHR0cHM6Ly9sLnByb2ZpdHNoYXJlLnJvLyJd"),'a[href^="/url/"]'],ruAd:[e("YVtocmVmKj0iLy9mZWJyYXJlLnJ1LyJd"),e("YVtocmVmKj0iLy91dGltZy5ydS8iXQ=="),e("YVtocmVmKj0iOi8vY2hpa2lkaWtpLnJ1Il0="),"#pgeldiz",".yandex-rtb-block"],thaiAds:["a[href*=macau-uta-popup]",e("I2Fkcy1nb29nbGUtbWlkZGxlX3JlY3RhbmdsZS1ncm91cA=="),e("LmFkczMwMHM="),".bumq",".img-kosana"],webAnnoyancesUltralist:["#mod-social-share-2","#social-tools",e("LmN0cGwtZnVsbGJhbm5lcg=="),".zergnet-recommend",".yt.btn-link.btn-md.btn"]}}function Bn(e){var t=e===void 0?{}:e,r=t.debug;return H(this,void 0,void 0,function(){var n,o,i,s,f,m;return B(this,function(p){switch(p.label){case 0:return Zn()?(n=Hn(),o=Object.keys(n),i=(m=[]).concat.apply(m,o.map(function(a){return n[a]})),[4,Yn(i)]):[2,void 0];case 1:return s=p.sent(),r&&zn(n,s),f=o.filter(function(a){var u=n[a],c=G(u.map(function(l){return s[l]}));return c>u.length*.6}),f.sort(),[2,f]}})})}function Zn(){return fe()||Mt()}function Yn(e){var t;return H(this,void 0,void 0,function(){var r,n,o,i,m,s,f,m;return B(this,function(p){switch(p.label){case 0:for(r=document,n=r.createElement("div"),o=new Array(e.length),i={},Tt(n),m=0;m<e.length;++m)s=sn(e[m]),s.tagName==="DIALOG"&&s.show(),f=r.createElement("div"),Tt(f),f.appendChild(s),n.appendChild(f),o[m]=s;p.label=1;case 1:return r.body?[3,3]:[4,ue(50)];case 2:return p.sent(),[3,1];case 3:r.body.appendChild(n);try{for(m=0;m<e.length;++m)o[m].offsetParent||(i[e[m]]=!0)}finally{(t=n.parentNode)===null||t===void 0||t.removeChild(n)}return[2,i]}})})}function Tt(e){e.style.setProperty("display","block","important")}function zn(e,t){for(var r="DOM blockers debug:\n```",n=0,o=Object.keys(e);n<o.length;n++){var i=o[n];r+=`
`.concat(i,":");for(var s=0,f=e[i];s<f.length;s++){var m=f[s];r+=`
  `.concat(t[m]?"\u{1F6AB}":"\u27A1\uFE0F"," ").concat(m)}}console.log("".concat(r,"\n```"))}function Xn(){for(var e=0,t=["rec2020","p3","srgb"];e<t.length;e++){var r=t[e];if(matchMedia("(color-gamut: ".concat(r,")")).matches)return r}}function Un(){if(kt("inverted"))return!0;if(kt("none"))return!1}function kt(e){return matchMedia("(inverted-colors: ".concat(e,")")).matches}function Jn(){if(Et("active"))return!0;if(Et("none"))return!1}function Et(e){return matchMedia("(forced-colors: ".concat(e,")")).matches}function qn(){if(matchMedia("(min-monochrome: 0)").matches){for(var e=0;e<=$n;++e)if(matchMedia("(max-monochrome: ".concat(e,")")).matches)return e;throw new Error("Too high value")}}function Kn(){if(ne("no-preference"))return 0;if(ne("high")||ne("more"))return 1;if(ne("low")||ne("less"))return-1;if(ne("forced"))return 10}function ne(e){return matchMedia("(prefers-contrast: ".concat(e,")")).matches}function Qn(){if(Rt("reduce"))return!0;if(Rt("no-preference"))return!1}function Rt(e){return matchMedia("(prefers-reduced-motion: ".concat(e,")")).matches}function ei(){if(Pt("high"))return!0;if(Pt("standard"))return!1}function Pt(e){return matchMedia("(dynamic-range: ".concat(e,")")).matches}function ti(){var e=A.acos||I,t=A.acosh||I,r=A.asin||I,n=A.asinh||I,o=A.atanh||I,i=A.atan||I,s=A.sin||I,f=A.sinh||I,m=A.cos||I,p=A.cosh||I,a=A.tan||I,u=A.tanh||I,c=A.exp||I,l=A.expm1||I,d=A.log1p||I,y=function(S){return A.pow(A.PI,S)},T=function(S){return A.log(S+A.sqrt(S*S-1))},h=function(S){return A.log(S+A.sqrt(S*S+1))},w=function(S){return A.log((1+S)/(1-S))/2},P=function(S){return A.exp(S)-1/A.exp(S)/2},C=function(S){return(A.exp(S)+1/A.exp(S))/2},U=function(S){return A.exp(S)-1},ie=function(S){return(A.exp(2*S)-1)/(A.exp(2*S)+1)},K=function(S){return A.log(1+S)};return{acos:e(.12312423423423424),acosh:t(1e308),acoshPf:T(1e154),asin:r(.12312423423423424),asinh:n(1),asinhPf:h(1),atanh:o(.5),atanhPf:w(.5),atan:i(.5),sin:s(-1e300),sinh:f(1),sinhPf:P(1),cos:m(10.000000000123),cosh:p(1),coshPf:C(1),tan:a(-1e300),tanh:u(1),tanhPf:ie(1),exp:c(1),expm1:l(1),expm1Pf:U(1),log1p:d(10),log1pPf:K(10),powPI:y(-100)}}function ni(){return ii(function(e,t){for(var r={},n={},o=0,i=Object.keys(He);o<i.length;o++){var s=i[o],f=He[s],m=f[0],p=m===void 0?{}:m,a=f[1],u=a===void 0?ri:a,c=e.createElement("span");c.textContent=u,c.style.whiteSpace="nowrap";for(var l=0,d=Object.keys(p);l<d.length;l++){var y=d[l],T=p[y];T!==void 0&&(c.style[y]=T)}r[s]=c,t.appendChild(e.createElement("br")),t.appendChild(c)}for(var h=0,w=Object.keys(He);h<w.length;h++){var s=w[h];n[s]=r[s].getBoundingClientRect().width}return n})}function ii(e,t){return t===void 0&&(t=4e3),Dt(function(r,n){var o=n.document,i=o.body,s=i.style;s.width="".concat(t,"px"),s.webkitTextSizeAdjust=s.textSizeAdjust="none",ze()?i.style.zoom="".concat(1/n.devicePixelRatio):fe()&&(i.style.zoom="reset");var f=o.createElement("div");return f.textContent=Ge([],Array(t/20<<0),!0).map(function(){return"word"}).join(" "),i.appendChild(f),e(o,i)},'<!doctype html><html><head><meta name="viewport" content="width=device-width, initial-scale=1">')}function oi(){var e,t=document.createElement("canvas"),r=(e=t.getContext("webgl"))!==null&&e!==void 0?e:t.getContext("experimental-webgl");if(r&&"getExtension"in r){var n=r.getExtension("WEBGL_debug_renderer_info");if(n)return{vendor:(r.getParameter(n.UNMASKED_VENDOR_WEBGL)||"").toString(),renderer:(r.getParameter(n.UNMASKED_RENDERER_WEBGL)||"").toString()}}}function ai(){return navigator.pdfViewerEnabled}function si(){var e=new Float32Array(1),t=new Uint8Array(e.buffer);return e[0]=1/0,e[0]=e[0]-e[0],t[3]}function ui(e){return Ur(ci,e,[])}function fi(e){var t=di(e),r=pi(t);return{score:t,comment:li.replace(/\$/g,"".concat(r))}}function di(e){if(Mt())return .4;if(fe())return Xe()?.5:.3;var t=e.platform.value||"";return/^Win/.test(t)?.6:/^Mac/.test(t)?.5:.7}function pi(e){return Lt(.99+.01*e,1e-4)}function mi(e){for(var t="",r=0,n=Object.keys(e).sort();r<n.length;r++){var o=n[r],i=e[o],s=i.error?"error":JSON.stringify(i.value);t+="".concat(t?"|":"").concat(o.replace(/([:|\\])/g,"\\$1"),":").concat(s)}return t}function Nt(e){return JSON.stringify(e,function(t,r){return r instanceof Error?Hr(r):r},2)}function Vt(e){return Wr(mi(e))}function hi(e){var t,r=fi(e);return{get visitorId(){return t===void 0&&(t=Vt(this.components)),t},set visitorId(n){t=n},confidence:r,components:e,version:It}}function wi(e){return e===void 0&&(e=50),jr(e,e*2)}function bi(e,t){var r=Date.now();return{get:function(n){return H(this,void 0,void 0,function(){var o,i,s;return B(this,function(f){switch(f.label){case 0:return o=Date.now(),[4,e()];case 1:return i=f.sent(),s=hi(i),(t||n!=null&&n.debug)&&console.log("Copy the text below to get the debug data:\n\n```\nversion: ".concat(s.version,`
userAgent: `).concat(navigator.userAgent,`
timeBetweenLoadAndGet: `).concat(o-r,`
visitorId: `).concat(s.visitorId,`
components: `).concat(Nt(i),"\n```")),[2,s]}})})}}}function vi(){if(!(window.__fpjs_d_m||Math.random()>=.001))try{var e=new XMLHttpRequest;e.open("get","https://m1.openfpcdn.io/fingerprintjs/v".concat(It,"/npm-monitoring"),!0),e.send()}catch(t){console.error(t)}}function gi(e){var t=e===void 0?{}:e,r=t.delayFallback,n=t.debug,o=t.monitoring,i=o===void 0?!0:o;return H(this,void 0,void 0,function(){var s;return B(this,function(f){switch(f.label){case 0:return i&&vi(),[4,wi(r)];case 1:return f.sent(),s=ui({debug:n}),[2,bi(s,n)]}})})}var It,un,ln,re,At,Tn,kn,Ae,We,$n,A,I,ri,He,ci,li,Ft,Gt=V(()=>{vt();It="3.4.2";un="mmMwWLliI0O&1",ln="48px",re=["monospace","sans-serif","serif"],At=["sans-serif-thin","ARNO PRO","Agency FB","Arabic Typesetting","Arial Unicode MS","AvantGarde Bk BT","BankGothic Md BT","Batang","Bitstream Vera Sans Mono","Calibri","Century","Century Gothic","Clarendon","EUROSTILE","Franklin Gothic","Futura Bk BT","Futura Md BT","GOTHAM","Gill Sans","HELV","Haettenschweiler","Helvetica Neue","Humanst521 BT","Leelawadee","Letter Gothic","Levenim MT","Lucida Bright","Lucida Sans","Menlo","MS Mincho","MS Outlook","MS Reference Specialty","MS UI Gothic","MT Extra","MYRIAD PRO","Marlett","Meiryo UI","Microsoft Uighur","Minion Pro","Monotype Corsiva","PMingLiU","Pristina","SCRIPTINA","Segoe UI Light","Serifa","SimHei","Small Fonts","Staccato222 BT","TRAJAN PRO","Univers CE 55 Medium","Vrinda","ZWAdobeF"];Tn=2500,kn=10;$n=100;A=Math,I=function(){return 0};ri="mmMwWLliI0fiflO&1",He={default:[],apple:[{font:"-apple-system-body"}],serif:[{fontFamily:"serif"}],sans:[{fontFamily:"sans-serif"}],mono:[{fontFamily:"monospace"}],min:[{fontSize:"1px"}],system:[{fontFamily:"system-ui"}]};ci={fonts:fn,domBlockers:Bn,fontPreferences:ni,audio:rn,screenFrame:Pn,osCpu:yn,languages:Sn,colorDepth:_n,deviceMemory:xn,screenResolution:An,hardwareConcurrency:In,timezone:Cn,sessionStorage:On,localStorage:Mn,indexedDB:Dn,openDatabase:Nn,cpuClass:Vn,platform:Fn,plugins:dn,canvas:pn,touchSupport:gn,vendor:Gn,vendorFlavors:jn,cookiesEnabled:Wn,colorGamut:Xn,invertedColors:Un,forcedColors:Jn,monochrome:qn,contrast:Kn,reducedMotion:Qn,hdr:ei,math:ti,videoCard:oi,pdfViewerEnabled:ai,architecture:si};li="$ if upgrade to Pro: https://fpjs.dev/pro";Ft={load:gi,hashComponents:Vt,componentsToDebugString:Nt}});var Wt=J((no,jt)=>{function D(e,t){typeof t=="boolean"&&(t={forever:t}),this._originalTimeouts=JSON.parse(JSON.stringify(e)),this._timeouts=e,this._options=t||{},this._maxRetryTime=t&&t.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}jt.exports=D;D.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)};D.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null};D.prototype.retry=function(e){if(this._timeout&&clearTimeout(this._timeout),!e)return!1;var t=new Date().getTime();if(e&&t-this._operationStart>=this._maxRetryTime)return this._errors.push(e),this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(e);var r=this._timeouts.shift();if(r===void 0)if(this._cachedTimeouts)this._errors.splice(0,this._errors.length-1),r=this._cachedTimeouts.slice(-1);else return!1;var n=this;return this._timer=setTimeout(function(){n._attempts++,n._operationTimeoutCb&&(n._timeout=setTimeout(function(){n._operationTimeoutCb(n._attempts)},n._operationTimeout),n._options.unref&&n._timeout.unref()),n._fn(n._attempts)},r),this._options.unref&&this._timer.unref(),!0};D.prototype.attempt=function(e,t){this._fn=e,t&&(t.timeout&&(this._operationTimeout=t.timeout),t.cb&&(this._operationTimeoutCb=t.cb));var r=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){r._operationTimeoutCb()},r._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};D.prototype.try=function(e){console.log("Using RetryOperation.try() is deprecated"),this.attempt(e)};D.prototype.start=function(e){console.log("Using RetryOperation.start() is deprecated"),this.attempt(e)};D.prototype.start=D.prototype.try;D.prototype.errors=function(){return this._errors};D.prototype.attempts=function(){return this._attempts};D.prototype.mainError=function(){if(this._errors.length===0)return null;for(var e={},t=null,r=0,n=0;n<this._errors.length;n++){var o=this._errors[n],i=o.message,s=(e[i]||0)+1;e[i]=s,s>=r&&(t=o,r=s)}return t}});var Ht=J($=>{var yi=Wt();$.operation=function(e){var t=$.timeouts(e);return new yi(t,{forever:e&&(e.forever||e.retries===1/0),unref:e&&e.unref,maxRetryTime:e&&e.maxRetryTime})};$.timeouts=function(e){if(e instanceof Array)return[].concat(e);var t={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var r in e)t[r]=e[r];if(t.minTimeout>t.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var n=[],o=0;o<t.retries;o++)n.push(this.createTimeout(o,t));return e&&e.forever&&!n.length&&n.push(this.createTimeout(o,t)),n.sort(function(i,s){return i-s}),n};$.createTimeout=function(e,t){var r=t.randomize?Math.random()+1:1,n=Math.round(r*Math.max(t.minTimeout,1)*Math.pow(t.factor,e));return n=Math.min(n,t.maxTimeout),n};$.wrap=function(e,t,r){if(t instanceof Array&&(r=t,t=null),!r){r=[];for(var n in e)typeof e[n]=="function"&&r.push(n)}for(var o=0;o<r.length;o++){var i=r[o],s=e[i];e[i]=function(m){var p=$.operation(t),a=Array.prototype.slice.call(arguments,1),u=a.pop();a.push(function(c){p.retry(c)||(c&&(arguments[0]=p.mainError()),u.apply(this,arguments))}),p.attempt(function(){m.apply(e,a)})}.bind(e,s),e[i].options=t}}});var Zt=J((oo,Bt)=>{Bt.exports=Ht()});var zt=J((ao,Yt)=>{var Si=Zt();function _i(e,t){function r(n,o){var i=t||{},s;"randomize"in i||(i.randomize=!0),s=Si.operation(i);function f(a){o(a||new Error("Aborted"))}function m(a,u){if(a.bail){f(a);return}s.retry(a)?i.onRetry&&i.onRetry(a,u):o(s.mainError())}function p(a){var u;try{u=e(f,a)}catch(c){m(c,a);return}Promise.resolve(u).then(n).catch(function(l){m(l,a)})}s.attempt(p)}return new Promise(r)}Yt.exports=_i});var Xt=J((Ue,Je)=>{(function(e,t){typeof Ue=="object"&&typeof Je!="undefined"?Je.exports=function(r,n,o,i,s){for(n=n.split?n.split("."):n,i=0;i<n.length;i++)r=r?r[n[i]]:s;return r===s?o:r}:typeof define=="function"&&define.amd?define(function(){return function(r,n,o,i,s){for(n=n.split?n.split("."):n,i=0;i<n.length;i++)r=r?r[n[i]]:s;return r===s?o:r}}):e.dlv=function(r,n,o,i,s){for(n=n.split?n.split("."):n,i=0;i<n.length;i++)r=r?r[n[i]]:s;return r===s?o:r}})(Ue)});var $e=J((de,Te)=>{(function(e,t){"use strict";var r="1.0.40",n="",o="?",i="function",s="undefined",f="object",m="string",p="major",a="model",u="name",c="type",l="vendor",d="version",y="architecture",T="console",h="mobile",w="tablet",P="smarttv",C="wearable",U="embedded",ie=500,K="Amazon",S="Apple",tt="ASUS",rt="BlackBerry",he="Browser",we="Chrome",Rr="Edge",be="Firefox",oe="Google",nt="Huawei",Ce="LG",Le="Microsoft",it="Motorola",ae="Opera",se="Samsung",ot="Sharp",ve="Sony",Oe="Xiaomi",Me="Zebra",at="Facebook",st="Chromium OS",ct="Mac OS",ut=" Browser",Pr=function(g,_){var v={};for(var k in g)_[k]&&_[k].length%2===0?v[k]=_[k].concat(g[k]):v[k]=g[k];return v},ge=function(g){for(var _={},v=0;v<g.length;v++)_[g[v].toUpperCase()]=g[v];return _},lt=function(g,_){return typeof g===m?Q(_).indexOf(Q(g))!==-1:!1},Q=function(g){return g.toLowerCase()},Ir=function(g){return typeof g===m?g.replace(/[^\d\.]/g,n).split(".")[0]:t},De=function(g,_){if(typeof g===m)return g=g.replace(/^\s\s*/,n),typeof _===s?g:g.substring(0,ie)},ce=function(g,_){for(var v=0,k,Y,j,x,b,W;v<_.length&&!b;){var Ne=_[v],pt=_[v+1];for(k=Y=0;k<Ne.length&&!b&&Ne[k];)if(b=Ne[k++].exec(g),b)for(j=0;j<pt.length;j++)W=b[++Y],x=pt[j],typeof x===f&&x.length>0?x.length===2?typeof x[1]==i?this[x[0]]=x[1].call(this,W):this[x[0]]=x[1]:x.length===3?typeof x[1]===i&&!(x[1].exec&&x[1].test)?this[x[0]]=W?x[1].call(this,W,x[2]):t:this[x[0]]=W?W.replace(x[1],x[2]):t:x.length===4&&(this[x[0]]=W?x[3].call(this,W.replace(x[1],x[2])):t):this[x]=W||t;v+=2}},ye=function(g,_){for(var v in _)if(typeof _[v]===f&&_[v].length>0){for(var k=0;k<_[v].length;k++)if(lt(_[v][k],g))return v===o?t:v}else if(lt(_[v],g))return v===o?t:v;return _.hasOwnProperty("*")?_["*"]:g},Cr={"1.0":"/8","1.2":"/1","1.3":"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"},ft={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2","8.1":"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},dt={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[d,[u,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[d,[u,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[u,d],[/opios[\/ ]+([\w\.]+)/i],[d,[u,ae+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[d,[u,ae+" GX"]],[/\bopr\/([\w\.]+)/i],[d,[u,ae]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[d,[u,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[d,[u,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\/([-\w\.]+)/i,/(heytap|ovi|115)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[u,d],[/quark(?:pc)?\/([-\w\.]+)/i],[d,[u,"Quark"]],[/\bddg\/([\w\.]+)/i],[d,[u,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[d,[u,"UC"+he]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[d,[u,"WeChat"]],[/konqueror\/([\w\.]+)/i],[d,[u,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[d,[u,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[d,[u,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[d,[u,"Smart Lenovo "+he]],[/(avast|avg)\/([\w\.]+)/i],[[u,/(.+)/,"$1 Secure "+he],d],[/\bfocus\/([\w\.]+)/i],[d,[u,be+" Focus"]],[/\bopt\/([\w\.]+)/i],[d,[u,ae+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[d,[u,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[d,[u,"Dolphin"]],[/coast\/([\w\.]+)/i],[d,[u,ae+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[d,[u,"MIUI"+ut]],[/fxios\/([\w\.-]+)/i],[d,[u,be]],[/\bqihoobrowser\/?([\w\.]*)/i],[d,[u,"360"]],[/\b(qq)\/([\w\.]+)/i],[[u,/(.+)/,"$1Browser"],d],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[u,/(.+)/,"$1"+ut],d],[/samsungbrowser\/([\w\.]+)/i],[d,[u,se+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[d,[u,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[u,"Sogou Mobile"],d],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[u,d],[/(lbbrowser|rekonq)/i,/\[(linkedin)app\]/i],[u],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[d,u],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[u,at],d],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[u,d],[/\bgsa\/([\w\.]+) .*safari\//i],[d,[u,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[d,[u,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[d,[u,we+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[u,we+" WebView"],d],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[d,[u,"Android "+he]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[u,d],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[d,[u,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[d,u],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[u,[d,ye,Cr]],[/(webkit|khtml)\/([\w\.]+)/i],[u,d],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[u,"Netscape"],d],[/(wolvic|librewolf)\/([\w\.]+)/i],[u,d],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[d,[u,be+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[u,[d,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[u,[d,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[y,"amd64"]],[/(ia32(?=;))/i],[[y,Q]],[/((?:i[346]|x)86)[;\)]/i],[[y,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[y,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[y,"armhf"]],[/windows (ce|mobile); ppc;/i],[[y,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[y,/ower/,n,Q]],[/(sun4\w)[;\)]/i],[[y,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[y,Q]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[a,[l,se],[c,w]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[a,[l,se],[c,h]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[a,[l,S],[c,h]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[a,[l,S],[c,w]],[/(macintosh);/i],[a,[l,S]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[a,[l,ot],[c,h]],[/(?:honor)([-\w ]+)[;\)]/i],[a,[l,"Honor"],[c,h]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[a,[l,nt],[c,w]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[a,[l,nt],[c,h]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i],[[a,/_/g," "],[l,Oe],[c,h]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[a,/_/g," "],[l,Oe],[c,w]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[a,[l,"OPPO"],[c,h]],[/\b(opd2\d{3}a?) bui/i],[a,[l,"OPPO"],[c,w]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[a,[l,"Vivo"],[c,h]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[a,[l,"Realme"],[c,h]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[a,[l,it],[c,h]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[a,[l,it],[c,w]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[a,[l,Ce],[c,w]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[a,[l,Ce],[c,h]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[a,[l,"Lenovo"],[c,w]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[a,/_/g," "],[l,"Nokia"],[c,h]],[/(pixel c)\b/i],[a,[l,oe],[c,w]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[a,[l,oe],[c,h]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[a,[l,ve],[c,h]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[a,"Xperia Tablet"],[l,ve],[c,w]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[a,[l,"OnePlus"],[c,h]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[a,[l,K],[c,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[a,/(.+)/g,"Fire Phone $1"],[l,K],[c,h]],[/(playbook);[-\w\),; ]+(rim)/i],[a,l,[c,w]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[a,[l,rt],[c,h]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[a,[l,tt],[c,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[a,[l,tt],[c,h]],[/(nexus 9)/i],[a,[l,"HTC"],[c,w]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[l,[a,/_/g," "],[c,h]],[/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\w*(\)| bui)/i],[a,[l,"TCL"],[c,w]],[/(itel) ((\w+))/i],[[l,Q],a,[c,ye,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[a,[l,"Acer"],[c,w]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[a,[l,"Meizu"],[c,h]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[a,[l,"Ulefone"],[c,h]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[a,[l,"Energizer"],[c,h]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[a,[l,"Cat"],[c,h]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[a,[l,"Smartfren"],[c,h]],[/droid.+; (a(?:015|06[35]|142p?))/i],[a,[l,"Nothing"],[c,h]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (imo) ((?!tab)[\w ]+?)(?: bui|\))/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[l,a,[c,h]],[/(imo) (tab \w+)/i,/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[l,a,[c,w]],[/(surface duo)/i],[a,[l,Le],[c,w]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[a,[l,"Fairphone"],[c,h]],[/(u304aa)/i],[a,[l,"AT&T"],[c,h]],[/\bsie-(\w*)/i],[a,[l,"Siemens"],[c,h]],[/\b(rct\w+) b/i],[a,[l,"RCA"],[c,w]],[/\b(venue[\d ]{2,7}) b/i],[a,[l,"Dell"],[c,w]],[/\b(q(?:mv|ta)\w+) b/i],[a,[l,"Verizon"],[c,w]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[a,[l,"Barnes & Noble"],[c,w]],[/\b(tm\d{3}\w+) b/i],[a,[l,"NuVision"],[c,w]],[/\b(k88) b/i],[a,[l,"ZTE"],[c,w]],[/\b(nx\d{3}j) b/i],[a,[l,"ZTE"],[c,h]],[/\b(gen\d{3}) b.+49h/i],[a,[l,"Swiss"],[c,h]],[/\b(zur\d{3}) b/i],[a,[l,"Swiss"],[c,w]],[/\b((zeki)?tb.*\b) b/i],[a,[l,"Zeki"],[c,w]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[l,"Dragon Touch"],a,[c,w]],[/\b(ns-?\w{0,9}) b/i],[a,[l,"Insignia"],[c,w]],[/\b((nxa|next)-?\w{0,9}) b/i],[a,[l,"NextBook"],[c,w]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[l,"Voice"],a,[c,h]],[/\b(lvtel\-)?(v1[12]) b/i],[[l,"LvTel"],a,[c,h]],[/\b(ph-1) /i],[a,[l,"Essential"],[c,h]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[a,[l,"Envizen"],[c,w]],[/\b(trio[-\w\. ]+) b/i],[a,[l,"MachSpeed"],[c,w]],[/\btu_(1491) b/i],[a,[l,"Rotor"],[c,w]],[/(shield[\w ]+) b/i],[a,[l,"Nvidia"],[c,w]],[/(sprint) (\w+)/i],[l,a,[c,h]],[/(kin\.[onetw]{3})/i],[[a,/\./g," "],[l,Le],[c,h]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[a,[l,Me],[c,w]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[a,[l,Me],[c,h]],[/smart-tv.+(samsung)/i],[l,[c,P]],[/hbbtv.+maple;(\d+)/i],[[a,/^/,"SmartTV"],[l,se],[c,P]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[l,Ce],[c,P]],[/(apple) ?tv/i],[l,[a,S+" TV"],[c,P]],[/crkey/i],[[a,we+"cast"],[l,oe],[c,P]],[/droid.+aft(\w+)( bui|\))/i],[a,[l,K],[c,P]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[a,[l,ot],[c,P]],[/(bravia[\w ]+)( bui|\))/i],[a,[l,ve],[c,P]],[/(mitv-\w{5}) bui/i],[a,[l,Oe],[c,P]],[/Hbbtv.*(technisat) (.*);/i],[l,a,[c,P]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[l,De],[a,De],[c,P]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[c,P]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[l,a,[c,T]],[/droid.+; (shield) bui/i],[a,[l,"Nvidia"],[c,T]],[/(playstation [345portablevi]+)/i],[a,[l,ve],[c,T]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[a,[l,Le],[c,T]],[/\b(sm-[lr]\d\d[05][fnuw]?s?)\b/i],[a,[l,se],[c,C]],[/((pebble))app/i],[l,a,[c,C]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[a,[l,S],[c,C]],[/droid.+; (glass) \d/i],[a,[l,oe],[c,C]],[/droid.+; (wt63?0{2,3})\)/i],[a,[l,Me],[c,C]],[/droid.+; (glass) \d/i],[a,[l,oe],[c,C]],[/(pico) (4|neo3(?: link|pro)?)/i],[l,a,[c,C]],[/; (quest( \d| pro)?)/i],[a,[l,at],[c,C]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[l,[c,U]],[/(aeobc)\b/i],[a,[l,K],[c,U]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[a,[c,h]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[a,[c,w]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[c,w]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[c,h]],[/(android[-\w\. ]{0,9});.+buil/i],[a,[l,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[d,[u,Rr+"HTML"]],[/(arkweb)\/([\w\.]+)/i],[u,d],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[d,[u,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[u,d],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[d,u]],os:[[/microsoft (windows) (vista|xp)/i],[u,d],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[u,[d,ye,ft]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[d,ye,ft],[u,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[d,/_/g,"."],[u,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[u,ct],[d,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[d,u],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[u,d],[/\(bb(10);/i],[d,[u,rt]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[d,[u,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[d,[u,be+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[d,[u,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[d,[u,"watchOS"]],[/crkey\/([\d\.]+)/i],[d,[u,we+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[u,st],d],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[u,d],[/(sunos) ?([\w\.\d]*)/i],[[u,"Solaris"],d],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[u,d]]},L=function(g,_){if(typeof g===f&&(_=g,g=t),!(this instanceof L))return new L(g,_).getResult();var v=typeof e!==s&&e.navigator?e.navigator:t,k=g||(v&&v.userAgent?v.userAgent:n),Y=v&&v.userAgentData?v.userAgentData:t,j=_?Pr(dt,_):dt,x=v&&v.userAgent==k;return this.getBrowser=function(){var b={};return b[u]=t,b[d]=t,ce.call(b,k,j.browser),b[p]=Ir(b[d]),x&&v&&v.brave&&typeof v.brave.isBrave==i&&(b[u]="Brave"),b},this.getCPU=function(){var b={};return b[y]=t,ce.call(b,k,j.cpu),b},this.getDevice=function(){var b={};return b[l]=t,b[a]=t,b[c]=t,ce.call(b,k,j.device),x&&!b[c]&&Y&&Y.mobile&&(b[c]=h),x&&b[a]=="Macintosh"&&v&&typeof v.standalone!==s&&v.maxTouchPoints&&v.maxTouchPoints>2&&(b[a]="iPad",b[c]=w),b},this.getEngine=function(){var b={};return b[u]=t,b[d]=t,ce.call(b,k,j.engine),b},this.getOS=function(){var b={};return b[u]=t,b[d]=t,ce.call(b,k,j.os),x&&!b[u]&&Y&&Y.platform&&Y.platform!="Unknown"&&(b[u]=Y.platform.replace(/chrome os/i,st).replace(/macos/i,ct)),b},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return k},this.setUA=function(b){return k=typeof b===m&&b.length>ie?De(b,ie):b,this},this.setUA(k),this};L.VERSION=r,L.BROWSER=ge([u,d,p]),L.CPU=ge([y]),L.DEVICE=ge([a,l,c,T,h,P,w,C,U]),L.ENGINE=L.OS=ge([u,d]),typeof de!==s?(typeof Te!==s&&Te.exports&&(de=Te.exports=L),de.UAParser=L):typeof define===i&&define.amd?define(function(){return L}):typeof e!==s&&(e.UAParser=L);var ee=typeof e!==s&&(e.jQuery||e.Zepto);if(ee&&!ee.ua){var Se=new L;ee.ua=Se.getResult(),ee.ua.get=function(){return Se.getUA()},ee.ua.set=function(g){Se.setUA(g);var _=Se.getResult();for(var v in _)ee.ua[v]=_[v]}}})(typeof window=="object"?window:de)});var Ut,xi,ke,Jt=V(()=>{"use strict";Ut=_e($e()),xi="17.2".split(".").map(Number).reduce((e,t,r)=>e+t*100**(2-r),0),ke=class{constructor(t){try{this.ua=new Ut.default(t),console.info("User agent:",this.ua.getResult())}catch(r){console.error("Error while parsing user agent",r)}}getPwaPromptType(){if(this.ua)return this.ua.getOS().name==="iOS"&&this.ua.getBrowser().name==="Mobile Safari"?"ios_safari":this.ua.getOS().name==="iOS"&&this.ua.getBrowser().name==="Chrome"?"ios_chrome":this.ua.getOS().name==="Android"?"android":"others"}isIOS(){return!!this.ua&&this.ua.getOS().name==="iOS"}isAndroid(){return!!this.ua&&this.ua.getOS().name==="Android"}isMobileDevice(){return!!this.ua&&this.ua.getDevice().type==="mobile"}isIOSPwaSupported(){if(!this.ua||!this.isIOS())return!1;let t=this.ua.getOS().version;return t?t.split(".").map(Number).reduce((n,o,i)=>n+o*100**(2-i),0)>=xi:!1}getDeviceInfo(){if(!this.ua)return null;let t=this.ua.getResult();return{isDesktop:!this.isMobileDevice(),device:t.device.type||"desktop",osName:t.os.name||"",osVersion:t.os.version||"",browserName:t.browser.name||"",browserVersion:t.browser.version||""}}}});var $t,uo,lo,qt,Ai,qe,Kt,Qt=V(()=>{"use strict";Jt();$t=_e($e()),uo=new $t.default,lo=new ke(navigator.userAgent),qt=()=>{var e;return!!("standalone"in window.navigator&&window.navigator.standalone===!0||(e=window.matchMedia)!=null&&e.call(window,"(display-mode: standalone)").matches)},Ai=["architecture","platformVersion","bitness","formFactor","fullVersionList","model","wow64"],Kt=()=>R(null,null,function*(){var e,t;if(!("userAgentData"in navigator))return{};if(!qe)try{qe=(yield(t=(e=navigator.userAgentData).getHighEntropyValues)==null?void 0:t.call(e,Ai))||{}}catch(r){console.error(r)}return qe||{}})});function q(){if(typeof crypto!="undefined"&&typeof crypto.randomUUID=="function")return crypto.randomUUID();let e="",t,r;for(t=0;t<32;t+=1)r=Math.random()*16|0,(t===8||t===12||t===16||t===20)&&(e+="-"),e+=(t===12?4:t===16?r&3|8:r).toString(16);return e}var Ee=V(()=>{"use strict"});function Ti(e){let t=()=>{if(!window.navigator.sendBeacon("/reports",JSON.stringify(e)))throw new Error("sendBeacon failed")},r=()=>{(0,er.default)(()=>fetch("/reports",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),{retries:3})};if(typeof navigator!="undefined"&&typeof navigator.sendBeacon=="function")try{t()}catch(n){console.error(n),r()}else r()}function ki(){return R(this,null,function*(){let e=q(),r=yield(yield Ft.load()).get();return{uuid:e,time:`${Date.now()}`,ctwid:"G0000000",appid:window.option.appId,page:{referer:document.referrer||"",network_connection:(0,tr.default)(window,"navigator.network.connection.effectiveType",""),url:window.location.href,fp:r.visitorId||"",country:"",region:"",lang:window.option.lang||"",userAgent:window.navigator.userAgent,userAgentData:yield Kt(),isStandalone:qt()?1:0}}})}function rr(r){return R(this,arguments,function*({action:e,payload:t={}}){let n=yield ki(),o=bt(Fe({},n),{type:e,payload:t});Ti(o)})}var er,tr,Ke=V(()=>{"use strict";Gt();er=_e(zt()),tr=_e(Xt());Qt();Ee()});function nr(e){let t=new URL(window.location.href);try{let r=t.searchParams.get("enable_flashlaunch");r?document.cookie=`gp_flashlaunch=${r}; path=/; Secure`:e?document.cookie=`gp_flashlaunch=${e}; path=/; Secure`:document.cookie="gp_flashlaunch=; path=/; Secure; expires=Thu, 01 Jan 1970 00:00:00 GMT"}catch(r){console.error("CLOUD_GAME_ENABLE_ERROR",r)}}var ir=V(()=>{"use strict";Ke()});var or,ar,sr=V(()=>{"use strict";or=e=>{let t=`${e}=`,r=document.cookie.split(";");for(let n=0;n<r.length;n+=1){let o=r[n];for(;o.charAt(0)===" ";)o=o.substring(1);if(o.indexOf(t)===0)return o.substring(t.length,o.length)}},ar=(e,t,r,n)=>{let o=new Date;o.setTime(o.getTime()+r*24*60*60*1e3);let i=`expires=${o.toUTCString()}`;document.cookie=`${e}=${t};${i}${n?`;domain=${n}`:""};path=/`}});function ur(e){return btoa(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function Ei(e){return R(this,null,function*(){navigator.hardwareConcurrency&&e.push({key:"cpu_cores",value:navigator.hardwareConcurrency,weight:8,stability:10}),navigator.deviceMemory&&e.push({key:"device_memory",value:navigator.deviceMemory,weight:8,stability:10}),navigator.maxTouchPoints!==void 0&&e.push({key:"touch_points",value:navigator.maxTouchPoints,weight:7,stability:9}),navigator.platform&&e.push({key:"platform",value:navigator.platform,weight:6,stability:8}),"oscpu"in navigator&&e.push({key:"os_cpu",value:navigator.oscpu,weight:5,stability:7})})}function Ri(e){return R(this,null,function*(){try{let t=document.createElement("canvas"),r=t.getContext("webgl")||t.getContext("experimental-webgl");if(!r)return;let n=r.getExtension("WEBGL_debug_renderer_info");if(n){let s=r.getParameter(n.UNMASKED_VENDOR_WEBGL);e.push({key:"gpu_vendor",value:s,weight:9,stability:10});let f=r.getParameter(n.UNMASKED_RENDERER_WEBGL);e.push({key:"gpu_renderer",value:f,weight:10,stability:10})}e.push({key:"max_texture_size",value:r.getParameter(r.MAX_TEXTURE_SIZE),weight:6,stability:9}),e.push({key:"max_viewport_dims",value:r.getParameter(r.MAX_VIEWPORT_DIMS).join("x"),weight:6,stability:9}),t.width=50,t.height=20,r.viewport(0,0,50,20),r.clearColor(.15,.3,.45,1),r.clear(r.COLOR_BUFFER_BIT);let o=new Uint8Array(4);r.readPixels(25,10,1,1,r.RGBA,r.UNSIGNED_BYTE,o);let i=`${o[0]}_${o[1]}_${o[2]}`;e.push({key:"webgl_pixel_test",value:i,weight:7,stability:8})}catch(t){}})}function Pi(e){if(!window.screen)return;let t=`${window.screen.width}x${window.screen.height}`;e.push({key:"screen_resolution",value:t,weight:8,stability:9}),e.push({key:"color_depth",value:window.screen.colorDepth,weight:6,stability:9}),e.push({key:"pixel_ratio",value:window.devicePixelRatio,weight:7,stability:8})}function Ii(e){try{let n=["audio/mp3","audio/ogg","audio/wav"],o=["video/mp4","video/webm","video/ogg"],i=document.createElement("audio"),s=document.createElement("video"),f=n.filter(p=>i.canPlayType(p)!=="").join("_"),m=o.filter(p=>s.canPlayType(p)!=="").join("_");e.push({key:"media_support",value:`${f}|${m}`,weight:4,stability:7})}catch(n){}let r=["Intl","WebAssembly","BigInt","SharedArrayBuffer","RTCPeerConnection","IntersectionObserver","ResizeObserver"].filter(n=>n in window).join("_");e.push({key:"supported_apis",value:r,weight:4,stability:7});try{let n=document.createElement("canvas");e.push({key:"canvas_support",value:!!n.getContext("2d"),weight:3,stability:8})}catch(n){}}function Ci(e){let t={chrome:"chrome"in window,msCredentials:"msCredentials"in window,MSInputMethodContext:"MSInputMethodContext"in window,InstallTrigger:"InstallTrigger"in window,safari:"safari"in window||"ApplePaySession"in window,Intl:typeof Intl!="undefined"?Object.keys(Intl).sort().join("_"):""};e.push({key:"browser_apis",value:JSON.stringify(t),weight:8,stability:7})}function Li(e){let t=["MozAppearance","WebkitAppearance","msTextSizeAdjust","scrollbarWidth","webkitCoverflowAdjust","webkitLineSnap"],r=document.documentElement,n=window.getComputedStyle(r),o=t.filter(i=>i in n).join("_");e.push({key:"css_features",value:o,weight:7,stability:7})}function Oi(e){return R(this,null,function*(){try{let t=JSON.stringify(e),r=new TextEncoder().encode(t),n=yield crypto.subtle.digest("SHA-256",r);return lr(n)}catch(t){return fr(JSON.stringify(e))}})}function Mi(e){return R(this,null,function*(){let t=document.createElement("canvas");t.width=100,t.height=40;let r=t.getContext("2d");if(r){r.textBaseline="alphabetic",r.fillStyle="#f60",r.fillRect(10,5,30,30),r.fillStyle="#069",r.font="15px Arial",r.fillText("BrowserID",15,25);let n=r.getImageData(0,0,100,40).data,o=[];for(let i=0;i<n.length;i+=41)o.push(n[i]);e.push({key:"browser_canvas",value:yield Oi(o),weight:8,stability:7})}})}function Di(e){return R(this,null,function*(){Ci(e),Li(e),yield Mi(e)})}function Ni(){return R(this,null,function*(){let e=[];return yield Promise.all([Ei(e),Ri(e),Pi(e),Ii(e),Di(e)]),e})}function Vi(e){return typeof e=="number"?e%1!==0?Number(e.toFixed(1)):e:typeof e=="string"?e.replace(/\s+[\d.]+(?:\.\d+)*(?:\w*)/g,""):e}function Re(e){return e===null||typeof e!="object"?String(e):Array.isArray(e)?`[${e.map(n=>Re(n)).join(",")}]`:`{${Object.keys(e).sort().map(n=>{let o=e[n];return`"${n}":${Re(o)}`}).join(",")}}`}function lr(e){let t=new Uint8Array(e),r="";for(let n=0;n<t.byteLength;n++)r+=String.fromCharCode(t[n]);return ur(r)}function fr(e){let t=new Uint8Array(32);for(let n=0;n<e.length;n++){let o=e.charCodeAt(n);if(t[n%32]=(t[n%32]+o)%256,n>0){let i=n*7%32;t[i]=(t[i]+o)%256}}for(let n=0;n<3;n++)for(let o=0;o<32;o++){let i=t[(o+31)%32],s=t[(o+1)%32];t[o]=(t[o]+i+s)%256}let r="";for(let n=0;n<t.byteLength;n++)r+=String.fromCharCode(t[n]);return ur(r)}function cr(e){return R(this,null,function*(){e.sort((r,n)=>r.key.localeCompare(n.key));let t=e.map(r=>({k:r.key,v:Vi(r.value)}));try{console.info("stableData",t);let r=Re(t),n=yield crypto.subtle.digest("SHA-256",new TextEncoder().encode(r));return lr(n)}catch(r){return fr(Re(t))}})}function dr(){return R(this,null,function*(){let e=yield Ni(),t=e.filter(r=>r.stability>=7);if(t.length<3){let r=e.filter(n=>n.stability>=5);return cr(r)}return cr(t)})}var pr=V(()=>{"use strict"});function ji(e,t){if(!t)return!0;let r=Number.parseInt(t,10);return Number.isNaN(r)?!0:e-r>7*24*60*60*1e3}function br(e,t){try{localStorage.setItem(e,t)}catch(r){console.info("[CONTEXT] storageSetItem error",r)}}function vr(e){try{return localStorage.getItem(e)}catch(t){return console.info("[CONTEXT] storageGetItem error",t),null}}function Wi(){return R(this,null,function*(){if(N)return N;let e=or(wr),t=vr(mr);N=e||t||"";let r=Date.now(),n=vr(hr),o=ji(r,n);if(!N||o)try{let i=yield dr();N=i&&Gi?`df${i}`:`dr${q()}`}catch(i){console.error("[CONTEXT] initDeviceId error",i),N=`dr${q()}`}return ar(wr,N,365,Fi),N&&(t!==N||o)&&(br(mr,N),br(hr,`${r}`)),N})}function yr(){return R(this,null,function*(){try{N=yield Wi()}catch(e){console.error("[CONTEXT] initContext error",e)}})}var mr,hr,gr,wr,Fi,N,Gi,Sr=V(()=>{"use strict";sr();pr();Ee();mr="gp_dvid",hr="gp_dvtm",gr=(()=>{switch(window.location.host.replace(/^h5\.|(\.)?g123\.jp$/g,"")){case"local":return{suffix:"_local",domain:".local.g123.jp"};case"stg":return{suffix:"_staging",domain:".stg.g123.jp"};default:return{suffix:"",domain:".g123.jp"}}})(),wr=`__gp_dvid${gr.suffix}`,Fi=gr.domain,Gi=(()=>{try{let e="gp_test";return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(e){return!1}})()});function Bi(){var e;pe||me||Ie||Z||(pe=document.getElementById("splash")||void 0,me=document.getElementById("iframe-game")||void 0,Ie=document.getElementById("splash-progress-container")||void 0,Z=((e=document.getElementById("splash-progress"))==null?void 0:e.querySelector("span"))||void 0)}function Zi(){pe&&(pe.style.opacity="1",pe.style.transition="opacity 500ms ease-in"),me&&(me.style.opacity="0",me.style.transition="opacity 500ms ease-out 500ms")}function Yi(){Ie&&Z&&(Ie.style.display="flex",Z.style.display="block",Z.style.transition="width 1000ms linear")}function xr(){return Z?Number.parseInt(Z.style.width):0}function Qe(e){if(Z){if(e!==0&&e<xr())return;Z.style.width=`${e}%`}}function zi(){if(!Z)return;Qe(0),Yi();let e=0,t=()=>{var n;if(e>=90||e<xr())return;let r=Math.floor(Math.random()*(Hi-Pe+1)+Pe);try{let o=((n=navigator.connection)==null?void 0:n.downlink)||0;o&&o<1&&(r=Math.floor(r*o),r<Pe&&(r=Pe))}catch(o){console.warn("[splash] Failed to get network information:",o)}e+=r,Qe(e),setTimeout(t,1e3)};setTimeout(t,1e3)}function _r(e){Bi(),Qe(0),Zi(),window.SPLASH_TIMER=setTimeout(zi,e)}function Ar(){let e=Date.now();console.info("[splash] initSplash",e);let t=!1;document.readyState==="loading"?document.addEventListener("readystatechange",()=>{if(document.readyState==="interactive"&&!t){t=!0;let r=Date.now()-e;_r(r>2e3?0:2e3-r)}}):t||(t=!0,_r(0))}var Pe,Hi,pe,me,Ie,Z,Tr=V(()=>{"use strict";Pe=Math.floor(3.2142857142857144),Hi=Math.floor(90/(8e3/1e3))});var Ki=J(Er=>{ir();Sr();Ke();Tr();Ee();window.__cloud_game_page_view_id__=q();window.__platform_init_time__=Date.now();var et=new URL(window.location.href);rr({action:"p_init"});Ar();window.option.appId==="arifure"?window.__cloud_game_enabled__=!0:nr();var Xi=()=>{var e,t;return!!(((e=window.navigator)==null?void 0:e.standalone)===!0||(t=window.matchMedia)!=null&&t.call(window,"(display-mode: standalone)").matches)};function Ui(){return/android/i.test(window.navigator.userAgent)}function Ji(e){var r;let t=e.searchParams.get("utm_source");return/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w.]+);)/i.test(window.navigator.userAgent)?"facebook":/\b(line)\/([\w.]+)\/iab/i.test(window.navigator.userAgent)||/YJApp/i.test(window.navigator.userAgent)?"":t==="maio"?"maio":t==="twitter"?"twitter":(r=e.searchParams.get("utm_campaign"))!=null&&r.includes("NewDisplayAndApp")?"campaign":""}function $i(e,t){if(!t)return;let r="from_native";if(e.searchParams.get(r))return;e.searchParams.set(r,t);let o=`${e.href.replace("https://","intent://")}#Intent;scheme=https;S.browser_fallback_url=${e.href};end`;window.location.replace(o)}(()=>{try{let e=et;if(Ui()){let t=Ji(e);t&&$i(e,t)}}catch(e){console.error(e)}})();window.perf||(window.perf={});window.perf.start=new Date().getTime();function qi(){let{stage:e}=window.option,{appId:t}=window.option,r=[`__Secure-STAGE-${t}=${e.toLowerCase()}`,"Secure","path=/"];(!e||e==="PUBLISHED")&&r.push("Max-Age=0"),document.cookie=r.join("; ")}try{qi()}catch(e){console.error(e)}var kr;if(Xi())try{let e="g123_game:pwa_saved_lang",t=(kr=window.option)==null?void 0:kr.appId,r=localStorage.getItem(`${e}:${t}`);if(r){let n=et,o=n.searchParams.get("lang");r!==o&&(n.searchParams.set("lang",r),window.history.replaceState(null,"",n.href))}}catch(e){console.error("PWA_RESTORE_LANG_ERROR",e)}typeof fetch=="function"&&typeof URL!="undefined"&&!window.__cloud_game_enabled__&&(window.prefetchSession=R(null,null,function*(){yield yr();let e={credentials:"same-origin",cache:"no-store"};navigator.standalone&&window.location.hash&&(e.headers={"x-session-key":decodeURIComponent(window.location.hash)});let t=new URL(`${window.location.origin}/api/v1/session?appId=${window.option.appId}`);window.option.code||t.searchParams.set("from",window.location.href);let r=et.searchParams.get("lang");return r&&t.searchParams.set("lang",r),fetch(t.href,e).then(n=>{if(!n.ok)throw n;return n.json()}).catch(n=>{console.error(n),window.prefetchSession=void 0})}))});Ki();})();

    </script>
    
    <style>
      @layer base{*,::backdrop,::file-selector-button,:after,:before{border:0 solid;box-sizing:border-box;margin:0;padding:0}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:--theme(--default-font-family,ui-sans-serif,system-ui,sans-serif,&#34;Apple Color Emoji&#34;,&#34;Segoe UI Emoji&#34;,&#34;Segoe UI Symbol&#34;,&#34;Noto Color Emoji&#34;);font-feature-settings:--theme(--default-font-feature-settings,normal);font-variation-settings:--theme(--default-font-variation-settings,normal);-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-tap-highlight-color:transparent}hr{border-top-width:1px;color:inherit;height:0}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:--theme(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,&#34;Liberation Mono&#34;,&#34;Courier New&#34;,monospace);font-feature-settings:--theme(--default-mono-font-feature-settings,normal);font-size:1em;font-variation-settings:--theme(--default-mono-font-variation-settings,normal)}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{border-collapse:collapse;border-color:inherit;text-indent:0}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}menu,ol,ul{list-style:none}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{height:auto;max-width:100%}::file-selector-button,button,input,optgroup,select,textarea{background-color:transparent;border-radius:0;color:inherit;font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;opacity:1}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::-moz-placeholder{opacity:1}::placeholder{opacity:1}@supports (not (-webkit-appearance:-apple-pay-button)) or (contain-intrinsic-size:1px){::-moz-placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-meridiem-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-year-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}::file-selector-button,button,input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}}@layer base{*,::backdrop,::file-selector-button,:after,:before{border-color:var(--color-gray-200,currentColor)}}body,html{font-size:16px;-webkit-font-smoothing:antialiased;height:100%;inset:0;overflow:hidden;position:fixed;width:100%}body,html,iframe{touch-action:none}body{background-color:#000}.skip-link{background:#000;color:#fff;left:0;padding:8px;position:absolute;top:-40px;z-index:100}.skip-link:focus{top:0}#splash{align-items:center;background-color:#136c72;display:flex;height:100%;justify-content:center;inset:0;position:absolute;width:100%;z-index:-100}#splash img,#splash svg{height:auto;width:200px}#splash-progress-container{align-items:flex-end;display:none;flex-direction:row;height:180px;left:auto;position:absolute;top:auto;width:360px}#splash-progress{background-color:#ffffff40;border-radius:4px;height:8px;overflow:hidden;width:100%}#splash-progress span{background-color:#e3ff34;background-size:100vw;border-radius:3px;box-shadow:inset 0 1px #ffffff80;display:block;height:100%;position:relative;transition:width 1s ease-in;width:0}@media screen and (max-width:576px){#splash img,#splash svg{width:130px}#splash-progress-container{height:126px;width:280px}}#iframe-game{border:0;display:block;height:100%;opacity:0;width:100%}@media screen and (orientation:landscape){#header{width:100%}}.seo-description-header-1{border-width:0;height:1px;margin:-1px;overflow:hidden;overflow-wrap:normal!important;padding:0;position:absolute!important;width:1px;word-break:normal!important;clip:&#34;rect(1px, 1px, 1px, 1px)&#34;;clip-path:&#34;inset(50%)&#34;}.hidden{display:none!important}#open-with-default-browser-top{background-color:#000c;display:none;inset:0;position:fixed;z-index:10000}#open-with-default-browser-top .imgs{float:right}#open-with-default-browser-top .imgs .main{margin-top:21px;width:218px}#open-with-default-browser-top .imgs .close{position:absolute;right:190px;top:10px;width:41px}#open-with-default-browser-bottom{background-color:#000c;display:none;inset:0;position:fixed;z-index:10000}#open-with-default-browser-bottom .imgs{bottom:0;position:absolute;right:0}#open-with-default-browser-bottom .imgs .main{width:218px}#open-with-default-browser-bottom .imgs .close{position:absolute;right:190px;top:-15px;width:41px}.CustomerSupportBox{bottom:4rem;height:650px;position:fixed;right:1rem;width:375px;z-index:var(--z-index-gbutton)}@media only screen and (max-width:576px){.CustomerSupportBox{height:100%;left:0;opacity:.95;position:absolute;top:0;width:100%}}.close{border:0 solid #fff;border-radius:50%;box-sizing:border-box;cursor:pointer;height:43px;position:absolute;right:0;top:0;width:43px}.close:before{transform:rotate(45deg)}.close:after,.close:before{background-color:#333a41;content:&#34;&#34;;height:2px;left:50%;margin-left:-9px;margin-top:-1px;position:absolute;top:50%;width:18px}.close:after{transform:rotate(-45deg)}

    </style>
      
    <script
      type="module"
      src="https://platform-sc.g123.jp/game/production/assets/app-rLJ3tKt9.js"
    ></script>
    
    <script
      type="module"
      src="https://platform-sc.g123.jp/game/production/assets/game-1db2353a-OPVs455c.js"
    ></script>
    
    <script
      type="module"
      src="https://platform-sc.g123.jp/game/production/assets/game-fd4578d2-CJjC28Jv.js"
    ></script>
    
    <script
      type="module"
      src="https://platform-sc.g123.jp/game/production/assets/game-d8b296a6-D6-XlEtG.js"
    ></script>
    

    <!-- VITE -->
    
    <style>
      .g123App {
        display: none;
      }
    </style>
  </head>
  <body>
    <h1 class="seo-description-header-1">平凡職業造就世界最強 反叛之魂</h1>
    <img
      src="/stats?k=perf&amp;t=init&amp;a=arifure&amp;d=20250806-76521dd&amp;img=1"
      style="
        width: 1px;
        height: 1px;
        position: absolute;
        left: -100px;
        top: -100px;
        opacity: 0;
      "
      aria-hidden="true"
    />
    <a class="skip-link" href="#iframe-game">Skip to main</a>
    <!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-PC66Z3W&gtm_auth=WBqlAWBzcGUtd2GJM6EsTg&gtm_preview=env-2&gtm_cookies_win=x"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
    <div id="splash">
      <img
        src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyMDAgNjYiPjxwYXRoIGZpbGw9IiNFM0ZGMzQiIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0ibTIzIDE1IDMtMWgyNFYxSDI0Yy0zIDAtNyAyLTkgNEw0IDE1Yy0zIDMtNCA2LTQgOXYzMmMwIDUgNSAxMCAxMCAxMGg0NFY0MGMwLTItMS01LTMtN2wtMTAtOS05IDlhNDg4MTQgNDg4MTQgMCAwIDAgOSAxMHYxMUgxNGwtMS0yVjI2bDEtMyA5LThabTQzIDEgMSAzdjQ3aDEzVjE2YzAtMi0xLTUtMy03TDY3IDBsLTkgOSA4IDdabTU0LTJoMmw2IDYgMSAydjRMOTYgMzljLTQgMi02IDUtNiA5djE4aDUzVjU0aC00MHYtM2wxLTIgMzItMTJjMy0yIDYtNSA2LTl2LTljMC0yLTEtNS0zLTZsLTktOWMtMi0yLTUtMy03LTNIOTF2MTNoMjlabTYyIDBoMmEzNjc3IDM2NzcgMCAwIDAgMyA0djEyaDEzVjE2YzAtMy0xLTUtMy03bC01LTVjLTItMi01LTMtNy0zaC0zNHYxM2gzMVoiIGNsaXAtcnVsZT0iZXZlbm9kZCIvPjxwYXRoIGZpbGw9IiNFM0ZGMzQiIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0ibTE4NyA1MC0zIDMtMiAxaC0zMnYxMmgzNWMyIDAgNS0xIDctM2w1LTVjMi0yIDMtNCAzLTd2LTJjMC0zLTEtNS0zLTdsLTE3LTE2LTEwIDggMTcgMTZaIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4="
        alt="G123"
        width="200"
        height="66"
      />
      <div id="splash-progress-container">
        <div id="splash-progress">
          <span></span>
        </div>
      </div>
    </div>
    <iframe
      id="iframe-game"
      allow="autoplay; screen-wake-lock"
      src="about:blank"
      title="平凡職業造就世界最強 反叛之魂 | 開始遊戲 - G123"
      style="touch-action: none"
    ></iframe>

    
    
    <iframe
      src="https://g123.jp/termly-consent-sync.html"
      style="display: none"
    ></iframe>

    <div id="open-with-default-browser-top">
      <div class="inner">
        <div class="imgs">
          <div style="position: relative">
            <span class="main"></span>
            <img
              alt="關閉"
              loading="lazy"
              class="close"
              src="https://platform-sc.g123.jp/game/production/img/clear-button.png"
            />
          </div>
        </div>
      </div>
    </div>
    <div id="open-with-default-browser-bottom">
      <div class="inner">
        <div class="imgs">
          <div style="position: relative">
            <span class="main"></span>
            <img
              alt="關閉"
              loading="lazy"
              class="close"
              src="https://platform-sc.g123.jp/game/production/img/clear-button.png"
            />
          </div>
        </div>
      </div>
    </div>
    <script>
      (function () {
        try {
          if (
            navigator.standalone &&
            window.matchMedia('(orientation: portrait)').matches
          ) {
            var splash = document.getElementById('splash');
            if (splash) {
              splash.style.transform =
                'translateY(-' +
                (window.screen.availHeight - window.innerHeight) / 2 +
                'px)';
            }
          }
        } catch (err) {}
      })();
    </script>
  </body>
</html>
