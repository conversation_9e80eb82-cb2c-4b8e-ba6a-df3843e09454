[1, ["cb1oWWMORD+4OS4th8cbY2@f9941", "6fyCV8EPxFPqzUrKP7RT3k@f9941", "59kOVJaLpOIalZ/4Xd3NgB", "452UFDwKpE4bt1Cz4gi+AT", "a8cxGqybtJGbyA1xM+yr6w@6c48a", "2bqoSnHt9NeJWhWHl4+W96", "f28HUhyqRHHKbqjFyhgzH9@f9941", "03b1pJ5DtMCIDN2E0plpZs@f9941", "9eqPkL9mhA6YFtq5FVdLrZ@f9941", "6fTC0HCGlJqYQcrdEzq9zk@f9941", "57TVhXLx9EcbwrJXsxDvaN@f9941", "97ZFBd01RNdYwCnWnrpqC8@f9941", "2112wG6KpDs6YEV51xMGny@f9941", "388m/lvPhKnbUnqte4IpN2@f9941", "28Q0xYmUZJhK1J8EBugU7q@f9941", "8aORR6phJB4rRZNVpu9/t5@f9941", "adiDL4L1dM/JtzbYekpyuo@f9941", "c0htl03FBJzJH8Cwj/dAzq@f9941", "4dZ2nv7C1Cuod5loET6XBB", "02VZWJ1gBI66fiGihJBOhf@f9941", "c3I7F0S05G7bu0W4BhssIr", "afYELPmONG3Z18jVDPuYpm@f9941", "e6SqskppJIcIuiQ90fa2XF@f9941", "cfahxI8DhJh4QdJtgvQmCe@f9941", "b1bkGoEDBO+rIs97+JY1qr@f9941", "2bZheqhsZMk6ANIxWBoJkQ", "7dj5uJT9FMn6OrOOx83tfK@f9941", "8beYaUvU5MGryyNPt1jW5q", "72JskoxJFIsqirpD88GaaU", "42vjPYcP5Bw4gsjqIOdeJ0", "b7jaebNkdJhr7OIIsyqLhp", "b09jPMSbBNuZyyNqf9NqtJ@f9941", "24PMKEMeJMaquoK0NXJVHq@f9941", "38fjbRunxL/ZV9M4e2zsF0", "dbW/lk1ZhBmarrOUoKsq6p", "6dJA/TI2tDaIjagi1yjUxK", "9fJMC59flMu6xlQRlB50oN@6c48a", "80TUjPz1xPG5JmWhrNFStf@f9941", "afDmzuFXBJwbGiukULnK5F@f9941", "7aXXrPwEJMTLi7629Atrro@f9941", "26O2ERo3lJsozK2+5d44A1@f9941", "6bt+C+mL9Ifaw5/8xL7Azt", "81c75mN89BQoQgIAKayYeF@f9941", "b0npEaocVKQZe7wW0GMNiV", "5cLfNhFu5GybughzK+ZXtR@f9941", "b5dXI3TRBMu64yHcnfzKgb", "e1/awd9CBHg7dbImhbY4xB", "ddv/lXan5I3KYzfND0FJ7v@f9941", "86d19W76BNirOVPBZs/NZd", "1bLsfU2HZKyoHfo3X7/1vA", "bfulNgOC9MVKl5erBrAZ3d@f9941", "47KUH3OD1I5aJI/bWEQ+8V@f9941", "1dZWq1YxxHQaOTP79Wb+bm@f9941", "acq3D7YdFHsJAi+FfhJKyf@6c48a"], ["node", "targetInfo", "_spriteFrame", "target", "source", "root", "_skeletonData", "asset", "data", "_defaultClip", "_texture", "_parent", "equipEffectNode", "item_icon", "kapai_di", "emptyNode", "roadAni", "soldierPrefab", "bulletPrefab", "dropAward", "_target", "ani_buff", "ani_buzhen", "ani_hero", "layout_xx2", "heroImg", "ani_action_eff", "icon_mszb_xz1", "icon_mszb_suo", "icon_mszb_jia", "goldText", "goldNode", "lihui", "hpPro1", "hpPro", "ani_eff", "shieldHpTxt", "shieldHpSpr", "shieldNode", "homeDeadAni", "homeAni", "bg_msyc_qp1", "txt_ct_ms1", "ct_mask", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "playerHead", "duanweiSpr", "duanweiNode", "buff<PERSON><PERSON><PERSON>", "buffNode", "hpText", "hpBar"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_lpos", "_children", "_lscale", "_euler", "_lrot"], 0, 9, 4, 1, 5, 2, 5, 5, 5], ["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_active", "_prefab", "_components", "_parent", "_lpos", "_children", "_lscale"], -2, 4, 12, 1, 5, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "_fillRange", "__prefab", "node", "_spriteFrame", "_color"], -1, 4, 1, 6, 5], ["sp.Skeleton", ["_premultipliedAlpha", "_preCacheMode", "defaultSkin", "defaultAnimation", "loop", "node", "__prefab", "_skeletonData", "_sockets"], -2, 1, 4, 6, 9], ["cc.Label", ["_string", "_actualFontSize", "_enableOutline", "_isBold", "_outlineWidth", "_lineHeight", "_fontSize", "_overflow", "_horizontalAlign", "_enableWrapText", "node", "__prefab", "_outlineColor", "_color"], -7, 1, 4, 5, 5], ["cc.Widget", ["_alignFlags", "_originalHeight", "_top", "_bottom", "_originalWidth", "node", "__prefab"], -2, 1, 4], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_affectedByScale", "_paddingTop", "_paddingBottom", "node", "__prefab"], -3, 1, 4], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedComponents", "mountedChil<PERSON>n", "removedComponents"], 2, 1, 9, 9, 9, 9], ["cc.TargetOverrideInfo", ["propertyPath", "target", "targetInfo", "source"], 2, 1, 4, 1], ["cc.Animation", ["playOnLoad", "node", "__prefab", "_clips", "_defaultClip"], 2, 1, 4, 3, 6], ["937ads1wZJGGK3uwQxKqtYQ", ["node", "__prefab", "emptyNode"], 3, 1, 4, 1], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_target"], 1, 1, 4, 1], ["cc.MotionStreak", ["_fadeTime", "_minSeg", "_stroke", "_fastMode", "_preview", "node", "__prefab", "_color", "_texture"], -2, 1, 4, 5, 6], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "nestedPrefabInstanceRoots", "root", "instance", "targetOverrides", "asset"], 1, 1, 4, 9, 6], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetInfo", ["localID"], 2], ["cc.UIOpacity", ["node", "__prefab"], 3, 1, 4], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 9], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["7563fP+WuxL1p5rM8Q2NGlY", ["node", "__prefab", "spfs"], 3, 1, 4, 3], ["4d62clHKtBAgbNRAvhLo+j/", ["homes", "node", "__prefab", "warNode", "topTip", "roads", "bulletNode"], 2, 1, 4, 1, 1, 2, 1], ["2adb7pzF1dDXYLQ5KjAA0uv", ["node", "__prefab", "icon_mszb_jia", "icon_mszb_suo", "icon_mszb_xz1", "ani_action_eff", "heroImg", "layout_xx2", "ani_hero", "ani_buzhen", "ani_buff"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["sp.SkeletonData", ["_name", "_native", "_atlasText", "textureNames", "textures"], -1, 3], ["7f95fcXeNFIvbgMyIQu7nYs", ["node", "__prefab", "hpPro", "hpPro1", "lihui", "goldNode", "goldText"], 3, 1, 4, 1, 1, 1, 1, 1], ["cc.ProgressBar", ["_totalLength", "_progress", "node", "__prefab", "_barSprite"], 1, 1, 4, 1], ["cc.RichText", ["_lineHeight", "_string", "_fontSize", "_maxWidth", "node", "__prefab"], -1, 1, 4], ["sp.Skeleton.SpineSocket", ["path", "target"], 2, 1], ["cc.MountedChildrenInfo", ["targetInfo", "nodes"], 3, 4, 2], ["60c4f9f7QxN+by6e5X3gQBy", ["node", "__prefab", "posNode", "equipNode", "equipBtn", "ct_intr"], 3, 1, 4, 2, 1, 1, 1]], [[15, 0, 2], [17, 0, 1, 2, 3, 4, 5, 5], [7, 0, 1, 2, 1], [20, 0, 2], [25, 0, 1, 2, 2], [7, 0, 1, 2, 3, 1], [9, 0, 3, 1, 2, 2], [2, 5, 4, 6, 1], [24, 0, 1, 2, 3], [0, 0, 1, 5, 3, 4, 6, 8, 3], [9, 0, 1, 2, 2], [2, 5, 4, 1], [26, 0, 1, 2, 2], [1, 2, 3, 7, 5, 3], [2, 0, 5, 4, 6, 2], [19, 0, 1, 2, 3, 4, 5, 4], [14, 0, 2], [7, 0, 1, 1], [8, 0, 1, 2, 2], [1, 0, 1, 7, 6, 5, 8, 10, 3], [0, 0, 1, 5, 3, 4, 3], [0, 0, 1, 5, 7, 3, 4, 6, 3], [2, 1, 0, 5, 4, 6, 3], [21, 0, 1, 1], [1, 0, 2], [0, 0, 1, 5, 7, 3, 4, 3], [0, 0, 1, 5, 3, 4, 6, 3], [0, 0, 1, 5, 3, 4, 8, 3], [0, 0, 5, 7, 3, 4, 6, 9, 2], [7, 0, 1, 3, 1], [6, 6, 7, 1], [28, 0, 1, 2, 1], [4, 0, 1, 5, 7, 3, 2, 4, 10, 11, 13, 8], [35, 0, 1, 2], [1, 0, 1, 7, 6, 5, 8, 3], [1, 0, 1, 7, 6, 5, 10, 3], [0, 0, 5, 3, 4, 6, 2], [0, 0, 2, 5, 3, 4, 6, 9, 3], [27, 0, 1, 2, 3], [13, 4, 0, 1, 2, 3, 5, 6, 7, 8, 6], [1, 0, 1, 7, 6, 5, 3], [0, 0, 2, 1, 5, 3, 4, 6, 8, 4], [3, 2, 3, 0, 1, 5, 6, 7, 5], [3, 0, 1, 5, 6, 3], [1, 0, 1, 9, 6, 5, 3], [0, 0, 2, 1, 5, 7, 3, 4, 4], [0, 0, 2, 1, 5, 3, 4, 4], [0, 0, 2, 1, 5, 3, 4, 8, 4], [0, 0, 1, 7, 3, 4, 3], [2, 2, 0, 5, 4, 6, 3], [5, 0, 2, 3, 1, 5, 6, 5], [5, 0, 4, 1, 5, 6, 4], [10, 1, 2, 3, 4, 1], [10, 0, 1, 2, 3, 4, 2], [3, 2, 3, 0, 1, 4, 5, 6, 7, 6], [3, 0, 1, 4, 5, 6, 4], [4, 0, 8, 1, 6, 5, 7, 3, 2, 4, 10, 11, 12, 10], [4, 0, 1, 6, 5, 7, 9, 3, 2, 4, 10, 11, 10], [1, 0, 4, 1, 7, 6, 5, 8, 4], [1, 0, 1, 7, 9, 6, 5, 8, 10, 3], [2, 1, 5, 4, 6, 2], [2, 1, 3, 5, 4, 3], [2, 4, 1], [5, 0, 3, 5, 6, 3], [16, 0, 1, 2, 3, 4, 5, 3], [18, 0, 1, 2, 3, 4, 5, 3], [8, 0, 1, 3, 2, 2], [22, 0, 1, 1], [11, 0, 1, 1], [6, 0, 1, 2, 3, 6, 7, 5], [6, 0, 1, 4, 5, 3, 6, 7, 6], [3, 2, 3, 0, 1, 4, 5, 6, 6], [4, 0, 1, 6, 3, 2, 4, 10, 11, 12, 7], [31, 0, 1, 2, 3, 4, 5], [33, 0, 1, 2, 3, 4, 3], [34, 0, 1, 2, 3, 4, 5, 5], [1, 0, 9, 6, 5, 2], [1, 0, 7, 6, 5, 8, 2], [1, 0, 9, 6, 5, 8, 2], [1, 0, 7, 9, 6, 5, 2], [1, 2, 3, 5, 3], [1, 0, 7, 6, 5, 2], [0, 0, 5, 7, 3, 4, 2], [0, 0, 1, 7, 3, 4, 6, 3], [0, 0, 2, 1, 5, 3, 4, 6, 4], [0, 0, 2, 1, 5, 7, 3, 4, 6, 4], [0, 0, 1, 5, 7, 3, 4, 6, 8, 3], [0, 0, 1, 3, 4, 10, 9, 3], [0, 0, 2, 1, 5, 7, 3, 4, 6, 8, 4], [0, 0, 7, 3, 4, 2], [0, 0, 5, 3, 4, 2], [0, 0, 2, 5, 3, 4, 3], [0, 0, 5, 3, 4, 6, 8, 2], [2, 0, 5, 4, 7, 6, 2], [2, 1, 0, 5, 4, 7, 3], [2, 1, 0, 5, 4, 3], [5, 0, 1, 5, 6, 3], [5, 0, 2, 5, 6, 3], [5, 0, 5, 6, 2], [8, 0, 1, 4, 3, 2, 5, 2], [23, 0, 1, 1], [11, 0, 1, 2, 1], [6, 0, 2, 6, 7, 3], [6, 0, 1, 6, 7, 3], [3, 0, 5, 6, 2], [3, 2, 0, 1, 5, 6, 7, 4], [3, 2, 3, 0, 1, 4, 5, 6, 8, 7, 6], [4, 0, 1, 6, 5, 2, 10, 11, 6], [4, 0, 1, 6, 5, 3, 2, 4, 10, 11, 12, 8], [4, 0, 8, 1, 5, 7, 3, 2, 4, 10, 11, 12, 9], [4, 0, 1, 6, 7, 9, 3, 2, 4, 10, 11, 9], [29, 0, 1, 2, 3, 4, 5, 6, 2], [12, 0, 1, 2, 3, 4, 3], [12, 0, 1, 2, 3, 3], [30, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [32, 0, 1, 2, 3, 4, 5, 6, 1], [13, 0, 1, 2, 3, 5, 6, 7, 8, 5], [36, 0, 1, 1], [37, 0, 1, 2, 3, 4, 5, 1]], [[[[16, "DTCtrlLogic"], [76, "DTCtrlLogic", [-14, -15, -16, -17, -18], [[-10, [2, -11, [0, "563p5K0FlEhINroTeMWFRE"], [5, 640, 1280]], [93, 0, -12, [0, "d49SlpgWFLgqSzF5C8tmy2"], [4, 4278913804], 47], [96, 5, 960, -13, [0, "afiNd4N65Ng60sXqlntgNl"]]], 1, 4, 4, 4], [64, "c46/YsCPVOJYA4mWEpNYRx", null, -9, 0, [[10, ["homes", "0"], -3, [3, ["1dDi2NdhdARpoa0StaJhoq"]]], [10, ["homes", "1"], -4, [3, ["1dDi2NdhdARpoa0StaJhoq"]]], [6, ["homes", "0"], -6, -5, [3, ["1dDi2NdhdARpoa0StaJhoq"]]], [6, ["homes", "1"], -8, -7, [3, ["1dDi2NdhdARpoa0StaJhoq"]]]], [-1, -2]]], [44, "item1", 33554432, [-23, -24, -25, -26, -27, -28, -29, -30], [[[2, -19, [0, "800U7Nj4lFVZTNPOORHgRn"], [5, 113, 115]], [52, -20, [0, "627EWxKghLx6uRo4WDrsj9"], [21], 22], [23, -21, [0, "99iHfiq7hGupwA+5mGvVOo"]], -22], 4, 4, 4, 1], [1, "58m892tA5BQagOFZNqC4iA", null, null, null, 1, 0]], [44, "item1", 33554432, [-35, -36, -37, -38, -39, -40, -41, -42], [[[2, -31, [0, "800U7Nj4lFVZTNPOORHgRn"], [5, 113, 115]], [52, -32, [0, "627EWxKghLx6uRo4WDrsj9"], [45], 46], [23, -33, [0, "99iHfiq7hGupwA+5mGvVOo"]], -34], 4, 4, 4, 1], [1, "58m892tA5BQagOFZNqC4iA", null, null, null, 1, 0]], [82, "war", 1, [-44, -45, -46, -47, -48, -49, -50], [[2, -43, [0, "bc5CSKmjZCgYkMm42RiOZk"], [5, 640, 1440]]], [1, "fe1VYb/LZPy6nkZTk8QV3K", null, null, null, 1, 0]], [13, 0, {}, 4, [65, "61/02t6o9GyKW+P/LzuvXn", null, -90, [66, "419NW9KCNLNJPnSsN7kXBH", 1, [[67, [3, ["61/02t6o9GyKW+P/LzuvXn"]], [[97, 1, 124.01996337890628, -89, [0, "56HsYMlEBJWpxrSu9DWzwE"]]]]], [[8, "DTHome_up", ["_name"], -65], [4, ["_lpos"], -66, [1, 0, 273.88, 0]], [4, ["_lrot"], -67, [3, 0, 0, 0, 1]], [4, ["_euler"], -68, [1, 0, 0, 0]], [4, ["_contentSize"], -69, [5, 751.0001220703125, 644.2000732421875]], [4, ["_anchorPoint"], -70, [0, 0.5, 0.5]], [4, ["_lpos"], -71, [1, -217.67486572265625, 496.6901550292969, 0]], [4, ["_lscale"], -72, [1, 0.8706994652748108, 0.8706994652748241, 1]], [4, ["_lpos"], -73, [1, -130.60491943359375, 496.6901550292969, 0]], [4, ["_lscale"], -74, [1, 0.8706994652748108, 0.8706994652748241, 1]], [4, ["_lpos"], -75, [1, -43.53497314453125, 496.6901550292969, 0]], [4, ["_lscale"], -76, [1, 0.8706994652748108, 0.8706994652748241, 1]], [4, ["_lpos"], -77, [1, 43.53497314453125, 496.6901550292969, 0]], [4, ["_lscale"], -78, [1, 0.8706994652748108, 0.8706994652748241, 1]], [4, ["_lpos"], -79, [1, 130.60491943359375, 496.6901550292969, 0]], [4, ["_lscale"], -80, [1, 0.8706994652748108, 0.8706994652748241, 1]], [4, ["_lpos"], -81, [1, 217.67486572265625, 496.6901550292969, 0]], [4, ["_lscale"], -82, [1, 0.8706994652748108, 0.8706994652748241, 1]], [8, true, ["_active"], -83], [12, ["_lpos"], [3, ["29aob3p5lNiY4RxOZ5zM9o"]], [1, 0, -203.569, 0]], [4, ["_contentSize"], -84, [5, 100, 100]], [4, ["_anchorPoint"], -85, [0, 0.5, 0.19403909622501106]], [8, null, ["_skeletonData"], -86], [8, "", ["defaultSkin"], -87], [8, "", ["defaultAnimation"], -88], [12, ["_lpos"], [3, ["69xyN9cYhIWK4CcjvDqy6j"]], [1, -252, -15.906, 0]], [12, ["_lpos"], [3, ["a2PqZ3mHpL/rhHmA0GAygn"]], [1, 0, 114.094, 0]], [12, ["_lpos"], [3, ["78OueR3pROerNIiAz4Dkeu"]], [1, 0, 142.056, 0]], [38, true, ["_enabled"], [3, ["a1JmNUxxdDFrkOy5B6HftI"]]], [12, ["_lpos"], [3, ["dftocmtS9DAJfKLBsfUTsV"]], [1, 0, 68.497, 0]]]], [[10, ["playerHead"], -51, [3, ["cdSAz8tiZPwYFWQVGKww65"]]], [10, ["<PERSON><PERSON><PERSON><PERSON>"], -52, [3, ["34goCFzhxCNaeCDZ72IZKB"]]], [10, ["equipNode"], -53, [3, ["58m892tA5BQagOFZNqC4iA"]]], [6, ["kapai_di"], -55, -54, [3, ["30SwXnGDZOzJhfcbZFqmvt"]]], [6, ["item_icon"], -57, -56, [3, ["4eDwtJcn1JlYXC2pBZ9vb6"]]], [10, ["ct_mask"], -58, [3, ["01uGYywOpN3qoZT3jlgVRN"]]], [10, ["txt_ct_ms1"], -59, [3, ["ce7cTEtElCX6fJKAd4lF2n"]]], [10, ["bg_msyc_qp1"], -60, [3, ["b4H+PVXjRF9r02JfaLqJAg"]]], [6, ["emptyNode"], -62, -61, [3, ["76/iOpIwxI/4fb3ZDzeow4"]]], [6, ["equipEffectNode"], -64, -63, [3, ["5dZVgsaRNB5p+DYvanNQGd"]]]], 2]], [24, "New Node"], [13, 0, {}, 4, [65, "61/02t6o9GyKW+P/LzuvXn", null, -112, [66, "0dpptdWaNPvarryrjfTMle", 1, [[67, [3, ["61/02t6o9GyKW+P/LzuvXn"]], [[63, 4, 141.52988916015624, -111, [0, "baX1odzONP8ILFAmyPseG7"]]]]], [[8, "DTHome_down", ["_name"], -105], [4, ["_lpos"], -106, [1, 0, -256.394, 0]], [4, ["_lrot"], -107, [3, 0, 0, 0, 1]], [4, ["_euler"], -108, [1, 0, 0, 0]], [4, ["_contentSize"], -109, [5, 751, 644.1522216796875]], [4, ["_anchorPoint"], -110, [0, 0.5, 0.5]], [12, ["_lpos"], [3, ["29aob3p5lNiY4RxOZ5zM9o"]], [1, 0, 269.664, 0]], [12, ["_lpos"], [3, ["719xGdtKlHt4uAVql4is8n", "79BcNar8tDmZucBHLjoa8/"]], [1, 250, -152.695, 0]], [12, ["_lpos"], [3, ["78OueR3pROerNIiAz4Dkeu"]], [1, 0, -37.72, 0]], [12, ["_lpos"], [3, ["a2PqZ3mHpL/rhHmA0GAygn"]], [1, 0, -65.682, 0]], [12, ["_lpos"], [3, ["69xyN9cYhIWK4CcjvDqy6j"]], [1, -252, -177.175, 0]]]], [[10, ["playerHead"], -91, [3, ["cdSAz8tiZPwYFWQVGKww65"]]], [10, ["<PERSON><PERSON><PERSON><PERSON>"], -92, [3, ["34goCFzhxCNaeCDZ72IZKB"]]], [10, ["equipNode"], -93, [3, ["58m892tA5BQagOFZNqC4iA"]]], [6, ["kapai_di"], -95, -94, [3, ["30SwXnGDZOzJhfcbZFqmvt"]]], [6, ["item_icon"], -97, -96, [3, ["4eDwtJcn1JlYXC2pBZ9vb6"]]], [10, ["ct_mask"], -98, [3, ["01uGYywOpN3qoZT3jlgVRN"]]], [10, ["txt_ct_ms1"], -99, [3, ["ce7cTEtElCX6fJKAd4lF2n"]]], [10, ["bg_msyc_qp1"], -100, [3, ["b4H+PVXjRF9r02JfaLqJAg"]]], [6, ["emptyNode"], -102, -101, [3, ["76/iOpIwxI/4fb3ZDzeow4"]]], [6, ["equipEffectNode"], -104, -103, [3, ["5dZVgsaRNB5p+DYvanNQGd"]]]], 26]], [24, "New Node"], [3, ["61/02t6o9GyKW+P/LzuvXn"]], [68, 2, [0, "085jt+4N9BWIMPoLcDWADM"]], [45, "pj_kuang_hui1", false, 33554432, 2, [-115, -116], [[2, -113, [0, "d3AtRMiJNCOYRAaUItr2qu"], [5, 110, 110]], [14, 0, -114, [0, "31IP5pa1VA76/aggSUz5Pz"], 10]], [1, "c91yTbP25N/4XFQDypTQOW", null, null, null, 1, 0]], [46, "kapai_xz1", false, 33554432, 2, [[2, -117, [0, "234+0KgaRKP4Oyw5E8wuzg"], [5, 135, 136]], [22, 1, 0, -118, [0, "d65WkruK9JBLqnkpusf4Fa"], 16], [53, true, -119, [0, "82d/HULiVNnIe/ZM8Xe+wN"], [17], 18], [23, -120, [0, "02ZF5BEgBK7aQ7v73tkrjm"]]], [1, "d6i3dsjTBAsYIwkgrHXPK3", null, null, null, 1, 0]], [25, "pj_kuang_hui2", 33554432, 2, [-123], [[2, -121, [0, "c7FZkiIlBBF71NSwy8Pk/K"], [5, 113, 115]], [7, -122, [0, "c2j/kZIkFKipkBz62pzOPD"], 19]], [1, "76/iOpIwxI/4fb3ZDzeow4", null, null, null, 1, 0]], [36, "DTRoad1", 4, [[5, -124, [0, "61ky5Ceh9DgrX4/zwDi0+f"], [5, 160, 576.973], [0, 0.5, 0]], [49, false, 0, -125, [0, "42rD3v2nRBk7JQ+eKnYA+C"], 23], [50, 5, 400, 463.027, 469, -126, [0, "0015tpa9FHv4bVssZ6qHfX"]]], [1, "df+mD/+sZLsLAiJ0yZDUN5", null, null, null, 1, 0], [1, -163.092, -256.973, 0]], [36, "DTRoad2", 4, [[5, -127, [0, "68azJtSqdA3pD+RpryvqL9"], [5, 160, 576.9730000000001], [0, 0.5, 0]], [49, false, 0, -128, [0, "98e6uubwFH1p5W5cxNs2by"], 24], [50, 5, 387.15999999999997, 475.86699999999996, 469, -129, [0, "a2SInfFg5JsLKEnrLtkIfi"]]], [1, "63h0KmnrxF2JyLYeWfBOt2", null, null, null, 1, 0], [1, 7.6720000000000255, -244.13300000000004, 0]], [36, "DTRoad3", 4, [[5, -130, [0, "e2/eIwX89ELrctEVw3ZRs5"], [5, 160, 576.973], [0, 0.5, 0]], [49, false, 0, -131, [0, "381jZ+9mZCdLqrJQ0QRcPx"], 25], [50, 5, 400, 463.027, 469, -132, [0, "b4kX5Vq01JxqBzdKkqsmCk"]]], [1, "b7r0B4hV5NO7nZNdzU4NQl", null, null, null, 1, 0], [1, 164.002, -256.973, 0]], [68, 3, [0, "085jt+4N9BWIMPoLcDWADM"]], [45, "pj_kuang_hui1", false, 33554432, 3, [-135, -136], [[2, -133, [0, "d3AtRMiJNCOYRAaUItr2qu"], [5, 110, 110]], [14, 0, -134, [0, "31IP5pa1VA76/aggSUz5Pz"], 34]], [1, "c91yTbP25N/4XFQDypTQOW", null, null, null, 1, 0]], [46, "kapai_xz1", false, 33554432, 3, [[2, -137, [0, "234+0KgaRKP4Oyw5E8wuzg"], [5, 135, 136]], [22, 1, 0, -138, [0, "d65WkruK9JBLqnkpusf4Fa"], 40], [53, true, -139, [0, "82d/HULiVNnIe/ZM8Xe+wN"], [41], 42], [23, -140, [0, "02ZF5BEgBK7aQ7v73tkrjm"]]], [1, "d6i3dsjTBAsYIwkgrHXPK3", null, null, null, 1, 0]], [25, "pj_kuang_hui2", 33554432, 3, [-143], [[2, -141, [0, "c7FZkiIlBBF71NSwy8Pk/K"], [5, 113, 115]], [7, -142, [0, "c2j/kZIkFKipkBz62pzOPD"], 43]], [1, "76/iOpIwxI/4fb3ZDzeow4", null, null, null, 1, 0]], [34, "kapai_di", 33554432, 2, [[[2, -144, [0, "83Iz4xnIJBp4B4NrxAK6Pg"], [5, 110, 112]], [14, 0, -145, [0, "e3MbdbnGpLSJv7jZQUGKXO"], 3], -146], 4, 4, 1], [1, "42Rk0WEWpD9J2OZPPe3K6X", null, null, null, 1, 0], [1, 0, -1, 0]], [35, "item_icon", 33554432, 2, [[[2, -147, [0, "0ciXTZfuFGvIhz57RrMKZw"], [5, 110, 110]], [30, -148, [0, "cfQr5+YJVLOqqnwVbtaBq0"]], -149], 4, 4, 1], [1, "b9Ux/7GgBHjY7TOGJJiEpi", null, null, null, 1, 0], [1, 0.9, 0.9, 1]], [41, "pj_kuang_sp1", false, 33554432, 2, [[2, -150, [0, "e4lauM7zFO4aX6clqVrRLz"], [5, 93, 90]], [14, 0, -151, [0, "9724Q2/XBDebIdwKApd6Kd"], 11], [31, -152, [0, "3cXBsYOTxFgb9BVVnSJlAo"], [12, 13, 14, 15]]], [1, "f51AN7O7FJgK4aYDFNvVXm", null, null, null, 1, 0], [1, -32, -32, 0], [1, 0.35, 0.35, 1]], [47, "ani_equip", false, 33554432, 2, [[2, -153, [0, "8cxIyJf61BNIMDrHZIPDdG"], [5, 120, 117]], [54, "default", "animation", false, 0, false, -154, [0, "5bcT+81TdJV4zYvJORitPP"], 20]], [1, "5dZVgsaRNB5p+DYvanNQGd", null, null, null, 1, 0], [1, 1.4, 1.4, 1]], [36, "bulletNode", 4, [[5, -155, [0, "3c6Ty6NmNJ+ZZ7gIDjclf9"], [5, 640, 1440], [0, 0.5, 0]], [51, 45, 160, 469, -156, [0, "e2HtNT3c1K8qZ/RR8y72Cm"]]], [1, "a5bxHuxc5G7YXYdfQtlzdB", null, null, null, 1, 0], [1, 0, -720, 0]], [3, ["61/02t6o9GyKW+P/LzuvXn"]], [34, "kapai_di", 33554432, 3, [[[2, -157, [0, "83Iz4xnIJBp4B4NrxAK6Pg"], [5, 110, 112]], [14, 0, -158, [0, "e3MbdbnGpLSJv7jZQUGKXO"], 27], -159], 4, 4, 1], [1, "42Rk0WEWpD9J2OZPPe3K6X", null, null, null, 1, 0], [1, 0, -1, 0]], [35, "item_icon", 33554432, 3, [[[2, -160, [0, "0ciXTZfuFGvIhz57RrMKZw"], [5, 110, 110]], [30, -161, [0, "cfQr5+YJVLOqqnwVbtaBq0"]], -162], 4, 4, 1], [1, "b9Ux/7GgBHjY7TOGJJiEpi", null, null, null, 1, 0], [1, 0.9, 0.9, 1]], [41, "pj_kuang_sp1", false, 33554432, 3, [[2, -163, [0, "e4lauM7zFO4aX6clqVrRLz"], [5, 93, 90]], [14, 0, -164, [0, "9724Q2/XBDebIdwKApd6Kd"], 35], [31, -165, [0, "3cXBsYOTxFgb9BVVnSJlAo"], [36, 37, 38, 39]]], [1, "f51AN7O7FJgK4aYDFNvVXm", null, null, null, 1, 0], [1, -32, -32, 0], [1, 0.35, 0.35, 1]], [47, "ani_equip", false, 33554432, 3, [[2, -166, [0, "8cxIyJf61BNIMDrHZIPDdG"], [5, 120, 117]], [54, "default", "animation", false, 0, false, -167, [0, "5bcT+81TdJV4zYvJORitPP"], 44]], [1, "5dZVgsaRNB5p+DYvanNQGd", null, null, null, 1, 0], [1, 1.4, 1.4, 1]], [26, "topTip", 33554432, 1, [[5, -168, [0, "85kc3v96ZFk7q3XB2wUaIa"], [5, 640, 1280], [0, 0.5, 0.6756341722276475]], [51, 45, 807, 432, -169, [0, "d0/rNKN0NOuJvYe7qTGCXy"]]], [1, "35p+YxvdZBu6MhedRCK35e", null, null, null, 1, 0], [1, 0, 224.8117404513889, 0]], [20, "bg_mszb_1", 33554432, 1, [[2, -170, [0, "f2013L8TJPtoV+QmO6RBZe"], [5, 640, 1440]], [7, -171, [0, "ddnm8zccxNGJ/J9tUQ6jHf"], 0]], [1, "59zkm6wCBB0IR8Kww8Q9Lr", null, null, null, 1, 0]], [20, "ani_bg", 33554432, 1, [[5, -172, [0, "88puu8799C+Ksz7tqdAjgy"], [5, 807, 432], [0, 0.5, 0.6756341722276475]], [42, "default", "animation", false, 0, -173, [0, "a9maRVAvVIEqKnlt3kWRNP"], 1]], [1, "bewrhqcORDqKQrlVOC+UWr", null, null, null, 1, 0]], [3, ["5fQDOXkp9OTrUACUVsbaH7"]], [9, "txt_item_jstj1", 33554432, 11, [[2, -174, [0, "46ZZq382dFa7LcUcAkKils"], [5, 180, 60]], [32, "Lv.100", 40, 60, 2, true, true, 3, -175, [0, "bey4sFeBtPDaHbYh+oX/Qj"], [4, 4290953922]]], [1, "51g0VuoRpO8LzDHgEFWoXH", null, null, null, 1, 0], [1, 0, 14, 0], [1, 0.5, 0.5, 1]], [9, "txt_item_jstj2", 33554432, 11, [[2, -176, [0, "d5ute3KyVLmqZvSuEdvhFO"], [5, 180, 60]], [32, "解鎖", 40, 60, 2, true, true, 3, -177, [0, "27sDHsU/xAV5kINrQNKYPw"], [4, 4290953922]]], [1, "1cWi93TqRDt5YPaW9LXMQn", null, null, null, 1, 0], [1, 0, -14, 0], [1, 0.5, 0.5, 1]], [9, "txt_item_dj", 33554432, 2, [[5, -178, [0, "2a+NEz1hJH37Gy8QtP+C4Q"], [5, 220, 96], [0, 1, 0.5]], [56, "", 2, 49, 48, 70, 2, true, true, 3, -179, [0, "eeoXHNF6JHCZD795RQ9Goj"], [4, 4280098330]]], [1, "8ev1z6IH5OgYw4mQ5jIqFq", null, null, null, 1, 0], [1, 50, 38, 0], [1, 0.5, 0.5, 1]], [27, "Label", 33554432, 13, [[2, -180, [0, "8f3IFg2NNAG4yOOElubeXs"], [5, 190, 80]], [57, "未装备", 44, 44, 44, 2, false, true, true, 3, -181, [0, "b5pp/8WtFHzIvvbaSpdmBr"]]], [1, "acmUlVU9NJa6ipAv3wSqeV", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [24, "New Node"], [77, "DTRoad", 4, [[[5, -182, [0, "01ylcqGqVK7YleE7rrMryG"], [5, 554.85400390625, 1233.5419921875], [0, 0.588334894631101, 0.6347347141702431]], -183], 4, 1], [1, "e9Gn0/jpVI4JyQttFjeDOg", null, null, null, 1, 0], [1, 0, 70, 0]], [9, "txt_item_jstj1", 33554432, 18, [[2, -184, [0, "46ZZq382dFa7LcUcAkKils"], [5, 180, 60]], [32, "Lv.100", 40, 60, 2, true, true, 3, -185, [0, "bey4sFeBtPDaHbYh+oX/Qj"], [4, 4290953922]]], [1, "51g0VuoRpO8LzDHgEFWoXH", null, null, null, 1, 0], [1, 0, 14, 0], [1, 0.5, 0.5, 1]], [9, "txt_item_jstj2", 33554432, 18, [[2, -186, [0, "d5ute3KyVLmqZvSuEdvhFO"], [5, 180, 60]], [32, "解鎖", 40, 60, 2, true, true, 3, -187, [0, "27sDHsU/xAV5kINrQNKYPw"], [4, 4290953922]]], [1, "1cWi93TqRDt5YPaW9LXMQn", null, null, null, 1, 0], [1, 0, -14, 0], [1, 0.5, 0.5, 1]], [9, "txt_item_dj", 33554432, 3, [[5, -188, [0, "2a+NEz1hJH37Gy8QtP+C4Q"], [5, 220, 96], [0, 1, 0.5]], [56, "", 2, 49, 48, 70, 2, true, true, 3, -189, [0, "eeoXHNF6JHCZD795RQ9Goj"], [4, 4280098330]]], [1, "8ev1z6IH5OgYw4mQ5jIqFq", null, null, null, 1, 0], [1, 50, 38, 0], [1, 0.5, 0.5, 1]], [27, "Label", 33554432, 20, [[2, -190, [0, "8f3IFg2NNAG4yOOElubeXs"], [5, 190, 80]], [57, "未装备", 44, 44, 44, 2, false, true, true, 3, -191, [0, "b5pp/8WtFHzIvvbaSpdmBr"]]], [1, "acmUlVU9NJa6ipAv3wSqeV", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [24, "New Node"], [26, "award", 33554432, 1, [[5, -192, [0, "24iDfm6khM5psEgRFGEbhZ"], [5, 640, 1280], [0, 0.5, 0.6756341722276475]], [51, 45, 807, 432, -193, [0, "b41aKqPJ5KyrQRepL3LB5Q"]]], [1, "52m+wagFxCT5+SiJ5yt/LV", null, null, null, 1, 0], [1, 0, 224.8117404513889, 0]], [111, [null, null], 1, [0, "039iUgdyRHB5UZ0n+XorEN"], 4, 31, [14, 15, 16], 25], [3, ["94/tRUAu5CLKnkqVT2WQfg"]], [3, ["c3FWXozQ5DPq/hAiYhWFXU", "eapMGiC3lGKbzPrEhU/yJw"]], [3, ["91AOoRyjBEn4/JLt//AJCR", "eapMGiC3lGKbzPrEhU/yJw"]], [3, ["77Z7monldFCZ0ObDTmbTvr", "eapMGiC3lGKbzPrEhU/yJw"]], [3, ["a7k1SUvvpLErTqfG+3H2fU", "eapMGiC3lGKbzPrEhU/yJw"]], [3, ["c8uX8KaHtHVrE7jK0t1yMK", "eapMGiC3lGKbzPrEhU/yJw"]], [3, ["d4UXDAu75HVqNHE2k1P3zX", "eapMGiC3lGKbzPrEhU/yJw"]], [3, ["3eoR0cSqlLb6AXeIQrlVun"]], [24, "New Node"], [31, 21, [0, "30SwXnGDZOzJhfcbZFqmvt"], [4, 5, 6, 7, 8, 9]], [11, 22, [0, "4eDwtJcn1JlYXC2pBZ9vb6"]], [71, "default", "into", false, 0, false, 40, [0, "41al0ATmVEbqsNkQU8axML"]], [3, ["94/tRUAu5CLKnkqVT2WQfg"]], [24, "New Node"], [31, 27, [0, "30SwXnGDZOzJhfcbZFqmvt"], [28, 29, 30, 31, 32, 33]], [11, 28, [0, "4eDwtJcn1JlYXC2pBZ9vb6"]]], 0, [0, -1, 7, 0, -2, 5, 0, 3, 5, 0, 3, 7, 0, 3, 5, 0, 4, 47, 0, 3, 7, 0, 4, 47, 0, 5, 1, 0, -1, 47, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 32, 0, -2, 33, 0, -3, 4, 0, -4, 31, 0, -5, 46, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -4, 10, 0, -1, 21, 0, -2, 22, 0, -3, 11, 0, -4, 37, 0, -5, 23, 0, -6, 12, 0, -7, 13, 0, -8, 24, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -4, 17, 0, -1, 27, 0, -2, 28, 0, -3, 18, 0, -4, 43, 0, -5, 29, 0, -6, 19, 0, -7, 20, 0, -8, 30, 0, 0, 4, 0, -1, 5, 0, -2, 40, 0, -3, 14, 0, -4, 15, 0, -5, 16, 0, -6, 25, 0, -7, 7, 0, 3, 56, 0, 3, 56, 0, 3, 6, 0, 3, 6, 0, 4, 10, 0, 3, 6, 0, 4, 10, 0, 3, 39, 0, 3, 39, 0, 3, 39, 0, 3, 6, 0, 4, 10, 0, 3, 6, 0, 4, 10, 0, 1, 9, 0, 1, 9, 0, 1, 9, 0, 1, 9, 0, 1, 48, 0, 1, 48, 0, 1, 49, 0, 1, 49, 0, 1, 50, 0, 1, 50, 0, 1, 51, 0, 1, 51, 0, 1, 52, 0, 1, 52, 0, 1, 53, 0, 1, 53, 0, 1, 54, 0, 1, 54, 0, 1, 9, 0, 1, 55, 0, 1, 55, 0, 1, 34, 0, 1, 34, 0, 1, 34, 0, 0, 5, 0, 5, 5, 0, 3, 61, 0, 3, 61, 0, 3, 8, 0, 3, 8, 0, 4, 17, 0, 3, 8, 0, 4, 17, 0, 3, 45, 0, 3, 45, 0, 3, 45, 0, 3, 8, 0, 4, 17, 0, 3, 8, 0, 4, 17, 0, 1, 26, 0, 1, 26, 0, 1, 26, 0, 1, 26, 0, 1, 60, 0, 1, 60, 0, 0, 7, 0, 5, 7, 0, 0, 11, 0, 0, 11, 0, -1, 35, 0, -2, 36, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, -1, 38, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 18, 0, 0, 18, 0, -1, 41, 0, -2, 42, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, -1, 44, 0, 0, 21, 0, 0, 21, 0, -3, 57, 0, 0, 22, 0, 0, 22, 0, -3, 58, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 27, 0, 0, 27, 0, -3, 62, 0, 0, 28, 0, 0, 28, 0, -3, 63, 0, 0, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 40, 0, -2, 59, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 46, 0, 0, 46, 0, 8, 1, 2, 11, 6, 3, 11, 8, 10, 12, 24, 10, 15, 13, 10, 13, 58, 10, 14, 57, 17, 12, 30, 17, 15, 20, 17, 13, 63, 17, 14, 62, 47, 16, 59, 193], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 47, 47, 47], [2, 6, 7, 2, -1, -2, -3, -4, -5, -6, 2, 2, -1, -2, -3, -4, 2, -1, 9, 2, 6, -1, 9, 2, 2, 2, 7, 2, -1, -2, -3, -4, -5, -6, 2, 2, -1, -2, -3, -4, 2, -1, 9, 2, 6, -1, 9, 2, 6, 17, 18, 19], [24, 25, 20, 0, 0, 7, 8, 9, 10, 11, 12, 1, 1, 13, 14, 15, 16, 2, 2, 17, 18, 3, 3, 19, 19, 19, 20, 0, 0, 7, 8, 9, 10, 11, 12, 1, 1, 13, 14, 15, 16, 2, 2, 17, 18, 3, 3, 26, 27, 28, 29, 30]], [[[16, "DTHeroPos"], [83, "DTHeroPos", 33554432, [-16, -17, -18, -19, -20, -21, -22, -23, -24, -25], [[2, -2, [0, "0cmhB8ksZMT4LHBwOup0fV"], [5, 80, 120]], [30, -3, [0, "bfaUz7z6ZGA5FDspBOuKnD"]], [112, 3, 0.9, -5, [0, "a8LHpw2sZHBIDKK060/+tf"], -4], [114, -15, [0, "95wRrNslRKMqpOgLgHfy2S"], -14, -13, -12, -11, -10, -9, -8, -7, -6]], [1, "eapMGiC3lGKbzPrEhU/yJw", null, null, null, -1, 0], [1, -250, 148, 0]], [21, "layout_xx2", 33554432, 1, [-28, -29, -30, -31, -32], [[2, -26, [0, "d6dPngpzRB5rYWFVWlBeMd"], [5, 85.75, 20]], [69, 1, 1, -2, true, -27, [0, "01+UF/gl1CI5wfJE6ZfM5C"]]], [1, "91ospcBH1GsK7nBCz6CylP", null, null, null, 1, 0], [1, 0, -38, 0]], [26, "bg_mszb_di1", 33554432, 1, [[2, -33, [0, "f44NFBmJ1Luau8G+mb7b98"], [5, 80, 41]], [7, -34, [0, "46qX26K8hDXIxFscKEb1MI"], 0], [98, 4, -35, [0, "a7urfAaM9C4pcqwBBHmwDO"]]], [1, "a3/QoaTDhIP60ux/PpvPIb", null, null, null, 1, 0], [1, 0, -39.5, 0]], [84, "icon_mszb_jia", false, 33554432, 1, [[2, -36, [0, "73U6wkuPhBiKMoc+QVdhRJ"], [5, 62, 93]], [7, -37, [0, "e3NaFOo7NC5oQGifINc1yI"], 1]], [1, "80JzWRxgdDG43zXl+uH7g8", null, null, null, 1, 0], [1, 0, 10, 0]], [26, "icon_mszb_suo", 33554432, 1, [[5, -38, [0, "27vF9B0A5CvrWRV+GeemUB"], [5, 752.0000610351562, 1280], [0, 0.1656497760001831, 0.3593862056732178]], [42, "default", "<None>", false, 0, -39, [0, "0fKmNDowJG15N3K8PldgVx"], 2]], [1, "a1FRXBkMNOvpeq3gno3TQe", null, null, null, 1, 0], [1, 0, 10, 0]], [85, "icon_mszb_xz1", false, 33554432, 1, [-41], [[2, -40, [0, "99K6H+hipEo4+6TimZrWl9"], [5, 76, 37]]], [1, "096GhRXw5I3b9M5VAclLyi", null, null, null, 1, 0], [1, 0, -39.5, 0]], [86, "chongwu1", 33554432, 1, [-44], [[2, -42, [0, "2dLuVs6cZGbLZ+Ij7ucAAR"], [5, 150, 150]], [30, -43, [0, "dcQwEuV2tL+4EvlKlnDey8"]]], [1, "91ZGpV0zRAVbaU+5a8KfsX", null, null, null, 1, 0], [1, 0, 13.745000000000005, 0], [1, 0.65, 0.65, 1]], [20, "ani_hero", 33554432, 1, [[17, -45, [0, "0cOlWqzbxGc7yStzXrFnC+"]], [104, false, -46, [0, "062kp4WdhNh69Ufqo3RneQ"]]], [1, "d6Ijae5JtODaJgpcuh9F5u", null, null, null, 1, 0]], [19, "ani_action_eff", 33554432, 1, [[[17, -47, [0, "f6zKhxAfhGxILZMcq7hwG8"]], -48], 4, 1], [1, "25+4I4leVD2JBUw9HPpD9C", null, null, null, 1, 0], [1, 0, -39, 0], [1, -1, 1, 1]], [26, "icon_mszb_arrow1", 33554432, 6, [[5, -49, [0, "b6JWRvSJNBe5C9rkI6Qjf0"], [5, 104, 119], [0, 0.4999999633202186, 0.44279018370043327]], [42, "default", "animation", false, 0, -50, [0, "30R0zEPN5EjprFb4VhSnX0"], 3]], [1, "5bmtfA/ANH6b8ZaRH4ugno", null, null, null, 1, 0], [1, 0, 26, 0]], [19, "lihui", 33554432, 7, [[[29, -51, [0, "57YyUNvzZG9ZPMuq08Q6Ls"], [0, 0.5, 0.16603750005639653]], -52], 4, 1], [1, "91Qpb3O0ZAiKuc79USQk49", null, null, null, 1, 0], [1, 0, -82, 0], [1, -1, 1, 1]], [9, "bg_hb_tj_xx1", 33554432, 2, [[2, -53, [0, "4aqaCifH5A3LB1ZLsE7u2N"], [5, 75, 75]], [7, -54, [0, "0e+L/ucBpHdLQHfg5XNFUd"], 4]], [1, "2635tdqnpPy6hQjetId2Q2", null, null, null, 1, 0], [1, -33.5, 0, 0], [1, 0.25, 0.25, 1]], [9, "bg_hb_tj_xx2", 33554432, 2, [[2, -55, [0, "b4PmU6SwFGYYr5JVL3LLjI"], [5, 75, 75]], [7, -56, [0, "6auRJjurZF3IL2oewQdAyX"], 5]], [1, "d3xmC4MZxMHrA2r6VE1JYT", null, null, null, 1, 0], [1, -16.75, 0, 0], [1, 0.25, 0.25, 1]], [27, "bg_hb_tj_xx3", 33554432, 2, [[2, -57, [0, "ef6LJY7aJLkZJWYOB78+v9"], [5, 75, 75]], [7, -58, [0, "7bpnBOo9JMZ6VDMEoH8GzP"], 6]], [1, "1cajA7X7JAsLo4exRYZLyr", null, null, null, 1, 0], [1, 0.25, 0.25, 1]], [9, "bg_hb_tj_xx4", 33554432, 2, [[2, -59, [0, "d4eRFhL+tEvJr5trUIrYxv"], [5, 75, 75]], [7, -60, [0, "57J+C3STpD7adY8icKV2b9"], 7]], [1, "51hJ6f1GxG4o3oQUtdbH5Z", null, null, null, 1, 0], [1, 16.75, 0, 0], [1, 0.25, 0.25, 1]], [9, "bg_hb_tj_xx5", 33554432, 2, [[2, -61, [0, "62v4kkNUdEoJjAUFDwgs9s"], [5, 75, 75]], [7, -62, [0, "24SMlMdKlK6b+U+4+Ehlzf"], 8]], [1, "27HaSzBgVMjach2jr1fzAZ", null, null, null, 1, 0], [1, 33.5, 0, 0], [1, 0.25, 0.25, 1]], [58, "ani_buzhen", false, 33554432, 1, [[[29, -63, [0, "7fbBKqVM5Pr5k03qsNTTay"], [0, 0.5, 0.6392722606658936]], -64], 4, 1], [1, "a6tw9UkW1O8KJX26IscPVS", null, null, null, 1, 0], [1, 0, -42.961, 0]], [58, "ani_buff", false, 33554432, 1, [[[29, -65, [0, "29d/ZH6WBMU7LXqW5fPYMO"], [0, 0.5, 0.6392722606658936]], -66], 4, 1], [1, "a4tVZVeR9LfKlF0ItJbgI8", null, null, null, 1, 0], [1, 0, -42.961, 0]], [43, false, 0, 9, [0, "eauMIBFqtEX4n+lJnMQy2E"]], [43, false, 0, 11, [0, "ad3K+lgctPp5XnjZQThVqT"]], [55, false, 0, false, 17, [0, "6dtP6Luo5Aip/UDtiPHANn"]], [55, false, 0, false, 18, [0, "0dQ/Kf8RROm4dTDBVLNPdD"]]], 0, [0, 5, 1, 0, 0, 1, 0, 0, 1, 0, 20, 1, 0, 0, 1, 0, 21, 22, 0, 22, 21, 0, 23, 8, 0, 24, 2, 0, 25, 20, 0, 26, 19, 0, 27, 6, 0, 28, 5, 0, 29, 4, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 9, 0, -5, 6, 0, -6, 7, 0, -7, 2, 0, -8, 8, 0, -9, 17, 0, -10, 18, 0, 0, 2, 0, 0, 2, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, -4, 15, 0, -5, 16, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -1, 10, 0, 0, 7, 0, 0, 7, 0, -1, 11, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -2, 19, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, -2, 20, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, -2, 21, 0, 0, 18, 0, -2, 22, 0, 8, 1, 66], [0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 6, 6, 2, 2, 2, 2, 2], [31, 32, 33, 34, 6, 6, 6, 6, 6]], [[[16, "DTBulletLogic"], [87, "DTBulletLogic", 33554432, [[5, -2, [0, "16mmiQPfFLiL9RLDUplUG/"], [5, 84, 51.000003814697266], [0, 0.6284428096952892, 0.5]], [105, "default", false, 0, -3, [0, "24XiqNeSBIYZGek0BNnjBi"], 0]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]]], 0, [0, 5, 1, 0, 0, 1, 0, 0, 1, 0, 8, 1, 3], [0], [6], [35]], [[[73, "skill_ms_1033_zd", ".bin", "\nskill_ms_1033_zd.png\nsize: 263,263\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nATK/sunquan_pf_skill_dg_zd_0000\n  rotate: false\n  xy: 2, 102\n  size: 259, 159\n  orig: 280, 170\n  offset: 17, 5\n  index: -1\n", ["skill_ms_1033_zd.png"], [0]], -1], 0, 0, [0], [-1], [36]], [[[16, "DTSoldierLogic"], [48, "DTSoldierLogic", 33554432, [-9, -10, -11, -12, -13], [[2, -2, [0, "37knrif9VN1I8diJC0kFiq"], [5, 30, 30]], [115, -8, [0, "fe852n65ZLbb6PsmWjQCH0"], -7, -6, -5, -4, -3]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [88, "token", false, 33554432, 1, [-17, -18], [[2, -14, [0, "d4uSofPmNE7Y6oASBeGesx"], [5, 88.888671875, 30]], [69, 1, 1, 4, true, -15, [0, "c6dDfM0lBE4bo/CyPQHvpK"]], [63, 4, 10.638, -16, [0, "18e/+xmW1AirQKjei8bdzZ"]]], [1, "7eX2PHSuBCU7WHAMicVuuu", null, null, null, 1, 0], [1, 0, 3.138, 0], [1, 0.5, 0.5, 1]], [59, "hpbg0", 33554432, 1, [-22], [[[5, -19, [0, "881G1USvdKp6gbYNIMWdng"], [5, 86, 29], [0, 0, 0.5]], [60, 1, -20, [0, "1cdrVaZaBFD7J54paDaLbf"], 0], -21], 4, 4, 1], [1, "b3N3EXsq1A5aD/KkZqmF2c", null, null, null, 1, 0], [1, -20.765, 57.133, 0], [1, 0.5, 0.5, 1]], [59, "hpbg1", 33554432, 1, [-26], [[[5, -23, [0, "53CyL68A5JKb4S1OuEbRBS"], [5, 86, 29], [0, 0, 0.5]], [60, 1, -24, [0, "4fgJsyi2hFmJp2nT7CK66b"], 1], -25], 4, 4, 1], [1, "5fPXvE0fhHyoKmqWD9kVYs", null, null, null, 1, 0], [1, -20.384, 56.461, 0], [1, 0.5, 0.5, 1]], [40, "lihui", 33554432, 1, [[[29, -27, [0, "aaF03pGxhErL2p6rXwGGeh"], [0, 0.5, 0.06045321934128561]], -28], 4, 1], [1, "a5kE990a1FU5LPuX/S0vFV", null, null, null, 1, 0]], [34, "hp", 33554432, 3, [[[5, -29, [0, "cfDScEOGNNYr5CJ0g3u5xU"], [5, 66, 9], [0, 0, 0.5]], -30], 4, 1], [1, "2auNu6SO5DUaW55rMrMthA", null, null, null, 1, 0], [1, 10.35, 0, 0]], [34, "hp", 33554432, 4, [[[5, -31, [0, "5drv9dU9lIXKPE5qGSgXx2"], [5, 66, 9], [0, 0, 0.5]], -32], 4, 1], [1, "4cHCoj/bxOAJ3Nmj9WZzW+", null, null, null, 1, 0], [1, 9.883, 0, 0]], [41, "txt_hp", false, 33554432, 1, [[2, -33, [0, "6axHjB3jFHEoFdB4nceTs/"], [5, 31.8076171875, 67]], [107, "1", 50, 50, 50, true, -34, [0, "60nYPHIqVADZRiL5Ws2VVj"]]], [1, "87J2eJgWdECLM884vTTDt8", null, null, null, 1, 0], [1, 0, 57.13300000000004, 0], [1, 0.5, 0.5, 1]], [26, "icon_token", 33554432, 2, [[2, -35, [0, "52wXNcDKRNiYlHNVHP0ufH"], [5, 31, 31]], [7, -36, [0, "47tIGrlYtEhKcxF0Q2Eovy"], 2]], [1, "e69lugwftC7qsXI2NCU6tL", null, null, null, 1, 0], [1, -28.9443359375, 0, 0]], [19, "txt_token1", 33554432, 2, [[[2, -37, [0, "02X/1WY6NLNr6rR5fHMT9x"], [5, 107.77734375, 81.6]], -38], 4, 1], [1, "c3/0bVdSxOh5HvvDp0GzOR", null, null, null, 1, 0], [1, 17.5, 0, 0], [1, 0.5, 0.5, 1]], [43, false, 0, 5, [0, "7dmRhDhSFKR7nVedQ5Y1GR"]], [94, 1, 0, 6, [0, "32/8Qsk5hLxKy/+pbV7dtV"], [4, 4279966704]], [74, 66, 1, 3, [0, "91a20l1H9DVYdRsZ4cU3Dx"], 12], [95, 1, 0, 7, [0, "7a1jo82xJFHL3aApF7OhGI"]], [74, 66, 1, 4, [0, "2cwhO/QetHdoAXad7Z0jmY"], 14], [108, "+20", 60, 60, 60, true, true, 3, 10, [0, "e1Hv/4htNJcoY5RkDD38+w"], [4, 4280098330]]], 0, [0, 5, 1, 0, 0, 1, 0, 30, 16, 0, 31, 2, 0, 32, 11, 0, 33, 15, 0, 34, 13, 0, 0, 1, 0, -1, 5, 0, -2, 3, 0, -3, 4, 0, -4, 8, 0, -5, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 9, 0, -2, 10, 0, 0, 3, 0, 0, 3, 0, -3, 13, 0, -1, 6, 0, 0, 4, 0, 0, 4, 0, -3, 15, 0, -1, 7, 0, 0, 5, 0, -2, 11, 0, 0, 6, 0, -2, 12, 0, 0, 7, 0, -2, 14, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -2, 16, 0, 8, 1, 38], [0, 0, 0, 12, 14], [2, 2, 2, 2, 2], [21, 21, 37, 38, 39]], [[[16, "ct_intr"], [48, "ct_intr", 33554432, [-3, -4], [[5, -2, [0, "904+haHhNN/4f4V1PZZ84l"], [5, 252, 102.24], [0, 0.5, 0]]], [1, "72RF9n2llLSLFSU/uM+sLC", null, null, null, -1, 0]], [25, "bg_msyc_qp1", 33554432, 1, [-8], [[5, -5, [0, "f6fMT9f4NHD6iyKIBNCXgH"], [5, 252, 86.24], [0, 0.5, 0]], [22, 1, 0, -6, [0, "332O9TgBhAKLJuh93obSvx"], 0], [70, 1, 2, 16, 16, true, -7, [0, "89tV8IcXZMsLF90PN9nvg4"]]], [1, "b4H+PVXjRF9r02JfaLqJAg", null, null, null, 1, 0]], [9, "txt_ct_ms1", 33554432, 2, [[2, -9, [0, "ecpW1CpURH6qHKzMpteQjO"], [5, 440, 108.47999999999999]], [75, 48, "<b><outline color=#000000 width=3>对生命值低于<color=#0fffff>30%</color>的目标伤害提升</outline></b>", 36, 440, -10, [0, "ce7cTEtElCX6fJKAd4lF2n"]]], [1, "9fiR2FP4JMmo1CmOcMwgpk", null, null, null, 1, 0], [1, 0, 43.12, 0], [1, 0.5, 0.5, 1]], [20, "ct_mask", 33554432, 1, [[2, -11, [0, "caqhvbkdhBGI3tKBDPz1P0"], [5, 640, 1600]]], [1, "01uGYywOpN3qoZT3jlgVRN", null, null, null, 1, 0]]], 0, [0, 5, 1, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 8, 1, 11], [0], [2], [22]], [[[16, "ty_txk1"], [48, "ty_txk1", 33554432, [-5, -6, -7, -8], [[2, -2, [0, "47Cs6IYElOBK5jyf6MFKqL"], [5, 112, 123]], [102, 1, 2, -3, [0, "46wgLU6vlHt54vDxwmo6H/"]], [113, 3, 0.9, -4, [0, "0bPUARKH9BMIt9E7Ci52at"]]], [1, "79BcNar8tDmZucBHLjoa8/", null, null, null, -1, 0]], [27, "icon_tx_di", 33554432, 1, [[2, -9, [0, "83Iz4xnIJBp4B4NrxAK6Pg"], [5, 150, 150]], [7, -10, [0, "e3MbdbnGpLSJv7jZQUGKXO"], 0]], [1, "42Rk0WEWpD9J2OZPPe3K6X", null, null, null, 1, 0], [1, 0.75, 0.75, 1]], [20, "<PERSON><PERSON><PERSON><PERSON>", 33554432, 1, [[2, -11, [0, "6cP4a1qqlAqKZFbaaU0XP2"], [5, 105, 105]], [11, -12, [0, "cdSAz8tiZPwYFWQVGKww65"]]], [1, "6bVIarBmlP8bobDR7pZWzE", null, null, null, 1, 0]], [27, "icon_txk1", 33554432, 1, [[2, -13, [0, "18qfB8zqxAGZUPTlqCSZhg"], [5, 183, 190]], [11, -14, [0, "34goCFzhxCNaeCDZ72IZKB"]]], [1, "42NwrH+3dFtrJTfzGLD8tn", null, null, null, 1, 0], [1, 0.88, 0.88, 1]], [9, "txt_wj_dj", 33554432, 1, [[5, -15, [0, "2a+NEz1hJH37Gy8QtP+C4Q"], [5, 220, 60], [0, 0, 0.5]], [109, "", 0, 41, 48, 2, true, true, 3, -16, [0, "eeoXHNF6JHCZD795RQ9Goj"], [4, 4280098330]]], [1, "8ev1z6IH5OgYw4mQ5jIqFq", null, null, null, 1, 0], [1, -47.292, -32.804, 0], [1, 0.5, 0.5, 1]]], 0, [0, 5, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 8, 1, 16], [0], [2], [40]], [[[16, "DropAward"], [89, "DropAward", [-4, -5, -6, -7, -8, -9, -10], [[2, -2, [0, "cbWJu7LRxL44htompeuvYx"], [5, 36, 36]], [23, -3, [0, "aanF0q1LFBzptqSNRG84LG"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [28, "ico1", 1, [-13], [[2, -11, [0, "a6Nr5bXWpFqoKAf+IHS84N"], [5, 74, 76]], [7, -12, [0, "43TSA06edOxKeXeNJnNKk7"], 2]], [1, "8bAwMj0nxFA7WUjSVef4v/", null, null, null, 1, 0], [1, -9.056660652160645, -39.86431884765625, 0], [1, 0, 0, 2.504478065487657e-06]], [28, "ico2", 1, [-16], [[2, -14, [0, "7effbuTyBDrrE88c1pu/Mq"], [5, 86, 87]], [11, -15, [0, "aayn0OiENHV51Bk4p2KurV"]]], [1, "09qxP1RS1EzLnOpRsjtVpK", null, null, null, 1, 0], [1, -9.056660652160645, -39.86431884765625, 0], [1, 0, 0, 2.504478065487657e-06]], [28, "ico3", 1, [-19], [[2, -17, [0, "2558kYTcVG4Ym8ESAQCwmG"], [5, 86, 87]], [11, -18, [0, "9bSOY/KCJPGYJ1bmKRHqho"]]], [1, "aa3T1ezZFEUrHXC6Mp9Syp", null, null, null, 1, 0], [1, -9.056660652160645, -39.86431884765625, 0], [1, 0, 0, 2.504478065487657e-06]], [28, "ico4", 1, [-22], [[2, -20, [0, "f26KGo/M9PV6yzC1u8gxcG"], [5, 86, 87]], [11, -21, [0, "90pElO5XtBHLVk+LkDtksU"]]], [1, "c3YaACuTRJD4UEvD5lzbj3", null, null, null, 1, 0], [1, -9.056660652160645, -39.86431884765625, 0], [1, 0, 0, 2.504478065487657e-06]], [28, "ico5", 1, [-25], [[2, -23, [0, "c9jl1YjAlHQK1SaaiUmNDM"], [5, 86, 87]], [11, -24, [0, "3btILD7pxPypkGzVQ7fd2h"]]], [1, "c9dCePyi9Iy57m3YB7tuQI", null, null, null, 1, 0], [1, -9.056660652160645, -39.86431884765625, 0], [1, 0, 0, 2.504478065487657e-06]], [28, "ico6", 1, [-28], [[2, -26, [0, "46xNl7WM9NSKIW4uOieMF5"], [5, 86, 87]], [11, -27, [0, "7fOPr46NlDbaQS7+Qacf+S"]]], [1, "e5FzPC+bNL9brO+jIrDYdA", null, null, null, 1, 0], [1, -9.056660652160645, -39.86431884765625, 0], [1, 0, 0, 2.504478065487657e-06]], [90, "spine", 1, [[5, -29, [0, "74QXVEN71MLKRa2rXXlbh8"], [5, 641.0000610351562, 1281], [0, 0.5000000476093217, 0.6773020783780982]], [106, "default", "lv1", false, 0, false, -30, [0, "da6cLMoJtGn7Lr0ClHgXgh"], [[33, "root/all_jb/jb11/jb1", 2], [33, "root/all_jb/jb12/jb2", 3], [33, "root/all_jb/jb14/jb3", 4], [33, "root/all_jy/jy11/jy1", 5], [33, "root/all_jy/jy12/jy2", 6], [33, "root/all_jy/jy13/jy3", 7]], 0]], [1, "0eEIiU11dBMKhElzepRgRY", null, null, null, 1, 0]], [91, "motion", false, 2, [[17, -31, [0, "d2eO9T7wxCaJL4oW0JikVk"]], [116, 0.15, 30, 20, true, -32, [0, "b523LkM4tMu7msOlCxybRV"], [4, 3036676095], 1]], [1, "42yzTyet9OFaYTW99ssdT1", null, null, null, 1, 0]], [37, "motion", false, 3, [[17, -33, [0, "03o0SjsjJFLo9tlKf0IRad"]], [39, true, 0.15, 30, 20, true, -34, [0, "541olAPd9CB6yc4QoPjCyn"], [4, 3036676095], 3]], [1, "20LsjStY1FjZ5BgLKT2HMB", null, null, null, 1, 0], [1, 0, -17.131, 0], [1, 0, 0, -2.5044780654876575e-06]], [37, "motion", false, 4, [[17, -35, [0, "8c5SlwlzFGrLg2bF8Z7P87"]], [39, true, 0.15, 30, 20, true, -36, [0, "d1j21PQlJMWaM50ZgJsuGt"], [4, 3036676095], 4]], [1, "16aYpthZRJaawpm5zXWSEL", null, null, null, 1, 0], [1, 0, -17.131, 0], [1, 0, 0, -2.5044780654876575e-06]], [37, "motion", false, 5, [[17, -37, [0, "42POw1JdhHoIqeLSQSf6T+"]], [39, true, 0.15, 30, 20, true, -38, [0, "93yR3h9ZZBfaaH/CPwT72z"], [4, 3036676095], 5]], [1, "a2Kx4zkX9Cs4bIsHblsEAg", null, null, null, 1, 0], [1, 0, -17.131, 0], [1, 0, 0, -2.5044780654876575e-06]], [37, "motion", false, 6, [[17, -39, [0, "6cFdhQUjNIZqdNu3Natqt7"]], [39, true, 0.15, 30, 20, true, -40, [0, "3agPUvy2VNJZ+nmwrfPVeX"], [4, 3036676095], 6]], [1, "0diqkYhHdEGIYgvEOJahq6", null, null, null, 1, 0], [1, 0, -17.131, 0], [1, 0, 0, -2.5044780654876575e-06]], [37, "motion", false, 7, [[17, -41, [0, "49L0p6LcJPVKJxABQQOZwa"]], [39, true, 0.15, 30, 20, true, -42, [0, "bcodfx4C9O75KDJREJZIT+"], [4, 3036676095], 7]], [1, "01aI3uAzdIJLKte/+0FnAg", null, null, null, 1, 0], [1, 0, -17.131, 0], [1, 0, 0, -2.504478065487658e-06]]], 0, [0, 5, 1, 0, 0, 1, 0, 0, 1, 0, -1, 8, 0, -2, 2, 0, -3, 3, 0, -4, 4, 0, -5, 5, 0, -6, 6, 0, -7, 7, 0, 0, 2, 0, 0, 2, 0, -1, 9, 0, 0, 3, 0, 0, 3, 0, -1, 10, 0, 0, 4, 0, 0, 4, 0, -1, 11, 0, 0, 5, 0, 0, 5, 0, -1, 12, 0, 0, 6, 0, 0, 6, 0, -1, 13, 0, 0, 7, 0, 0, 7, 0, -1, 14, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 8, 1, 42], [0, 0, 0, 0, 0, 0, 0, 0], [6, 10, 2, 10, 10, 10, 10, 10], [41, 4, 42, 4, 4, 4, 4, 4]], [[[16, "DTHomeLogic"], [78, "DTHomeLogic", [-33, -34, -35, -36, -37, -38, -39, -40], [[[2, -31, [0, "94/tRUAu5CLKnkqVT2WQfg"], [5, 806, 432]], -32], 4, 1], [64, "61/02t6o9GyKW+P/LzuvXn", null, -30, 0, [[6, ["playerHead"], -11, -10, [3, ["cdSAz8tiZPwYFWQVGKww65"]]], [6, ["<PERSON><PERSON><PERSON><PERSON>"], -13, -12, [3, ["34goCFzhxCNaeCDZ72IZKB"]]], [6, ["equipNode"], -15, -14, [3, ["58m892tA5BQagOFZNqC4iA"]]], [6, ["kapai_di"], -17, -16, [3, ["30SwXnGDZOzJhfcbZFqmvt"]]], [6, ["item_icon"], -19, -18, [3, ["4eDwtJcn1JlYXC2pBZ9vb6"]]], [6, ["ct_mask"], -21, -20, [3, ["01uGYywOpN3qoZT3jlgVRN"]]], [6, ["txt_ct_ms1"], -23, -22, [3, ["ce7cTEtElCX6fJKAd4lF2n"]]], [6, ["bg_msyc_qp1"], -25, -24, [3, ["b4H+PVXjRF9r02JfaLqJAg"]]], [6, ["emptyNode"], -27, -26, [3, ["76/iOpIwxI/4fb3ZDzeow4"]]], [6, ["equipEffectNode"], -29, -28, [3, ["5dZVgsaRNB5p+DYvanNQGd"]]]], [-1, -2, -3, -4, -5, -6, -7, -8, -9]], [1, 0, 381.346, 0]], [44, "item1", 33554432, [-45, -46, -47, -48, -49, -50, -51, -52], [[[2, -41, [0, "800U7Nj4lFVZTNPOORHgRn"], [5, 113, 115]], [52, -42, [0, "627EWxKghLx6uRo4WDrsj9"], [24], 25], [23, -43, [0, "99iHfiq7hGupwA+5mGvVOo"]], -44], 4, 4, 4, 1], [1, "58m892tA5BQagOFZNqC4iA", null, null, null, 1, 0]], [13, 0, {}, 1, [15, "79BcNar8tDmZucBHLjoa8/", null, null, -61, [99, "719xGdtKlHt4uAVql4is8n", 1, [[117, [3, ["58m892tA5BQagOFZNqC4iA"]], [-59, -60]]], [[100, [3, ["58m892tA5BQagOFZNqC4iA"]], [-58]]], [[8, "ty_item1", ["_name"], -53], [4, ["_lpos"], -54, [1, 250, -2, 0]], [4, ["_lrot"], -55, [3, 0, 0, 0, 1]], [4, ["_euler"], -56, [1, 0, 0, 0]], [4, ["_lscale"], -57, [1, 0.6, 0.6, 1]], [38, false, ["_active"], [3, ["c91yTbP25N/4XFQDypTQOW"]]], [38, true, ["_active"], [3, ["8ev1z6IH5OgYw4mQ5jIqFq"]]], [38, true, ["_enabled"], [3, ["9724Q2/XBDebIdwKApd6Kd"]]]], [[3, ["cbEam9q2RBR46ze3UR92KF"]], [3, ["12Ayw6S+5GsrINLpF3ISBF"]], [3, ["95duzTjhlDQakb7/fTulj9"]]]], 6]], [79, "ani_0", 1, [-64, -65, -66, -67, -68, -69], [[[29, -62, [0, "3eoR0cSqlLb6AXeIQrlVun"], [0, 0.5, 0.19403909622501106]], -63], 4, 1], [1, "29aob3p5lNiY4RxOZ5zM9o", null, null, null, 1, 0]], [13, 0, {}, 1, [15, "72RF9n2llLSLFSU/uM+sLC", null, null, -75, [18, "68zY6gaqFFi74toL8vJJGj", 1, [[8, "ct_intr", ["_name"], -70], [4, ["_lpos"], -71, [1, 0, 0, 0]], [4, ["_lrot"], -72, [3, 0, 0, 0, 1]], [4, ["_euler"], -73, [1, 0, 0, 0]], [8, false, ["_active"], -74], [12, ["_lpos"], [3, ["b4H+PVXjRF9r02JfaLqJAg"]], [1, 0, 0, 0]]]], 33]], [80, 0, {}, [15, "79BcNar8tDmZucBHLjoa8/", null, null, -81, [18, "fd6f2oQJFETYg+OeGtf9Bx", 1, [[8, "ty_txk1", ["_name"], -76], [4, ["_lpos"], -77, [1, 0, 64, 0]], [4, ["_lrot"], -78, [3, 0, 0, 0, 1]], [4, ["_euler"], -79, [1, 0, 0, 0]], [4, ["_lscale"], -80, [1, 0.65, 0.65, 1]]]], 28]], [118, 1, [0, "1dDi2NdhdARpoa0StaJhoq"], [-82, -83, -84, -85, -86, -87], 2, 3, 5], [25, "pj_kuang_hui2", 33554432, 2, [-90], [[2, -88, [0, "c7FZkiIlBBF71NSwy8Pk/K"], [5, 113, 115]], [7, -89, [0, "c2j/kZIkFKipkBz62pzOPD"], 26]], [1, "76/iOpIwxI/4fb3ZDzeow4", null, null, null, 1, 0]], [101, 2, [0, "085jt+4N9BWIMPoLcDWADM"], 8], [21, "bg_mszb_jdt2", 33554432, 1, [-93, -94, -95], [[2, -91, [0, "b56ti320JHB7uieXlU0SCs"], [5, 338, 28]], [22, 1, 0, -92, [0, "5eI+3BfkVIzro25VwT8Prt"], 31]], [1, "a2PqZ3mHpL/rhHmA0GAygn", null, null, null, 1, 0], [1, 0, 66, 0]], [21, "Layout_hd_xt", 33554432, 10, [-98, -99], [[2, -96, [0, "7a7f4H5npFh7uwPzYzMeUd"], [5, 340, 80]], [30, -97, [0, "f3Ari3Y8NN5469wEC6myb/"]]], [1, "34ENmVHrhAq6cWXRI7mr2N", null, null, null, 1, 0], [1, 0, -42.329, 0]], [45, "pj_kuang_hui1", false, 33554432, 2, [-102, -103], [[2, -100, [0, "d3AtRMiJNCOYRAaUItr2qu"], [5, 110, 110]], [14, 0, -101, [0, "31IP5pa1VA76/aggSUz5Pz"], 14]], [1, "c91yTbP25N/4XFQDypTQOW", null, null, null, 1, 0]], [46, "kapai_xz1", false, 33554432, 2, [[2, -104, [0, "234+0KgaRKP4Oyw5E8wuzg"], [5, 135, 136]], [22, 1, 0, -105, [0, "d65WkruK9JBLqnkpusf4Fa"], 20], [53, true, -106, [0, "82d/HULiVNnIe/ZM8Xe+wN"], [21], 22], [23, -107, [0, "02ZF5BEgBK7aQ7v73tkrjm"]]], [1, "d6i3dsjTBAsYIwkgrHXPK3", null, null, null, 1, 0]], [47, "ani_equip", false, 33554432, 2, [[2, -108, [0, "8cxIyJf61BNIMDrHZIPDdG"], [5, 120, 117]], [54, "default", "animation", false, 0, false, -109, [0, "5bcT+81TdJV4zYvJORitPP"], 23]], [1, "5dZVgsaRNB5p+DYvanNQGd", null, null, null, 1, 0], [1, 1.4, 1.4, 1]], [3, ["79BcNar8tDmZucBHLjoa8/"]], [21, "bg_mszb_up4", 33554432, 1, [-111, 6, -112], [[2, -110, [0, "bdo4w3xIhHqJL4qiu6IGgY"], [5, 75, 141]]], [1, "69xyN9cYhIWK4CcjvDqy6j", null, null, null, 1, 0], [1, -252, -82, 0]], [21, "bg", 33554432, 16, [-115], [[2, -113, [0, "ac9tEkosNDYo2tWYXFie6P"], [5, 75, 141]], [7, -114, [0, "94eWswxM1ElKdc5Bmma1HP"], 27]], [1, "cf+dlvkpRD2aYOuJY7AaBi", null, null, null, 1, 0], [1, 0, 10.245, 0]], [3, ["79BcNar8tDmZucBHLjoa8/"]], [21, "bg_mszb_hdjdt2", 33554432, 11, [-118, -119], [[2, -116, [0, "601tvjYRNJeY5AuBueRfs5"], [5, 338, 28]], [22, 1, 0, -117, [0, "f0bKm40xpK4bSaaEs/FsEE"], 29]], [1, "4czHEfEqdBpYWNNxRucCNw", null, null, null, 1, 0], [1, 0, 8.287, 0]], [21, "buff1", 33554432, 1, [-122], [[2, -120, [0, "fco1C1yrRIFLiMiG8MzG8Z"], [5, 23, 23]], [103, 1, 1, -121, [0, "02Kn+fGz1Elr1AHw6NDJvN"]]], [1, "78OueR3pROerNIiAz4Dkeu", null, null, null, 1, 0], [1, 0, 93.962, 0]], [25, "icon_buff", 33554432, 20, [-125], [[2, -123, [0, "ecvJpB7tBBoaNyfH4IWJxP"], [5, 23, 23]], [11, -124, [0, "17j8xxp+VDbZBij7dDqt2F"]]], [1, "a1Ump5AxhIm4q9MKTssv+O", null, null, null, 1, 0]], [3, ["72RF9n2llLSLFSU/uM+sLC"]], [25, "bg_msyc_qp1", 33554432, 5, [-129], [[5, -126, [0, "f6fMT9f4NHD6iyKIBNCXgH"], [5, 252, 86.24], [0, 0.5, 0]], [22, 1, 0, -127, [0, "332O9TgBhAKLJuh93obSvx"], 34], [70, 1, 2, 16, 16, true, -128, [0, "89tV8IcXZMsLF90PN9nvg4"]]], [1, "b4H+PVXjRF9r02JfaLqJAg", null, null, null, 1, 0]], [13, 0, {}, 4, [15, "eapMGiC3lGKbzPrEhU/yJw", null, null, -134, [18, "c3FWXozQ5DPq/hAiYhWFXU", 1, [[8, "pos0", ["_name"], -130], [4, ["_lpos"], -131, [1, -50, 148, 0]], [4, ["_lrot"], -132, [3, 0, 0, 0, 1]], [4, ["_euler"], -133, [1, 0, 0, 0]], [38, false, ["_active"], [3, ["096GhRXw5I3b9M5VAclLyi"]]]]], 0]], [3, ["eapMGiC3lGKbzPrEhU/yJw"]], [13, 0, {}, 4, [15, "eapMGiC3lGKbzPrEhU/yJw", null, null, -139, [18, "91AOoRyjBEn4/JLt//AJCR", 1, [[8, "pos1", ["_name"], -135], [4, ["_lpos"], -136, [1, 50, 148, 0]], [4, ["_lrot"], -137, [3, 0, 0, 0, 1]], [4, ["_euler"], -138, [1, 0, 0, 0]]]], 1]], [3, ["eapMGiC3lGKbzPrEhU/yJw"]], [13, 0, {}, 4, [15, "eapMGiC3lGKbzPrEhU/yJw", null, null, -144, [18, "77Z7monldFCZ0ObDTmbTvr", 1, [[8, "pos2", ["_name"], -140], [4, ["_lpos"], -141, [1, -150, 148, 0]], [4, ["_lrot"], -142, [3, 0, 0, 0, 1]], [4, ["_euler"], -143, [1, 0, 0, 0]]]], 2]], [3, ["eapMGiC3lGKbzPrEhU/yJw"]], [13, 0, {}, 4, [15, "eapMGiC3lGKbzPrEhU/yJw", null, null, -149, [18, "a7k1SUvvpLErTqfG+3H2fU", 1, [[8, "pos3", ["_name"], -145], [4, ["_lpos"], -146, [1, 150, 148, 0]], [4, ["_lrot"], -147, [3, 0, 0, 0, 1]], [4, ["_euler"], -148, [1, 0, 0, 0]]]], 3]], [3, ["eapMGiC3lGKbzPrEhU/yJw"]], [13, 0, {}, 4, [15, "eapMGiC3lGKbzPrEhU/yJw", null, null, -154, [18, "c8uX8KaHtHVrE7jK0t1yMK", 1, [[8, "pos4", ["_name"], -150], [4, ["_lpos"], -151, [1, -250, 148, 0]], [4, ["_lrot"], -152, [3, 0, 0, 0, 1]], [4, ["_euler"], -153, [1, 0, 0, 0]]]], 4]], [3, ["eapMGiC3lGKbzPrEhU/yJw"]], [13, 0, {}, 4, [15, "eapMGiC3lGKbzPrEhU/yJw", null, null, -159, [18, "d4UXDAu75HVqNHE2k1P3zX", 1, [[8, "pos5", ["_name"], -155], [4, ["_lpos"], -156, [1, 250, 148, 0]], [4, ["_lrot"], -157, [3, 0, 0, 0, 1]], [4, ["_euler"], -158, [1, 0, 0, 0]]]], 5]], [3, ["eapMGiC3lGKbzPrEhU/yJw"]], [34, "kapai_di", 33554432, 2, [[[2, -160, [0, "83Iz4xnIJBp4B4NrxAK6Pg"], [5, 110, 112]], [14, 0, -161, [0, "e3MbdbnGpLSJv7jZQUGKXO"], 7], -162], 4, 4, 1], [1, "42Rk0WEWpD9J2OZPPe3K6X", null, null, null, 1, 0], [1, 0, -1, 0]], [35, "item_icon", 33554432, 2, [[[2, -163, [0, "0ciXTZfuFGvIhz57RrMKZw"], [5, 110, 110]], [30, -164, [0, "cfQr5+YJVLOqqnwVbtaBq0"]], -165], 4, 4, 1], [1, "b9Ux/7GgBHjY7TOGJJiEpi", null, null, null, 1, 0], [1, 0.9, 0.9, 1]], [41, "pj_kuang_sp1", false, 33554432, 2, [[2, -166, [0, "e4lauM7zFO4aX6clqVrRLz"], [5, 93, 90]], [14, 0, -167, [0, "9724Q2/XBDebIdwKApd6Kd"], 15], [31, -168, [0, "3cXBsYOTxFgb9BVVnSJlAo"], [16, 17, 18, 19]]], [1, "f51AN7O7FJgK4aYDFNvVXm", null, null, null, 1, 0], [1, -32, -32, 0], [1, 0.35, 0.35, 1]], [92, "ani_eff", 1, [[29, -169, [0, "e9PXj5MXBFWbOUSCTd2kIP"], [0, 0.5, 0.5220125786163522]], [43, false, 0, -170, [0, "87oZynb51EZ61dj31K+R93"]]], [1, "f1BAlZGLRHIIbZ+vcq18KC", null, null, null, 1, 0], [1, 0, 66, 0], [1, 0.6, 0.6, 1]], [81, "ani_dead", 1, [[[5, -171, [0, "f0LYigX8pDJ7h12gIgaY0A"], [5, 808, 434], [0, 0.4975247524752475, 0.5]], -172], 4, 1], [1, "dftocmtS9DAJfKLBsfUTsV", null, null, null, 1, 0]], [9, "txt_item_jstj1", 33554432, 12, [[2, -173, [0, "46ZZq382dFa7LcUcAkKils"], [5, 180, 60]], [32, "Lv.100", 40, 60, 2, true, true, 3, -174, [0, "bey4sFeBtPDaHbYh+oX/Qj"], [4, 4290953922]]], [1, "51g0VuoRpO8LzDHgEFWoXH", null, null, null, 1, 0], [1, 0, 14, 0], [1, 0.5, 0.5, 1]], [9, "txt_item_jstj2", 33554432, 12, [[2, -175, [0, "d5ute3KyVLmqZvSuEdvhFO"], [5, 180, 60]], [32, "解鎖", 40, 60, 2, true, true, 3, -176, [0, "27sDHsU/xAV5kINrQNKYPw"], [4, 4290953922]]], [1, "1cWi93TqRDt5YPaW9LXMQn", null, null, null, 1, 0], [1, 0, -14, 0], [1, 0.5, 0.5, 1]], [9, "txt_item_dj", 33554432, 2, [[5, -177, [0, "2a+NEz1hJH37Gy8QtP+C4Q"], [5, 220, 96], [0, 1, 0.5]], [56, "", 2, 49, 48, 70, 2, true, true, 3, -178, [0, "eeoXHNF6JHCZD795RQ9Goj"], [4, 4280098330]]], [1, "8ev1z6IH5OgYw4mQ5jIqFq", null, null, null, 1, 0], [1, 50, 38, 0], [1, 0.5, 0.5, 1]], [27, "Label", 33554432, 8, [[2, -179, [0, "8f3IFg2NNAG4yOOElubeXs"], [5, 190, 80]], [57, "未装备", 44, 44, 44, 2, false, true, true, 3, -180, [0, "b5pp/8WtFHzIvvbaSpdmBr"]]], [1, "acmUlVU9NJa6ipAv3wSqeV", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [19, "icon_mszb_dw1", 33554432, 17, [[[2, -181, [0, "2bHPV3Zw1JKawJXiI0V/y7"], [5, 211, 204]], -182], 4, 1], [1, "22or0v5PNHmriH61AvSLOd", null, null, null, 1, 0], [1, 0, -19.705, 0], [1, 0.3, 0.3, 1]], [19, "txt_name1", 33554432, 16, [[[2, -183, [0, "74gznNw/lOT586L33k6oWB"], [5, 250, 60]], -184], 4, 1], [1, "4fo+tpD+VCS530ins8PtMQ", null, null, null, 1, 0], [1, 0, 100, 0], [1, 0.5, 0.5, 1]], [40, "img_mszb_jdt4", 33554432, 10, [[[2, -185, [0, "a1BOVVcNtADqyif1Q31BOC"], [5, 317, 18]], -186], 4, 1], [1, "22WqtwMANI/ozG4HhNqu46", null, null, null, 1, 0]], [35, "txt_hp1", 33554432, 10, [[[2, -187, [0, "85wa4kzn1Jlruc/XIIufHg"], [5, 126.12890625, 56.4]], -188], 4, 1], [1, "2bscPEpHlEXqQG5bdwcpwN", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [40, "img_mszb_jdt5", 33554432, 19, [[[2, -189, [0, "6b6LLE4D5HoZ+3lDNedFKT"], [5, 317, 17]], -190], 4, 1], [1, "bcdLGEWCdIw5Y13hX1a1nI", null, null, null, 1, 0]], [19, "txt_hp1", 33554432, 19, [[[2, -191, [0, "bdJK+Cy6hArYNGK8Led7FO"], [5, 126.12890625, 56.4]], -192], 4, 1], [1, "fchUn+IfJBao8k22C1HarS", null, null, null, 1, 0], [1, 0, 1, 0], [1, 0.5, 0.5, 1]], [36, "ani_hd", 11, [[5, -193, [0, "05mpk6r0tE+LnxvHs3/r6N"], [5, 726.0000610351562, 636], [0, 0.5009297351738599, 0.5220125786163522]], [42, "default", "animation", false, 0, -194, [0, "aepf7QYhVF/K7+AbE0JAqw"], 30]], [1, "4f2l81Xr5JQKs5tzhbwcsQ", null, null, null, 1, 0], [1, 0, 42.32899999999995, 0]], [20, "buff_mask", 33554432, 21, [[2, -195, [0, "eb+7nQbOpH4a7KO1llCqn1"], [5, 23, 23]], [7, -196, [0, "14XnTBukJOdL4h4Qqcj2Am"], 32]], [1, "f6Hnd8zOFMFLqCGV/8MDch", null, null, null, 1, 0]], [19, "txt_ct_ms1", 33554432, 23, [[[2, -197, [0, "ecpW1CpURH6qHKzMpteQjO"], [5, 440, 108.47999999999999]], -198], 4, 1], [1, "9fiR2FP4JMmo1CmOcMwgpk", null, null, null, 1, 0], [1, 0, 43.12, 0], [1, 0.5, 0.5, 1]], [71, "default", "animation", false, 0, false, 40, [0, "a1JmNUxxdDFrkOy5B6HftI"]], [55, false, 0, false, 4, [0, "5fQDOXkp9OTrUACUVsbaH7"]], [31, 36, [0, "30SwXnGDZOzJhfcbZFqmvt"], [8, 9, 10, 11, 12, 13]], [11, 37, [0, "4eDwtJcn1JlYXC2pBZ9vb6"]], [11, 45, [0, "a5RaJ6iz5KzrS06eO3F0+N"]], [110, "玩家的名字X字", 36, 36, 2, false, true, true, 3, 46, [0, "2c0TGPZEZNr7zRVC+4yu+P"]], [61, 3, 1, 47, [0, "8enmQLH/1MJYHE5Hcm7AUC"]], [72, "300015", 36, 36, true, true, 3, 48, [0, "bbp7cdNY9G/78GHKzfJfQH"], [4, 4280229916]], [61, 3, 1, 49, [0, "ceS88G8EdIzInoYEvyGu/w"]], [72, "300015", 36, 36, true, true, 3, 50, [0, "98UdvcDuxDoIYs7Egr3hzK"], [4, 4280229916]], [62, [0, "cdSAz8tiZPwYFWQVGKww65"]], [40, "<PERSON><PERSON><PERSON><PERSON>", 33554432, 6, [[[2, -199, [0, "6cP4a1qqlAqKZFbaaU0XP2"], [5, 105, 105]], 64], 4, 1], [1, "6bVIarBmlP8bobDR7pZWzE", null, null, null, 1, 0]], [62, [0, "34goCFzhxCNaeCDZ72IZKB"]], [35, "icon_txk1", 33554432, 6, [[[2, -200, [0, "18qfB8zqxAGZUPTlqCSZhg"], [5, 183, 190]], 66], 4, 1], [1, "42NwrH+3dFtrJTfzGLD8tn", null, null, null, 1, 0], [1, 0.88, 0.88, 1]], [20, "ct_mask", 33554432, 5, [[2, -201, [0, "caqhvbkdhBGI3tKBDPz1P0"], [5, 640, 1600]]], [1, "01uGYywOpN3qoZT3jlgVRN", null, null, null, 1, 0]], [75, 48, "<b><outline color=#000000 width=3>对生命值低于<color=#0fffff>30%</color>的目标伤害提升</outline></b>", 36, 440, 53, [0, "ce7cTEtElCX6fJKAd4lF2n"]]], 0, [0, -1, 5, 0, -2, 6, 0, -3, 3, 0, -4, 34, 0, -5, 32, 0, -6, 30, 0, -7, 28, 0, -8, 26, 0, -9, 24, 0, 3, 6, 0, 4, 7, 0, 3, 6, 0, 4, 7, 0, 3, 3, 0, 4, 7, 0, 3, 3, 0, 4, 9, 0, 3, 3, 0, 4, 9, 0, 3, 5, 0, 4, 7, 0, 3, 5, 0, 4, 7, 0, 3, 5, 0, 4, 7, 0, 3, 3, 0, 4, 9, 0, 3, 3, 0, 4, 9, 0, 5, 1, 0, 0, 1, 0, -2, 7, 0, -1, 40, 0, -2, 4, 0, -3, 3, 0, -4, 16, 0, -5, 10, 0, -6, 20, 0, -7, 5, 0, -8, 39, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -4, 9, 0, -1, 36, 0, -2, 37, 0, -3, 12, 0, -4, 43, 0, -5, 38, 0, -6, 13, 0, -7, 8, 0, -8, 14, 0, 1, 15, 0, 1, 15, 0, 1, 15, 0, 1, 15, 0, 1, 15, 0, -1, 9, 0, -1, 8, 0, -2, 14, 0, 5, 3, 0, 0, 4, 0, -2, 55, 0, -1, 24, 0, -2, 26, 0, -3, 28, 0, -4, 30, 0, -5, 32, 0, -6, 34, 0, 1, 22, 0, 1, 22, 0, 1, 22, 0, 1, 22, 0, 1, 22, 0, 5, 5, 0, 1, 18, 0, 1, 18, 0, 1, 18, 0, 1, 18, 0, 1, 18, 0, 5, 6, 0, -1, 24, 0, -2, 26, 0, -3, 28, 0, -4, 30, 0, -5, 32, 0, -6, 34, 0, 0, 8, 0, 0, 8, 0, -1, 44, 0, 0, 10, 0, 0, 10, 0, -1, 47, 0, -2, 48, 0, -3, 11, 0, 0, 11, 0, 0, 11, 0, -1, 19, 0, -2, 51, 0, 0, 12, 0, 0, 12, 0, -1, 41, 0, -2, 42, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 16, 0, -1, 17, 0, -3, 46, 0, 0, 17, 0, 0, 17, 0, -1, 45, 0, 0, 19, 0, 0, 19, 0, -1, 49, 0, -2, 50, 0, 0, 20, 0, 0, 20, 0, -1, 21, 0, 0, 21, 0, 0, 21, 0, -1, 52, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, -1, 53, 0, 1, 25, 0, 1, 25, 0, 1, 25, 0, 1, 25, 0, 5, 24, 0, 1, 27, 0, 1, 27, 0, 1, 27, 0, 1, 27, 0, 5, 26, 0, 1, 29, 0, 1, 29, 0, 1, 29, 0, 1, 29, 0, 5, 28, 0, 1, 31, 0, 1, 31, 0, 1, 31, 0, 1, 31, 0, 5, 30, 0, 1, 33, 0, 1, 33, 0, 1, 33, 0, 1, 33, 0, 5, 32, 0, 1, 35, 0, 1, 35, 0, 1, 35, 0, 1, 35, 0, 5, 34, 0, 0, 36, 0, 0, 36, 0, -3, 56, 0, 0, 37, 0, 0, 37, 0, -3, 57, 0, 0, 38, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, -2, 54, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, -2, 58, 0, 0, 46, 0, -2, 59, 0, 0, 47, 0, -2, 60, 0, 0, 48, 0, -2, 61, 0, 0, 49, 0, -2, 62, 0, 0, 50, 0, -2, 63, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, -2, 69, 0, 0, 65, 0, 0, 67, 0, 0, 68, 0, 8, 1, 2, 11, 3, 6, 11, 16, 7, 35, 39, 7, 36, 63, 7, 37, 62, 7, 38, 11, 7, 39, 54, 7, 40, 55, 7, 41, 23, 7, 42, 69, 7, 43, 68, 7, 44, 66, 7, 45, 59, 7, 46, 64, 7, 47, 58, 7, 48, 17, 7, 49, 20, 7, 50, 21, 7, 51, 61, 7, 52, 60, 9, 12, 14, 9, 13, 57, 9, 14, 56, 64, 0, 65, 66, 0, 67, 201], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 58, 60, 62], [7, 7, 7, 7, 7, 7, 7, 2, -1, -2, -3, -4, -5, -6, 2, 2, -1, -2, -3, -4, 2, -1, 9, 6, -1, 9, 2, 2, 7, 2, 6, 2, 2, 7, 2, 6, 2, 2, 2], [5, 5, 5, 5, 5, 5, 43, 0, 0, 7, 8, 9, 10, 11, 12, 1, 1, 13, 14, 15, 16, 2, 2, 18, 3, 3, 17, 44, 45, 23, 46, 23, 47, 48, 22, 49, 50, 51, 52]], [[[73, "ani_ms_2033hd", ".bin", "\nani_ms_2033hd.png\nsize: 947,947\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n001/qiu_xian_00000\n  rotate: true\n  xy: 318, 559\n  size: 156, 150\n  orig: 180, 180\n  offset: 12, 18\n  index: -1\n001/qiu_xian_00001\n  rotate: true\n  xy: 317, 401\n  size: 156, 151\n  orig: 180, 180\n  offset: 12, 17\n  index: -1\n001/qiu_xian_00002\n  rotate: true\n  xy: 317, 243\n  size: 156, 152\n  orig: 180, 180\n  offset: 12, 16\n  index: -1\n001/qiu_xian_00003\n  rotate: true\n  xy: 784, 632\n  size: 155, 153\n  orig: 180, 180\n  offset: 13, 15\n  index: -1\n001/qiu_xian_00004\n  rotate: true\n  xy: 784, 475\n  size: 155, 153\n  orig: 180, 180\n  offset: 13, 15\n  index: -1\n001/qiu_xian_00005\n  rotate: true\n  xy: 628, 316\n  size: 155, 154\n  orig: 180, 180\n  offset: 13, 14\n  index: -1\n001/qiu_xian_00006\n  rotate: true\n  xy: 784, 318\n  size: 155, 154\n  orig: 180, 180\n  offset: 13, 14\n  index: -1\n001/qiu_xian_00007\n  rotate: true\n  xy: 630, 159\n  size: 155, 154\n  orig: 180, 180\n  offset: 13, 14\n  index: -1\n001/qiu_xian_00008\n  rotate: true\n  xy: 786, 161\n  size: 155, 154\n  orig: 180, 180\n  offset: 13, 14\n  index: -1\n001/qiu_xian_00009\n  rotate: false\n  xy: 471, 316\n  size: 155, 155\n  orig: 180, 180\n  offset: 13, 13\n  index: -1\n001/qiu_xian_00010\n  rotate: false\n  xy: 473, 159\n  size: 155, 155\n  orig: 180, 180\n  offset: 13, 13\n  index: -1\n001/qiu_xian_00011\n  rotate: false\n  xy: 473, 2\n  size: 155, 155\n  orig: 180, 180\n  offset: 13, 13\n  index: -1\n001/qiu_xian_00012\n  rotate: true\n  xy: 630, 2\n  size: 155, 154\n  orig: 180, 180\n  offset: 13, 13\n  index: -1\n001/qiu_xian_00013\n  rotate: false\n  xy: 786, 4\n  size: 155, 155\n  orig: 180, 180\n  offset: 13, 12\n  index: -1\n001/qiu_xian_00014\n  rotate: false\n  xy: 160, 401\n  size: 155, 156\n  orig: 180, 180\n  offset: 13, 12\n  index: -1\n001/qiu_xian_00015\n  rotate: false\n  xy: 724, 789\n  size: 155, 156\n  orig: 180, 180\n  offset: 13, 12\n  index: -1\n001/qiu_xian_00016\n  rotate: false\n  xy: 2, 85\n  size: 155, 156\n  orig: 180, 180\n  offset: 13, 12\n  index: -1\n001/qiu_xian_00017\n  rotate: false\n  xy: 2, 559\n  size: 156, 156\n  orig: 180, 180\n  offset: 12, 12\n  index: -1\n001/qiu_xian_00018\n  rotate: false\n  xy: 408, 789\n  size: 156, 156\n  orig: 180, 180\n  offset: 12, 12\n  index: -1\n001/qiu_xian_00019\n  rotate: false\n  xy: 160, 243\n  size: 155, 156\n  orig: 180, 180\n  offset: 13, 12\n  index: -1\n001/qiu_xian_00020\n  rotate: false\n  xy: 159, 85\n  size: 155, 156\n  orig: 180, 180\n  offset: 13, 12\n  index: -1\n001/qiu_xian_00021\n  rotate: false\n  xy: 316, 85\n  size: 155, 156\n  orig: 180, 180\n  offset: 13, 12\n  index: -1\n001/qiu_xian_00022\n  rotate: false\n  xy: 470, 631\n  size: 155, 156\n  orig: 180, 180\n  offset: 13, 12\n  index: -1\n001/qiu_xian_00023\n  rotate: false\n  xy: 470, 473\n  size: 155, 156\n  orig: 180, 180\n  offset: 13, 12\n  index: -1\n001/qiu_xian_00024\n  rotate: false\n  xy: 627, 631\n  size: 155, 156\n  orig: 180, 180\n  offset: 13, 12\n  index: -1\n001/qiu_xian_00025\n  rotate: false\n  xy: 627, 473\n  size: 155, 156\n  orig: 180, 180\n  offset: 13, 12\n  index: -1\n001/qiu_xian_00026\n  rotate: false\n  xy: 2, 401\n  size: 156, 156\n  orig: 180, 180\n  offset: 12, 12\n  index: -1\n001/qiu_xian_00027\n  rotate: false\n  xy: 160, 559\n  size: 156, 156\n  orig: 180, 180\n  offset: 12, 12\n  index: -1\n001/qiu_xian_00028\n  rotate: false\n  xy: 566, 789\n  size: 156, 156\n  orig: 180, 180\n  offset: 12, 12\n  index: -1\n001/qiu_xian_00029\n  rotate: false\n  xy: 2, 243\n  size: 156, 156\n  orig: 180, 180\n  offset: 12, 12\n  index: -1\neff_dian\n  rotate: false\n  xy: 408, 780\n  size: 7, 7\n  orig: 7, 7\n  offset: 0, 0\n  index: -1\neff_xian\n  rotate: false\n  xy: 881, 814\n  size: 2, 131\n  orig: 2, 154\n  offset: 0, 13\n  index: -1\nzhao\n  rotate: false\n  xy: 2, 717\n  size: 404, 228\n  orig: 408, 243\n  offset: 2, 14\n  index: -1\n", ["ani_ms_2033hd.png"], [0]], -1], 0, 0, [0], [-1], [53]]]]