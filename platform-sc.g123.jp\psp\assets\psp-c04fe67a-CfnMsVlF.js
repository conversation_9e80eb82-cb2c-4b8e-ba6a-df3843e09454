const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/psp-c35df622-_BoEEC7n.js","assets/psp-661cb157-D6Az8Wj_.js","assets/psp-cd6a1b8d-CSw1A9l7.js","assets/psp-68be3bf8-B37Yb1EQ.js"])))=>i.map(i=>d[i]);
import{n as R,al as N,F as w,am as vt,q as H,an as G,ao as $,b as tt,d as at,ap as O,aq as g,ar as Z,L as Vt,as as de,at as Ft,au as sn,av as se,aw as Pt,ax as on,f as P,ay as Se,v as mr,az as pt,aA as Ae,aB as fe,g as _r,aC as gr,h as br,k as vr,j as yr,t as j,l as Tr,aa as cn,p as yt,u as un,a6 as ln,w as Er,y as wr,z as dn,x as k,A as Sr,D as M,e as Ie,aD as Ce,a5 as Ar,B as Ir,I as V,s as L,aE as Cr,G as D,H as fn,K as F,aF as Re,aG as oe,a1 as Rr,aH as Et,aI as zt,aJ as pn,V as xr,aK as hn,aL as kr,a7 as wt,aM as U,aN as Nr,U as Or,_ as pe,X as Lr,$ as Mr,aO as Tt,aP as q,aQ as Dr,aR as mn,aS as it,N as _n,a9 as et,Q as Vr,C as Pr,S as Ur,R as xe,a0 as Fr,a as zr,E as Br,aT as gn,aU as Ut,aV as St,aW as Hr,aX as Lt,aY as he,aZ as Gr,c as Kr,a_ as me,a$ as $r,b0 as bn,P as Bt,b1 as _e,b2 as Wr,J as Yr,b3 as vn,a4 as Xr,b4 as jr,b5 as qr,ac as Zr,ab as Jr,M as Zt,b6 as Qr,b7 as ta,a8 as ea,ad as na,ai as ra,ae as aa,ah as ia,ag as sa,af as oa,b8 as ca,b9 as ua,ba as la,bb as da,bc as fa,bd as yn,be as ce,ak as Tn,aj as pa}from"./psp-661cb157-D6Az8Wj_.js";import{_ as En}from"./psp-cd6a1b8d-CSw1A9l7.js";function ge(t,e,n){return document.readyState===e||document.readyState==="complete"?(n(),{stop:R}):N(t,window,e==="complete"?"load":"DOMContentLoaded",n,{once:!0})}function ha(t,e){return new Promise(n=>{ge(t,e,n)})}function ma(t,e){if(window.requestIdleCallback&&window.cancelIdleCallback){const n=window.requestIdleCallback(w(t),e);return()=>window.cancelIdleCallback(n)}return ga(t)}const _a=50;function ga(t){const e=vt(),n=H(()=>{t({didTimeout:!1,timeRemaining:()=>Math.max(0,_a-(vt()-e))})},0);return()=>G(n)}const ba=$,va=30;function ya(){const t=[];function e(r){let a;if(r.didTimeout){const i=performance.now();a=()=>va-(performance.now()-i)}else a=r.timeRemaining.bind(r);for(;a()>0&&t.length;)t.shift()();t.length&&n()}function n(){ma(e,{timeout:ba})}return{push(r){t.push(r)===1&&n()}}}const b={ACTION:"action",ERROR:"error",LONG_TASK:"long_task",VIEW:"view",RESOURCE:"resource",VITAL:"vital"},wn={LONG_TASK:"long-task",LONG_ANIMATION_FRAME:"long-animation-frame"},X={INITIAL_LOAD:"initial_load",ROUTE_CHANGE:"route_change",BF_CACHE:"bf_cache"},be={CLICK:"click",CUSTOM:"custom"},mt={RAGE_CLICK:"rage_click",ERROR_CLICK:"error_click",DEAD_CLICK:"dead_click"},Sn={DURATION:"duration"};function Ta(){return{vitalsByName:new Map,vitalsByReference:new WeakMap}}function Ea(t,e,n){function r(i){return!e.wasInPageStateDuringPeriod("frozen",i.startClocks.relative,i.duration)}function a(i){r(i)&&t.notify(12,Sa(i))}return{addDurationVital:a,startDurationVital:(i,s={})=>An(n,i,s),stopDurationVital:(i,s={})=>{In(a,n,i,s)}}}function An({vitalsByName:t,vitalsByReference:e},n,r={}){const a={name:n,startClocks:tt(),context:r.context,description:r.description},i={__dd_vital_reference:!0};return t.set(n,a),e.set(i,a),i}function In(t,{vitalsByName:e,vitalsByReference:n},r,a={}){const i=typeof r=="string"?e.get(r):n.get(r);i&&(t(wa(i,i.startClocks,a,tt())),typeof r=="string"?e.delete(r):n.delete(r))}function wa(t,e,n,r){var a;return{name:t.name,type:Sn.DURATION,startClocks:e,duration:O(e.timeStamp,r.timeStamp),context:at(t.context,n.context),description:(a=n.description)!==null&&a!==void 0?a:t.description}}function Sa(t,e){const n={date:t.startClocks.timeStamp,vital:{id:Z(),type:t.type,name:t.name,duration:g(t.duration),description:t.description},type:b.VITAL};return n._dd={vital:{computed_value:!0}},{rawRumEvent:n,startTime:t.startClocks.relative,duration:t.duration,customerContext:t.context,domainContext:{}}}function Cn(t,e,n){if(t)for(const r of t){const a=r[e];a&&a(n)}}const ke=new Map;function Rn(t,e){if(e===100)return!0;if(e===0)return!1;const n=ke.get(e);if(n&&t===n.sessionId)return n.decision;let r;return window.BigInt?r=Aa(BigInt(`0x${t.split("-")[4]}`),e):r=Vt(e),ke.set(e,{sessionId:t,decision:r}),r}function Aa(t,e){const n=BigInt("1111111111111111111"),r=BigInt("0x1************0000"),a=t*n%r;return Number(a)<=e/100*Number(r)}function Ia(){return kn(64)}function xn(){return kn(63)}function kn(t){const e=crypto.getRandomValues(new Uint32Array(2));return t===63&&(e[e.length-1]>>>=1),{toString(n=10){let r=e[1],a=e[0],i="";do{const s=r%n*4294967296+a;r=Math.floor(r/n),a=Math.floor(s/n),i=(s%n).toString(n)+i}while(r||a);return i}}}function ut(t){return t.toString(16).padStart(16,"0")}function Ca(t){const e=t;return Ft(e)==="object"&&de(e.match)&&Array.isArray(e.propagatorTypes)}function Ra(t){t.status===0&&!t.isAborted&&(t.traceId=void 0,t.spanId=void 0,t.traceSampled=void 0)}function xa(t,e,n,r){return{clearTracingIfNeeded:Ra,traceFetch:a=>Ne(t,a,e,n,r,i=>{var s;if(a.input instanceof Request&&!(!((s=a.init)===null||s===void 0)&&s.headers))a.input=new Request(a.input),Object.keys(i).forEach(o=>{a.input.headers.append(o,i[o])});else{a.init=Pt(a.init);const o=[];a.init.headers instanceof Headers?a.init.headers.forEach((c,u)=>{o.push([u,c])}):Array.isArray(a.init.headers)?a.init.headers.forEach(c=>{o.push(c)}):a.init.headers&&Object.keys(a.init.headers).forEach(c=>{o.push([c,a.init.headers[c]])}),a.init.headers=o.concat(on(i))}}),traceXhr:(a,i)=>Ne(t,a,e,n,r,s=>{Object.keys(s).forEach(o=>{i.setRequestHeader(o,s[o])})})}}function Ne(t,e,n,r,a,i){const s=n.findTrackedSession();if(!s)return;const o=t.allowedTracingUrls.find(l=>sn([l.match],e.url,!0));if(!o)return;const c=Rn(s.id,t.traceSampleRate);(c||t.traceContextInjection===se.ALL)&&(e.traceSampled=c,e.traceId=Ia(),e.spanId=xn(),i(ka(e.traceId,e.spanId,e.traceSampled,s.id,o.propagatorTypes,r,a,t)))}function ka(t,e,n,r,a,i,s,o){const c={};if(a.forEach(u=>{switch(u){case"datadog":{Object.assign(c,{"x-datadog-origin":"rum","x-datadog-parent-id":e.toString(),"x-datadog-sampling-priority":n?"1":"0","x-datadog-trace-id":t.toString()});break}case"tracecontext":{Object.assign(c,{traceparent:`00-************0000${ut(t)}-${ut(e)}-0${n?"1":"0"}`,tracestate:`dd=s:${n?"1":"0"};o:rum`});break}case"b3":{Object.assign(c,{b3:`${ut(t)}-${ut(e)}-${n?"1":"0"}`});break}case"b3multi":{Object.assign(c,{"X-B3-TraceId":ut(t),"X-B3-SpanId":ut(e),"X-B3-Sampled":n?"1":"0"});break}}}),o.propagateTraceBaggage){const u={"session.id":r},l=i.getContext().id;typeof l=="string"&&(u["user.id"]=l);const d=s.getContext().id;typeof d=="string"&&(u["account.id"]=d);const f=Object.entries(u).map(([m,p])=>`${m}=${encodeURIComponent(p)}`).join(",");f&&(c.baggage=f)}return c}const Nn=["tracecontext","datadog"];function Na(t){var e,n,r,a,i,s,o;if(t.trackFeatureFlagsForEvents!==void 0&&!Array.isArray(t.trackFeatureFlagsForEvents)&&P.warn("trackFeatureFlagsForEvents should be an array"),!t.applicationId){P.error("Application ID is not configured, no RUM data will be collected.");return}if(!Se(t.sessionReplaySampleRate,"Session Replay")||!Se(t.traceSampleRate,"Trace"))return;if(t.excludedActivityUrls!==void 0&&!Array.isArray(t.excludedActivityUrls)){P.error("Excluded Activity Urls should be an array");return}const c=Oa(t);if(!c)return;const u=mr(t);if(!u)return;const l=(e=t.sessionReplaySampleRate)!==null&&e!==void 0?e:0;return{applicationId:t.applicationId,version:t.version||void 0,actionNameAttribute:t.actionNameAttribute,sessionReplaySampleRate:l,startSessionReplayRecordingManually:t.startSessionReplayRecordingManually!==void 0?!!t.startSessionReplayRecordingManually:l===0,traceSampleRate:(n=t.traceSampleRate)!==null&&n!==void 0?n:100,rulePsr:fe(t.traceSampleRate)?t.traceSampleRate/100:void 0,allowedTracingUrls:c,excludedActivityUrls:(r=t.excludedActivityUrls)!==null&&r!==void 0?r:[],workerUrl:t.workerUrl,compressIntakeRequests:!!t.compressIntakeRequests,trackUserInteractions:!!(!((a=t.trackUserInteractions)!==null&&a!==void 0)||a),trackViewsManually:!!t.trackViewsManually,trackResources:!!(!((i=t.trackResources)!==null&&i!==void 0)||i),trackLongTasks:!!(!((s=t.trackLongTasks)!==null&&s!==void 0)||s),trackBfcacheViews:!!t.trackBfcacheViews,subdomain:t.subdomain,defaultPrivacyLevel:Ae(pt,t.defaultPrivacyLevel)?t.defaultPrivacyLevel:pt.MASK,enablePrivacyForActionName:!!t.enablePrivacyForActionName,customerDataTelemetrySampleRate:1,traceContextInjection:Ae(se,t.traceContextInjection)?t.traceContextInjection:se.SAMPLED,plugins:t.plugins||[],trackFeatureFlagsForEvents:t.trackFeatureFlagsForEvents||[],profilingSampleRate:(o=t.profilingSampleRate)!==null&&o!==void 0?o:0,propagateTraceBaggage:!!t.propagateTraceBaggage,...u}}function Oa(t){if(t.allowedTracingUrls===void 0)return[];if(!Array.isArray(t.allowedTracingUrls)){P.error("Allowed Tracing URLs should be an array");return}if(t.allowedTracingUrls.length!==0&&t.service===void 0){P.error("Service needs to be configured when tracing is enabled");return}const e=[];return t.allowedTracingUrls.forEach(n=>{de(n)?e.push({match:n,propagatorTypes:Nn}):Ca(n)?e.push(n):P.warn("Allowed Tracing Urls parameters should be a string, RegExp, function, or an object. Ignoring parameter",n)}),e}function La(t){const e=new Set;return Array.isArray(t.allowedTracingUrls)&&t.allowedTracingUrls.length>0&&t.allowedTracingUrls.forEach(n=>{de(n)?Nn.forEach(r=>e.add(r)):Ft(n)==="object"&&Array.isArray(n.propagatorTypes)&&n.propagatorTypes.forEach(r=>e.add(r))}),Array.from(e)}function Ma(t){var e;const n=_r(t);return{session_replay_sample_rate:t.sessionReplaySampleRate,start_session_replay_recording_manually:t.startSessionReplayRecordingManually,trace_sample_rate:t.traceSampleRate,trace_context_injection:t.traceContextInjection,action_name_attribute:t.actionNameAttribute,use_allowed_tracing_urls:Array.isArray(t.allowedTracingUrls)&&t.allowedTracingUrls.length>0,selected_tracing_propagators:La(t),default_privacy_level:t.defaultPrivacyLevel,enable_privacy_for_action_name:t.enablePrivacyForActionName,use_excluded_activity_urls:Array.isArray(t.excludedActivityUrls)&&t.excludedActivityUrls.length>0,use_worker_url:!!t.workerUrl,compress_intake_requests:t.compressIntakeRequests,track_views_manually:t.trackViewsManually,track_user_interactions:t.trackUserInteractions,track_resources:t.trackResources,track_long_task:t.trackLongTasks,track_bfcache_views:t.trackBfcacheViews,plugins:(e=t.plugins)===null||e===void 0?void 0:e.map(r=>{var a;return{name:r.name,...(a=r.getConfigurationTelemetry)===null||a===void 0?void 0:a.call(r)}}),track_feature_flags_for_events:t.trackFeatureFlagsForEvents,...n}}const Da="v1";function Va(t,e){Ua(t,n=>{e(Pa(t,n))})}function Pa(t,e){return{...t,...e}}function Ua(t,e){const n=new XMLHttpRequest;N(t,n,"load",function(){if(n.status===200){const r=JSON.parse(n.responseText);e(r.rum)}else Oe()}),N(t,n,"error",function(){Oe()}),n.open("GET",Fa(t)),n.send()}function Fa(t){return`https://sdk-configuration.${gr("rum",t)}/${Da}/${encodeURIComponent(t.remoteConfigurationId)}.json`}function Oe(){P.error("Error fetching the remote configuration.")}function za({ignoreInitIfSyntheticsWillInjectRum:t=!0,startDeflateWorker:e},n,r,a){const i=Er(),s=br();Jt(s,k.globalContext,i);const o=vr();Jt(o,k.userContext,i);const c=yr();Jt(c,k.accountContext,i);let u,l,d,f;const m=n.observable.subscribe(h),p={};function h(){if(!d||!f||!n.isGranted())return;m.unsubscribe();let _;if(f.trackViewsManually){if(!u)return;i.remove(u.callback),_=u.options}const v=a(f,l,_);i.drain(v)}function y(_){const v=yt();if(v&&(_=Ba(_)),d=_,H(()=>{wr(Ma(_))}),f){dn("DD_RUM",_);return}const A=Na(_);if(A){if(!v&&!A.sessionStoreStrategyType){P.warn("No storage available for session. We will not send any data.");return}A.compressIntakeRequests&&!v&&e&&(l=e(A,"Datadog RUM",R),!l)||(f=A,un().subscribe(R),n.tryToInit(A.trackingConsent),h())}}const S=_=>{i.add(v=>v.addDurationVital(_))};return{init(_,v){if(!_){P.error("Missing configuration");return}Tr(_.enableExperimentalFeatures),d=_,!(t&&cn())&&(Cn(_.plugins,"onInit",{initConfiguration:_,publicApi:v}),_.remoteConfigurationId?Va(_,y):y(_))},get initConfiguration(){return d},getInternalContext:R,stopSession:R,addTiming(_,v=j()){i.add(A=>A.addTiming(_,v))},startView(_,v=tt()){const A=W=>{W.startView(_,v)};i.add(A),u||(u={options:_,callback:A},h())},setViewName(_){i.add(v=>v.setViewName(_))},setViewContext(_){i.add(v=>v.setViewContext(_))},setViewContextProperty(_,v){i.add(A=>A.setViewContextProperty(_,v))},getViewContext:()=>p,globalContext:s,userContext:o,accountContext:c,addAction(_){i.add(v=>v.addAction(_))},addError(_){i.add(v=>v.addError(_))},addFeatureFlagEvaluation(_,v){i.add(A=>A.addFeatureFlagEvaluation(_,v))},startDurationVital(_,v){return An(r,_,v)},stopDurationVital(_,v){In(S,r,_,v)},addDurationVital:S}}function Ba(t){var e,n;return{...t,applicationId:"********-aaaa-0000-aaaa-************",clientToken:"empty",sessionSampleRate:100,defaultPrivacyLevel:(e=t.defaultPrivacyLevel)!==null&&e!==void 0?e:(n=ln())===null||n===void 0?void 0:n.getPrivacyLevel()}}function Jt(t,e,n){t.changeObservable.subscribe(()=>{const r=t.getContext();n.add(a=>a[e].setContext(r))})}function Ha(t,e,n,r={}){const a=Ir(),i=Ta();let s=za(r,a,i,(l,d,f)=>{const m=t(l,e,n,f,d&&r.createDeflateEncoder?p=>r.createDeflateEncoder(l,d,p):Ar,a,i,r.sdkName);return e.onRumStart(m.lifeCycle,l,m.session,m.viewHistory,d),n.onRumStart(m.lifeCycle,m.hooks,l,m.session,m.viewHistory),s=Ga(s,m),Cn(l.plugins,"onRumStart",{strategy:s,addEvent:m.addEvent}),m});const o=()=>s,c=w(l=>{const d=typeof l=="object"?l:{name:l};s.startView(d),V({feature:"start-view"})}),u=Sr({init:w(l=>{s.init(l,u)}),setTrackingConsent:w(l=>{a.update(l),V({feature:"set-tracking-consent",tracking_consent:l})}),setViewName:w(l=>{s.setViewName(l),V({feature:"set-view-name"})}),setViewContext:w(l=>{s.setViewContext(l),V({feature:"set-view-context"})}),setViewContextProperty:w((l,d)=>{s.setViewContextProperty(l,d),V({feature:"set-view-context-property"})}),getViewContext:w(()=>(V({feature:"set-view-context-property"}),s.getViewContext())),getInternalContext:w(l=>s.getInternalContext(l)),getInitConfiguration:w(()=>fn(s.initConfiguration)),addAction:(l,d)=>{const f=Ie("action");Ce(()=>{s.addAction({name:L(l),context:L(d),startClocks:tt(),type:be.CUSTOM,handlingStack:f}),V({feature:"add-action"})})},addError:(l,d)=>{const f=Ie("error");Ce(()=>{s.addError({error:l,handlingStack:f,context:L(d),startClocks:tt()}),V({feature:"add-error"})})},addTiming:w((l,d)=>{s.addTiming(L(l),d)}),setGlobalContext:M(o,k.globalContext,D.setContext,"set-global-context"),getGlobalContext:M(o,k.globalContext,D.getContext,"get-global-context"),setGlobalContextProperty:M(o,k.globalContext,D.setContextProperty,"set-global-context-property"),removeGlobalContextProperty:M(o,k.globalContext,D.removeContextProperty,"remove-global-context-property"),clearGlobalContext:M(o,k.globalContext,D.clearContext,"clear-global-context"),setUser:M(o,k.userContext,D.setContext,"set-user"),getUser:M(o,k.userContext,D.getContext,"get-user"),setUserProperty:M(o,k.userContext,D.setContextProperty,"set-user-property"),removeUserProperty:M(o,k.userContext,D.removeContextProperty,"remove-user-property"),clearUser:M(o,k.userContext,D.clearContext,"clear-user"),setAccount:M(o,k.accountContext,D.setContext,"set-account"),getAccount:M(o,k.accountContext,D.getContext,"get-account"),setAccountProperty:M(o,k.accountContext,D.setContextProperty,"set-account-property"),removeAccountProperty:M(o,k.accountContext,D.removeContextProperty,"remove-account-property"),clearAccount:M(o,k.accountContext,D.clearContext,"clear-account"),startView:c,stopSession:w(()=>{s.stopSession(),V({feature:"stop-session"})}),addFeatureFlagEvaluation:w((l,d)=>{s.addFeatureFlagEvaluation(L(l),L(d)),V({feature:"add-feature-flag-evaluation"})}),getSessionReplayLink:w(()=>e.getSessionReplayLink()),startSessionReplayRecording:w(l=>{e.start(l),V({feature:"start-session-replay-recording",force:l&&l.force})}),stopSessionReplayRecording:w(()=>e.stop()),addDurationVital:w((l,d)=>{V({feature:"add-duration-vital"}),s.addDurationVital({name:L(l),type:Sn.DURATION,startClocks:Cr(d.startTime),duration:d.duration,context:L(d&&d.context),description:L(d&&d.description)})}),startDurationVital:w((l,d)=>(V({feature:"start-duration-vital"}),s.startDurationVital(L(l),{context:L(d&&d.context),description:L(d&&d.description)}))),stopDurationVital:w((l,d)=>{V({feature:"stop-duration-vital"}),s.stopDurationVital(typeof l=="string"?L(l):l,{context:L(d&&d.context),description:L(d&&d.description)})})});return u}function Ga(t,e){return{init:n=>{dn("DD_RUM",n)},initConfiguration:t.initConfiguration,...e}}function Ka(){const t=$a();return new F(e=>{if(!t)return;const n=new t(w(r=>e.notify(r)));return n.observe(document,{attributes:!0,characterData:!0,childList:!0,subtree:!0}),()=>n.disconnect()})}function $a(){let t;const e=window;if(e.Zone&&(t=Re(e,"MutationObserver"),e.MutationObserver&&t===e.MutationObserver)){const n=new e.MutationObserver(R),r=Re(n,"originalInstance");t=r&&r.constructor}return t||(t=e.MutationObserver),t}function Wa(){const t=new F,{stop:e}=oe(window,"open",()=>t.notify());return{observable:t,stop:e}}function Ya(t,e,n,r,a){return{get:i=>{const s=n.findView(i),o=a.findUrl(i),c=e.findTrackedSession(i);if(c&&s&&o){const u=r.findActionId(i);return{application_id:t,session_id:c.id,user_action:u?{id:u}:void 0,view:{id:s.id,name:s.name,referrer:o.referrer,url:o.url}}}}}}const Xa=Rr,ja=zt;function qa(t){const e=Et({expireDelay:ja});t.subscribe(1,r=>{e.add(n(r),r.startClocks.relative)}),t.subscribe(6,({endClocks:r})=>{e.closeActive(r.relative)}),t.subscribe(3,r=>{const a=e.find(r.startClocks.relative);a&&(r.name&&(a.name=r.name),r.context&&(a.context=r.context),a.sessionIsActive=r.sessionIsActive)}),t.subscribe(10,()=>{e.reset()});function n(r){return{service:r.service,version:r.version,context:r.context,id:r.id,name:r.name,startClocks:r.startClocks}}return{findView:r=>e.find(r),stop:()=>{e.stop()}}}const On="initial_document",Za=[[U.DOCUMENT,t=>On===t],[U.XHR,t=>t==="xmlhttprequest"],[U.FETCH,t=>t==="fetch"],[U.BEACON,t=>t==="beacon"],[U.CSS,(t,e)=>/\.css$/i.test(e)],[U.JS,(t,e)=>/\.js$/i.test(e)],[U.IMAGE,(t,e)=>["image","img","icon"].includes(t)||/\.(gif|jpg|jpeg|tiff|png|svg|ico)$/i.exec(e)!==null],[U.FONT,(t,e)=>/\.(woff|eot|woff2|ttf)$/i.exec(e)!==null],[U.MEDIA,(t,e)=>["audio","video"].includes(t)||/\.(mp3|mp4)$/i.exec(e)!==null]];function Ja(t){const e=t.name;if(!kr(e))return wt(`Failed to construct URL for "${t.name}"`),U.OTHER;const n=Nr(e);for(const[r,a]of Za)if(a(t.initiatorType,n))return r;return U.OTHER}function Le(...t){for(let e=1;e<t.length;e+=1)if(t[e-1]>t[e])return!1;return!0}function Qa(t){return t.initiatorType==="xmlhttprequest"||t.initiatorType==="fetch"}function Ln(t){const{duration:e,startTime:n,responseEnd:r}=t;return e===0&&n<r?O(n,r):e}function ti(t){if(!Dn(t))return;const{startTime:e,fetchStart:n,workerStart:r,redirectStart:a,redirectEnd:i,domainLookupStart:s,domainLookupEnd:o,connectStart:c,secureConnectionStart:u,connectEnd:l,requestStart:d,responseStart:f,responseEnd:m}=t,p={download:rt(e,f,m),first_byte:rt(e,d,f)};return 0<r&&r<n&&(p.worker=rt(e,r,n)),n<l&&(p.connect=rt(e,c,l),c<=u&&u<=l&&(p.ssl=rt(e,u,l))),n<o&&(p.dns=rt(e,s,o)),e<i&&(p.redirect=rt(e,a,i)),p}function Mn(t){return t.duration>=0}function Dn(t){const e=Le(t.startTime,t.fetchStart,t.domainLookupStart,t.domainLookupEnd,t.connectStart,t.connectEnd,t.requestStart,t.responseStart,t.responseEnd),n=ei(t)?Le(t.startTime,t.redirectStart,t.redirectEnd,t.fetchStart):!0;return e&&n}function ei(t){return t.redirectEnd>t.startTime}function rt(t,e,n){if(t<=e&&e<=n)return{duration:g(O(e,n)),start:g(O(t,e))}}function Vn(t){return t.nextHopProtocol===""?void 0:t.nextHopProtocol}function Pn(t){return t.deliveryType===""?"other":t.deliveryType}function ni(t){if(t.startTime<t.responseStart){const{encodedBodySize:e,decodedBodySize:n,transferSize:r}=t;return{size:n,encoded_body_size:e,decoded_body_size:n,transfer_size:r}}return{size:void 0,encoded_body_size:void 0,decoded_body_size:void 0,transfer_size:void 0}}function ve(t){return t&&(!xr(t)||pn(hn.TRACK_INTAKE_REQUESTS))}const ri=/data:(.+)?(;base64)?,/g,ai=24e3;function ii(t,e=ai){if(t.length<=e||!t.startsWith("data:"))return t;const n=t.substring(0,100).match(ri);return n?`${n[0]}[...]`:t}let Me=1;function si(t,e,n,r,a){const i=xa(e,n,r,a);oi(t,e,i),ci(t,i)}function oi(t,e,n){const r=Or(e).subscribe(a=>{const i=a;if(ve(i.url))switch(i.state){case"start":n.traceXhr(i,i.xhr),i.requestIndex=Un(),t.notify(7,{requestIndex:i.requestIndex,url:i.url});break;case"complete":n.clearTracingIfNeeded(i),t.notify(8,{duration:i.duration,method:i.method,requestIndex:i.requestIndex,spanId:i.spanId,startClocks:i.startClocks,status:i.status,traceId:i.traceId,traceSampled:i.traceSampled,type:pe.XHR,url:i.url,xhr:i.xhr,isAborted:i.isAborted,handlingStack:i.handlingStack});break}});return{stop:()=>r.unsubscribe()}}function ci(t,e){const n=un().subscribe(r=>{const a=r;if(ve(a.url))switch(a.state){case"start":e.traceFetch(a),a.requestIndex=Un(),t.notify(7,{requestIndex:a.requestIndex,url:a.url});break;case"resolve":ui(a,i=>{e.clearTracingIfNeeded(a),t.notify(8,{duration:i,method:a.method,requestIndex:a.requestIndex,responseType:a.responseType,spanId:a.spanId,startClocks:a.startClocks,status:a.status,traceId:a.traceId,traceSampled:a.traceSampled,type:pe.FETCH,url:a.url,response:a.response,init:a.init,input:a.input,isAborted:a.isAborted,handlingStack:a.handlingStack})});break}});return{stop:()=>n.unsubscribe()}}function Un(){const t=Me;return Me+=1,t}function ui(t,e){const n=t.response&&Lr(t.response);!n||!n.body?e(O(t.startClocks.timeStamp,j())):Mr(n.body,()=>{e(O(t.startClocks.timeStamp,j()))},{bytesLimit:Number.POSITIVE_INFINITY,collectStreamBody:!1})}function Fn(t){return fe(t)&&t<0?void 0:t}function zn({lifeCycle:t,isChildEvent:e,onChange:n=R}){const r={errorCount:0,longTaskCount:0,resourceCount:0,actionCount:0,frustrationCount:0},a=t.subscribe(13,i=>{var s;if(!(i.type==="view"||i.type==="vital"||!e(i)))switch(i.type){case b.ERROR:r.errorCount+=1,n();break;case b.ACTION:r.actionCount+=1,i.action.frustration&&(r.frustrationCount+=i.action.frustration.type.length),n();break;case b.LONG_TASK:r.longTaskCount+=1,n();break;case b.RESOURCE:!((s=i._dd)===null||s===void 0)&&s.discarded||(r.resourceCount+=1,n());break}});return{stop:()=>{a.unsubscribe()},eventCounts:r}}function li(t,e){const n=vt();let r=!1;const{stop:a}=Tt(t,window,["click","mousedown","keydown","touchstart","pointerdown"],o=>{if(!o.cancelable)return;const c={entryType:"first-input",processingStart:q(),processingEnd:q(),startTime:o.timeStamp,duration:0,name:"",cancelable:!1,target:null,toJSON:()=>({})};o.type==="pointerdown"?i(t,c):s(c)},{passive:!0,capture:!0});return{stop:a};function i(o,c){Tt(o,window,["pointerup","pointercancel"],u=>{u.type==="pointerup"&&s(c)},{once:!0})}function s(o){if(!r){r=!0,a();const c=o.processingStart-o.startTime;c>=0&&c<vt()-n&&e(o)}}}var T;(function(t){t.EVENT="event",t.FIRST_INPUT="first-input",t.LARGEST_CONTENTFUL_PAINT="largest-contentful-paint",t.LAYOUT_SHIFT="layout-shift",t.LONG_TASK="longtask",t.LONG_ANIMATION_FRAME="long-animation-frame",t.NAVIGATION="navigation",t.PAINT="paint",t.RESOURCE="resource",t.VISIBILITY_STATE="visibility-state"})(T||(T={}));function K(t,e){return new F(n=>{if(!window.PerformanceObserver)return;const r=c=>{const u=pi(c);u.length>0&&n.notify(u)};let a,i=!0;const s=new PerformanceObserver(w(c=>{i?a=H(()=>r(c.getEntries())):r(c.getEntries())}));try{s.observe(e)}catch{if([T.RESOURCE,T.NAVIGATION,T.LONG_TASK,T.PAINT].includes(e.type)){e.buffered&&(a=H(()=>r(performance.getEntriesByType(e.type))));try{s.observe({entryTypes:[e.type]})}catch{return}}}i=!1,di(t);let o;return!st(T.FIRST_INPUT)&&e.type===T.FIRST_INPUT&&({stop:o}=li(t,c=>{r([c])})),()=>{s.disconnect(),o&&o(),G(a)}})}let _t;function di(t){return!_t&&fi()&&"addEventListener"in performance&&(_t=N(t,performance,"resourcetimingbufferfull",()=>{performance.clearResourceTimings()})),()=>{_t?.stop()}}function fi(){return window.performance!==void 0&&"getEntries"in performance}function st(t){return window.PerformanceObserver&&PerformanceObserver.supportedEntryTypes!==void 0&&PerformanceObserver.supportedEntryTypes.includes(t)}function pi(t){return t.filter(e=>!hi(e))}function hi(t){return t.entryType===T.RESOURCE&&(!ve(t.name)||!Mn(t))}function Bn(t){return t.nodeType===Node.TEXT_NODE}function mi(t){return t.nodeType===Node.COMMENT_NODE}function ot(t){return t.nodeType===Node.ELEMENT_NODE}function Hn(t){return ot(t)&&!!t.shadowRoot}function _i(t){const e=t;return!!e.host&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&ot(e.host)}function Gc(t){return t.childNodes.length>0||Hn(t)}function Kc(t,e){let n=t.firstChild;for(;n;)e(n),n=n.nextSibling;Hn(t)&&e(t.shadowRoot)}function gi(t){return _i(t)?t.host:t.parentNode}const Gn=100,bi=100,De="data-dd-excluded-activity-mutations";function ye(t,e,n,r,a,i){const s=yi(t,e,n,r);return vi(s,a,i)}function vi(t,e,n){let r,a=!1;const i=H(w(()=>u({hadActivity:!1})),Gn),s=n!==void 0?H(w(()=>u({hadActivity:!0,end:j()})),n):void 0,o=t.subscribe(({isBusy:l})=>{G(i),G(r);const d=j();l||(r=H(w(()=>u({hadActivity:!0,end:d})),bi))}),c=()=>{a=!0,G(i),G(r),G(s),o.unsubscribe()};function u(l){a||(c(),e(l))}return{stop:c}}function yi(t,e,n,r){return new F(a=>{const i=[];let s,o=0;return i.push(e.subscribe(u=>{u.every(Ti)||c()}),n.subscribe(c),K(r,{type:T.RESOURCE}).subscribe(u=>{u.some(l=>!Qt(r,l.name))&&c()}),t.subscribe(7,u=>{Qt(r,u.url)||(s===void 0&&(s=u.requestIndex),o+=1,c())}),t.subscribe(8,u=>{Qt(r,u.url)||s===void 0||u.requestIndex<s||(o-=1,c())})),()=>{i.forEach(u=>u.unsubscribe())};function c(){a.notify({isBusy:o>0})}})}function Qt(t,e){return sn(t.excludedActivityUrls,e)}function Ti(t){const e=t.type==="characterData"?t.target.parentElement:t.target;return!!(e&&ot(e)&&e.matches(`[${De}], [${De}] *`))}const E={IGNORE:"ignore",HIDDEN:"hidden",ALLOW:pt.ALLOW,MASK:pt.MASK,MASK_USER_INPUT:pt.MASK_USER_INPUT},Ei="data-dd-privacy",$c="hidden",wi="dd-privacy-",te="***",Wc="data:image/gif;base64,R0lGODlhAQABAIAAAMLCwgAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw==",Si={INPUT:!0,OUTPUT:!0,TEXTAREA:!0,SELECT:!0,OPTION:!0,DATALIST:!0,OPTGROUP:!0},Ai="x";function Kn(t,e,n){if(n&&n.has(t))return n.get(t);const r=gi(t),a=r?Kn(r,e,n):e,i=Ci(t),s=Ii(i,a);return n&&n.set(t,s),s}function Ii(t,e){switch(e){case E.HIDDEN:case E.IGNORE:return e}switch(t){case E.ALLOW:case E.MASK:case E.MASK_USER_INPUT:case E.HIDDEN:case E.IGNORE:return t;default:return e}}function Ci(t){if(ot(t)){if(t.tagName==="BASE")return E.ALLOW;if(t.tagName==="INPUT"){const e=t;if(e.type==="password"||e.type==="email"||e.type==="tel"||e.type==="hidden")return E.MASK;const n=e.getAttribute("autocomplete");if(n&&(n.startsWith("cc-")||n.endsWith("-password")))return E.MASK}if(t.matches(ft(E.HIDDEN)))return E.HIDDEN;if(t.matches(ft(E.MASK)))return E.MASK;if(t.matches(ft(E.MASK_USER_INPUT)))return E.MASK_USER_INPUT;if(t.matches(ft(E.ALLOW)))return E.ALLOW;if(ki(t))return E.IGNORE}}function Ri(t,e){switch(e){case E.MASK:case E.HIDDEN:case E.IGNORE:return!0;case E.MASK_USER_INPUT:return Bn(t)?Ve(t.parentNode):Ve(t);default:return!1}}function Ve(t){if(!t||t.nodeType!==t.ELEMENT_NODE)return!1;const e=t;if(e.tagName==="INPUT")switch(e.type){case"button":case"color":case"reset":case"submit":return!1}return!!Si[e.tagName]}const xi=t=>t.replace(/\S/g,Ai);function Yc(t,e,n){var r;const a=(r=t.parentElement)===null||r===void 0?void 0:r.tagName;let i=t.textContent||"";if(e&&!i.trim())return;const s=n;if(a==="SCRIPT")i=te;else if(s===E.HIDDEN)i=te;else if(Ri(t,s))if(a==="DATALIST"||a==="SELECT"||a==="OPTGROUP"){if(!i.trim())return}else a==="OPTION"?i=te:i=xi(i);return i}function ki(t){if(t.nodeName==="SCRIPT")return!0;if(t.nodeName==="LINK"){const n=e("rel");return/preload|prefetch/i.test(n)&&e("as")==="script"||n==="shortcut icon"||n==="icon"}if(t.nodeName==="META"){const n=e("name"),r=e("rel"),a=e("property");return/^msapplication-tile(image|color)$/.test(n)||n==="application-name"||r==="icon"||r==="apple-touch-icon"||r==="shortcut icon"||n==="keywords"||n==="description"||/^(og|twitter|fb):/.test(a)||/^(og|twitter):/.test(n)||n==="pinterest"||n==="robots"||n==="googlebot"||n==="bingbot"||t.hasAttribute("http-equiv")||n==="author"||n==="generator"||n==="framework"||n==="publisher"||n==="progid"||/^article:/.test(a)||/^product:/.test(a)||n==="google-site-verification"||n==="yandex-verification"||n==="csrf-token"||n==="p:domain_verify"||n==="verify-v1"||n==="verification"||n==="shopify-checkout-api-token"}function e(n){return(t.getAttribute(n)||"").toLowerCase()}return!1}function ft(t){return`[${Ei}="${t}"], .${wi}${t}`}const Te="data-dd-action-name",Ni="Masked Element";function Oi(t,{enablePrivacyForActionName:e,actionNameAttribute:n},r){const a=Pe(t,Te)||n&&Pe(t,n);return a?{name:a,nameSource:"custom_attribute"}:r===E.MASK?{name:Ni,nameSource:"mask_placeholder"}:Ue(t,n,Li,e)||Ue(t,n,Mi,e)||{name:"",nameSource:"blank"}}function Pe(t,e){const n=t.closest(`[${e}]`);if(!n)return;const r=n.getAttribute(e);return Wn($n(r.trim()))}const Li=[(t,e)=>{if("labels"in t&&t.labels&&t.labels.length>0)return Mt(t.labels[0],e)},t=>{if(t.nodeName==="INPUT"){const e=t,n=e.getAttribute("type");if(n==="button"||n==="submit"||n==="reset")return{name:e.value,nameSource:"text_content"}}},(t,e,n)=>{if(t.nodeName==="BUTTON"||t.nodeName==="LABEL"||t.getAttribute("role")==="button")return Mt(t,e,n)},t=>gt(t,"aria-label"),(t,e,n)=>{const r=t.getAttribute("aria-labelledby");if(r)return{name:r.split(/\s+/).map(a=>Vi(t,a)).filter(a=>!!a).map(a=>Yn(a,e,n)).join(" "),nameSource:"text_content"}},t=>gt(t,"alt"),t=>gt(t,"name"),t=>gt(t,"title"),t=>gt(t,"placeholder"),(t,e)=>{if("options"in t&&t.options.length>0)return Mt(t.options[0],e)}],Mi=[(t,e,n)=>Mt(t,e,n)],Di=10;function Ue(t,e,n,r){let a=t,i=0;for(;i<=Di&&a&&a.nodeName!=="BODY"&&a.nodeName!=="HTML"&&a.nodeName!=="HEAD";){for(const s of n){const o=s(a,e,r);if(o){const{name:c,nameSource:u}=o,l=c&&c.trim();if(l)return{name:Wn($n(l)),nameSource:u}}}if(a.nodeName==="FORM")break;a=a.parentElement,i+=1}}function $n(t){return t.replace(/\s+/g," ")}function Wn(t){return t.length>100?`${Dr(t,100)} [...]`:t}function Vi(t,e){return t.ownerDocument?t.ownerDocument.getElementById(e):null}function gt(t,e){return{name:t.getAttribute(e)||"",nameSource:"standard_attribute"}}function Mt(t,e,n){return{name:Yn(t,e,n)||"",nameSource:"text_content"}}function Yn(t,e,n){if(!t.isContentEditable){if("innerText"in t){let r=t.innerText;const a=i=>{const s=t.querySelectorAll(i);for(let o=0;o<s.length;o+=1){const c=s[o];if("innerText"in c){const u=c.innerText;u&&u.trim().length>0&&(r=r.replace(u,""))}}};return a(`[${Te}]`),e&&a(`[${e}]`),n&&a(`${ft(E.HIDDEN)}, ${ft(E.MASK)}`),r}return t.textContent}}const Pi=[Te,"data-testid","data-test","data-qa","data-cy","data-test-id","data-qa-id","data-testing","data-component","data-element","data-source-file"],Ui=[jn,zi],Fi=[jn,Bi,Hi];function At(t,e){if(!t.isConnected)return;let n,r=t;for(;r&&r.nodeName!=="HTML";){const a=Fe(r,Ui,Ki,e,n);if(a)return a;n=Fe(r,Fi,$i,e,n)||Ht(Gi(r),n),r=r.parentElement}return n}function Xn(t){return/[0-9]/.test(t)}function zi(t){if(t.id&&!Xn(t.id))return`#${CSS.escape(t.id)}`}function Bi(t){if(t.tagName==="BODY")return;const e=t.classList;for(let n=0;n<e.length;n+=1){const r=e[n];if(!Xn(r))return`${CSS.escape(t.tagName)}.${CSS.escape(r)}`}}function Hi(t){return CSS.escape(t.tagName)}function jn(t,e){if(e){const r=n(e);if(r)return r}for(const r of Pi){const a=n(r);if(a)return a}function n(r){if(t.hasAttribute(r))return`${CSS.escape(t.tagName)}[${r}="${CSS.escape(t.getAttribute(r))}"]`}}function Gi(t){let e=t.parentElement.firstElementChild,n=1;for(;e&&e!==t;)e.tagName===t.tagName&&(n+=1),e=e.nextElementSibling;return`${CSS.escape(t.tagName)}:nth-of-type(${n})`}function Fe(t,e,n,r,a){for(const i of e){const s=i(t,r);if(s&&n(t,s,a))return Ht(s,a)}}function Ki(t,e,n){return t.ownerDocument.querySelectorAll(Ht(e,n)).length===1}function $i(t,e,n){let r;if(n===void 0)r=s=>s.matches(e);else{const s=Ht(`${e}:scope`,n);r=o=>o.querySelector(s)!==null}let i=t.parentElement.firstElementChild;for(;i;){if(i!==t&&r(i))return!1;i=i.nextElementSibling}return!0}function Ht(t,e){return e?`${t}>${e}`:t}const qn=$,Wi=100;function Yi(t,e){const n=[];let r=0,a;i(t);function i(c){c.stopObservable.subscribe(s),n.push(c),G(a),a=H(o,qn)}function s(){r===1&&n.every(c=>c.isStopped())&&(r=2,e(n))}function o(){G(a),r===0&&(r=1,s())}return{tryAppend:c=>r!==0?!1:n.length>0&&!Xi(n[n.length-1].event,c.event)?(o(),!1):(i(c),!0),stop:()=>{o()}}}function Xi(t,e){return t.target===e.target&&ji(t,e)<=Wi&&t.timeStamp-e.timeStamp<=qn}function ji(t,e){return Math.sqrt(Math.pow(t.clientX-e.clientX,2)+Math.pow(t.clientY-e.clientY,2))}function qi(t,{onPointerDown:e,onPointerUp:n}){let r,a={selection:!1,input:!1,scroll:!1},i;const s=[N(t,window,"pointerdown",o=>{Be(o)&&(r=ze(),a={selection:!1,input:!1,scroll:!1},i=e(o))},{capture:!0}),N(t,window,"selectionchange",()=>{(!r||!ze())&&(a.selection=!0)},{capture:!0}),N(t,window,"scroll",()=>{a.scroll=!0},{capture:!0,passive:!0}),N(t,window,"pointerup",o=>{if(Be(o)&&i){const c=a;n(i,o,()=>c),i=void 0}},{capture:!0}),N(t,window,"input",()=>{a.input=!0},{capture:!0})];return{stop:()=>{s.forEach(o=>o.stop())}}}function ze(){const t=window.getSelection();return!t||t.isCollapsed}function Be(t){return t.target instanceof Element&&t.isPrimary!==!1}const He=3;function Zi(t,e){if(Ji(t))return e.addFrustration(mt.RAGE_CLICK),t.some(Ge)&&e.addFrustration(mt.DEAD_CLICK),e.hasError&&e.addFrustration(mt.ERROR_CLICK),{isRage:!0};const n=t.some(r=>r.getUserActivity().selection);return t.forEach(r=>{r.hasError&&r.addFrustration(mt.ERROR_CLICK),Ge(r)&&!n&&r.addFrustration(mt.DEAD_CLICK)}),{isRage:!1}}function Ji(t){if(t.some(e=>e.getUserActivity().selection||e.getUserActivity().scroll))return!1;for(let e=0;e<t.length-(He-1);e+=1)if(t[e+He-1].event.timeStamp-t[e].event.timeStamp<=$)return!0;return!1}const Qi='input:not([type="checkbox"]):not([type="radio"]):not([type="button"]):not([type="submit"]):not([type="reset"]):not([type="range"]),textarea,select,[contenteditable],[contenteditable] *,canvas,a[href],a[href] *';function Ge(t){if(t.hasPageActivity||t.getUserActivity().input||t.getUserActivity().scroll)return!1;let e=t.event.target;return e.tagName==="LABEL"&&e.hasAttribute("for")&&(e=document.getElementById(e.getAttribute("for"))),!e||!e.matches(Qi)}const Zn=10*$,bt=new Map;function ts(t){const e=bt.get(t);return bt.delete(t),e}function Jn(t,e){bt.set(t,e),bt.forEach((n,r)=>{O(r,q())>Zn&&bt.delete(r)})}const es=5*it;function ns(t,e,n,r){const a=Et({expireDelay:es}),i=new F;let s;t.subscribe(10,()=>{a.reset()}),t.subscribe(5,l),t.subscribe(11,d=>{d.reason===mn.UNLOADING&&l()});const{stop:o}=qi(r,{onPointerDown:d=>rs(r,t,e,d,n),onPointerUp:({clickActionBase:d,hadActivityOnPointerDown:f},m,p)=>{as(r,t,e,n,a,i,u,d,m,p,f)}});return{stop:()=>{l(),i.notify(),o()},actionContexts:{findActionId:d=>a.findAll(d)}};function u(d){if(!s||!s.tryAppend(d)){const f=d.clone();s=Yi(d,m=>{ss(m,f)})}}function l(){s&&s.stop()}}function rs(t,e,n,r,a){const i=t.enablePrivacyForActionName?Kn(r.target,t.defaultPrivacyLevel):E.ALLOW;if(i===E.HIDDEN)return;const s=is(r,i,t);let o=!1;return ye(e,n,a,t,c=>{o=c.hadActivity},Gn),{clickActionBase:s,hadActivityOnPointerDown:()=>o}}function as(t,e,n,r,a,i,s,o,c,u,l){var d;const f=Qn(e,a,u,o,c);s(f);const m=(d=o?.target)===null||d===void 0?void 0:d.selector;m&&Jn(c.timeStamp,m);const{stop:p}=ye(e,n,r,t,S=>{S.hadActivity&&S.end<f.startClocks.timeStamp?f.discard():S.hadActivity?f.stop(S.end):l()?f.stop(f.startClocks.timeStamp):f.stop()},Zn),h=e.subscribe(5,({endClocks:S})=>{f.stop(S.timeStamp)}),y=i.subscribe(()=>{f.stop()});f.stopObservable.subscribe(()=>{h.unsubscribe(),p(),y.unsubscribe()})}function is(t,e,n){const r=t.target.getBoundingClientRect(),a=At(t.target,n.actionNameAttribute);a&&Jn(t.timeStamp,a);const i=Oi(t.target,n,e);return{type:be.CLICK,target:{width:Math.round(r.width),height:Math.round(r.height),selector:a},position:{x:Math.round(t.clientX-r.left),y:Math.round(t.clientY-r.top)},name:i.name,nameSource:i.nameSource}}function Qn(t,e,n,r,a){const i=Z(),s=tt(),o=e.add(i,s.relative),c=zn({lifeCycle:t,isChildEvent:p=>p.action!==void 0&&(Array.isArray(p.action.id)?p.action.id.includes(i):p.action.id===i)});let u=0,l;const d=[],f=new F;function m(p){u===0&&(l=p,u=1,l?o.close(_n(l)):o.remove(),c.stop(),f.notify())}return{event:a,stop:m,stopObservable:f,get hasError(){return c.eventCounts.errorCount>0},get hasPageActivity(){return l!==void 0},getUserActivity:n,addFrustration:p=>{d.push(p)},startClocks:s,isStopped:()=>u===1||u===2,clone:()=>Qn(t,e,n,r,a),validate:p=>{if(m(),u!==1)return;const{resourceCount:h,errorCount:y,longTaskCount:S}=c.eventCounts,x={duration:l&&O(s.timeStamp,l),startClocks:s,id:i,frustrationTypes:d,counts:{resourceCount:h,errorCount:y,longTaskCount:S},events:p??[a],event:a,...r};t.notify(0,x),u=2},discard:()=>{m(),u=2}}}function ss(t,e){const{isRage:n}=Zi(t,e);n?(t.forEach(r=>r.discard()),e.stop(j()),e.validate(t.map(r=>r.event))):(e.discard(),t.forEach(r=>r.validate()))}function os(t,e,n,r,a){t.subscribe(0,o=>t.notify(12,Ke(o))),e.register(0,({startTime:o,eventType:c})=>{if(c!==b.ERROR&&c!==b.RESOURCE&&c!==b.LONG_TASK)return et;const u=i.findActionId(o);return u?{type:c,action:{id:u}}:et});let i={findActionId:R},s=R;return a.trackUserInteractions&&({actionContexts:i,stop:s}=ns(t,n,r,a)),{addAction:o=>{t.notify(12,Ke(o))},actionContexts:i,stop:s}}function Ke(t){const e=Nt(t)?{action:{id:t.id,loading_time:Fn(g(t.duration)),frustration:{type:t.frustrationTypes},error:{count:t.counts.errorCount},long_task:{count:t.counts.longTaskCount},resource:{count:t.counts.resourceCount}},_dd:{action:{target:t.target,position:t.position,name_source:t.nameSource}}}:void 0,n=at({action:{id:Z(),target:{name:t.name},type:t.type},date:t.startClocks.timeStamp,type:b.ACTION},e),r=Nt(t)?t.duration:void 0,a=Nt(t)?void 0:t.context,i=Nt(t)?{events:t.events}:{handlingStack:t.handlingStack};return{customerContext:a,rawRumEvent:n,duration:r,startTime:t.startClocks.relative,domainContext:i}}function Nt(t){return t.type!==be.CUSTOM}function cs(t){const e=Vr([Pr.error]).subscribe(n=>t.notify(n.error));return{stop:()=>{e.unsubscribe()}}}function us(t,e){const n=Ur(t,[xe.cspViolation,xe.intervention]).subscribe(r=>e.notify(r));return{stop:()=>{n.unsubscribe()}}}function ls(t,e){const n=new F;return cs(n),Fr(n),us(e,n),n.subscribe(r=>t.notify(14,{error:r})),ds(t)}function ds(t){return t.subscribe(14,({error:e,customerContext:n})=>{n=at(e.context,n),t.notify(12,{customerContext:n,...fs(e)})}),{addError:({error:e,handlingStack:n,componentStack:r,startClocks:a,context:i})=>{const s=zr({originalError:e,handlingStack:n,componentStack:r,startClocks:a,nonErrorPrefix:"Provided",source:Br.CUSTOM,handling:"handled"});t.notify(14,{customerContext:i,error:s})}}}function fs(t){const e={date:t.startClocks.timeStamp,error:{id:Z(),message:t.message,source:t.source,stack:t.stack,handling_stack:t.handlingStack,component_stack:t.componentStack,type:t.type,handling:t.handling,causes:t.causes,source_type:"browser",fingerprint:t.fingerprint,csp:t.csp},type:b.ERROR},n={error:t.originalError,handlingStack:t.handlingStack};return{rawRumEvent:e,startTime:t.startClocks.relative,domainContext:n}}const $e=new WeakSet;function ps(t){if(!performance||!("getEntriesByName"in performance))return;const e=performance.getEntriesByName(t.url,"resource");if(!e.length||!("toJSON"in e[0]))return;const n=e.filter(r=>!$e.has(r)).filter(r=>Mn(r)&&Dn(r)).filter(r=>hs(r,t.startClocks.relative,tr({startTime:t.startClocks.relative,duration:t.duration})));if(n.length===1)return $e.add(n[0]),n[0].toJSON()}function tr(t){return gn(t.startTime,t.duration)}function hs(t,e,n){return t.startTime>=e-1&&tr(t)<=gn(n,1)}const ms=2*it;function _s(t){const e=gs(t)||bs(t);if(!(!e||e.traceTime<=vt()-ms))return e.traceId}function gs(t){const e=t.querySelector("meta[name=dd-trace-id]"),n=t.querySelector("meta[name=dd-trace-time]");return er(e&&e.content,n&&n.content)}function bs(t){const e=vs(t);if(e)return er(Ut(e,"trace-id"),Ut(e,"trace-time"))}function er(t,e){const n=e&&Number(e);if(!(!t||!n))return{traceId:t,traceTime:n}}function vs(t){for(let e=0;e<t.childNodes.length;e+=1){const n=We(t.childNodes[e]);if(n)return n}if(t.body)for(let e=t.body.childNodes.length-1;e>=0;e-=1){const n=t.body.childNodes[e],r=We(n);if(r)return r;if(!Bn(n))break}}function We(t){if(t&&mi(t)){const e=/^\s*DATADOG;(.*?)\s*$/.exec(t.data);if(e)return e[1]}}function nr(){if(st(T.NAVIGATION)){const n=performance.getEntriesByType(T.NAVIGATION)[0];if(n)return n}const t=ys(),e={entryType:T.NAVIGATION,initiatorType:"navigation",name:window.location.href,startTime:0,duration:t.loadEventEnd,decodedBodySize:0,encodedBodySize:0,transferSize:0,workerStart:0,toJSON:()=>({...e,toJSON:void 0}),...t};return e}function ys(){const t={},e=performance.timing;for(const n in e)if(fe(e[n])){const r=n,a=e[r];t[r]=a===0?0:_n(a)}return t}function Ts(t,e,n=nr){ge(t,"interactive",()=>{const r=n(),a=Object.assign(r.toJSON(),{entryType:T.RESOURCE,initiatorType:On,duration:r.responseEnd,traceId:_s(document),toJSON:()=>({...a,toJSON:void 0})});e(a)})}function Es(t,e,n,r=ya(),a=Ts){t.subscribe(8,o=>{s(()=>ws(o,e,n))});const i=K(e,{type:T.RESOURCE,buffered:!0}).subscribe(o=>{for(const c of o)Qa(c)||s(()=>Ye(c,e))});a(e,o=>{s(()=>Ye(o,e))});function s(o){r.push(()=>{const c=o();c&&t.notify(12,c)})}return{stop:()=>{i.unsubscribe()}}}function ws(t,e,n){const r=ps(t),a=r?St(r.startTime):t.startClocks,i=Ss(t,e);if(!e.trackResources&&!i)return;const s=t.type===pe.XHR?U.XHR:U.FETCH,o=r?rr(r):void 0,c=r?Ln(r):Is(n,a,t.duration),u=at({date:a.timeStamp,resource:{id:Z(),type:s,duration:g(c),method:t.method,status_code:t.status,protocol:r&&Vn(r),url:ii(t.url),delivery_type:r&&Pn(r)},type:b.RESOURCE,_dd:{discarded:!e.trackResources}},i,o);return{startTime:a.relative,duration:c,rawRumEvent:u,domainContext:{performanceEntry:r,xhr:t.xhr,response:t.response,requestInput:t.input,requestInit:t.init,error:t.error,isAborted:t.isAborted,handlingStack:t.handlingStack}}}function Ye(t,e){const n=St(t.startTime),r=As(t,e);if(!e.trackResources&&!r)return;const a=Ja(t),i=rr(t),s=Ln(t),o=at({date:n.timeStamp,resource:{id:Z(),type:a,duration:g(s),url:t.name,status_code:Cs(t.responseStatus),protocol:Vn(t),delivery_type:Pn(t)},type:b.RESOURCE,_dd:{discarded:!e.trackResources}},r,i);return{startTime:n.relative,duration:s,rawRumEvent:o,domainContext:{performanceEntry:t}}}function rr(t){const{renderBlockingStatus:e}=t;return{resource:{render_blocking_status:e,...ni(t),...ti(t)}}}function Ss(t,e){if(t.traceSampled&&t.traceId&&t.spanId)return{_dd:{span_id:t.spanId.toString(),trace_id:t.traceId.toString(),rule_psr:e.rulePsr}}}function As(t,e){if(t.traceId)return{_dd:{trace_id:t.traceId,span_id:xn().toString(),rule_psr:e.rulePsr}}}function Is(t,e,n){return t.wasInPageStateDuringPeriod("frozen",e.relative,n)?void 0:n}function Cs(t){return t===0?void 0:t}function Rs(t,e,n){const{stop:r,eventCounts:a}=zn({lifeCycle:t,isChildEvent:i=>i.view.id===e,onChange:n});return{stop:r,eventCounts:a}}const xs=10*it;function ks(t,e,n){return{stop:K(t,{type:T.PAINT,buffered:!0}).subscribe(a=>{const i=a.find(s=>s.name==="first-contentful-paint"&&s.startTime<e.timeStamp&&s.startTime<xs);i&&n(i.startTime)}).unsubscribe}}function Ns(t,e){requestAnimationFrame(()=>{requestAnimationFrame(()=>{e(O(t,q()))})})}function Os(t,e,n){const r=K(t,{type:T.FIRST_INPUT,buffered:!0}).subscribe(a=>{const i=a.find(s=>s.startTime<e.timeStamp);if(i){const s=O(i.startTime,i.processingStart);let o;i.target&&ot(i.target)&&(o=At(i.target,t.actionNameAttribute)),n({delay:s>=0?s:0,time:i.startTime,targetSelector:o})}});return{stop:()=>{r.unsubscribe()}}}function Ls(t,e,n=nr){return Vs(t,()=>{const r=n();Ds(r)||e(Ms(r))})}function Ms(t){return{domComplete:t.domComplete,domContentLoaded:t.domContentLoadedEventEnd,domInteractive:t.domInteractive,loadEvent:t.loadEventEnd,firstByte:t.responseStart>=0&&t.responseStart<=q()?t.responseStart:void 0}}function Ds(t){return t.loadEventEnd<=0}function Vs(t,e){let n;const{stop:r}=ge(t,"complete",()=>{n=H(()=>e())});return{stop:()=>{r(),G(n)}}}const Ps=10*it;function Us(t,e,n,r){let a=1/0;const{stop:i}=Tt(t,n,["pointerdown","keydown"],c=>{a=c.timeStamp},{capture:!0,once:!0});let s=0;const o=K(t,{type:T.LARGEST_CONTENTFUL_PAINT,buffered:!0}).subscribe(c=>{const u=Hr(c,l=>l.entryType===T.LARGEST_CONTENTFUL_PAINT&&l.startTime<a&&l.startTime<e.timeStamp&&l.startTime<Ps&&l.size>s);if(u){let l;u.element&&(l=At(u.element,t.actionNameAttribute)),r({value:u.startTime,targetSelector:l,resourceUrl:Fs(u)}),s=u.size}});return{stop:()=>{i(),o.unsubscribe()}}}function Fs(t){return t.url===""?void 0:t.url}function ar(t,e,n=window){if(document.visibilityState==="hidden")return{timeStamp:0,stop:R};if(st(T.VISIBILITY_STATE)){const i=performance.getEntriesByType(T.VISIBILITY_STATE).filter(s=>s.name==="hidden").find(s=>s.startTime>=e.relative);if(i)return{timeStamp:i.startTime,stop:R}}let r=1/0;const{stop:a}=Tt(t,n,["pagehide","visibilitychange"],i=>{(i.type==="pagehide"||document.visibilityState==="hidden")&&(r=i.timeStamp,a())},{capture:!0});return{get timeStamp(){return r},stop:a}}function zs(t,e,n,r){const a={},{stop:i}=Ls(t,d=>{n(d.loadEvent),a.navigationTimings=d,r()}),s=ar(t,e),{stop:o}=ks(t,s,d=>{a.firstContentfulPaint=d,r()}),{stop:c}=Us(t,s,window,d=>{a.largestContentfulPaint=d,r()}),{stop:u}=Os(t,s,d=>{a.firstInput=d,r()});function l(){i(),o(),c(),u(),s.stop()}return{stop:l,initialViewMetrics:a}}const ue=(t,e)=>t*e,Bs=(t,e)=>{const n=Math.max(t.left,e.left),r=Math.max(t.top,e.top),a=Math.min(t.right,e.right),i=Math.min(t.bottom,e.bottom);return n>=a||r>=i?0:ue(a-n,i-r)},Xe=t=>{const e=ue(t.previousRect.width,t.previousRect.height),n=ue(t.currentRect.width,t.currentRect.height),r=Bs(t.previousRect,t.currentRect);return e+n-r};function Hs(t,e,n){if(!Ys())return{stop:R};let r=0,a;n({value:0});const i=Ws(),s=K(t,{type:T.LAYOUT_SHIFT,buffered:!0}).subscribe(o=>{var c;for(const u of o){if(u.hadRecentInput||u.startTime<e)continue;const{cumulatedValue:l,isMaxValue:d}=i.update(u);if(d){const f=Gs(u.sources);a={target:f?.node?new WeakRef(f.node):void 0,time:O(e,u.startTime),previousRect:f?.previousRect,currentRect:f?.currentRect,devicePixelRatio:window.devicePixelRatio}}if(l>r){r=l;const f=(c=a?.target)===null||c===void 0?void 0:c.deref();n({value:Lt(r,4),targetSelector:f&&At(f,t.actionNameAttribute),time:a?.time,previousRect:a?.previousRect?je(a.previousRect):void 0,currentRect:a?.currentRect?je(a.currentRect):void 0,devicePixelRatio:a?.devicePixelRatio})}}});return{stop:()=>{s.unsubscribe()}}}function Gs(t){let e;for(const n of t)if(n.node&&ot(n.node)){const r=Xe(n);(!e||Xe(e)<r)&&(e=n)}return e}function je({x:t,y:e,width:n,height:r}){return{x:t,y:e,width:n,height:r}}const Ks=5*$,$s=$;function Ws(){let t=0,e,n,r=0;return{update:a=>{const i=e===void 0||a.startTime-n>=$s||a.startTime-e>=Ks;let s;return i?(e=n=a.startTime,r=t=a.value,s=!0):(t+=a.value,n=a.startTime,s=a.value>r,s&&(r=a.value)),{cumulatedValue:t,isMaxValue:s}}}}function Ys(){return st(T.LAYOUT_SHIFT)&&"WeakRef"in window}let Dt,ir=0,ee=1/0,ne=0;function Xs(){"interactionCount"in performance||Dt||(Dt=new window.PerformanceObserver(w(t=>{t.getEntries().forEach(e=>{const n=e;n.interactionId&&(ee=Math.min(ee,n.interactionId),ne=Math.max(ne,n.interactionId),ir=(ne-ee)/7+1)})})),Dt.observe({type:"event",buffered:!0,durationThreshold:0}))}const qe=()=>Dt?ir:window.performance.interactionCount||0,Ze=10,js=1*it;function qs(t,e,n){if(!Qs())return{getInteractionToNextPaint:()=>{},setViewEnd:R,stop:R};const{getViewInteractionCount:r,stopViewInteractionCount:a}=Js(n);let i=1/0;const s=Zs(r);let o=-1,c,u;function l(m){for(const h of m)h.interactionId&&h.startTime>=e&&h.startTime<=i&&s.process(h);const p=s.estimateP98Interaction();p&&p.duration!==o&&(o=p.duration,u=O(e,p.startTime),c=ts(p.startTime),!c&&p.target&&ot(p.target)&&(c=At(p.target,t.actionNameAttribute)))}const d=K(t,{type:T.FIRST_INPUT,buffered:!0}).subscribe(l),f=K(t,{type:T.EVENT,durationThreshold:40,buffered:!0}).subscribe(l);return{getInteractionToNextPaint:()=>{if(o>=0)return{value:Math.min(o,js),targetSelector:c,time:u};if(r())return{value:0}},setViewEnd:m=>{i=m,a()},stop:()=>{f.unsubscribe(),d.unsubscribe()}}}function Zs(t){const e=[];function n(){e.sort((r,a)=>a.duration-r.duration).splice(Ze)}return{process(r){const a=e.findIndex(s=>r.interactionId===s.interactionId),i=e[e.length-1];a!==-1?r.duration>e[a].duration&&(e[a]=r,n()):(e.length<Ze||r.duration>i.duration)&&(e.push(r),n())},estimateP98Interaction(){const r=Math.min(e.length-1,Math.floor(t()/50));return e[r]}}}function Js(t){Xs();const e=t===X.INITIAL_LOAD?0:qe();let n={stopped:!1};function r(){return qe()-e}return{getViewInteractionCount:()=>n.stopped?n.interactionCount:r(),stopViewInteractionCount:()=>{n={stopped:!0,interactionCount:r()}}}}function Qs(){return st(T.EVENT)&&window.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype}function to(t,e,n,r,a,i,s){let o=a===X.INITIAL_LOAD,c=!0;const u=[],l=ar(r,i);function d(){if(!c&&!o&&u.length>0){const m=Math.max(...u);m<l.timeStamp-i.relative&&s(m)}}const{stop:f}=ye(t,e,n,r,m=>{c&&(c=!1,m.hadActivity&&u.push(O(i.timeStamp,m.end)),d())});return{stop:()=>{f(),l.stop()},setLoadEvent:m=>{o&&(o=!1,u.push(m),d())}}}function Xc(){let t;const e=window.visualViewport;return e?t=e.pageLeft-e.offsetLeft:window.scrollX!==void 0?t=window.scrollX:t=window.pageXOffset||0,Math.round(t)}function eo(){let t;const e=window.visualViewport;return e?t=e.pageTop-e.offsetTop:window.scrollY!==void 0?t=window.scrollY:t=window.pageYOffset||0,Math.round(t)}let re;function no(t){return re||(re=ro(t)),re}function ro(t){return new F(e=>{const{throttled:n}=he(()=>{e.notify(Ee())},200);return N(t,window,"resize",n,{capture:!0,passive:!0}).stop})}function Ee(){const t=window.visualViewport;return t?{width:Number(t.width*t.scale),height:Number(t.height*t.scale)}:{width:Number(window.innerWidth||0),height:Number(window.innerHeight||0)}}const ao=$;function io(t,e,n,r=oo(t)){let a=0,i=0,s=0;const o=r.subscribe(({scrollDepth:c,scrollTop:u,scrollHeight:l})=>{let d=!1;if(c>a&&(a=c,d=!0),l>i){i=l;const f=q();s=O(e.relative,f),d=!0}d&&n({maxDepth:Math.min(a,i),maxDepthScrollTop:u,maxScrollHeight:i,maxScrollHeightTime:s})});return{stop:()=>o.unsubscribe()}}function so(){const t=eo(),{height:e}=Ee(),n=Math.round((document.scrollingElement||document.documentElement).scrollHeight),r=Math.round(e+t);return{scrollHeight:n,scrollDepth:r,scrollTop:t}}function oo(t,e=ao){return new F(n=>{function r(){n.notify(so())}if(window.ResizeObserver){const a=he(r,e,{leading:!1,trailing:!0}),i=document.scrollingElement||document.documentElement,s=new ResizeObserver(w(a.throttled));i&&s.observe(i);const o=N(t,window,"scroll",a.throttled,{passive:!0});return()=>{a.cancel(),s.disconnect(),o.stop()}}})}function co(t,e,n,r,a,i,s){const o={},{stop:c,setLoadEvent:u}=to(t,e,n,r,i,s,h=>{o.loadingTime=h,a()}),{stop:l}=io(r,s,h=>{o.scroll=h}),{stop:d}=Hs(r,s.relative,h=>{o.cumulativeLayoutShift=h,a()}),{stop:f,getInteractionToNextPaint:m,setViewEnd:p}=qs(r,s.relative,i);return{stop:()=>{c(),d(),l()},stopINPTracking:f,setLoadEvent:u,setViewEnd:p,getCommonViewMetrics:()=>(o.interactionToNextPaint=m(),o)}}function uo(t,e){const{stop:n}=N(t,window,"pageshow",r=>{r.persisted&&e(r)},{capture:!0});return n}function lo(t,e,n){Ns(t.relative,r=>{e.firstContentfulPaint=r,e.largestContentfulPaint={value:r},n()})}const fo=3e3,po=5*it,ho=5*it;function mo(t,e,n,r,a,i,s,o){const c=new Set;let u=f(X.INITIAL_LOAD,Gr(),o),l;m();let d;s&&(d=p(i),a.trackBfcacheViews&&(l=uo(a,h=>{u.end();const y=St(h.timeStamp);u=f(X.BF_CACHE,y,void 0)})));function f(h,y,S){const x=_o(e,n,r,a,t,h,y,S);return c.add(x),x.stopObservable.subscribe(()=>{c.delete(x)}),x}function m(){e.subscribe(10,()=>{u=f(X.ROUTE_CHANGE,void 0,{name:u.name,service:u.service,version:u.version,context:u.contextManager.getContext()})}),e.subscribe(9,()=>{u.end({sessionIsActive:!1})})}function p(h){return h.subscribe(({oldLocation:y,newLocation:S})=>{bo(y,S)&&(u.end(),u=f(X.ROUTE_CHANGE))})}return{addTiming:(h,y=j())=>{u.addTiming(h,y)},startView:(h,y)=>{u.end({endClocks:y}),u=f(X.ROUTE_CHANGE,y,h)},setViewContext:h=>{u.contextManager.setContext(h)},setViewContextProperty:(h,y)=>{u.contextManager.setContextProperty(h,y)},setViewName:h=>{u.setViewName(h)},getViewContext:()=>u.contextManager.getContext(),stop:()=>{d&&d.unsubscribe(),l&&l(),u.end(),c.forEach(h=>h.stop())}}}function _o(t,e,n,r,a,i,s=tt(),o){const c=Z(),u=new F,l={};let d=0,f;const m=Pt(a),p=Kr();let h=!0,y=o?.name;const S=o?.service||r.service,x=o?.version||r.version,_=o?.context;_&&p.setContext(_);const v={id:c,name:y,startClocks:s,service:S,version:x,context:_};t.notify(1,v),t.notify(2,v);const{throttled:A,cancel:W}=he(Q,fo,{leading:!1}),{setLoadEvent:ct,setViewEnd:Kt,stop:It,stopINPTracking:Ct,getCommonViewMetrics:Rt}=co(t,e,n,r,J,i,s),{stop:$t,initialViewMetrics:xt}=i===X.INITIAL_LOAD?zs(r,s,ct,J):{stop:R,initialViewMetrics:{}};i===X.BF_CACHE&&lo(s,xt,J);const{stop:Wt,eventCounts:Yt}=Rs(t,c,J),Xt=me(Q,po),jt=t.subscribe(11,z=>{z.reason===mn.UNLOADING&&Q()});Q(),p.changeObservable.subscribe(J);function kt(){t.notify(3,{id:c,name:y,context:p.getContext(),startClocks:s,sessionIsActive:h})}function J(){kt(),A()}function Q(){W(),kt(),d+=1;const z=f===void 0?j():f.timeStamp;t.notify(4,{customTimings:l,documentVersion:d,id:c,name:y,service:S,version:x,context:p.getContext(),loadingType:i,location:m,startClocks:s,commonViewMetrics:Rt(),initialViewMetrics:xt,duration:O(s.timeStamp,z),isActive:f===void 0,sessionIsActive:h,eventCounts:Yt})}return{get name(){return y},service:S,version:x,contextManager:p,stopObservable:u,end(z={}){var Y,nt;f||(f=(Y=z.endClocks)!==null&&Y!==void 0?Y:tt(),h=(nt=z.sessionIsActive)!==null&&nt!==void 0?nt:!0,t.notify(5,{endClocks:f}),t.notify(6,{endClocks:f}),bn(Xt),Kt(f.relative),It(),jt.unsubscribe(),Q(),H(()=>{this.stop()},ho))},stop(){$t(),Wt(),Ct(),u.notify()},addTiming(z,Y){if(f)return;const nt=$r(Y)?Y:O(s.timeStamp,Y);l[go(z)]=nt,J()},setViewName(z){y=z,Q()}}}function go(t){const e=t.replace(/[^a-zA-Z0-9-_.@$]/g,"_");return e!==t&&P.warn(`Invalid timing name: ${t}, sanitized to: ${e}`),e}function bo(t,e){return t.pathname!==e.pathname||!vo(e.hash)&&Je(e.hash)!==Je(t.hash)}function vo(t){const e=t.substring(1);return e!==""&&!!document.getElementById(e)}function Je(t){const e=t.indexOf("?");return e<0?t:t.slice(0,e)}function yo(t,e,n,r,a,i,s,o,c,u){return t.subscribe(4,l=>t.notify(12,To(l,n,o))),e.register(0,({startTime:l,eventType:d})=>{const f=c.findView(l);return f?{type:d,service:f.service,version:f.version,context:f.context,view:{id:f.id,name:f.name}}:Bt}),mo(r,t,a,i,n,s,!n.trackViewsManually,u)}function To(t,e,n){var r,a,i,s,o,c,u,l,d,f,m,p,h,y,S,x,_,v;const A=n.getReplayStats(t.id),W=(a=(r=t.commonViewMetrics)===null||r===void 0?void 0:r.cumulativeLayoutShift)===null||a===void 0?void 0:a.devicePixelRatio,ct={_dd:{document_version:t.documentVersion,replay_stats:A,cls:W?{device_pixel_ratio:W}:void 0,configuration:{start_session_replay_recording_manually:e.startSessionReplayRecordingManually}},date:t.startClocks.timeStamp,type:b.VIEW,view:{action:{count:t.eventCounts.actionCount},frustration:{count:t.eventCounts.frustrationCount},cumulative_layout_shift:(i=t.commonViewMetrics.cumulativeLayoutShift)===null||i===void 0?void 0:i.value,cumulative_layout_shift_time:g((s=t.commonViewMetrics.cumulativeLayoutShift)===null||s===void 0?void 0:s.time),cumulative_layout_shift_target_selector:(o=t.commonViewMetrics.cumulativeLayoutShift)===null||o===void 0?void 0:o.targetSelector,first_byte:g((c=t.initialViewMetrics.navigationTimings)===null||c===void 0?void 0:c.firstByte),dom_complete:g((u=t.initialViewMetrics.navigationTimings)===null||u===void 0?void 0:u.domComplete),dom_content_loaded:g((l=t.initialViewMetrics.navigationTimings)===null||l===void 0?void 0:l.domContentLoaded),dom_interactive:g((d=t.initialViewMetrics.navigationTimings)===null||d===void 0?void 0:d.domInteractive),error:{count:t.eventCounts.errorCount},first_contentful_paint:g(t.initialViewMetrics.firstContentfulPaint),first_input_delay:g((f=t.initialViewMetrics.firstInput)===null||f===void 0?void 0:f.delay),first_input_time:g((m=t.initialViewMetrics.firstInput)===null||m===void 0?void 0:m.time),first_input_target_selector:(p=t.initialViewMetrics.firstInput)===null||p===void 0?void 0:p.targetSelector,interaction_to_next_paint:g((h=t.commonViewMetrics.interactionToNextPaint)===null||h===void 0?void 0:h.value),interaction_to_next_paint_time:g((y=t.commonViewMetrics.interactionToNextPaint)===null||y===void 0?void 0:y.time),interaction_to_next_paint_target_selector:(S=t.commonViewMetrics.interactionToNextPaint)===null||S===void 0?void 0:S.targetSelector,is_active:t.isActive,name:t.name,largest_contentful_paint:g((x=t.initialViewMetrics.largestContentfulPaint)===null||x===void 0?void 0:x.value),largest_contentful_paint_target_selector:(_=t.initialViewMetrics.largestContentfulPaint)===null||_===void 0?void 0:_.targetSelector,load_event:g((v=t.initialViewMetrics.navigationTimings)===null||v===void 0?void 0:v.loadEvent),loading_time:Fn(g(t.commonViewMetrics.loadingTime)),loading_type:t.loadingType,long_task:{count:t.eventCounts.longTaskCount},performance:Eo(t.commonViewMetrics,t.initialViewMetrics),resource:{count:t.eventCounts.resourceCount},time_spent:g(t.duration)},display:t.commonViewMetrics.scroll?{scroll:{max_depth:t.commonViewMetrics.scroll.maxDepth,max_depth_scroll_top:t.commonViewMetrics.scroll.maxDepthScrollTop,max_scroll_height:t.commonViewMetrics.scroll.maxScrollHeight,max_scroll_height_time:g(t.commonViewMetrics.scroll.maxScrollHeightTime)}}:void 0,privacy:{replay_level:e.defaultPrivacyLevel}};return _e(t.customTimings)||(ct.view.custom_timings=Wr(t.customTimings,g)),{rawRumEvent:ct,startTime:t.startClocks.relative,duration:t.duration,domainContext:{location:t.location}}}function Eo({cumulativeLayoutShift:t,interactionToNextPaint:e},{firstContentfulPaint:n,firstInput:r,largestContentfulPaint:a}){return{cls:t&&{score:t.value,timestamp:g(t.time),target_selector:t.targetSelector,previous_rect:t.previousRect,current_rect:t.currentRect},fcp:n&&{timestamp:g(n)},fid:r&&{duration:g(r.delay),timestamp:g(r.time),target_selector:r.targetSelector},inp:e&&{duration:g(e.value),timestamp:g(e.time),target_selector:e.targetSelector},lcp:a&&{timestamp:g(a.value),target_selector:a.targetSelector,resource_url:a.resourceUrl}}}const wo="rum";function So(t,e,n){const r=Yr(t,wo,a=>Io(t,a),n);return r.expireObservable.subscribe(()=>{e.notify(9)}),r.renewObservable.subscribe(()=>{e.notify(10)}),r.sessionStateUpdateObservable.subscribe(({previousState:a,newState:i})=>{if(!a.forcedReplay&&i.forcedReplay){const s=r.findSession();s&&(s.isReplayForced=!0)}}),{findTrackedSession:a=>{const i=r.findSession(a);if(!(!i||i.trackingType==="0"))return{id:i.id,sessionReplay:i.trackingType==="1"?1:i.isReplayForced?2:0,anonymousId:i.anonymousId}},expire:r.expire,expireObservable:r.expireObservable,setForcedReplay:()=>r.updateSessionState({forcedReplay:"1"})}}function Ao(){const t={id:"********-aaaa-0000-aaaa-************",sessionReplay:vn("records")?1:0};return{findTrackedSession:()=>t,expire:R,expireObservable:new F,setForcedReplay:R}}function Io(t,e){return Co(e)?e:Vt(t.sessionSampleRate)?Vt(t.sessionReplaySampleRate)?"1":"2":"0"}function Co(t){return t==="0"||t==="1"||t==="2"}function Ro(t,e,n,r,a,i){const s=t.replica,o=Xr(t,{endpoint:t.rumEndpointBuilder,encoder:i(2)},s&&{endpoint:s.rumEndpointBuilder,transformMessage:c=>at(c,{application:{id:s.applicationId}}),encoder:i(3)},n,r,a);return e.subscribe(13,c=>{c.type===b.VIEW?o.upsert(c,c.view.id):o.add(c)}),o}function xo(t){const e=ln();t.subscribe(13,n=>{e.send("rum",n)})}const ko=zt;function No(t,e,n,r){const a=Et({expireDelay:ko});let i;t.subscribe(1,({startClocks:c})=>{const u=r.href;a.add(o({url:u,referrer:i||document.referrer}),c.relative),i=u}),t.subscribe(6,({endClocks:c})=>{a.closeActive(c.relative)});const s=n.subscribe(({newLocation:c})=>{const u=a.find();if(u){const l=q();a.closeActive(l),a.add(o({url:c.href,referrer:u.referrer}),l)}});function o({url:c,referrer:u}){return{url:c,referrer:u}}return e.register(0,({startTime:c,eventType:u})=>{const l=a.find(c);return l?{type:u,view:{url:l.url,referrer:l.referrer}}:Bt}),{findUrl:c=>a.find(c),stop:()=>{s.unsubscribe(),a.stop()}}}function Oo(t,e){let n=Pt(e);return new F(r=>{const{stop:a}=Lo(t,s),{stop:i}=Mo(t,s);function s(){if(n.href===e.href)return;const o=Pt(e);r.notify({newLocation:o,oldLocation:n}),n=o}return()=>{a(),i()}})}function Lo(t,e){const{stop:n}=oe(Qe("pushState"),"pushState",({onPostCall:i})=>{i(e)}),{stop:r}=oe(Qe("replaceState"),"replaceState",({onPostCall:i})=>{i(e)}),{stop:a}=N(t,window,"popstate",e);return{stop:()=>{n(),r(),a()}}}function Mo(t,e){return N(t,window,"hashchange",e)}function Qe(t){return Object.prototype.hasOwnProperty.call(history,t)?history:History.prototype}const Do=zt;function Vo(t,e,n){const r=Et({expireDelay:Do});return t.subscribe(1,({startClocks:a})=>{r.add({},a.relative)}),t.subscribe(6,({endClocks:a})=>{r.closeActive(a.relative)}),e.register(0,({startTime:a,eventType:i})=>{if(!n.trackFeatureFlagsForEvents.concat([b.VIEW,b.ERROR]).includes(i))return et;const o=r.find(a);return!o||_e(o)?et:{type:i,feature_flags:o}}),{addFeatureFlagEvaluation:(a,i)=>{const s=r.find();s&&(s[a]=i)}}}const Po=10*$;let ht,ae;function Uo(t,e,n,r){e.enabled&&Vt(t.customerDataTelemetrySampleRate)&&(sr(),ae=!1,n.subscribe(13,()=>{ae=!0}),r.subscribe(({bytesCount:i,messagesCount:s})=>{ae&&(ht.batchCount+=1,en(ht.batchBytesCount,i),en(ht.batchMessagesCount,s))}),me(Fo,Po))}function Fo(){ht.batchCount!==0&&(wt("Customer data measures",ht),sr())}function tn(){return{min:1/0,max:0,sum:0}}function en(t,e){t.sum+=e,t.min=Math.min(t.min,e),t.max=Math.max(t.max,e)}function sr(){ht={batchCount:0,batchBytesCount:tn(),batchMessagesCount:tn()}}const zo=4e3,Bo=500,Ho=zt;function Go(t,e,n=Bo){const r=Et({expireDelay:Ho,maxEntries:zo});let a;st(T.VISIBILITY_STATE)&&performance.getEntriesByType(T.VISIBILITY_STATE).forEach(u=>{const l=u.name==="hidden"?"hidden":"active";s(l,u.startTime)}),s(or(),q());const{stop:i}=Tt(e,window,["pageshow","focus","blur","visibilitychange","resume","freeze","pagehide"],c=>{s($o(c),c.timeStamp)},{capture:!0});function s(c,u=q()){c!==a&&(a=c,r.closeActive(u),r.add({state:a,startTime:u},u))}function o(c,u,l){return r.findAll(u,l).some(d=>d.state===c)}return t.register(0,({startTime:c,duration:u=0,eventType:l})=>{if(l===b.VIEW){const d=r.findAll(c,u);return{type:l,_dd:{page_states:Ko(d,c,n)}}}return l===b.ACTION||l===b.ERROR?{type:l,view:{in_foreground:o("active",c,0)}}:et}),{wasInPageStateDuringPeriod:o,addPageState:s,stop:()=>{i(),r.stop()}}}function Ko(t,e,n){if(t.length!==0)return t.slice(-n).reverse().map(({state:r,startTime:a})=>({state:r,start:g(O(e,a))}))}function $o(t){return t.type==="freeze"?"frozen":t.type==="pagehide"?t.persisted?"frozen":"terminated":or()}function or(){return document.visibilityState==="hidden"?"hidden":document.hasFocus()?"active":"passive"}function Wo(t,e){let n;const r=requestAnimationFrame(w(()=>{n=Ee()})),a=no(e).subscribe(i=>{n=i}).unsubscribe;return t.register(0,({eventType:i})=>({type:i,display:n?{viewport:n}:void 0})),{stop:()=>{a(),r&&cancelAnimationFrame(r)}}}function Yo(t,e){const n=window.cookieStore?Xo(t):qo;return new F(r=>n(e,a=>r.notify(a)))}function Xo(t){return(e,n)=>N(t,window.cookieStore,"change",a=>{const i=a.changed.find(s=>s.name===e)||a.deleted.find(s=>s.name===e);i&&n(i.value)}).stop}const jo=$;function qo(t,e){const n=Ut(document.cookie,t),r=me(()=>{const a=Ut(document.cookie,t);a!==n&&e(a)},jo);return()=>{bn(r)}}const nn="datadog-ci-visibility-test-execution-id";function Zo(t,e,n=Yo(t,nn)){var r;let a=jr(nn)||((r=window.Cypress)===null||r===void 0?void 0:r.env("traceId"));const i=n.subscribe(s=>{a=s});return e.register(0,({eventType:s})=>typeof a!="string"?et:{type:s,session:{type:"ci_test"},ci_test:{test_execution_id:a}}),{stop:()=>{i.unsubscribe()}}}function Jo(t,e){const n=K(e,{type:T.LONG_ANIMATION_FRAME,buffered:!0}).subscribe(r=>{for(const a of r){const i=St(a.startTime),s={date:i.timeStamp,long_task:{id:Z(),entry_type:wn.LONG_ANIMATION_FRAME,duration:g(a.duration),blocking_duration:g(a.blockingDuration),first_ui_event_timestamp:g(a.firstUIEventTimestamp),render_start:g(a.renderStart),style_and_layout_start:g(a.styleAndLayoutStart),start_time:g(a.startTime),scripts:a.scripts.map(o=>({duration:g(o.duration),pause_duration:g(o.pauseDuration),forced_style_and_layout_duration:g(o.forcedStyleAndLayoutDuration),start_time:g(o.startTime),execution_start:g(o.executionStart),source_url:o.sourceURL,source_function_name:o.sourceFunctionName,source_char_position:o.sourceCharPosition,invoker:o.invoker,invoker_type:o.invokerType,window_attribution:o.windowAttribution}))},type:b.LONG_TASK,_dd:{discarded:!1}};t.notify(12,{rawRumEvent:s,startTime:i.relative,duration:a.duration,domainContext:{performanceEntry:a}})}});return{stop:()=>n.unsubscribe()}}function Qo(t,e){const n=K(e,{type:T.LONG_TASK,buffered:!0}).subscribe(r=>{for(const a of r){if(a.entryType!==T.LONG_TASK||!e.trackLongTasks)break;const i=St(a.startTime),s={date:i.timeStamp,long_task:{id:Z(),entry_type:wn.LONG_TASK,duration:g(a.duration)},type:b.LONG_TASK,_dd:{discarded:!1}};t.notify(12,{rawRumEvent:s,startTime:i.relative,duration:a.duration,domainContext:{performanceEntry:a}})}});return{stop(){n.unsubscribe()}}}function tc(t){t.register(0,({eventType:e})=>{if(!qr())return et;const n=Zr(),r=Jr();return{type:e,session:{type:"synthetics"},synthetics:{test_id:n,result_id:r,injected:cn()}}})}function ec(t,e,n){const r=fn(t),a=n(r);return on(e).forEach(([i,s])=>le(t,r,i.split(/\.|(?=\[\])/),s)),a}function le(t,e,n,r){const[a,...i]=n;if(a==="[]"){Array.isArray(t)&&Array.isArray(e)&&t.forEach((s,o)=>le(s,e[o],i,r));return}if(!(!rn(t)||!rn(e))){if(i.length>0)return le(t[a],e[a],i,r);nc(t,a,e[a],r)}}function nc(t,e,n,r){const a=Ft(n);a===r?t[e]=L(n):r==="object"&&(a==="undefined"||a==="null")&&(t[e]={})}function rn(t){return Ft(t)==="object"}const lt={"view.name":"string","view.url":"string","view.referrer":"string"},dt={context:"object"},Ot={service:"string",version:"string"};let cr;function rc(t,e,n,r){cr={[b.VIEW]:{"view.performance.lcp.resource_url":"string",...dt,...lt,...Ot},[b.ERROR]:{"error.message":"string","error.stack":"string","error.resource.url":"string","error.fingerprint":"string",...dt,...lt,...Ot},[b.RESOURCE]:{"resource.url":"string",...pn(hn.WRITABLE_RESOURCE_GRAPHQL)?{"resource.graphql":"object"}:{},...dt,...lt,...Ot},[b.ACTION]:{"action.target.name":"string",...dt,...lt,...Ot},[b.LONG_TASK]:{"long_task.scripts[].source_url":"string","long_task.scripts[].invoker":"string",...dt,...lt},[b.VITAL]:{...dt,...lt}};const a={[b.ERROR]:Zt(b.ERROR,t.eventRateLimiterThreshold,r),[b.ACTION]:Zt(b.ACTION,t.eventRateLimiterThreshold,r),[b.VITAL]:Zt(b.VITAL,t.eventRateLimiterThreshold,r)};e.subscribe(12,({startTime:i,duration:s,rawRumEvent:o,domainContext:c,customerContext:u})=>{const l=n.triggerHook(0,{eventType:o.type,startTime:i,duration:s});if(l===Bt)return;const d=at(l,{context:u},o);ac(d,t.beforeSend,c,a)&&(_e(d.context)&&delete d.context,e.notify(13,d))})}function ac(t,e,n,r){var a;if(e){const s=ec(t,cr[t.type],o=>e(o,n));if(s===!1&&t.type!==b.VIEW)return!1;s===!1&&P.warn("Can't dismiss view events using beforeSend!")}return!((a=r[t.type])===null||a===void 0?void 0:a.isLimitReached())}function ic(t,e,n,r){t.register(0,({eventType:a,startTime:i})=>{const s=e.findTrackedSession(i),o=r.findView(i);if(!s||!o)return Bt;let c,u,l;return a===b.VIEW?(c=n.getReplayStats(o.id)?!0:void 0,u=s.sessionReplay===1,l=o.sessionIsActive?void 0:!1):c=n.isRecording()?!0:void 0,{type:a,session:{id:s.id,type:"user",has_replay:c,sampled_for_replay:u,is_active:l}}})}function sc(t){t.register(0,({eventType:e})=>({type:e,connectivity:Qr()}))}function oc(t,e,n){t.register(0,({eventType:r})=>({type:r,_dd:{format_version:2,drift:ta(),configuration:{session_sample_rate:Lt(e.sessionSampleRate,3),session_replay_sample_rate:Lt(e.sessionReplaySampleRate,3),profiling_sample_rate:Lt(e.profilingSampleRate,3)},browser_sdk_version:yt()?"6.13.0":void 0,sdk_name:n},application:{id:e.applicationId},date:j(),source:"browser"}))}const cc=ea,uc=[b.ACTION,b.ERROR,b.LONG_TASK,b.RESOURCE,b.VITAL];function lc(t){return{addEvent:(e,n,r,a)=>{uc.includes(n.type)&&t.notify(12,{startTime:e,rawRumEvent:n,domainContext:r,duration:a})}}}function dc(t,e,n,r,a,i,s,o){const c=[],u=new Xa,l=cc();u.subscribe(13,I=>na("rum",I));const d=I=>{u.notify(14,{error:I}),wt("Error reported to customer",{"error.message":I.message})},f=ra(t),m=f.subscribe(I=>{u.notify(11,I)});c.push(()=>m.unsubscribe());const p=aa("browser-rum-sdk",t,d,f,a);c.push(p.stop),p.setContextProvider("application.id",()=>t.applicationId);const h=yt()?Ao():So(t,u,i);if(p.setContextProvider("session.id",()=>{var I;return(I=h.findTrackedSession())===null||I===void 0?void 0:I.id}),p.setContextProvider("usr.anonymous_id",()=>{var I;return(I=h.findTrackedSession())===null||I===void 0?void 0:I.anonymousId}),yt())xo(u);else{const I=Ro(t,u,d,f,h.expireObservable,a);c.push(()=>I.stop()),Uo(t,p,u,I.flushObservable)}const y=Ka(),S=Oo(t,location),{observable:x,stop:_}=Wa();c.push(_),oc(l,t,o);const v=Go(l,t),A=qa(u);c.push(()=>A.stop()),p.setContextProvider("view.id",()=>{var I;return(I=A.findView())===null||I===void 0?void 0:I.id});const W=No(u,l,S,location);c.push(()=>W.stop());const ct=Vo(u,l,t);ic(l,h,e,A),sc(l);const Kt=ia(l,t,"rum",!0),It=sa(l,t,h,"rum"),Ct=oa(l,t,"rum"),{actionContexts:Rt,addAction:$t,addEvent:xt,stop:Wt}=fc(u,l,t,v,y,x,d);c.push(Wt),p.setContextProvider("action.id",()=>Rt.findActionId());const{addTiming:Yt,startView:Xt,setViewName:jt,setViewContext:kt,setViewContextProperty:J,getViewContext:Q,stop:z}=yo(u,l,t,location,y,x,S,e,A,r);c.push(z);const{stop:Y}=Es(u,t,v);if(c.push(Y),t.trackLongTasks)if(st(T.LONG_ANIMATION_FRAME)){const{stop:I}=Jo(u,t);c.push(I)}else Qo(u,t);const{addError:nt}=ls(u,t);si(u,t,h,It,Ct);const qt=Ea(u,v,s),hr=Ya(t.applicationId,h,A,Rt,W);return c.push(()=>n.stop()),{addAction:$t,addEvent:xt,addError:nt,addTiming:Yt,addFeatureFlagEvaluation:ct.addFeatureFlagEvaluation,startView:Xt,setViewContext:kt,setViewContextProperty:J,getViewContext:Q,setViewName:jt,lifeCycle:u,viewHistory:A,session:h,stopSession:()=>h.expire(),getInternalContext:hr.get,startDurationVital:qt.startDurationVital,stopDurationVital:qt.stopDurationVital,addDurationVital:qt.addDurationVital,globalContext:Kt,userContext:It,accountContext:Ct,stop:()=>{c.forEach(I=>I())},hooks:l}}function fc(t,e,n,r,a,i,s){const o=os(t,e,a,i,n),c=lc(t),u=Wo(e,n),l=Zo(n,e);return tc(e),rc(n,t,e,s),{pageStateHistory:r,addAction:o.addAction,addEvent:c.addEvent,actionContexts:o.actionContexts,stop:()=>{o.stop(),l.stop(),u.stop(),r.stop()}}}function pc(t,{session:e,viewContext:n,errorType:r}){const a=e?e.id:"no-session-id",i=[];r!==void 0&&i.push(`error-type=${r}`),n&&(i.push(`seed=${n.id}`),i.push(`from=${n.startClocks.timeStamp}`));const s=hc(t),o=`/rum/replay/sessions/${a}`;return`${s}${o}?${i.join("&")}`}function hc(t){const e=t.site,n=t.subdomain||mc(t);return`https://${n?`${n}.`:""}${e}`}function mc(t){switch(t.site){case la:case ua:return"app";case ca:return"dd";default:return}}const _c=1e3;let B;function jc(t){return Gt(t).segments_count}function qc(t){Gt(t).segments_count+=1}function Zc(t){Gt(t).records_count+=1}function Jc(t,e){Gt(t).segments_total_raw_size+=e}function gc(t){return B?.get(t)}function Gt(t){B||(B=new Map);let e;return B.has(t)?e=B.get(t):(e={records_count:0,segments_count:0,segments_total_raw_size:0},B.set(t,e),B.size>_c&&bc()),e}function bc(){if(!B)return;const t=B.keys().next().value;t&&B.delete(t)}function ur(t,e,n){let r=0,a=[],i,s=0;const o=[],{stop:c}=N(t,e,"message",({data:d})=>{if(d.type!=="wrote"||d.streamId!==n)return;r+=d.additionalBytesCount,a.push(d.result),i=d.trailer;const f=o.shift();f&&f.id===d.id?f.writeCallback?f.writeCallback(d.result.byteLength):f.finishCallback&&f.finishCallback():(c(),wt("Worker responses received out of order."))});function u(){const d=a.length===0?new Uint8Array(0):da(a.concat(i)),f={rawBytesCount:r,output:d,outputBytesCount:d.byteLength,encoding:"deflate"};return r=0,a=[],f}function l(){s>0&&(e.postMessage({action:"reset",streamId:n}),s=0)}return{isAsync:!0,get isEmpty(){return s===0},write(d,f){e.postMessage({action:"write",id:s,data:d,streamId:n}),o.push({id:s,writeCallback:f,data:d}),s+=1},finish(d){l(),o.length?(o.forEach(f=>{delete f.writeCallback}),o[o.length-1].finishCallback=()=>d(u())):d(u())},finishSync(){l();const d=o.map(f=>(delete f.writeCallback,delete f.finishCallback,f.data)).join("");return{...u(),pendingData:d}},estimateEncodedBytesCount(d){return d.length/8},stop(){c()}}}function we({configuredUrl:t,error:e,source:n,scriptType:r}){if(P.error(`${n} failed to start: an error occurred while initializing the ${r}:`,e),e instanceof Event||e instanceof Error&&vc(e.message)){let a;t?a=`Please make sure the ${r} URL ${t} is correct and CSP is correctly configured.`:a="Please make sure CSP is correctly configured.",P.error(`${a} See documentation at ${fa}/integrations/content_security_policy_logs/#use-csp-with-real-user-monitoring-and-session-replay`)}else r==="worker"&&yn(e)}function vc(t){return t.includes("Content Security Policy")||t.includes("requires 'TrustedScriptURL'")}const yc=30*$;function lr(t){return new Worker(t.workerUrl||URL.createObjectURL(new Blob(['(()=>{function t(t){const e=t.reduce((t,e)=>t+e.length,0),a=new Uint8Array(e);let n=0;for(const e of t)a.set(e,n),n+=e.length;return a}function e(t){for(var e=t.length;--e>=0;)t[e]=0}var a=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),n=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),r=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),i=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),s=Array(576);e(s);var h=Array(60);e(h);var l=Array(512);e(l);var _=Array(256);e(_);var o=Array(29);e(o);var d,u,f,c=Array(30);function p(t,e,a,n,r){this.static_tree=t,this.extra_bits=e,this.extra_base=a,this.elems=n,this.max_length=r,this.has_stree=t&&t.length}function g(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}e(c);var v=function(t){return t<256?l[t]:l[256+(t>>>7)]},w=function(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255},m=function(t,e,a){t.bi_valid>16-a?(t.bi_buf|=e<<t.bi_valid&65535,w(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=a-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=a)},b=function(t,e,a){m(t,a[2*e],a[2*e+1])},y=function(t,e){var a=0;do{a|=1&t,t>>>=1,a<<=1}while(--e>0);return a>>>1},z=function(t,e,a){var n,r,i=Array(16),s=0;for(n=1;n<=15;n++)i[n]=s=s+a[n-1]<<1;for(r=0;r<=e;r++){var h=t[2*r+1];0!==h&&(t[2*r]=y(i[h]++,h))}},k=function(t){var e;for(e=0;e<286;e++)t.dyn_ltree[2*e]=0;for(e=0;e<30;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0},x=function(t){t.bi_valid>8?w(t,t.bi_buf):t.bi_valid>0&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0},A=function(t,e,a,n){var r=2*e,i=2*a;return t[r]<t[i]||t[r]===t[i]&&n[e]<=n[a]},U=function(t,e,a){for(var n=t.heap[a],r=a<<1;r<=t.heap_len&&(r<t.heap_len&&A(e,t.heap[r+1],t.heap[r],t.depth)&&r++,!A(e,n,t.heap[r],t.depth));)t.heap[a]=t.heap[r],a=r,r<<=1;t.heap[a]=n},B=function(t,e,r){var i,s,h,l,d=0;if(0!==t.last_lit)do{i=t.pending_buf[t.d_buf+2*d]<<8|t.pending_buf[t.d_buf+2*d+1],s=t.pending_buf[t.l_buf+d],d++,0===i?b(t,s,e):(h=_[s],b(t,h+256+1,e),0!==(l=a[h])&&(s-=o[h],m(t,s,l)),i--,h=v(i),b(t,h,r),0!==(l=n[h])&&(i-=c[h],m(t,i,l)))}while(d<t.last_lit);b(t,256,e)},I=function(t,e){var a,n,r,i=e.dyn_tree,s=e.stat_desc.static_tree,h=e.stat_desc.has_stree,l=e.stat_desc.elems,_=-1;for(t.heap_len=0,t.heap_max=573,a=0;a<l;a++)0!==i[2*a]?(t.heap[++t.heap_len]=_=a,t.depth[a]=0):i[2*a+1]=0;for(;t.heap_len<2;)i[2*(r=t.heap[++t.heap_len]=_<2?++_:0)]=1,t.depth[r]=0,t.opt_len--,h&&(t.static_len-=s[2*r+1]);for(e.max_code=_,a=t.heap_len>>1;a>=1;a--)U(t,i,a);r=l;do{a=t.heap[1],t.heap[1]=t.heap[t.heap_len--],U(t,i,1),n=t.heap[1],t.heap[--t.heap_max]=a,t.heap[--t.heap_max]=n,i[2*r]=i[2*a]+i[2*n],t.depth[r]=(t.depth[a]>=t.depth[n]?t.depth[a]:t.depth[n])+1,i[2*a+1]=i[2*n+1]=r,t.heap[1]=r++,U(t,i,1)}while(t.heap_len>=2);t.heap[--t.heap_max]=t.heap[1],function(t,e){var a,n,r,i,s,h,l=e.dyn_tree,_=e.max_code,o=e.stat_desc.static_tree,d=e.stat_desc.has_stree,u=e.stat_desc.extra_bits,f=e.stat_desc.extra_base,c=e.stat_desc.max_length,p=0;for(i=0;i<=15;i++)t.bl_count[i]=0;for(l[2*t.heap[t.heap_max]+1]=0,a=t.heap_max+1;a<573;a++)(i=l[2*l[2*(n=t.heap[a])+1]+1]+1)>c&&(i=c,p++),l[2*n+1]=i,n>_||(t.bl_count[i]++,s=0,n>=f&&(s=u[n-f]),h=l[2*n],t.opt_len+=h*(i+s),d&&(t.static_len+=h*(o[2*n+1]+s)));if(0!==p){do{for(i=c-1;0===t.bl_count[i];)i--;t.bl_count[i]--,t.bl_count[i+1]+=2,t.bl_count[c]--,p-=2}while(p>0);for(i=c;0!==i;i--)for(n=t.bl_count[i];0!==n;)(r=t.heap[--a])>_||(l[2*r+1]!==i&&(t.opt_len+=(i-l[2*r+1])*l[2*r],l[2*r+1]=i),n--)}}(t,e),z(i,_,t.bl_count)},E=function(t,e,a){var n,r,i=-1,s=e[1],h=0,l=7,_=4;for(0===s&&(l=138,_=3),e[2*(a+1)+1]=65535,n=0;n<=a;n++)r=s,s=e[2*(n+1)+1],++h<l&&r===s||(h<_?t.bl_tree[2*r]+=h:0!==r?(r!==i&&t.bl_tree[2*r]++,t.bl_tree[32]++):h<=10?t.bl_tree[34]++:t.bl_tree[36]++,h=0,i=r,0===s?(l=138,_=3):r===s?(l=6,_=3):(l=7,_=4))},C=function(t,e,a){var n,r,i=-1,s=e[1],h=0,l=7,_=4;for(0===s&&(l=138,_=3),n=0;n<=a;n++)if(r=s,s=e[2*(n+1)+1],!(++h<l&&r===s)){if(h<_)do{b(t,r,t.bl_tree)}while(0!==--h);else 0!==r?(r!==i&&(b(t,r,t.bl_tree),h--),b(t,16,t.bl_tree),m(t,h-3,2)):h<=10?(b(t,17,t.bl_tree),m(t,h-3,3)):(b(t,18,t.bl_tree),m(t,h-11,7));h=0,i=r,0===s?(l=138,_=3):r===s?(l=6,_=3):(l=7,_=4)}},D=!1,M=function(t,e,a,n){m(t,0+(n?1:0),3),function(t,e,a){x(t),w(t,a),w(t,~a),t.pending_buf.set(t.window.subarray(e,e+a),t.pending),t.pending+=a}(t,e,a)},j=M,L=function(t,e,a,n){for(var r=65535&t,i=t>>>16&65535,s=0;0!==a;){a-=s=a>2e3?2e3:a;do{i=i+(r=r+e[n++]|0)|0}while(--s);r%=65521,i%=65521}return r|i<<16},S=new Uint32Array(function(){for(var t,e=[],a=0;a<256;a++){t=a;for(var n=0;n<8;n++)t=1&t?3988292384^t>>>1:t>>>1;e[a]=t}return e}()),T=function(t,e,a,n){var r=S,i=n+a;t^=-1;for(var s=n;s<i;s++)t=t>>>8^r[255&(t^e[s])];return-1^t},O={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},q=j,F=function(t,e,a){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&a,t.last_lit++,0===e?t.dyn_ltree[2*a]++:(t.matches++,e--,t.dyn_ltree[2*(_[a]+256+1)]++,t.dyn_dtree[2*v(e)]++),t.last_lit===t.lit_bufsize-1},G=-2,H=258,J=262,K=103,N=113,P=666,Q=function(t,e){return t.msg=O[e],e},R=function(t){return(t<<1)-(t>4?9:0)},V=function(t){for(var e=t.length;--e>=0;)t[e]=0},W=function(t,e,a){return(e<<t.hash_shift^a)&t.hash_mask},X=function(t){var e=t.state,a=e.pending;a>t.avail_out&&(a=t.avail_out),0!==a&&(t.output.set(e.pending_buf.subarray(e.pending_out,e.pending_out+a),t.next_out),t.next_out+=a,e.pending_out+=a,t.total_out+=a,t.avail_out-=a,e.pending-=a,0===e.pending&&(e.pending_out=0))},Y=function(t,e){(function(t,e,a,n){var r,l,_=0;t.level>0?(2===t.strm.data_type&&(t.strm.data_type=function(t){var e,a=4093624447;for(e=0;e<=31;e++,a>>>=1)if(1&a&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<256;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0}(t)),I(t,t.l_desc),I(t,t.d_desc),_=function(t){var e;for(E(t,t.dyn_ltree,t.l_desc.max_code),E(t,t.dyn_dtree,t.d_desc.max_code),I(t,t.bl_desc),e=18;e>=3&&0===t.bl_tree[2*i[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),r=t.opt_len+3+7>>>3,(l=t.static_len+3+7>>>3)<=r&&(r=l)):r=l=a+5,a+4<=r&&-1!==e?M(t,e,a,n):4===t.strategy||l===r?(m(t,2+(n?1:0),3),B(t,s,h)):(m(t,4+(n?1:0),3),function(t,e,a,n){var r;for(m(t,e-257,5),m(t,a-1,5),m(t,n-4,4),r=0;r<n;r++)m(t,t.bl_tree[2*i[r]+1],3);C(t,t.dyn_ltree,e-1),C(t,t.dyn_dtree,a-1)}(t,t.l_desc.max_code+1,t.d_desc.max_code+1,_+1),B(t,t.dyn_ltree,t.dyn_dtree)),k(t),n&&x(t)})(t,t.block_start>=0?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,X(t.strm)},Z=function(t,e){t.pending_buf[t.pending++]=e},$=function(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e},tt=function(t,e,a,n){var r=t.avail_in;return r>n&&(r=n),0===r?0:(t.avail_in-=r,e.set(t.input.subarray(t.next_in,t.next_in+r),a),1===t.state.wrap?t.adler=L(t.adler,e,r,a):2===t.state.wrap&&(t.adler=T(t.adler,e,r,a)),t.next_in+=r,t.total_in+=r,r)},et=function(t,e){var a,n,r=t.max_chain_length,i=t.strstart,s=t.prev_length,h=t.nice_match,l=t.strstart>t.w_size-J?t.strstart-(t.w_size-J):0,_=t.window,o=t.w_mask,d=t.prev,u=t.strstart+H,f=_[i+s-1],c=_[i+s];t.prev_length>=t.good_match&&(r>>=2),h>t.lookahead&&(h=t.lookahead);do{if(_[(a=e)+s]===c&&_[a+s-1]===f&&_[a]===_[i]&&_[++a]===_[i+1]){i+=2,a++;do{}while(_[++i]===_[++a]&&_[++i]===_[++a]&&_[++i]===_[++a]&&_[++i]===_[++a]&&_[++i]===_[++a]&&_[++i]===_[++a]&&_[++i]===_[++a]&&_[++i]===_[++a]&&i<u);if(n=H-(u-i),i=u-H,n>s){if(t.match_start=e,s=n,n>=h)break;f=_[i+s-1],c=_[i+s]}}}while((e=d[e&o])>l&&0!==--r);return s<=t.lookahead?s:t.lookahead},at=function(t){var e,a,n,r,i,s=t.w_size;do{if(r=t.window_size-t.lookahead-t.strstart,t.strstart>=s+(s-J)){t.window.set(t.window.subarray(s,s+s),0),t.match_start-=s,t.strstart-=s,t.block_start-=s,e=a=t.hash_size;do{n=t.head[--e],t.head[e]=n>=s?n-s:0}while(--a);e=a=s;do{n=t.prev[--e],t.prev[e]=n>=s?n-s:0}while(--a);r+=s}if(0===t.strm.avail_in)break;if(a=tt(t.strm,t.window,t.strstart+t.lookahead,r),t.lookahead+=a,t.lookahead+t.insert>=3)for(i=t.strstart-t.insert,t.ins_h=t.window[i],t.ins_h=W(t,t.ins_h,t.window[i+1]);t.insert&&(t.ins_h=W(t,t.ins_h,t.window[i+3-1]),t.prev[i&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=i,i++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<J&&0!==t.strm.avail_in)},nt=function(t,e){for(var a,n;;){if(t.lookahead<J){if(at(t),t.lookahead<J&&0===e)return 1;if(0===t.lookahead)break}if(a=0,t.lookahead>=3&&(t.ins_h=W(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==a&&t.strstart-a<=t.w_size-J&&(t.match_length=et(t,a)),t.match_length>=3)if(n=F(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){t.match_length--;do{t.strstart++,t.ins_h=W(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart}while(0!==--t.match_length);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=W(t,t.ins_h,t.window[t.strstart+1]);else n=F(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(n&&(Y(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<2?t.strstart:2,4===e?(Y(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(Y(t,!1),0===t.strm.avail_out)?1:2},rt=function(t,e){for(var a,n,r;;){if(t.lookahead<J){if(at(t),t.lookahead<J&&0===e)return 1;if(0===t.lookahead)break}if(a=0,t.lookahead>=3&&(t.ins_h=W(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==a&&t.prev_length<t.max_lazy_match&&t.strstart-a<=t.w_size-J&&(t.match_length=et(t,a),t.match_length<=5&&(1===t.strategy||3===t.match_length&&t.strstart-t.match_start>4096)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){r=t.strstart+t.lookahead-3,n=F(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;do{++t.strstart<=r&&(t.ins_h=W(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart)}while(0!==--t.prev_length);if(t.match_available=0,t.match_length=2,t.strstart++,n&&(Y(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if((n=F(t,0,t.window[t.strstart-1]))&&Y(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(n=F(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,4===e?(Y(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(Y(t,!1),0===t.strm.avail_out)?1:2};function it(t,e,a,n,r){this.good_length=t,this.max_lazy=e,this.nice_length=a,this.max_chain=n,this.func=r}var st=[new it(0,0,0,0,function(t,e){var a=65535;for(a>t.pending_buf_size-5&&(a=t.pending_buf_size-5);;){if(t.lookahead<=1){if(at(t),0===t.lookahead&&0===e)return 1;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var n=t.block_start+a;if((0===t.strstart||t.strstart>=n)&&(t.lookahead=t.strstart-n,t.strstart=n,Y(t,!1),0===t.strm.avail_out))return 1;if(t.strstart-t.block_start>=t.w_size-J&&(Y(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(Y(t,!0),0===t.strm.avail_out?3:4):(t.strstart>t.block_start&&(Y(t,!1),t.strm.avail_out),1)}),new it(4,4,8,4,nt),new it(4,5,16,8,nt),new it(4,6,32,32,nt),new it(4,4,16,16,rt),new it(8,16,32,32,rt),new it(8,16,128,128,rt),new it(8,32,128,256,rt),new it(32,128,258,1024,rt),new it(32,258,258,4096,rt)];function ht(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=8,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),V(this.dyn_ltree),V(this.dyn_dtree),V(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),V(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),V(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}for(var lt=function(t){var e,i=function(t){if(!t||!t.state)return Q(t,G);t.total_in=t.total_out=0,t.data_type=2;var e=t.state;return e.pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?42:N,t.adler=2===e.wrap?0:1,e.last_flush=0,function(t){D||(function(){var t,e,i,g,v,w=Array(16);for(i=0,g=0;g<28;g++)for(o[g]=i,t=0;t<1<<a[g];t++)_[i++]=g;for(_[i-1]=g,v=0,g=0;g<16;g++)for(c[g]=v,t=0;t<1<<n[g];t++)l[v++]=g;for(v>>=7;g<30;g++)for(c[g]=v<<7,t=0;t<1<<n[g]-7;t++)l[256+v++]=g;for(e=0;e<=15;e++)w[e]=0;for(t=0;t<=143;)s[2*t+1]=8,t++,w[8]++;for(;t<=255;)s[2*t+1]=9,t++,w[9]++;for(;t<=279;)s[2*t+1]=7,t++,w[7]++;for(;t<=287;)s[2*t+1]=8,t++,w[8]++;for(z(s,287,w),t=0;t<30;t++)h[2*t+1]=5,h[2*t]=y(t,5);d=new p(s,a,257,286,15),u=new p(h,n,0,30,15),f=new p([],r,0,19,7)}(),D=!0),t.l_desc=new g(t.dyn_ltree,d),t.d_desc=new g(t.dyn_dtree,u),t.bl_desc=new g(t.bl_tree,f),t.bi_buf=0,t.bi_valid=0,k(t)}(e),0}(t);return 0===i&&((e=t.state).window_size=2*e.w_size,V(e.head),e.max_lazy_match=st[e.level].max_lazy,e.good_match=st[e.level].good_length,e.nice_match=st[e.level].nice_length,e.max_chain_length=st[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=2,e.match_available=0,e.ins_h=0),i},_t=function(t,e){var a,n;if(!t||!t.state||e>5||e<0)return t?Q(t,G):G;var r=t.state;if(!t.output||!t.input&&0!==t.avail_in||r.status===P&&4!==e)return Q(t,0===t.avail_out?-5:G);r.strm=t;var i=r.last_flush;if(r.last_flush=e,42===r.status)if(2===r.wrap)t.adler=0,Z(r,31),Z(r,139),Z(r,8),r.gzhead?(Z(r,(r.gzhead.text?1:0)+(r.gzhead.hcrc?2:0)+(r.gzhead.extra?4:0)+(r.gzhead.name?8:0)+(r.gzhead.comment?16:0)),Z(r,255&r.gzhead.time),Z(r,r.gzhead.time>>8&255),Z(r,r.gzhead.time>>16&255),Z(r,r.gzhead.time>>24&255),Z(r,9===r.level?2:r.strategy>=2||r.level<2?4:0),Z(r,255&r.gzhead.os),r.gzhead.extra&&r.gzhead.extra.length&&(Z(r,255&r.gzhead.extra.length),Z(r,r.gzhead.extra.length>>8&255)),r.gzhead.hcrc&&(t.adler=T(t.adler,r.pending_buf,r.pending,0)),r.gzindex=0,r.status=69):(Z(r,0),Z(r,0),Z(r,0),Z(r,0),Z(r,0),Z(r,9===r.level?2:r.strategy>=2||r.level<2?4:0),Z(r,3),r.status=N);else{var h=8+(r.w_bits-8<<4)<<8;h|=(r.strategy>=2||r.level<2?0:r.level<6?1:6===r.level?2:3)<<6,0!==r.strstart&&(h|=32),h+=31-h%31,r.status=N,$(r,h),0!==r.strstart&&($(r,t.adler>>>16),$(r,65535&t.adler)),t.adler=1}if(69===r.status)if(r.gzhead.extra){for(a=r.pending;r.gzindex<(65535&r.gzhead.extra.length)&&(r.pending!==r.pending_buf_size||(r.gzhead.hcrc&&r.pending>a&&(t.adler=T(t.adler,r.pending_buf,r.pending-a,a)),X(t),a=r.pending,r.pending!==r.pending_buf_size));)Z(r,255&r.gzhead.extra[r.gzindex]),r.gzindex++;r.gzhead.hcrc&&r.pending>a&&(t.adler=T(t.adler,r.pending_buf,r.pending-a,a)),r.gzindex===r.gzhead.extra.length&&(r.gzindex=0,r.status=73)}else r.status=73;if(73===r.status)if(r.gzhead.name){a=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>a&&(t.adler=T(t.adler,r.pending_buf,r.pending-a,a)),X(t),a=r.pending,r.pending===r.pending_buf_size)){n=1;break}n=r.gzindex<r.gzhead.name.length?255&r.gzhead.name.charCodeAt(r.gzindex++):0,Z(r,n)}while(0!==n);r.gzhead.hcrc&&r.pending>a&&(t.adler=T(t.adler,r.pending_buf,r.pending-a,a)),0===n&&(r.gzindex=0,r.status=91)}else r.status=91;if(91===r.status)if(r.gzhead.comment){a=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>a&&(t.adler=T(t.adler,r.pending_buf,r.pending-a,a)),X(t),a=r.pending,r.pending===r.pending_buf_size)){n=1;break}n=r.gzindex<r.gzhead.comment.length?255&r.gzhead.comment.charCodeAt(r.gzindex++):0,Z(r,n)}while(0!==n);r.gzhead.hcrc&&r.pending>a&&(t.adler=T(t.adler,r.pending_buf,r.pending-a,a)),0===n&&(r.status=K)}else r.status=K;if(r.status===K&&(r.gzhead.hcrc?(r.pending+2>r.pending_buf_size&&X(t),r.pending+2<=r.pending_buf_size&&(Z(r,255&t.adler),Z(r,t.adler>>8&255),t.adler=0,r.status=N)):r.status=N),0!==r.pending){if(X(t),0===t.avail_out)return r.last_flush=-1,0}else if(0===t.avail_in&&R(e)<=R(i)&&4!==e)return Q(t,-5);if(r.status===P&&0!==t.avail_in)return Q(t,-5);if(0!==t.avail_in||0!==r.lookahead||0!==e&&r.status!==P){var l=2===r.strategy?function(t,e){for(var a;;){if(0===t.lookahead&&(at(t),0===t.lookahead)){if(0===e)return 1;break}if(t.match_length=0,a=F(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,a&&(Y(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(Y(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(Y(t,!1),0===t.strm.avail_out)?1:2}(r,e):3===r.strategy?function(t,e){for(var a,n,r,i,s=t.window;;){if(t.lookahead<=H){if(at(t),t.lookahead<=H&&0===e)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&t.strstart>0&&(n=s[r=t.strstart-1])===s[++r]&&n===s[++r]&&n===s[++r]){i=t.strstart+H;do{}while(n===s[++r]&&n===s[++r]&&n===s[++r]&&n===s[++r]&&n===s[++r]&&n===s[++r]&&n===s[++r]&&n===s[++r]&&r<i);t.match_length=H-(i-r),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(a=F(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(a=F(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),a&&(Y(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(Y(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(Y(t,!1),0===t.strm.avail_out)?1:2}(r,e):st[r.level].func(r,e);if(3!==l&&4!==l||(r.status=P),1===l||3===l)return 0===t.avail_out&&(r.last_flush=-1),0;if(2===l&&(1===e?function(t){m(t,2,3),b(t,256,s),function(t){16===t.bi_valid?(w(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}(t)}(r):5!==e&&(q(r,0,0,!1),3===e&&(V(r.head),0===r.lookahead&&(r.strstart=0,r.block_start=0,r.insert=0))),X(t),0===t.avail_out))return r.last_flush=-1,0}return 4!==e?0:r.wrap<=0?1:(2===r.wrap?(Z(r,255&t.adler),Z(r,t.adler>>8&255),Z(r,t.adler>>16&255),Z(r,t.adler>>24&255),Z(r,255&t.total_in),Z(r,t.total_in>>8&255),Z(r,t.total_in>>16&255),Z(r,t.total_in>>24&255)):($(r,t.adler>>>16),$(r,65535&t.adler)),X(t),r.wrap>0&&(r.wrap=-r.wrap),0!==r.pending?0:1)},ot=function(t){if(!t||!t.state)return G;var e=t.state.status;return 42!==e&&69!==e&&73!==e&&91!==e&&e!==K&&e!==N&&e!==P?Q(t,G):(t.state=null,e===N?Q(t,-3):0)},dt=new Uint8Array(256),ut=0;ut<256;ut++)dt[ut]=ut>=252?6:ut>=248?5:ut>=240?4:ut>=224?3:ut>=192?2:1;dt[254]=dt[254]=1;var ft=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0},ct=Object.prototype.toString;function pt(){this.options={level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0};var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new ft,this.strm.avail_out=0;var e,a,n=function(t,e,a,n,r,i){if(!t)return G;var s=1;if(-1===e&&(e=6),n<0?(s=0,n=-n):n>15&&(s=2,n-=16),r<1||r>9||8!==a||n<8||n>15||e<0||e>9||i<0||i>4)return Q(t,G);8===n&&(n=9);var h=new ht;return t.state=h,h.strm=t,h.wrap=s,h.gzhead=null,h.w_bits=n,h.w_size=1<<h.w_bits,h.w_mask=h.w_size-1,h.hash_bits=r+7,h.hash_size=1<<h.hash_bits,h.hash_mask=h.hash_size-1,h.hash_shift=~~((h.hash_bits+3-1)/3),h.window=new Uint8Array(2*h.w_size),h.head=new Uint16Array(h.hash_size),h.prev=new Uint16Array(h.w_size),h.lit_bufsize=1<<r+6,h.pending_buf_size=4*h.lit_bufsize,h.pending_buf=new Uint8Array(h.pending_buf_size),h.d_buf=1*h.lit_bufsize,h.l_buf=3*h.lit_bufsize,h.level=e,h.strategy=i,h.method=a,lt(t)}(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(0!==n)throw Error(O[n]);if(t.header&&(e=this.strm,a=t.header,e&&e.state&&(2!==e.state.wrap||(e.state.gzhead=a))),t.dictionary){var r;if(r="[object ArrayBuffer]"===ct.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,0!==(n=function(t,e){var a=e.length;if(!t||!t.state)return G;var n=t.state,r=n.wrap;if(2===r||1===r&&42!==n.status||n.lookahead)return G;if(1===r&&(t.adler=L(t.adler,e,a,0)),n.wrap=0,a>=n.w_size){0===r&&(V(n.head),n.strstart=0,n.block_start=0,n.insert=0);var i=new Uint8Array(n.w_size);i.set(e.subarray(a-n.w_size,a),0),e=i,a=n.w_size}var s=t.avail_in,h=t.next_in,l=t.input;for(t.avail_in=a,t.next_in=0,t.input=e,at(n);n.lookahead>=3;){var _=n.strstart,o=n.lookahead-2;do{n.ins_h=W(n,n.ins_h,n.window[_+3-1]),n.prev[_&n.w_mask]=n.head[n.ins_h],n.head[n.ins_h]=_,_++}while(--o);n.strstart=_,n.lookahead=2,at(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=2,n.match_available=0,t.next_in=h,t.input=l,t.avail_in=s,n.wrap=r,0}(this.strm,r)))throw Error(O[n]);this._dict_set=!0}}function gt(t,e,a){try{t.postMessage({type:"errored",error:e,streamId:a})}catch(n){t.postMessage({type:"errored",error:e+"",streamId:a})}}function vt(t){const e=t.strm.adler;return new Uint8Array([3,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e])}pt.prototype.push=function(t,e){var a,n,r=this.strm,i=this.options.chunkSize;if(this.ended)return!1;for(n=e===~~e?e:!0===e?4:0,"[object ArrayBuffer]"===ct.call(t)?r.input=new Uint8Array(t):r.input=t,r.next_in=0,r.avail_in=r.input.length;;)if(0===r.avail_out&&(r.output=new Uint8Array(i),r.next_out=0,r.avail_out=i),(2===n||3===n)&&r.avail_out<=6)this.onData(r.output.subarray(0,r.next_out)),r.avail_out=0;else{if(1===(a=_t(r,n)))return r.next_out>0&&this.onData(r.output.subarray(0,r.next_out)),a=ot(this.strm),this.onEnd(a),this.ended=!0,0===a;if(0!==r.avail_out){if(n>0&&r.next_out>0)this.onData(r.output.subarray(0,r.next_out)),r.avail_out=0;else if(0===r.avail_in)break}else this.onData(r.output)}return!0},pt.prototype.onData=function(t){this.chunks.push(t)},pt.prototype.onEnd=function(t){0===t&&(this.result=function(t){for(var e=0,a=0,n=t.length;a<n;a++)e+=t[a].length;for(var r=new Uint8Array(e),i=0,s=0,h=t.length;i<h;i++){var l=t[i];r.set(l,s),s+=l.length}return r}(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},function(e=self){try{const a=new Map;e.addEventListener("message",n=>{try{const r=function(e,a){switch(a.action){case"init":return{type:"initialized",version:"6.13.0"};case"write":{let n=e.get(a.streamId);n||(n=new pt,e.set(a.streamId,n));const r=n.chunks.length,i=function(t){if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(t);let e,a,n,r,i,s=t.length,h=0;for(r=0;r<s;r++)a=t.charCodeAt(r),55296==(64512&a)&&r+1<s&&(n=t.charCodeAt(r+1),56320==(64512&n)&&(a=65536+(a-55296<<10)+(n-56320),r++)),h+=a<128?1:a<2048?2:a<65536?3:4;for(e=new Uint8Array(h),i=0,r=0;i<h;r++)a=t.charCodeAt(r),55296==(64512&a)&&r+1<s&&(n=t.charCodeAt(r+1),56320==(64512&n)&&(a=65536+(a-55296<<10)+(n-56320),r++)),a<128?e[i++]=a:a<2048?(e[i++]=192|a>>>6,e[i++]=128|63&a):a<65536?(e[i++]=224|a>>>12,e[i++]=128|a>>>6&63,e[i++]=128|63&a):(e[i++]=240|a>>>18,e[i++]=128|a>>>12&63,e[i++]=128|a>>>6&63,e[i++]=128|63&a);return e}(a.data);return n.push(i,2),{type:"wrote",id:a.id,streamId:a.streamId,result:t(n.chunks.slice(r)),trailer:vt(n),additionalBytesCount:i.length}}case"reset":e.delete(a.streamId)}}(a,n.data);r&&e.postMessage(r)}catch(t){gt(e,t,n.data&&"streamId"in n.data?n.data.streamId:void 0)}})}catch(t){gt(e,t)}}()})();'])))}let C={status:0};function dr(t,e,n,r=lr){switch(C.status===0&&Tc(t,e,r),C.status){case 1:return C.initializationFailureCallbacks.push(n),C.worker;case 3:return C.worker}}function an(){return C.status}function Tc(t,e,n=lr){try{const r=n(t),{stop:a}=N(t,r,"error",o=>{ie(t,e,o)}),{stop:i}=N(t,r,"message",({data:o})=>{o.type==="errored"?ie(t,e,o.error,o.streamId):o.type==="initialized"&&wc(o.version)});r.postMessage({action:"init"}),H(()=>Ec(e),yc),C={status:1,worker:r,stop:()=>{a(),i()},initializationFailureCallbacks:[]}}catch(r){ie(t,e,r)}}function Ec(t){C.status===1&&(P.error(`${t} failed to start: a timeout occurred while initializing the Worker`),C.initializationFailureCallbacks.forEach(e=>e()),C={status:2})}function wc(t){C.status===1&&(C={status:3,worker:C.worker,stop:C.stop,version:t})}function ie(t,e,n,r){C.status===1||C.status===0?(we({configuredUrl:t.workerUrl,error:n,source:e,scriptType:"worker"}),C.status===1&&C.initializationFailureCallbacks.forEach(a=>a()),C={status:2}):yn(n,{worker_version:C.status===3&&C.version,stream_id:r})}function fr(){return typeof Array.from=="function"&&typeof CSSSupportsRule=="function"&&typeof URL.createObjectURL=="function"&&"forEach"in NodeList.prototype}function Sc(t,e,n,r){const a=e.findTrackedSession(),i=Ac(a,r),s=n.findView();return pc(t,{viewContext:s,errorType:i,session:a})}function Ac(t,e){if(!fr())return"browser-not-supported";if(!t)return"rum-not-tracked";if(t.sessionReplay===0)return"incorrect-session-plan";if(!e)return"replay-not-started"}function Ic(t,e,n,r,a,i){let s=0,o;e.subscribe(9,()=>{(s===2||s===3)&&(l(),s=1)}),e.subscribe(10,()=>{s===1&&u()});const c=async()=>{const[d]=await Promise.all([a(),ha(t,"interactive")]);if(s!==2)return;const f=i();if(!f||!d){s=0;return}({stop:o}=d(e,t,n,r,f)),s=3};function u(d){const f=n.findTrackedSession();if(Cc(f,d)){s=1;return}Rc(s)||(s=2,c().catch(ce),xc(f,d)&&n.setForcedReplay())}function l(){s===3&&o?.(),s=0}return{start:u,stop:l,getSessionReplayLink(){return Sc(t,n,r,s!==0)},isRecording:()=>s===3}}function Cc(t,e){return!t||t.sessionReplay===0&&(!e||!e.force)}function Rc(t){return t===2||t===3}function xc(t,e){return e&&e.force&&t.sessionReplay===0}function kc(){let t=0;return{strategy:{start(){t=1},stop(){t=2},isRecording:()=>!1,getSessionReplayLink:R},shouldStartImmediately(e){return t===1||t===0&&!e.startSessionReplayRecordingManually}}}function Nc(t,e){if(yt()&&!vn("records")||!fr())return{start:R,stop:R,getReplayStats:()=>{},onRumStart:R,isRecording:()=>!1,getSessionReplayLink:()=>{}};let{strategy:n,shouldStartImmediately:r}=kc();return{start:i=>n.start(i),stop:()=>n.stop(),getSessionReplayLink:()=>n.getSessionReplayLink(),onRumStart:a,isRecording:()=>an()===3&&n.isRecording(),getReplayStats:i=>an()===3?gc(i):void 0};function a(i,s,o,c,u){let l;function d(){return l||(u??(u=dr(s,"Datadog Session Replay",()=>{n.stop()},e)),u&&(l=ur(s,u,1))),l}n=Ic(s,i,o,c,t,d),r(s)&&n.start()}}async function Oc(t=Lc){try{return await t()}catch(e){we({error:e,source:"Recorder",scriptType:"module"})}}async function Lc(){return(await En(()=>import("./psp-c35df622-_BoEEC7n.js"),__vite__mapDeps([0,1,2]))).startRecording}function Mc(){return Tn().Profiler!==void 0}const Dc=t=>{let e={status:"starting"};return t.register(0,({eventType:n})=>n!==b.VIEW&&n!==b.LONG_TASK?et:{type:n,_dd:{profiling:e}}),{get:()=>e,set:n=>{e=n}}};async function Vc(t=Pc){try{return await t()}catch(e){we({error:e,source:"Profiler",scriptType:"module"})}}async function Pc(){return(await En(()=>import("./psp-68be3bf8-B37Yb1EQ.js"),__vite__mapDeps([3,1,2]))).createRumProfiler}function Uc(){let t;function e(n,r,a,i,s){const o=i.findTrackedSession();if(!o||!Rn(o.id,a.profilingSampleRate))return;const c=Dc(r);if(!Mc()){c.set({status:"error",error_reason:"not-supported-by-browser"});return}Vc().then(u=>{if(!u){wt("[DD_RUM] Failed to lazy load the RUM Profiler"),c.set({status:"error",error_reason:"failed-to-lazy-load"});return}t=u(a,n,i,c),t.start(s.findView())}).catch(ce)}return{onRumStart:e,stop:()=>{t?.stop().catch(ce)}}}const Fc=Nc(Oc),zc=Uc(),pr=Ha(dc,Fc,zc,{startDeflateWorker:dr,createDeflateEncoder:ur,sdkName:"rum"});pa(Tn(),"DD_RUM",pr);const Qc=Object.freeze(Object.defineProperty({__proto__:null,DefaultPrivacyLevel:pt,datadogRum:pr},Symbol.toStringTag,{value:"Module"}));export{be as A,te as C,E as N,Ei as P,b as R,Pi as S,Wc as a,ii as b,Yc as c,Ci as d,$c as e,Kc as f,gi as g,Gc as h,_i as i,Hn as j,Kn as k,eo as l,Xc as m,no as n,ma as o,$a as p,Ee as q,Ii as r,Ri as s,Zc as t,jc as u,qc as v,Jc as w,st as x,T as y,Qc as z};
