const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/game-a0e37d72-s2bF6hzN.js","assets/game-fd4578d2-CJjC28Jv.js","assets/game-1db2353a-OPVs455c.js","assets/contextTrackingHelper-nB-OPn1T.css","assets/game-d8b296a6-D6-XlEtG.js","assets/game-6f09ab72-Bi-UxEEh.js","assets/game-8cc43aba-CiMuGUxk.js","assets/app-rLJ3tKt9.js","assets/game-33507154-FZO9iRpN.js","assets/game-a361386b-BqmJhnkd.js","assets/game-9d70c10e-C_hpcOIE.js","assets/game-00a0a3d9-os4tGKDs.js","assets/game-828640fc-Cs6SrrRS.js","assets/game-e80e6666-B6RRqfIv.js","assets/game-c536211d-D56Zha_z.js","assets/game-d999a8e8-BZTf8JuJ.js","assets/game-cdefd992-Bz4r6sWt.js","assets/game-ac023766-B4FMI-sS.js","assets/game-8e5b257f-Bpk5yijc.js"])))=>i.map(i=>d[i]);
import{_ as Be}from"./game-1db2353a-OPVs455c.js";import{B as b,D as ne,u as r,C as ge,r as H,a0 as mt,R as J,d as I,A as Z,y as _,x as X2,Q as Q2,c as eo,q as M,ax as to,ay as no,a4 as k0,az as oo,a3 as ro,aA as K0,m as so,aB as io,z as ze,a as he,ap as S,k as ht,I as S0,f as pt,am as t2,an as n2,i as o2,aC as ao,aD as lo,g as co,aE as po,ad as fo,e as N0,n as uo,T as mo,M as ho,aF as go,aG as vo,aH as r2,aI as wo,aJ as q0,ak as yo,ao as i0,aK as xo,aL as bo,O as Ao}from"./game-fd4578d2-CJjC28Jv.js";import{e as s2,G as _o,b as Co,c as ko}from"./game-6f09ab72-Bi-UxEEh.js";import{b as Ye,a as G,w as i2,l as L0,M as F0,P as So}from"./game-8cc43aba-CiMuGUxk.js";import{u as P}from"./game-33507154-FZO9iRpN.js";import{a as a2,b as No,s as ke,e as Ge,f as l2,g as c2,t as Lo,h as Fo,F as d2,i as Eo,j as Mo,k as Ro,m as Oo,n as Io}from"./app-rLJ3tKt9.js";import{a as E0,s as M0,b as Po,t as To,c as Bo,p as p2,l as Go}from"./game-a361386b-BqmJhnkd.js";import{g as Uo}from"./game-d8b296a6-D6-XlEtG.js";import{A as f2,I as Do}from"./game-9d70c10e-C_hpcOIE.js";import{m as u2,e as m2}from"./game-00a0a3d9-os4tGKDs.js";function $o(e){a2(1,arguments);var t=No(e),n=t.getTime();return n}function J0(e){return a2(1,arguments),Math.floor($o(e)/1e3)}var X0,Q0;function a0(){return a0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},a0.apply(null,arguments)}var Zo=function(e){return b("svg",a0({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"currentColor"},e),X0||(X0=b("path",{fill:"inherit",fillRule:"evenodd",d:"M7 12a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1m4 0a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H12a1 1 0 0 1-1-1m4 0a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H16a1 1 0 0 1-1-1",clipRule:"evenodd"})),Q0||(Q0=b("path",{fill:"inherit",fillRule:"evenodd",d:"M4.754 15.404a1 1 0 0 1 .08.493L4.5 19.5l3.418-.407a1 1 0 0 1 .535.084l.215.099A8 8 0 0 0 12 20a8 8 0 1 0-8-8 8 8 0 0 0 .665 3.2l.09.204Zm3.08 5.69A10 10 0 0 0 12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12a10 10 0 0 0 .832 4l-.397 4.524a1 1 0 0 0 1.074 1.057l4.324-.488Z",clipRule:"evenodd"})))};const Vt=ne((e,t)=>r(ge,{ref:t,IconSvg:Zo,...e}));var en,tn;function l0(){return l0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},l0.apply(null,arguments)}var Ho=function(e){return b("svg",l0({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"currentColor"},e),en||(en=b("path",{fill:"inherit",fillRule:"evenodd",d:"M3 7a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1zm2 1v12h10V8z",clipRule:"evenodd"})),tn||(tn=b("path",{fill:"inherit",fillRule:"evenodd",d:"M7 3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1h-4a1 1 0 1 1 0-2h3V4H9v3a1 1 0 0 1-2 0z",clipRule:"evenodd"})))};const h2=ne((e,t)=>r(ge,{ref:t,IconSvg:Ho,...e}));var nn,on,rn;function c0(){return c0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},c0.apply(null,arguments)}var zo=function(e){return b("svg",c0({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"currentColor"},e),nn||(nn=b("path",{fill:"inherit",fillRule:"evenodd",d:"M1.998 9.227A6 6 0 0 1 7.948 4h7.578a6 6 0 0 1 5.95 5.227l.968 7.452a3.542 3.542 0 0 1-6.133 2.839L13.113 16H10.36l-3.198 3.518a3.542 3.542 0 0 1-6.133-2.839l.968-7.452ZM7.948 6A4 4 0 0 0 3.98 9.485l-.968 7.452a1.542 1.542 0 0 0 2.67 1.235l3.496-3.845a1 1 0 0 1 .74-.327h3.636a1 1 0 0 1 .74.327l3.495 3.845a1.542 1.542 0 0 0 2.67-1.235l-.967-7.452A4 4 0 0 0 15.526 6z",clipRule:"evenodd"})),on||(on=b("path",{fill:"inherit",fillRule:"evenodd",d:"M12.737 10.5a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1",clipRule:"evenodd"})),rn||(rn=b("path",{fill:"inherit",d:"M8.237 8a1 1 0 0 0-1 1v.5h-.5a1 1 0 0 0 0 2h.5v.5a1 1 0 1 0 2 0v-.5h.5a1 1 0 0 0 0-2h-.5V9a1 1 0 0 0-1-1"})))};const g2=ne((e,t)=>r(ge,{ref:t,IconSvg:zo,...e}));var sn,an;function d0(){return d0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},d0.apply(null,arguments)}var Vo=function(e){return b("svg",d0({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),sn||(sn=b("g",{fill:"currentColor",clipPath:"url(#mobile-install_svg__a)"},b("path",{d:"M17 18H7V6h6a1 1 0 1 0 0-2H7V3h6.004a.996.996 0 0 0 .002-1.991L7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2v-4a1 1 0 1 0-2 0zm0 3H7v-1h10z"}),b("path",{d:"M17.293 13.293a1 1 0 0 0 1.414 0l3.587-3.587a.998.998 0 0 0-1.41-1.413L19 10.17V4a1 1 0 1 0-2 0v6.17l-1.884-1.877a.998.998 0 0 0-1.41 1.413z"}))),an||(an=b("defs",null,b("clipPath",{id:"mobile-install_svg__a"},b("path",{fill:"#fff",d:"M0 0h24v24H0z"})))))};const jo=ne((e,t)=>r(ge,{ref:t,IconSvg:Vo,...e}));var ln;function p0(){return p0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},p0.apply(null,arguments)}var Wo=function(e){return b("svg",p0({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"currentColor"},e),ln||(ln=b("path",{fill:"inherit",fillRule:"evenodd",d:"M12 4a8 8 0 0 0-6.454 12.728A8.98 8.98 0 0 1 12 14a8.98 8.98 0 0 1 6.454 2.728A8 8 0 0 0 12 4m5.078 14.182A6.98 6.98 0 0 0 12 16a6.98 6.98 0 0 0-5.078 2.182A7.96 7.96 0 0 0 12 20a7.96 7.96 0 0 0 5.078-1.818M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10a9.97 9.97 0 0 1-2.829 6.97A9.97 9.97 0 0 1 12 22a9.97 9.97 0 0 1-7.171-3.03A9.97 9.97 0 0 1 2 12m10-5a2 2 0 1 0 0 4 2 2 0 0 0 0-4M8 9a4 4 0 1 1 8 0 4 4 0 0 1-8 0",clipRule:"evenodd"})))};const Yo=ne((e,t)=>r(ge,{ref:t,IconSvg:Wo,...e}));var cn,dn;function f0(){return f0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},f0.apply(null,arguments)}var Ko=function(e){return b("svg",f0({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"currentColor"},e),cn||(cn=b("path",{fill:"inherit",fillRule:"evenodd",d:"M11.99 21.04a9 9 0 0 0 5.954-2.226 1 1 0 1 0-1.317-1.506A7 7 0 1 1 18.52 9.45a1 1 0 1 0 1.858-.74 9 9 0 1 0-8.388 12.33",clipRule:"evenodd"})),dn||(dn=b("path",{fill:"inherit",fillRule:"evenodd",d:"M13.95 9.08a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-5a1 1 0 1 0-2 0v4h-4a1 1 0 0 0-1 1",clipRule:"evenodd"})))};const qo=ne((e,t)=>r(ge,{ref:t,IconSvg:Ko,...e}));var pn,fn;function u0(){return u0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},u0.apply(null,arguments)}var Jo=function(e){return b("svg",u0({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),pn||(pn=b("path",{fill:"#262626",fillRule:"evenodd",d:"M11.293 1.293a1 1 0 0 1 1.414 0L15.414 4H19a1 1 0 0 1 1 1v3.586l2.707 2.707a1 1 0 0 1 0 1.414L20 15.414V19a1 1 0 0 1-1 1h-3.586l-2.707 2.707a1 1 0 0 1-1.414 0L8.586 20H5a1 1 0 0 1-1-1v-3.586l-2.707-2.707a1 1 0 0 1 0-1.414L4 8.586V5a1 1 0 0 1 1-1h3.586zM12 3.414 9.707 5.707A1 1 0 0 1 9 6H6v3a1 1 0 0 1-.293.707L3.414 12l2.293 2.293A1 1 0 0 1 6 15v3h3a1 1 0 0 1 .707.293L12 20.586l2.293-2.293A1 1 0 0 1 15 18h3v-3a1 1 0 0 1 .293-.707L20.586 12l-2.293-2.293A1 1 0 0 1 18 9V6h-3a1 1 0 0 1-.707-.293z",clipRule:"evenodd"})),fn||(fn=b("path",{fill:"#262626",fillRule:"evenodd",d:"M12 10a2 2 0 1 0 0 4 2 2 0 0 0 0-4m-4 2a4 4 0 1 1 8 0 4 4 0 0 1-8 0",clipRule:"evenodd"})))};const v2=ne((e,t)=>r(ge,{ref:t,IconSvg:Jo,...e}));var un,mn,hn;function m0(){return m0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},m0.apply(null,arguments)}var Xo=function(e){return b("svg",m0({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),un||(un=b("path",{fill:"#E3FF34",d:"M11 21a9 9 0 1 0-7.465-3.97L3.5 17l-1 3.5 3.655-.914A8.96 8.96 0 0 0 11 21"})),mn||(mn=b("path",{fill:"#222",fillRule:"evenodd",d:"M7 12a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1m4 0a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H12a1 1 0 0 1-1-1m4 0a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H16a1 1 0 0 1-1-1",clipRule:"evenodd"})),hn||(hn=b("path",{fill:"#262626",fillRule:"evenodd",d:"M4.754 15.404a1 1 0 0 1 .08.493L4.5 19.5l3.418-.407a1 1 0 0 1 .535.084l.215.099A8 8 0 0 0 12 20a8 8 0 1 0-8-8 8 8 0 0 0 .665 3.2l.09.204Zm3.08 5.69A10 10 0 0 0 12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12a10 10 0 0 0 .832 4l-.397 4.524a1 1 0 0 0 1.074 1.057l4.324-.488Z",clipRule:"evenodd"})))};const Qo=ne((e,t)=>r(ge,{ref:t,IconSvg:Xo,...e}));var gn,vn,wn,yn;function h0(){return h0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},h0.apply(null,arguments)}var er=function(e){return b("svg",h0({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),gn||(gn=b("path",{fill:"#262626",fillRule:"evenodd",d:"M1.998 9.227A6 6 0 0 1 7.948 4h7.578a6 6 0 0 1 5.95 5.227l.968 7.452a3.542 3.542 0 0 1-6.133 2.839L13.113 16H10.36l-3.198 3.518a3.542 3.542 0 0 1-6.133-2.839l.968-7.452ZM7.948 6A4 4 0 0 0 3.98 9.485l-.968 7.452a1.542 1.542 0 0 0 2.67 1.235l3.496-3.845a1 1 0 0 1 .74-.327h3.636a1 1 0 0 1 .74.327l3.495 3.845a1.542 1.542 0 0 0 2.67-1.235l-.967-7.452A4 4 0 0 0 15.526 6z",clipRule:"evenodd"})),vn||(vn=b("path",{fill:"#E3FF34",d:"M3.98 9.485A4 4 0 0 1 7.949 6h7.578a4 4 0 0 1 3.967 3.485l.967 7.452a1.542 1.542 0 0 1-2.67 1.235l-3.495-3.845a1 1 0 0 0-.74-.327H9.919a1 1 0 0 0-.74.327l-3.496 3.845a1.542 1.542 0 0 1-2.67-1.235z"})),wn||(wn=b("path",{fill:"#262626",fillRule:"evenodd",d:"M12.737 10.5a1 1 0 0 1 1-1h3a1 1 0 1 1 0 2h-3a1 1 0 0 1-1-1",clipRule:"evenodd"})),yn||(yn=b("path",{fill:"#262626",d:"M8.237 8a1 1 0 0 0-1 1v.5h-.5a1 1 0 0 0 0 2h.5v.5a1 1 0 1 0 2 0v-.5h.5a1 1 0 0 0 0-2h-.5V9a1 1 0 0 0-1-1"})))};const w2=ne((e,t)=>r(ge,{ref:t,IconSvg:er,...e}));var xn,bn,An,_n,Cn,kn,Sn;function g0(){return g0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},g0.apply(null,arguments)}var tr=function(e){return b("svg",g0({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),xn||(xn=b("path",{fill:"#E3FF34",d:"M7 14v6h3v-6"})),bn||(bn=b("path",{fill:"#262626",fillRule:"evenodd",d:"M7 13a1 1 0 0 1 1 1v5h1v-5a1 1 0 1 1 2 0v6a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1v-6a1 1 0 0 1 1-1",clipRule:"evenodd"})),An||(An=b("path",{fill:"#E3FF34",d:"M18 14a3 3 0 1 0 0-6"})),_n||(_n=b("path",{fill:"#262626",fillRule:"evenodd",d:"M17 8a1 1 0 0 1 1-1 4 4 0 1 1 0 8 1 1 0 1 1 0-2 2 2 0 1 0 0-4 1 1 0 0 1-1-1",clipRule:"evenodd"})),Cn||(Cn=b("path",{fill:"#E3FF34",d:"M12 8H3v6h9l6 5V4z"})),kn||(kn=b("path",{fill:"#262626",fillRule:"evenodd",d:"M18.472 3.118A1 1 0 0 1 19 4v15a1 1 0 0 1-1.64.768L11.638 15H3a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h8.697l5.748-3.832a1 1 0 0 1 1.027-.05M17 5.868l-4.445 2.964A1 1 0 0 1 12 9H4v4h8a1 1 0 0 1 .64.232L17 16.865z",clipRule:"evenodd"})),Sn||(Sn=b("path",{fill:"#262626",fillRule:"evenodd",d:"M10 7a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0V8a1 1 0 0 1 1-1",clipRule:"evenodd"})))};const y2=ne((e,t)=>r(ge,{ref:t,IconSvg:tr,...e}));var Nn,Ln;function v0(){return v0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},v0.apply(null,arguments)}var nr=function(e){return b("svg",v0({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),Nn||(Nn=b("circle",{cx:12,cy:12,r:9,fill:"#E3FF34"})),Ln||(Ln=b("path",{fill:"#262626",fillRule:"evenodd",d:"M12 4a8 8 0 0 0-6.454 12.728A8.98 8.98 0 0 1 12 14a8.98 8.98 0 0 1 6.454 2.728A8 8 0 0 0 12 4m5.078 14.182A6.98 6.98 0 0 0 12 16a6.98 6.98 0 0 0-5.078 2.182A7.96 7.96 0 0 0 12 20a7.96 7.96 0 0 0 5.078-1.818M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10a9.97 9.97 0 0 1-2.829 6.97A9.97 9.97 0 0 1 12 22a9.97 9.97 0 0 1-7.171-3.03A9.97 9.97 0 0 1 2 12m10-5a2 2 0 1 0 0 4 2 2 0 0 0 0-4M8 9a4 4 0 1 1 8 0 4 4 0 0 1-8 0",clipRule:"evenodd"})))};const or=ne((e,t)=>r(ge,{ref:t,IconSvg:nr,...e}));var Fn,En,Mn,Rn;function w0(){return w0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},w0.apply(null,arguments)}var rr=function(e){return b("svg",w0({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),Fn||(Fn=b("path",{fill:"#E3FF34",fillRule:"evenodd",d:"M11.293 1.293a1 1 0 0 1 1.414 0L15.414 4H19a1 1 0 0 1 1 1v3.586l2.707 2.707a1 1 0 0 1 0 1.414L20 15.414V19a1 1 0 0 1-1 1h-3.586l-2.707 2.707a1 1 0 0 1-1.414 0L8.586 20H5a1 1 0 0 1-1-1v-3.586l-2.707-2.707a1 1 0 0 1 0-1.414L4 8.586V5a1 1 0 0 1 1-1h3.586z",clipRule:"evenodd"})),En||(En=b("circle",{cx:12,cy:12,r:3,fill:"#fff"})),Mn||(Mn=b("path",{fill:"#262626",fillRule:"evenodd",d:"M11.293 1.293a1 1 0 0 1 1.414 0L15.414 4H19a1 1 0 0 1 1 1v3.586l2.707 2.707a1 1 0 0 1 0 1.414L20 15.414V19a1 1 0 0 1-1 1h-3.586l-2.707 2.707a1 1 0 0 1-1.414 0L8.586 20H5a1 1 0 0 1-1-1v-3.586l-2.707-2.707a1 1 0 0 1 0-1.414L4 8.586V5a1 1 0 0 1 1-1h3.586zM12 3.414 9.707 5.707A1 1 0 0 1 9 6H6v3a1 1 0 0 1-.293.707L3.414 12l2.293 2.293A1 1 0 0 1 6 15v3h3a1 1 0 0 1 .707.293L12 20.586l2.293-2.293A1 1 0 0 1 15 18h3v-3a1 1 0 0 1 .293-.707L20.586 12l-2.293-2.293A1 1 0 0 1 18 9V6h-3a1 1 0 0 1-.707-.293z",clipRule:"evenodd"})),Rn||(Rn=b("path",{fill:"#262626",fillRule:"evenodd",d:"M12 10a2 2 0 1 0 0 4 2 2 0 0 0 0-4m-4 2a4 4 0 1 1 8 0 4 4 0 0 1-8 0",clipRule:"evenodd"})))};const x2=ne((e,t)=>r(ge,{ref:t,IconSvg:rr,...e})),y0=ne(({show:e=!0,content:t,offsetTop:n=0,offsetRight:o=0,isOverlay:s=!0,children:i},a)=>e?r("div",t?{className:"relative flex w-fit",children:[i,r("div",{ref:a,className:H("absolute","px-[3px] py-px","box-content rounded-lg","text-xxs text-font-overlay","whitespace-nowrap bg-error-default font-extrabold",{"border-[1.5px] border-surface-primary":s}),style:{top:`${n}px`,right:`${o}px`},children:t})]}:{className:"relative flex w-fit",children:[i,r("div",{ref:a,className:H("absolute","size-[0.4375rem]","box-content rounded-full","bg-error-default",{"border-[1.5px] border-surface-primary":s}),style:{top:`${n}px`,right:`${o}px`}})]}):r(mt,{children:i})),On='<svg xmlns="http://www.w3.org/2000/svg" width="48" height="42" fill="none"><path fill="url(#a)" stroke="#FFD8A6" stroke-width=".2" d="M4.996 11.256v.043l.032.03L23.122 28.39l.169.159v-7.942l-.026-.029L5.234.933 5.06.745V1l-.064 10.256Z"/><path fill="url(#b)" stroke="#FFD8A6" stroke-width=".2" d="M4.996 22.63v.046l.035.03L23.126 38.3l.165.142v-9.05l-.031-.03-18.031-17.06-.168-.159-.001.23-.064 10.257Z"/><path fill="url(#c)" stroke="#FFD8A6" stroke-width=".2" d="M42.483 11.256v.043l-.031.03-18.095 17.06-.168.16v-7.942l.026-.029L42.246.932l.172-.187.002.254.063 10.257Z"/><path fill="url(#d)" stroke="#FFD8A6" stroke-width=".2" d="M42.483 22.63v.046l-.034.03L24.354 38.3l-.165.142v-9.05l.03-.03 18.032-17.06.168-.159v.23l.064 10.257Z"/><circle cx="23.544" cy="21.68" r="16.294" fill="url(#e)" stroke="#FFF0C9" stroke-width=".5"/><circle cx="23.442" cy="21.68" r="13.442" fill="url(#f)"/><mask id="i" fill="#fff"><path fill-rule="evenodd" d="M17.076 39.194 19.17 37.1l4.136 4.136 4.123-4.123 2.107 2.107 3.076-3.076-3.076-3.076-2.081 2.081L23.306 31l-4.136 4.136-2.094-2.094L14 36.118l3.076 3.076Z" clip-rule="evenodd"/></mask><path fill="url(#g)" fill-rule="evenodd" d="M17.076 39.194 19.17 37.1l4.136 4.136 4.123-4.123 2.107 2.107 3.076-3.076-3.076-3.076-2.081 2.081L23.306 31l-4.136 4.136-2.094-2.094L14 36.118l3.076 3.076Z" clip-rule="evenodd"/><path fill="url(#h)" d="m19.17 37.1.141-.141-.14-.142-.142.142.141.141Zm-2.094 2.094-.***************.142-.141-.142-.142Zm6.23 2.042-.***************.142-.142-.142-.14Zm4.123-4.123.142-.141-.142-.142-.***************Zm2.107 2.107-.**************.141-.14-.141-.142Zm3.076-*************.142-.142-.142-.141-.141.141Zm-3.076-3.076.141-.141-.141-.142-.***************Zm-2.081 2.081-.***************.141-.142-.141-.141ZM23.306 31l.142-.141-.142-.142-.***************Zm-4.136 4.136-.***************.141-.141-.14-.142Zm-2.094-2.094.142-.141-.142-.142-.***************ZM14 36.118l-.141-.141-.***************.141-.142Zm5.029.84-2.094 2.095.283.283 2.093-2.094-.282-.283Zm4.419 4.137-4.137-4.136-.282.282 4.136 4.136.283-.282Zm3.84-4.123-4.123 4.123.283.282 4.122-4.122-.282-.283Zm2.39 2.107-2.108-2.107-.282.283 2.107 2.106.282-.282Zm2.793-3.076-3.076 3.076.282.282 3.076-3.075-.282-.283Zm-3.076-2.794 3.076 3.076.282-.282-3.076-3.076-.282.282Zm-1.799 2.081 2.081-2.08-.282-.283-2.081 2.08.282.283Zm-4.431-4.148 4.149 4.148.282-.282-4.148-4.15-.283.284Zm-3.854 4.136 4.137-4.136-.283-.283-4.136 4.136.282.283Zm-2.376-2.094 2.094 2.094.282-.283-2.093-2.094-.283.282Zm-2.793 3.076 3.076-3.076-.283-.283-3.076 3.076.283.283Zm3.076 2.793-3.076-3.076-.283.283 3.076 3.076.283-.283Z" mask="url(#i)"/><defs><linearGradient id="a" x1="2.58" x2="25.254" y1="3.58" y2="26.254" gradientUnits="userSpaceOnUse"><stop stop-color="#F1D293"/><stop offset="1" stop-color="#986825"/></linearGradient><linearGradient id="b" x1="2.58" x2="25.254" y1="14.954" y2="37.628" gradientUnits="userSpaceOnUse"><stop stop-color="#F1D293"/><stop offset="1" stop-color="#986825"/></linearGradient><linearGradient id="c" x1="44.9" x2="22.226" y1="3.58" y2="26.254" gradientUnits="userSpaceOnUse"><stop stop-color="#F1D293"/><stop offset="1" stop-color="#986825"/></linearGradient><linearGradient id="d" x1="44.9" x2="22.226" y1="14.954" y2="37.628" gradientUnits="userSpaceOnUse"><stop stop-color="#F1D293"/><stop offset="1" stop-color="#986825"/></linearGradient><linearGradient id="e" x1="32.761" x2="15.745" y1="36.806" y2="8.445" gradientUnits="userSpaceOnUse"><stop stop-color="#C69567"/><stop offset="1" stop-color="#E1C988"/></linearGradient><linearGradient id="f" x1="14.172" x2="31.322" y1="12.873" y2="35.122" gradientUnits="userSpaceOnUse"><stop stop-color="#734426"/><stop offset="1" stop-color="#DB8B58"/></linearGradient><linearGradient id="h" x1="27.998" x2="31.074" y1="34.606" y2="37.682" gradientUnits="userSpaceOnUse"><stop stop-color="#FFD5A4"/><stop offset="1" stop-color="#D67E18"/></linearGradient><radialGradient id="g" cx="0" cy="0" r="1" gradientTransform="translate(23.875 36.17) scale(8.27194)" gradientUnits="userSpaceOnUse"><stop stop-color="#FFDCB1"/><stop offset="1" stop-color="#CFA572"/></radialGradient></defs></svg>',In='<svg xmlns="http://www.w3.org/2000/svg" width="50" height="39" fill="none"><path fill="url(#a)" stroke="url(#b)" stroke-width=".2" d="m14.034 35.652.05.06.071-.036 22.94-11.738.23-.118-.25-.067-7.167-1.92-.03-.009-.03.011-20.883 7.649-.134.049.093.11 5.11 6.009Z"/><path fill="url(#c)" stroke="url(#d)" stroke-width=".2" d="m6.171 14.804.008.054.05.023 24 10.92.16.073-.02-.175-.75-6.71-.005-.052-.045-.025-24.5-13.5-.174-.096.026.197 1.25 9.29Z"/><path fill="url(#e)" stroke="url(#f)" stroke-width=".2" d="m3.9 18.511.007.058.054.023 23.743 10.012.239.101-.11-.235-3.14-6.722-.014-.03-.028-.016L3.11 9.912l-.168-.092.021.191.936 8.5Z"/><path fill="url(#g)" stroke="url(#h)" stroke-width=".2" d="m10.034 33.574.051.06.07-.036 22.94-11.737.23-.119-.25-.067-7.167-1.92-.03-.008-.03.01-20.882 7.649-.135.05.093.108 5.11 6.01Z"/><path fill="url(#i)" stroke="url(#j)" stroke-width=".2" d="m14.034 35.652.05.06.071-.036 22.94-11.738.23-.118-.25-.067-7.167-1.92-.03-.009-.03.011-20.883 7.649-.134.049.093.11 5.11 6.009Z"/><path fill="url(#k)" stroke="url(#l)" stroke-width=".2" d="m6.171 14.804.008.054.05.023 24 10.92.16.073-.02-.175-.75-6.71-.005-.052-.045-.025-24.5-13.5-.174-.096.026.197 1.25 9.29Z"/><path fill="url(#m)" stroke="url(#n)" stroke-width=".2" d="m3.9 18.511.007.058.054.023 23.743 10.012.239.101-.11-.235-3.14-6.722-.014-.03-.028-.016L3.11 9.912l-.168-.092.021.191.936 8.5Z"/><path fill="url(#o)" stroke="url(#p)" stroke-width=".2" d="m10.034 33.574.051.06.07-.036 22.94-11.737.23-.119-.25-.067-7.167-1.92-.03-.008-.03.01-20.882 7.649-.135.05.093.108 5.11 6.01Z"/><path fill="url(#q)" stroke="url(#r)" stroke-width=".2" d="m35.015 35.652-.051.06-.07-.036-22.94-11.738-.23-.118.25-.067 7.166-1.92.031-.009.03.011 20.882 7.649.135.049-.093.11-5.11 6.009Z"/><path fill="url(#s)" stroke="url(#t)" stroke-width=".2" d="m42.878 14.804-.008.054-.05.023-24 10.92-.16.073.019-.175.75-6.71.006-.052.045-.025 24.5-13.5.174-.096-.027.197-1.25 9.29Z"/><path fill="url(#u)" stroke="url(#v)" stroke-width=".2" d="m45.148 18.511-.006.058-.054.023-23.743 10.012-.24.101.11-.235 3.14-6.722.014-.03.029-.016 21.539-11.79.168-.092-.02.191-.937 8.5Z"/><path fill="url(#w)" stroke="url(#x)" stroke-width=".2" d="m39.015 33.574-.051.06-.07-.036-22.94-11.737-.231-.119.25-.067 7.167-1.92.03-.008.03.01 20.883 7.649.135.05-.093.108-5.11 6.01Z"/><circle cx="25" cy="19.5" r="15.75" fill="url(#y)" stroke="url(#z)" stroke-width=".5"/><path fill="url(#A)" stroke="url(#B)" stroke-width=".3" d="M38.57 19.635c0 7.355-6.134 13.325-13.71 13.325-7.576 0-13.71-5.97-13.71-13.325 0-7.355 6.134-13.325 13.71-13.325 7.576 0 13.71 5.97 13.71 13.325Z"/><path fill="url(#C)" d="m16 5.45 5.05-2.95L26 5.45 21.05 8.5 16 5.45Z"/><path fill="url(#D)" d="m24 5.45 5.051-2.95 4.95 2.95-4.95 3.05-5.05-3.05Z"/><path fill="url(#E)" d="M20 5.45h7v7h-7z" transform="rotate(-45 20 5.45)"/><path fill="url(#F)" stroke="#C4D8F1" stroke-width=".2" d="m21.129 33.55 3.937-4.889 3.807 4.89-3.807 4.79-3.937-4.79Z"/><path fill="url(#G)" d="m25.044 30.5 2.622 3.03-2.622 2.97-2.71-2.97 2.71-3.03Z"/><defs><linearGradient id="a" x1="8.847" x2="35.704" y1="36.833" y2="21.327" gradientUnits="userSpaceOnUse"><stop stop-color="#E7E7EB"/><stop offset="1" stop-color="#989AA0"/></linearGradient><linearGradient id="b" x1="8.847" x2="35.704" y1="36.833" y2="21.327" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="c" x1="7.766" x2="29.694" y1="7.705" y2="29.634" gradientUnits="userSpaceOnUse"><stop stop-color="#E7E7EB"/><stop offset="1" stop-color="#989AA0"/></linearGradient><linearGradient id="d" x1="7.765" x2="29.694" y1="7.705" y2="29.634" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="e" x1="-.34" x2="28.809" y1="15.272" y2="25.86" gradientUnits="userSpaceOnUse"><stop stop-color="#E7E7EB"/><stop offset="1" stop-color="#989AA0"/></linearGradient><linearGradient id="f" x1="-.34" x2="28.809" y1="15.272" y2="25.86" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="g" x1="4.847" x2="31.704" y1="34.755" y2="19.249" gradientUnits="userSpaceOnUse"><stop stop-color="#E7E7EB"/><stop offset="1" stop-color="#989AA0"/></linearGradient><linearGradient id="h" x1="4.847" x2="31.704" y1="34.755" y2="19.249" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="i" x1="10.049" x2="23.549" y1="36.5" y2="28.5" gradientUnits="userSpaceOnUse"><stop stop-color="#DFE6EF"/><stop offset="1" stop-color="#7389CB"/></linearGradient><linearGradient id="j" x1="8.847" x2="35.704" y1="36.833" y2="21.327" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="k" x1="7.766" x2="16.549" y1="7.705" y2="13" gradientUnits="userSpaceOnUse"><stop stop-color="#DFE6EF"/><stop offset="1" stop-color="#8992AC"/></linearGradient><linearGradient id="l" x1="7.765" x2="29.694" y1="7.705" y2="29.634" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="m" x1="2.049" x2="13.549" y1="13" y2="19" gradientUnits="userSpaceOnUse"><stop stop-color="#DFE6EF"/><stop offset="1" stop-color="#959FBE"/></linearGradient><linearGradient id="n" x1="-.34" x2="28.809" y1="15.272" y2="25.86" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="o" x1="4.847" x2="18.55" y1="34.755" y2="27" gradientUnits="userSpaceOnUse"><stop stop-color="#DFE6EF"/><stop offset="1" stop-color="#8A97C0"/></linearGradient><linearGradient id="p" x1="4.847" x2="31.704" y1="34.755" y2="19.249" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="q" x1="40.202" x2="22.548" y1="36.833" y2="24.5" gradientUnits="userSpaceOnUse"><stop stop-color="#DFE6EF"/><stop offset="1" stop-color="#6D82BE"/></linearGradient><linearGradient id="r" x1="40.202" x2="13.345" y1="36.833" y2="21.327" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="s" x1="43.049" x2="28.549" y1="8.5" y2="14" gradientUnits="userSpaceOnUse"><stop stop-color="#DFE6EF"/><stop offset="1" stop-color="#889ECF"/></linearGradient><linearGradient id="t" x1="41.283" x2="19.354" y1="7.705" y2="29.634" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="u" x1="49.389" x2="20.24" y1="15.272" y2="25.86" gradientUnits="userSpaceOnUse"><stop offset=".002" stop-color="#E0E6F0"/><stop offset=".828" stop-color="#687BB3"/></linearGradient><linearGradient id="v" x1="49.389" x2="20.24" y1="15.272" y2="25.86" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="w" x1="44.202" x2="25.548" y1="34.755" y2="25" gradientUnits="userSpaceOnUse"><stop stop-color="#DFE6EF"/><stop offset="1" stop-color="#818FB8"/></linearGradient><linearGradient id="x" x1="44.202" x2="17.345" y1="34.755" y2="19.249" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="y" x1="33.914" x2="17.457" y1="34.128" y2="6.7" gradientUnits="userSpaceOnUse"><stop stop-color="#A9C1DD"/><stop offset="1" stop-color="#E7E7EB"/></linearGradient><linearGradient id="z" x1="15" x2="36.5" y1="6" y2="33.5" gradientUnits="userSpaceOnUse"><stop stop-color="#FAFAFA"/><stop offset="1" stop-color="#7C95BB" stop-opacity="0"/></linearGradient><linearGradient id="A" x1="15.301" x2="32.364" y1="10.807" y2="33.575" gradientUnits="userSpaceOnUse"><stop stop-color="#6873A8"/><stop offset="1" stop-color="#B5D0EF"/></linearGradient><linearGradient id="B" x1="15.828" x2="32.822" y1="9.27" y2="30.987" gradientUnits="userSpaceOnUse"><stop stop-color="#C9D4DF"/><stop offset="1" stop-color="#E8F4FF" stop-opacity=".33"/></linearGradient><linearGradient id="F" x1="23.029" x2="26.991" y1="31" y2="36.03" gradientUnits="userSpaceOnUse"><stop stop-color="#F2F2FF"/><stop offset="1" stop-color="#B9C9DD"/></linearGradient><linearGradient id="G" x1="26.528" x2="23.007" y1="35" y2="32.856" gradientUnits="userSpaceOnUse"><stop stop-color="#D9E8F2"/><stop offset="1" stop-color="#A0B1DE"/></linearGradient><radialGradient id="C" cx="0" cy="0" r="1" gradientTransform="matrix(-4.50003 0 0 -4.4998 20.5 5.5)" gradientUnits="userSpaceOnUse"><stop stop-color="#F8FBFF"/><stop offset=".719" stop-color="#6586B9"/></radialGradient><radialGradient id="D" cx="0" cy="0" r="1" gradientTransform="matrix(6.00004 0 0 5.99973 28.5 5.5)" gradientUnits="userSpaceOnUse"><stop offset=".208" stop-color="#6586B9"/><stop offset=".969" stop-color="#DFECFF"/></radialGradient><radialGradient id="E" cx="0" cy="0" r="1" gradientTransform="rotate(90 7.275 16.225) scale(3.50002)" gradientUnits="userSpaceOnUse"><stop stop-color="#E7F2FF"/><stop offset="1" stop-color="#ABC4E1"/></radialGradient></defs></svg>',Pn='<svg xmlns="http://www.w3.org/2000/svg" width="49" height="42" fill="none"><path fill="url(#a)" stroke="url(#b)" stroke-width=".2" d="m14.034 35.651.05.06.071-.035 22.94-11.738.23-.118-.25-.067-7.167-1.92-.03-.009-.03.01-20.883 7.65-.134.049.093.109 5.11 6.01Z"/><path fill="url(#c)" stroke="url(#d)" stroke-width=".2" d="m6.171 14.804.007.054.05.023 24 10.92.161.073-.02-.175-.75-6.71-.005-.052-.045-.025-24.5-13.5-.174-.096.026.197 1.25 9.29Z"/><path fill="url(#e)" stroke="url(#f)" stroke-width=".2" d="m3.9 18.51.007.06.054.022 23.743 10.012.239.101-.11-.235-3.14-6.722-.014-.03-.028-.016L3.11 9.912l-.168-.092.021.191.936 8.5Z"/><path fill="url(#g)" stroke="url(#h)" stroke-width=".2" d="m10.034 33.574.051.06.07-.036 22.94-11.737.23-.119-.25-.067-7.167-1.92-.03-.008-.03.01-20.883 7.649-.134.05.093.108 5.11 6.01Z"/><path fill="url(#i)" stroke="url(#j)" stroke-width=".2" d="m34.467 35.651-.052.06-.07-.035-22.939-11.738-.231-.118.25-.067 7.167-1.92.03-.009.03.01 20.883 7.65.135.049-.093.109-5.11 6.01Z"/><path fill="url(#k)" stroke="url(#l)" stroke-width=".2" d="m42.33 14.804-.008.054-.05.023-24 10.92-.161.073.02-.175.75-6.71.005-.052.046-.025 24.5-13.5.173-.096-.026.197-1.25 9.29Z"/><path fill="url(#m)" stroke="url(#n)" stroke-width=".2" d="m44.6 18.51-.007.06-.054.022-23.742 10.012-.24.101.11-.235 3.14-6.722.014-.03.028-.016 21.54-11.79.168-.092-.021.191-.936 8.5Z"/><path fill="url(#o)" stroke="url(#p)" stroke-width=".2" d="m38.467 33.574-.052.06-.07-.036-22.939-11.737-.231-.119.25-.067 7.167-1.92.03-.008.03.01 20.883 7.649.135.05-.093.108-5.11 6.01Z"/><path fill="url(#q)" stroke="#FFFAE7" stroke-width=".2" d="m34.656 1.074.048.087-.048-.087-6.9 3.763a.1.1 0 0 1-.121-.02L24.22 1.093a.3.3 0 0 0-.442 0l-3.414 3.724a.1.1 0 0 1-.121.02l-6.9-3.763a.3.3 0 0 0-.444.263V12.8a.3.3 0 0 0 .3.3h21.6a.3.3 0 0 0 .3-.3V1.337a.3.3 0 0 0-.444-.263Z"/><path fill="url(#r)" stroke="url(#s)" stroke-width=".5" d="M39.75 21.5c0 8.698-7.051 15.75-15.75 15.75-8.698 0-15.75-7.052-15.75-15.75 0-8.7 7.052-15.75 15.75-15.75 8.699 0 15.75 7.051 15.75 15.75Z"/><circle cx="24" cy="21.5" r="13" fill="url(#t)"/><circle cx="24" cy="21.5" r="12.85" stroke="url(#u)" stroke-opacity=".6" stroke-width=".3"/><mask id="x" fill="#fff"><path fill-rule="evenodd" d="m42 31-13 .067-2.261 2.683h-5.478l-2.26-2.683L6 31l4.5 6h9.301l4.2 5 4.198-5H37.5l4.5-6Z" clip-rule="evenodd"/></mask><path fill="url(#v)" fill-rule="evenodd" d="m42 31-13 .067-2.261 2.683h-5.478l-2.26-2.683L6 31l4.5 6h9.301l4.2 5 4.198-5H37.5l4.5-6Z" clip-rule="evenodd"/><path fill="url(#w)" d="M29 31.067v-.2h-.093l-.06.07.153.13ZM42 31l.16.12.242-.322-.403.002.001.2Zm-15.26 2.75v.2h.093l.06-.071-.153-.13Zm-5.478 0-.153.129.06.07h.093v-.2Zm-2.26-2.683.152-.13-.06-.07h-.092v.2ZM6 31l.001-.2-.403-.002.242.322L6 31Zm4.5 6-.16.12.06.08h.1V37Zm9.301 0 .154-.129-.06-.071H19.8v.2Zm4.2 5-.***************.153-.183L24 42Zm4.198-5v-.2h-.093l-.06.071.153.129Zm9.301 0v.2h.1l.06-.08-.16-.12Zm-8.499-5.733 13-.067-.002-.4-13 .066.002.4Zm-2.11 2.612 2.262-2.684-.306-.257-2.261 2.683.306.258Zm-5.63.07h5.478v-.4h-5.478v.4Zm.153-.328-2.261-2.683-.306.258 2.261 2.683.306-.258Zm-2.413-2.754-13-.067-.002.4 13 .067.002-.4ZM5.84 31.12l4.5 6 .32-.24-4.5-6-.32.24Zm4.66 6.08h9.301v-.4H10.5v.4Zm13.653 4.671-4.198-5-.307.258 4.199 5 .306-.258Zm3.893-5-4.199 5 .306.257 4.2-5-.307-.257ZM37.5 36.8h-9.3v.4h9.3v-.4Zm4.34-5.92-4.5 6 .32.24 4.5-6-.32-.24Z" mask="url(#x)"/><path fill="url(#y)" stroke="url(#z)" stroke-width=".2" d="m24.146 28.938-.078-.1-.079.1-4.067 5.05-.05.063.05.063 4.068 4.95.078.095.077-.097 3.933-4.95.05-.06-.049-.063-3.933-5.05Z"/><path fill="url(#A)" stroke="url(#B)" stroke-width=".2" d="m21.468 34.03 2.576-2.879 2.49 2.878-2.49 2.821-2.576-2.82Z"/><path fill="url(#C)" stroke="url(#D)" stroke-width=".2" d="m31.522 10.398-4.966 2.738-.028-5.622 4.834-2.702.16 5.586Z"/><path fill="url(#E)" stroke="url(#F)" stroke-width=".2" d="m16.576 10.228 4.967 2.738.028-5.621-4.834-2.702-.16 5.585Z"/><path fill="url(#G)" stroke="url(#H)" stroke-width=".2" d="M19.142 8.95h6.8v6.8h-6.8z" transform="rotate(-45 19.142 8.95)"/><defs><linearGradient id="a" x1="8.846" x2="35.704" y1="36.833" y2="21.327" gradientUnits="userSpaceOnUse"><stop stop-color="#FEE27D"/><stop offset="1" stop-color="#A7830D"/></linearGradient><linearGradient id="b" x1="8.846" x2="35.704" y1="36.833" y2="21.327" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="c" x1="7.765" x2="16.5" y1="7.705" y2="14" gradientUnits="userSpaceOnUse"><stop stop-color="#FFF792"/><stop offset="1" stop-color="#FF8A00"/></linearGradient><linearGradient id="d" x1="7.765" x2="29.694" y1="7.705" y2="29.634" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="e" x1="2.5" x2="15.5" y1="11" y2="19" gradientUnits="userSpaceOnUse"><stop stop-color="#FFF895"/><stop offset="1" stop-color="#FF8A00"/></linearGradient><linearGradient id="f" x1="-.34" x2="28.808" y1="15.272" y2="25.86" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="g" x1="6.5" x2="11.5" y1="29.5" y2="28" gradientUnits="userSpaceOnUse"><stop stop-color="#FFEF6B"/><stop offset="1" stop-color="#FF8A00"/></linearGradient><linearGradient id="h" x1="4.847" x2="31.704" y1="34.755" y2="19.249" gradientUnits="userSpaceOnUse"><stop stop-color="#FFC700"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="i" x1="39.654" x2="12.797" y1="36.833" y2="21.327" gradientUnits="userSpaceOnUse"><stop stop-color="#FEE27D"/><stop offset="1" stop-color="#A7830D"/></linearGradient><linearGradient id="j" x1="39.654" x2="12.796" y1="36.833" y2="21.327" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="k" x1="40.735" x2="32" y1="7.705" y2="14" gradientUnits="userSpaceOnUse"><stop stop-color="#F9EF76"/><stop offset="1" stop-color="#FF8A00"/></linearGradient><linearGradient id="l" x1="40.735" x2="18.806" y1="7.705" y2="29.634" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="m" x1="46" x2="28.5" y1="11" y2="21.5" gradientUnits="userSpaceOnUse"><stop stop-color="#FBF279"/><stop offset="1" stop-color="#FF8A00"/></linearGradient><linearGradient id="n" x1="48.84" x2="19.692" y1="15.272" y2="25.86" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="o" x1="42" x2="36.001" y1="29.5" y2="27" gradientUnits="userSpaceOnUse"><stop stop-color="#FFF59F"/><stop offset="1" stop-color="#FF8A00"/></linearGradient><linearGradient id="p" x1="43.654" x2="16.796" y1="34.755" y2="19.249" gradientUnits="userSpaceOnUse"><stop stop-color="#FFC700"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="q" x1="14.571" x2="26.16" y1=".5" y2="18.786" gradientUnits="userSpaceOnUse"><stop stop-color="#FDF32B"/><stop offset="1" stop-color="#FFBA69"/></linearGradient><linearGradient id="r" x1="27" x2="16.457" y1="36.5" y2="8.7" gradientUnits="userSpaceOnUse"><stop stop-color="#ED7200"/><stop offset="1" stop-color="#FFF97D"/></linearGradient><linearGradient id="s" x1="14" x2="35.5" y1="8" y2="35.5" gradientUnits="userSpaceOnUse"><stop stop-color="#FFF8DE"/><stop offset="1" stop-color="#FFF8D1" stop-opacity="0"/></linearGradient><linearGradient id="t" x1="17.5" x2="31.621" y1="10.499" y2="34.5" gradientUnits="userSpaceOnUse"><stop stop-color="#FF9901"/><stop offset="1" stop-color="#FFF2A0"/></linearGradient><linearGradient id="u" x1="16" x2="34.5" y1="11.499" y2="30" gradientUnits="userSpaceOnUse"><stop stop-color="#FF965B"/><stop offset="1" stop-color="#C07D19"/></linearGradient><linearGradient id="v" x1="10" x2="30.5" y1="31.5" y2="39.5" gradientUnits="userSpaceOnUse"><stop stop-color="#FFF850"/><stop offset="1" stop-color="#FE9923"/></linearGradient><linearGradient id="w" x1="19.5" x2="28" y1="31" y2="44.5" gradientUnits="userSpaceOnUse"><stop stop-color="#FFE881"/><stop offset="1" stop-color="#FFF8D9"/></linearGradient><linearGradient id="z" x1="27.5" x2="21" y1="37" y2="32" gradientUnits="userSpaceOnUse"><stop stop-color="#E68E4E" stop-opacity="0"/><stop offset="1" stop-color="#FFBB56"/></linearGradient><linearGradient id="A" x1="25.5" x2="21.979" y1="35.5" y2="33.356" gradientUnits="userSpaceOnUse"><stop stop-color="#FFC977"/><stop offset="1" stop-color="#FB782E"/></linearGradient><linearGradient id="B" x1="22.5" x2="25.5" y1="32.5" y2="35.5" gradientUnits="userSpaceOnUse"><stop stop-color="#FFE7A9"/><stop offset="1" stop-color="#FFC123"/></linearGradient><linearGradient id="D" x1="26.5" x2="31.599" y1="7" y2="10.5" gradientUnits="userSpaceOnUse"><stop stop-color="#FFE99B"/><stop offset="1" stop-color="#FFD466"/></linearGradient><linearGradient id="F" x1="21.5" x2="17" y1="13" y2="5" gradientUnits="userSpaceOnUse"><stop stop-color="#FFC46A"/><stop offset="1" stop-color="#FCFFED"/></linearGradient><linearGradient id="H" x1="22.5" x2="22.5" y1="8.95" y2="15.95" gradientUnits="userSpaceOnUse"><stop stop-color="#FBFFCE"/><stop offset="1" stop-color="#FFCD4E"/></linearGradient><radialGradient id="y" cx="0" cy="0" r="1" gradientTransform="matrix(-3.00001 -4.50003 7.5787 -5.05245 24 33.5)" gradientUnits="userSpaceOnUse"><stop stop-color="#FFFCC5"/><stop offset=".849" stop-color="#FFBA34"/></radialGradient><radialGradient id="C" cx="0" cy="0" r="1" gradientTransform="matrix(3.00002 -5.19619 5.19592 2.99987 28.75 9.433)" gradientUnits="userSpaceOnUse"><stop offset=".208" stop-color="#FFA800"/><stop offset=".969" stop-color="#FFF7DA"/></radialGradient><radialGradient id="E" cx="0" cy="0" r="1" gradientTransform="matrix(-3.00002 -5.19619 5.19592 -2.99986 19.348 9.263)" gradientUnits="userSpaceOnUse"><stop offset=".208" stop-color="#FFA800"/><stop offset=".969" stop-color="#FFF7DA"/></radialGradient><radialGradient id="G" cx="0" cy="0" r="1" gradientTransform="matrix(2.12096 9.9703 -7.9942 1.7006 22.5 12.45)" gradientUnits="userSpaceOnUse"><stop offset=".536" stop-color="#FFE791"/><stop offset="1" stop-color="#FF5621"/></radialGradient></defs></svg>',sr=typeof window<"u"?window.btoa(On):Buffer.from(On).toString("base64"),ir=typeof window<"u"?window.btoa(In):Buffer.from(In).toString("base64"),ar=typeof window<"u"?window.btoa(Pn):Buffer.from(Pn).toString("base64"),b2=ne(({rank:e=0},t)=>{if(e===0)return null;let n,o;return e<=5&&(n={backgroundImage:`url('data:image/svg+xml;base64,${sr}')`},o={color:"#fff7ee",WebkitTextStroke:"0.5px #ae6d43"}),e>5&&e<=10&&(n={backgroundImage:`url('data:image/svg+xml;base64,${ir}')`},o={color:"#f3f8ff",WebkitTextStroke:"0.5px #6974a8"}),e>10&&(n={backgroundImage:`url('data:image/svg+xml;base64,${ar}')`},o={color:"#fffbf1",WebkitTextStroke:"0.5px #f3a126"}),r("div",{ref:t,className:H("flex items-center justify-center","bg-contain bg-center bg-no-repeat","h-10 w-12"),style:n,children:r("div",{className:"relative",children:r("span",{className:H("absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2","text-xs font-normal not-italic text-transparent"),style:{fontFamily:"Impact, sans-serif",...o},children:["R",e]})})})}),ft=Math.min,Ve=Math.max,Zt=Math.round,Dt=Math.floor,Se=e=>({x:e,y:e}),lr={left:"right",right:"left",bottom:"top",top:"bottom"},cr={start:"end",end:"start"};function x0(e,t,n){return Ve(e,ft(t,n))}function Et(e,t){return typeof e=="function"?e(t):e}function je(e){return e.split("-")[0]}function Mt(e){return e.split("-")[1]}function A2(e){return e==="x"?"y":"x"}function R0(e){return e==="y"?"height":"width"}const dr=new Set(["top","bottom"]);function Pe(e){return dr.has(je(e))?"y":"x"}function O0(e){return A2(Pe(e))}function pr(e,t,n){n===void 0&&(n=!1);const o=Mt(e),s=O0(e),i=R0(s);let a=s==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=Ht(a)),[a,Ht(a)]}function fr(e){const t=Ht(e);return[b0(e),t,b0(t)]}function b0(e){return e.replace(/start|end/g,t=>cr[t])}const Tn=["left","right"],Bn=["right","left"],ur=["top","bottom"],mr=["bottom","top"];function hr(e,t,n){switch(e){case"top":case"bottom":return n?t?Bn:Tn:t?Tn:Bn;case"left":case"right":return t?ur:mr;default:return[]}}function gr(e,t,n,o){const s=Mt(e);let i=hr(je(e),n==="start",o);return s&&(i=i.map(a=>a+"-"+s),t&&(i=i.concat(i.map(b0)))),i}function Ht(e){return e.replace(/left|right|bottom|top/g,t=>lr[t])}function vr(e){return{top:0,right:0,bottom:0,left:0,...e}}function _2(e){return typeof e!="number"?vr(e):{top:e,right:e,bottom:e,left:e}}function zt(e){const{x:t,y:n,width:o,height:s}=e;return{width:o,height:s,top:n,left:t,right:t+o,bottom:n+s,x:t,y:n}}function Gn(e,t,n){let{reference:o,floating:s}=e;const i=Pe(t),a=O0(t),l=R0(a),c=je(t),d=i==="y",f=o.x+o.width/2-s.width/2,p=o.y+o.height/2-s.height/2,u=o[l]/2-s[l]/2;let h;switch(c){case"top":h={x:f,y:o.y-s.height};break;case"bottom":h={x:f,y:o.y+o.height};break;case"right":h={x:o.x+o.width,y:p};break;case"left":h={x:o.x-s.width,y:p};break;default:h={x:o.x,y:o.y}}switch(Mt(t)){case"start":h[a]-=u*(n&&d?-1:1);break;case"end":h[a]+=u*(n&&d?-1:1);break}return h}const wr=async(e,t,n)=>{const{placement:o="bottom",strategy:s="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),c=await(a.isRTL==null?void 0:a.isRTL(t));let d=await a.getElementRects({reference:e,floating:t,strategy:s}),{x:f,y:p}=Gn(d,o,c),u=o,h={},w=0;for(let m=0;m<l.length;m++){const{name:y,fn:g}=l[m],{x:C,y:x,data:N,reset:F}=await g({x:f,y:p,initialPlacement:o,placement:u,strategy:s,middlewareData:h,rects:d,platform:a,elements:{reference:e,floating:t}});f=C??f,p=x??p,h={...h,[y]:{...h[y],...N}},F&&w<=50&&(w++,typeof F=="object"&&(F.placement&&(u=F.placement),F.rects&&(d=F.rects===!0?await a.getElementRects({reference:e,floating:t,strategy:s}):F.rects),{x:f,y:p}=Gn(d,u,c)),m=-1)}return{x:f,y:p,placement:u,strategy:s,middlewareData:h}};async function C2(e,t){var n;t===void 0&&(t={});const{x:o,y:s,platform:i,rects:a,elements:l,strategy:c}=e,{boundary:d="clippingAncestors",rootBoundary:f="viewport",elementContext:p="floating",altBoundary:u=!1,padding:h=0}=Et(t,e),w=_2(h),y=l[u?p==="floating"?"reference":"floating":p],g=zt(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(y)))==null||n?y:y.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:d,rootBoundary:f,strategy:c})),C=p==="floating"?{x:o,y:s,width:a.floating.width,height:a.floating.height}:a.reference,x=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),N=await(i.isElement==null?void 0:i.isElement(x))?await(i.getScale==null?void 0:i.getScale(x))||{x:1,y:1}:{x:1,y:1},F=zt(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:C,offsetParent:x,strategy:c}):C);return{top:(g.top-F.top+w.top)/N.y,bottom:(F.bottom-g.bottom+w.bottom)/N.y,left:(g.left-F.left+w.left)/N.x,right:(F.right-g.right+w.right)/N.x}}const yr=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:s,rects:i,platform:a,elements:l,middlewareData:c}=t,{element:d,padding:f=0}=Et(e,t)||{};if(d==null)return{};const p=_2(f),u={x:n,y:o},h=O0(s),w=R0(h),m=await a.getDimensions(d),y=h==="y",g=y?"top":"left",C=y?"bottom":"right",x=y?"clientHeight":"clientWidth",N=i.reference[w]+i.reference[h]-u[h]-i.floating[w],F=u[h]-i.reference[h],U=await(a.getOffsetParent==null?void 0:a.getOffsetParent(d));let R=U?U[x]:0;(!R||!await(a.isElement==null?void 0:a.isElement(U)))&&(R=l.floating[x]||i.floating[w]);const ce=N/2-F/2,re=R/2-m[w]/2-1,se=ft(p[g],re),be=ft(p[C],re),de=se,Ae=R-m[w]-be,T=R/2-m[w]/2+ce,oe=x0(de,T,Ae),X=!c.arrow&&Mt(s)!=null&&T!==oe&&i.reference[w]/2-(T<de?se:be)-m[w]/2<0,V=X?T<de?T-de:T-Ae:0;return{[h]:u[h]+V,data:{[h]:oe,centerOffset:T-oe-V,...X&&{alignmentOffset:V}},reset:X}}}),xr=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:s,middlewareData:i,rects:a,initialPlacement:l,platform:c,elements:d}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:u,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:m=!0,...y}=Et(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const g=je(s),C=Pe(l),x=je(l)===l,N=await(c.isRTL==null?void 0:c.isRTL(d.floating)),F=u||(x||!m?[Ht(l)]:fr(l)),U=w!=="none";!u&&U&&F.push(...gr(l,m,w,N));const R=[l,...F],ce=await C2(t,y),re=[];let se=((o=i.flip)==null?void 0:o.overflows)||[];if(f&&re.push(ce[g]),p){const T=pr(s,a,N);re.push(ce[T[0]],ce[T[1]])}if(se=[...se,{placement:s,overflows:re}],!re.every(T=>T<=0)){var be,de;const T=(((be=i.flip)==null?void 0:be.index)||0)+1,oe=R[T];if(oe&&(!(p==="alignment"?C!==Pe(oe):!1)||se.every(D=>D.overflows[0]>0&&Pe(D.placement)===C)))return{data:{index:T,overflows:se},reset:{placement:oe}};let X=(de=se.filter(V=>V.overflows[0]<=0).sort((V,D)=>V.overflows[1]-D.overflows[1])[0])==null?void 0:de.placement;if(!X)switch(h){case"bestFit":{var Ae;const V=(Ae=se.filter(D=>{if(U){const Q=Pe(D.placement);return Q===C||Q==="y"}return!0}).map(D=>[D.placement,D.overflows.filter(Q=>Q>0).reduce((Q,Fe)=>Q+Fe,0)]).sort((D,Q)=>D[1]-Q[1])[0])==null?void 0:Ae[0];V&&(X=V);break}case"initialPlacement":X=l;break}if(s!==X)return{reset:{placement:X}}}return{}}}},br=new Set(["left","top"]);async function Ar(e,t){const{placement:n,platform:o,elements:s}=e,i=await(o.isRTL==null?void 0:o.isRTL(s.floating)),a=je(n),l=Mt(n),c=Pe(n)==="y",d=br.has(a)?-1:1,f=i&&c?-1:1,p=Et(t,e);let{mainAxis:u,crossAxis:h,alignmentAxis:w}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return l&&typeof w=="number"&&(h=l==="end"?w*-1:w),c?{x:h*f,y:u*d}:{x:u*d,y:h*f}}const _r=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:s,y:i,placement:a,middlewareData:l}=t,c=await Ar(t,e);return a===((n=l.offset)==null?void 0:n.placement)&&(o=l.arrow)!=null&&o.alignmentOffset?{}:{x:s+c.x,y:i+c.y,data:{...c,placement:a}}}}},Cr=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:s}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:y=>{let{x:g,y:C}=y;return{x:g,y:C}}},...c}=Et(e,t),d={x:n,y:o},f=await C2(t,c),p=Pe(je(s)),u=A2(p);let h=d[u],w=d[p];if(i){const y=u==="y"?"top":"left",g=u==="y"?"bottom":"right",C=h+f[y],x=h-f[g];h=x0(C,h,x)}if(a){const y=p==="y"?"top":"left",g=p==="y"?"bottom":"right",C=w+f[y],x=w-f[g];w=x0(C,w,x)}const m=l.fn({...t,[u]:h,[p]:w});return{...m,data:{x:m.x-n,y:m.y-o,enabled:{[u]:i,[p]:a}}}}}};function jt(){return typeof window<"u"}function gt(e){return k2(e)?(e.nodeName||"").toLowerCase():"#document"}function le(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Le(e){var t;return(t=(k2(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function k2(e){return jt()?e instanceof Node||e instanceof le(e).Node:!1}function ye(e){return jt()?e instanceof Element||e instanceof le(e).Element:!1}function Ne(e){return jt()?e instanceof HTMLElement||e instanceof le(e).HTMLElement:!1}function Un(e){return!jt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof le(e).ShadowRoot}const kr=new Set(["inline","contents"]);function Rt(e){const{overflow:t,overflowX:n,overflowY:o,display:s}=xe(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!kr.has(s)}const Sr=new Set(["table","td","th"]);function Nr(e){return Sr.has(gt(e))}const Lr=[":popover-open",":modal"];function Wt(e){return Lr.some(t=>{try{return e.matches(t)}catch{return!1}})}const Fr=["transform","translate","scale","rotate","perspective"],Er=["transform","translate","scale","rotate","perspective","filter"],Mr=["paint","layout","strict","content"];function I0(e){const t=P0(),n=ye(e)?xe(e):e;return Fr.some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||Er.some(o=>(n.willChange||"").includes(o))||Mr.some(o=>(n.contain||"").includes(o))}function Rr(e){let t=Te(e);for(;Ne(t)&&!ut(t);){if(I0(t))return t;if(Wt(t))return null;t=Te(t)}return null}function P0(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const Or=new Set(["html","body","#document"]);function ut(e){return Or.has(gt(e))}function xe(e){return le(e).getComputedStyle(e)}function Yt(e){return ye(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Te(e){if(gt(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Un(e)&&e.host||Le(e);return Un(t)?t.host:t}function S2(e){const t=Te(e);return ut(t)?e.ownerDocument?e.ownerDocument.body:e.body:Ne(t)&&Rt(t)?t:S2(t)}function Lt(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const s=S2(e),i=s===((o=e.ownerDocument)==null?void 0:o.body),a=le(s);if(i){const l=A0(a);return t.concat(a,a.visualViewport||[],Rt(s)?s:[],l&&n?Lt(l):[])}return t.concat(s,Lt(s,[],n))}function A0(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function N2(e){const t=xe(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const s=Ne(e),i=s?e.offsetWidth:n,a=s?e.offsetHeight:o,l=Zt(n)!==i||Zt(o)!==a;return l&&(n=i,o=a),{width:n,height:o,$:l}}function T0(e){return ye(e)?e:e.contextElement}function ct(e){const t=T0(e);if(!Ne(t))return Se(1);const n=t.getBoundingClientRect(),{width:o,height:s,$:i}=N2(t);let a=(i?Zt(n.width):n.width)/o,l=(i?Zt(n.height):n.height)/s;return(!a||!Number.isFinite(a))&&(a=1),(!l||!Number.isFinite(l))&&(l=1),{x:a,y:l}}const Ir=Se(0);function L2(e){const t=le(e);return!P0()||!t.visualViewport?Ir:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Pr(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==le(e)?!1:t}function We(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const s=e.getBoundingClientRect(),i=T0(e);let a=Se(1);t&&(o?ye(o)&&(a=ct(o)):a=ct(e));const l=Pr(i,n,o)?L2(i):Se(0);let c=(s.left+l.x)/a.x,d=(s.top+l.y)/a.y,f=s.width/a.x,p=s.height/a.y;if(i){const u=le(i),h=o&&ye(o)?le(o):o;let w=u,m=A0(w);for(;m&&o&&h!==w;){const y=ct(m),g=m.getBoundingClientRect(),C=xe(m),x=g.left+(m.clientLeft+parseFloat(C.paddingLeft))*y.x,N=g.top+(m.clientTop+parseFloat(C.paddingTop))*y.y;c*=y.x,d*=y.y,f*=y.x,p*=y.y,c+=x,d+=N,w=le(m),m=A0(w)}}return zt({width:f,height:p,x:c,y:d})}function B0(e,t){const n=Yt(e).scrollLeft;return t?t.left+n:We(Le(e)).left+n}function F2(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=o.left+t.scrollLeft-(n?0:B0(e,o)),i=o.top+t.scrollTop;return{x:s,y:i}}function Tr(e){let{elements:t,rect:n,offsetParent:o,strategy:s}=e;const i=s==="fixed",a=Le(o),l=t?Wt(t.floating):!1;if(o===a||l&&i)return n;let c={scrollLeft:0,scrollTop:0},d=Se(1);const f=Se(0),p=Ne(o);if((p||!p&&!i)&&((gt(o)!=="body"||Rt(a))&&(c=Yt(o)),Ne(o))){const h=We(o);d=ct(o),f.x=h.x+o.clientLeft,f.y=h.y+o.clientTop}const u=a&&!p&&!i?F2(a,c,!0):Se(0);return{width:n.width*d.x,height:n.height*d.y,x:n.x*d.x-c.scrollLeft*d.x+f.x+u.x,y:n.y*d.y-c.scrollTop*d.y+f.y+u.y}}function Br(e){return Array.from(e.getClientRects())}function Gr(e){const t=Le(e),n=Yt(e),o=e.ownerDocument.body,s=Ve(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),i=Ve(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let a=-n.scrollLeft+B0(e);const l=-n.scrollTop;return xe(o).direction==="rtl"&&(a+=Ve(t.clientWidth,o.clientWidth)-s),{width:s,height:i,x:a,y:l}}function Ur(e,t){const n=le(e),o=Le(e),s=n.visualViewport;let i=o.clientWidth,a=o.clientHeight,l=0,c=0;if(s){i=s.width,a=s.height;const d=P0();(!d||d&&t==="fixed")&&(l=s.offsetLeft,c=s.offsetTop)}return{width:i,height:a,x:l,y:c}}const Dr=new Set(["absolute","fixed"]);function $r(e,t){const n=We(e,!0,t==="fixed"),o=n.top+e.clientTop,s=n.left+e.clientLeft,i=Ne(e)?ct(e):Se(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y,c=s*i.x,d=o*i.y;return{width:a,height:l,x:c,y:d}}function Dn(e,t,n){let o;if(t==="viewport")o=Ur(e,n);else if(t==="document")o=Gr(Le(e));else if(ye(t))o=$r(t,n);else{const s=L2(e);o={x:t.x-s.x,y:t.y-s.y,width:t.width,height:t.height}}return zt(o)}function E2(e,t){const n=Te(e);return n===t||!ye(n)||ut(n)?!1:xe(n).position==="fixed"||E2(n,t)}function Zr(e,t){const n=t.get(e);if(n)return n;let o=Lt(e,[],!1).filter(l=>ye(l)&&gt(l)!=="body"),s=null;const i=xe(e).position==="fixed";let a=i?Te(e):e;for(;ye(a)&&!ut(a);){const l=xe(a),c=I0(a);!c&&l.position==="fixed"&&(s=null),(i?!c&&!s:!c&&l.position==="static"&&!!s&&Dr.has(s.position)||Rt(a)&&!c&&E2(e,a))?o=o.filter(f=>f!==a):s=l,a=Te(a)}return t.set(e,o),o}function Hr(e){let{element:t,boundary:n,rootBoundary:o,strategy:s}=e;const a=[...n==="clippingAncestors"?Wt(t)?[]:Zr(t,this._c):[].concat(n),o],l=a[0],c=a.reduce((d,f)=>{const p=Dn(t,f,s);return d.top=Ve(p.top,d.top),d.right=ft(p.right,d.right),d.bottom=ft(p.bottom,d.bottom),d.left=Ve(p.left,d.left),d},Dn(t,l,s));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function zr(e){const{width:t,height:n}=N2(e);return{width:t,height:n}}function Vr(e,t,n){const o=Ne(t),s=Le(t),i=n==="fixed",a=We(e,!0,i,t);let l={scrollLeft:0,scrollTop:0};const c=Se(0);function d(){c.x=B0(s)}if(o||!o&&!i)if((gt(t)!=="body"||Rt(s))&&(l=Yt(t)),o){const h=We(t,!0,i,t);c.x=h.x+t.clientLeft,c.y=h.y+t.clientTop}else s&&d();i&&!o&&s&&d();const f=s&&!o&&!i?F2(s,l):Se(0),p=a.left+l.scrollLeft-c.x-f.x,u=a.top+l.scrollTop-c.y-f.y;return{x:p,y:u,width:a.width,height:a.height}}function e0(e){return xe(e).position==="static"}function $n(e,t){if(!Ne(e)||xe(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Le(e)===n&&(n=n.ownerDocument.body),n}function M2(e,t){const n=le(e);if(Wt(e))return n;if(!Ne(e)){let s=Te(e);for(;s&&!ut(s);){if(ye(s)&&!e0(s))return s;s=Te(s)}return n}let o=$n(e,t);for(;o&&Nr(o)&&e0(o);)o=$n(o,t);return o&&ut(o)&&e0(o)&&!I0(o)?n:o||Rr(e)||n}const jr=async function(e){const t=this.getOffsetParent||M2,n=this.getDimensions,o=await n(e.floating);return{reference:Vr(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function Wr(e){return xe(e).direction==="rtl"}const Yr={convertOffsetParentRelativeRectToViewportRelativeRect:Tr,getDocumentElement:Le,getClippingRect:Hr,getOffsetParent:M2,getElementRects:jr,getClientRects:Br,getDimensions:zr,getScale:ct,isElement:ye,isRTL:Wr};function R2(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Kr(e,t){let n=null,o;const s=Le(e);function i(){var l;clearTimeout(o),(l=n)==null||l.disconnect(),n=null}function a(l,c){l===void 0&&(l=!1),c===void 0&&(c=1),i();const d=e.getBoundingClientRect(),{left:f,top:p,width:u,height:h}=d;if(l||t(),!u||!h)return;const w=Dt(p),m=Dt(s.clientWidth-(f+u)),y=Dt(s.clientHeight-(p+h)),g=Dt(f),x={rootMargin:-w+"px "+-m+"px "+-y+"px "+-g+"px",threshold:Ve(0,ft(1,c))||1};let N=!0;function F(U){const R=U[0].intersectionRatio;if(R!==c){if(!N)return a();R?a(!1,R):o=setTimeout(()=>{a(!1,1e-7)},1e3)}R===1&&!R2(d,e.getBoundingClientRect())&&a(),N=!1}try{n=new IntersectionObserver(F,{...x,root:s.ownerDocument})}catch{n=new IntersectionObserver(F,x)}n.observe(e)}return a(!0),i}function qr(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:s=!0,ancestorResize:i=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:c=!1}=o,d=T0(e),f=s||i?[...d?Lt(d):[],...Lt(t)]:[];f.forEach(g=>{s&&g.addEventListener("scroll",n,{passive:!0}),i&&g.addEventListener("resize",n)});const p=d&&l?Kr(d,n):null;let u=-1,h=null;a&&(h=new ResizeObserver(g=>{let[C]=g;C&&C.target===d&&h&&(h.unobserve(t),cancelAnimationFrame(u),u=requestAnimationFrame(()=>{var x;(x=h)==null||x.observe(t)})),n()}),d&&!c&&h.observe(d),h.observe(t));let w,m=c?We(e):null;c&&y();function y(){const g=We(e);m&&!R2(m,g)&&n(),m=g,w=requestAnimationFrame(y)}return n(),()=>{var g;f.forEach(C=>{s&&C.removeEventListener("scroll",n),i&&C.removeEventListener("resize",n)}),p?.(),(g=h)==null||g.disconnect(),h=null,c&&cancelAnimationFrame(w)}}const Jr=_r,Xr=Cr,Qr=xr,es=yr,Zn=(e,t,n)=>{const o=new Map,s={platform:Yr,...n},i={...s.platform,_c:o};return wr(e,t,{...s,platform:i})};var t0={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var Hn;function ts(){return Hn||(Hn=1,function(e){(function(){var t={}.hasOwnProperty;function n(){for(var i="",a=0;a<arguments.length;a++){var l=arguments[a];l&&(i=s(i,o(l)))}return i}function o(i){if(typeof i=="string"||typeof i=="number")return i;if(typeof i!="object")return"";if(Array.isArray(i))return n.apply(null,i);if(i.toString!==Object.prototype.toString&&!i.toString.toString().includes("[native code]"))return i.toString();var a="";for(var l in i)t.call(i,l)&&i[l]&&(a=s(a,l));return a}function s(i,a){return a?i?i+" "+a:i+a:i}e.exports?(n.default=n,e.exports=n):window.classNames=n})()}(t0)),t0.exports}var ns=ts();const _0=Uo(ns);var zn={};const os="react-tooltip-core-styles",rs="react-tooltip-base-styles",Vn={core:!1,base:!1};function jn({css:e,id:t=rs,type:n="base",ref:o}){var s,i;if(!e||typeof document>"u"||Vn[n]||n==="core"&&typeof process<"u"&&(!((s=process==null?void 0:zn)===null||s===void 0)&&s.REACT_TOOLTIP_DISABLE_CORE_STYLES)||n!=="base"&&typeof process<"u"&&(!((i=process==null?void 0:zn)===null||i===void 0)&&i.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;n==="core"&&(t=os),o||(o={});const{insertAt:a}=o;if(document.getElementById(t))return;const l=document.head||document.getElementsByTagName("head")[0],c=document.createElement("style");c.id=t,c.type="text/css",a==="top"&&l.firstChild?l.insertBefore(c,l.firstChild):l.appendChild(c),c.styleSheet?c.styleSheet.cssText=e:c.appendChild(document.createTextNode(e)),Vn[n]=!0}const Wn=async({elementReference:e=null,tooltipReference:t=null,tooltipArrowReference:n=null,place:o="top",offset:s=10,strategy:i="absolute",middlewares:a=[Jr(Number(s)),Qr({fallbackAxisSideDirection:"start"}),Xr({padding:5})],border:l,arrowSize:c=8})=>{if(!e)return{tooltipStyles:{},tooltipArrowStyles:{},place:o};if(t===null)return{tooltipStyles:{},tooltipArrowStyles:{},place:o};const d=a;return n?(d.push(es({element:n,padding:5})),Zn(e,t,{placement:o,strategy:i,middleware:d}).then(({x:f,y:p,placement:u,middlewareData:h})=>{var w,m;const y={left:`${f}px`,top:`${p}px`,border:l},{x:g,y:C}=(w=h.arrow)!==null&&w!==void 0?w:{x:0,y:0},x=(m={top:"bottom",right:"left",bottom:"top",left:"right"}[u.split("-")[0]])!==null&&m!==void 0?m:"bottom",N=l&&{borderBottom:l,borderRight:l};let F=0;if(l){const U=`${l}`.match(/(\d+)px/);F=U?.[1]?Number(U[1]):1}return{tooltipStyles:y,tooltipArrowStyles:{left:g!=null?`${g}px`:"",top:C!=null?`${C}px`:"",right:"",bottom:"",...N,[x]:`-${c/2+F}px`},place:u}})):Zn(e,t,{placement:"bottom",strategy:i,middleware:d}).then(({x:f,y:p,placement:u})=>({tooltipStyles:{left:`${f}px`,top:`${p}px`},tooltipArrowStyles:{},place:u}))},Yn=(e,t)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(e,t),Kn=(e,t,n)=>{let o=null;const s=function(...i){const a=()=>{o=null};!o&&(e.apply(this,i),o=setTimeout(a,t))};return s.cancel=()=>{o&&(clearTimeout(o),o=null)},s},qn=e=>e!==null&&!Array.isArray(e)&&typeof e=="object",C0=(e,t)=>{if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((s,i)=>C0(s,t[i]));if(Array.isArray(e)!==Array.isArray(t))return!1;if(!qn(e)||!qn(t))return e===t;const n=Object.keys(e),o=Object.keys(t);return n.length===o.length&&n.every(s=>C0(e[s],t[s]))},ss=e=>{if(!(e instanceof HTMLElement||e instanceof SVGElement))return!1;const t=getComputedStyle(e);return["overflow","overflow-x","overflow-y"].some(n=>{const o=t.getPropertyValue(n);return o==="auto"||o==="scroll"})},Jn=e=>{if(!e)return null;let t=e.parentElement;for(;t;){if(ss(t))return t;t=t.parentElement}return document.scrollingElement||document.documentElement},is=typeof window<"u"?eo:_,ue=e=>{e.current&&(clearTimeout(e.current),e.current=null)},as="DEFAULT_TOOLTIP_ID",ls={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},cs=Q2({getTooltipData:()=>ls});function O2(e=as){return X2(cs).getTooltipData(e)}var it={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},n0={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};const ds=({forwardRef:e,id:t,className:n,classNameArrow:o,variant:s="dark",anchorId:i,anchorSelect:a,place:l="top",offset:c=10,events:d=["hover"],openOnClick:f=!1,positionStrategy:p="absolute",middlewares:u,wrapper:h,delayShow:w=0,delayHide:m=0,float:y=!1,hidden:g=!1,noArrow:C=!1,clickable:x=!1,closeOnEsc:N=!1,closeOnScroll:F=!1,closeOnResize:U=!1,openEvents:R,closeEvents:ce,globalCloseEvents:re,imperativeModeOnly:se,style:be,position:de,afterShow:Ae,afterHide:T,disableTooltip:oe,content:X,contentWrapperRef:V,isOpen:D,defaultIsOpen:Q=!1,setIsOpen:Fe,activeAnchor:j,setActiveAnchor:Ke,border:Ot,opacity:It,arrowColor:Pt,arrowSize:qe=8,role:Kt="tooltip"})=>{var vt;const ee=Z(null),Je=Z(null),ve=Z(null),Re=Z(null),wt=Z(null),[Oe,qt]=I({tooltipStyles:{},tooltipArrowStyles:{},place:l}),[ie,Tt]=I(!1),[Ue,De]=I(!1),[$,yt]=I(null),xt=Z(!1),bt=Z(null),{anchorRefs:At,setActiveAnchor:Bt}=O2(t),Xe=Z(!1),[Ie,_t]=I([]),$e=Z(!1),Qe=f||d.includes("click"),Jt=Qe||R?.click||R?.dblclick||R?.mousedown,et=R?{...R}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!R&&Qe&&Object.assign(et,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});const Ze=ce?{...ce}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!ce&&Qe&&Object.assign(Ze,{mouseleave:!1,blur:!1,mouseout:!1});const we=re?{...re}:{escape:N||!1,scroll:F||!1,resize:U||!1,clickOutsideAnchor:Jt||!1};se&&(Object.assign(et,{mouseover:!1,focus:!1,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(Ze,{mouseout:!1,blur:!1,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(we,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),is(()=>($e.current=!0,()=>{$e.current=!1}),[]);const z=v=>{$e.current&&(v&&De(!0),setTimeout(()=>{$e.current&&(Fe?.(v),D===void 0&&Tt(v))},10))};_(()=>{if(D===void 0)return()=>null;D&&De(!0);const v=setTimeout(()=>{Tt(D)},10);return()=>{clearTimeout(v)}},[D]),_(()=>{if(ie!==xt.current)if(ue(wt),xt.current=ie,ie)Ae?.();else{const v=(k=>{const L=k.match(/^([\d.]+)(ms|s)$/);if(!L)return 0;const[,W,q]=L;return Number(W)*(q==="ms"?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"));wt.current=setTimeout(()=>{De(!1),yt(null),T?.()},v+25)}},[ie]);const Gt=v=>{qt(k=>C0(k,v)?k:v)},Ct=(v=w)=>{ue(ve),Ue?z(!0):ve.current=setTimeout(()=>{z(!0)},v)},tt=(v=m)=>{ue(Re),Re.current=setTimeout(()=>{Xe.current||z(!1)},v)},kt=v=>{var k;if(!v)return;const L=(k=v.currentTarget)!==null&&k!==void 0?k:v.target;if(!L?.isConnected)return Ke(null),void Bt({current:null});w?Ct():z(!0),Ke(L),Bt({current:L}),ue(Re)},nt=()=>{x?tt(m||100):m?tt():z(!1),ue(ve)},ot=({x:v,y:k})=>{var L;const W={getBoundingClientRect:()=>({x:v,y:k,width:0,height:0,top:k,left:v,right:v,bottom:k})};Wn({place:(L=$?.place)!==null&&L!==void 0?L:l,offset:c,elementReference:W,tooltipReference:ee.current,tooltipArrowReference:Je.current,strategy:p,middlewares:u,border:Ot,arrowSize:qe}).then(q=>{Gt(q)})},rt=v=>{if(!v)return;const k=v,L={x:k.clientX,y:k.clientY};ot(L),bt.current=L},St=v=>{var k;if(!ie)return;const L=v.target;L.isConnected&&(!((k=ee.current)===null||k===void 0)&&k.contains(L)||[document.querySelector(`[id='${i}']`),...Ie].some(W=>W?.contains(L))||(z(!1),ue(ve)))},Ut=Kn(kt,50),Y=Kn(nt,50),pe=v=>{Y.cancel(),Ut(v)},A=()=>{Ut.cancel(),Y()},E=M(()=>{var v,k;const L=(v=$?.position)!==null&&v!==void 0?v:de;L?ot(L):y?bt.current&&ot(bt.current):j?.isConnected&&Wn({place:(k=$?.place)!==null&&k!==void 0?k:l,offset:c,elementReference:j,tooltipReference:ee.current,tooltipArrowReference:Je.current,strategy:p,middlewares:u,border:Ot,arrowSize:qe}).then(W=>{$e.current&&Gt(W)})},[ie,j,X,be,l,$?.place,c,p,de,$?.position,y,qe]);_(()=>{var v,k;const L=new Set(At);Ie.forEach(O=>{oe?.(O)||L.add({current:O})});const W=document.querySelector(`[id='${i}']`);W&&!oe?.(W)&&L.add({current:W});const q=()=>{z(!1)},_e=Jn(j),Ce=Jn(ee.current);we.scroll&&(window.addEventListener("scroll",q),_e?.addEventListener("scroll",q),Ce?.addEventListener("scroll",q));let te=null;we.resize?window.addEventListener("resize",q):j&&ee.current&&(te=qr(j,ee.current,E,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));const fe=O=>{O.key==="Escape"&&z(!1)};we.escape&&window.addEventListener("keydown",fe),we.clickOutsideAnchor&&window.addEventListener("click",St);const B=[],st=O=>!!(O?.target&&j?.contains(O.target)),K2=O=>{ie&&st(O)||kt(O)},q2=O=>{ie&&st(O)&&nt()},z0=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],V0=["click","dblclick","mousedown","mouseup"];Object.entries(et).forEach(([O,Ee])=>{Ee&&(z0.includes(O)?B.push({event:O,listener:pe}):V0.includes(O)&&B.push({event:O,listener:K2}))}),Object.entries(Ze).forEach(([O,Ee])=>{Ee&&(z0.includes(O)?B.push({event:O,listener:A}):V0.includes(O)&&B.push({event:O,listener:q2}))}),y&&B.push({event:"pointermove",listener:rt});const j0=()=>{Xe.current=!0},W0=()=>{Xe.current=!1,nt()},Y0=x&&(Ze.mouseout||Ze.mouseleave);return Y0&&((v=ee.current)===null||v===void 0||v.addEventListener("mouseover",j0),(k=ee.current)===null||k===void 0||k.addEventListener("mouseout",W0)),B.forEach(({event:O,listener:Ee})=>{L.forEach(Xt=>{var Nt;(Nt=Xt.current)===null||Nt===void 0||Nt.addEventListener(O,Ee)})}),()=>{var O,Ee;we.scroll&&(window.removeEventListener("scroll",q),_e?.removeEventListener("scroll",q),Ce?.removeEventListener("scroll",q)),we.resize?window.removeEventListener("resize",q):te?.(),we.clickOutsideAnchor&&window.removeEventListener("click",St),we.escape&&window.removeEventListener("keydown",fe),Y0&&((O=ee.current)===null||O===void 0||O.removeEventListener("mouseover",j0),(Ee=ee.current)===null||Ee===void 0||Ee.removeEventListener("mouseout",W0)),B.forEach(({event:Xt,listener:Nt})=>{L.forEach(J2=>{var Qt;(Qt=J2.current)===null||Qt===void 0||Qt.removeEventListener(Xt,Nt)})})}},[j,E,Ue,At,Ie,R,ce,re,Qe,w,m]),_(()=>{var v,k;let L=(k=(v=$?.anchorSelect)!==null&&v!==void 0?v:a)!==null&&k!==void 0?k:"";!L&&t&&(L=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`);const W=new MutationObserver(q=>{const _e=[],Ce=[];q.forEach(te=>{if(te.type==="attributes"&&te.attributeName==="data-tooltip-id"&&(te.target.getAttribute("data-tooltip-id")===t?_e.push(te.target):te.oldValue===t&&Ce.push(te.target)),te.type==="childList"){if(j){const fe=[...te.removedNodes].filter(B=>B.nodeType===1);if(L)try{Ce.push(...fe.filter(B=>B.matches(L))),Ce.push(...fe.flatMap(B=>[...B.querySelectorAll(L)]))}catch{}fe.some(B=>{var st;return!!(!((st=B?.contains)===null||st===void 0)&&st.call(B,j))&&(De(!1),z(!1),Ke(null),ue(ve),ue(Re),!0)})}if(L)try{const fe=[...te.addedNodes].filter(B=>B.nodeType===1);_e.push(...fe.filter(B=>B.matches(L))),_e.push(...fe.flatMap(B=>[...B.querySelectorAll(L)]))}catch{}}}),(_e.length||Ce.length)&&_t(te=>[...te.filter(fe=>!Ce.includes(fe)),..._e])});return W.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{W.disconnect()}},[t,a,$?.anchorSelect,j]),_(()=>{E()},[E]),_(()=>{if(!V?.current)return()=>null;const v=new ResizeObserver(()=>{setTimeout(()=>E())});return v.observe(V.current),()=>{v.disconnect()}},[X,V?.current]),_(()=>{var v;const k=document.querySelector(`[id='${i}']`),L=[...Ie,k];j&&L.includes(j)||Ke((v=Ie[0])!==null&&v!==void 0?v:k)},[i,Ie,j]),_(()=>(Q&&z(!0),()=>{ue(ve),ue(Re)}),[]),_(()=>{var v;let k=(v=$?.anchorSelect)!==null&&v!==void 0?v:a;if(!k&&t&&(k=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`),k)try{const L=Array.from(document.querySelectorAll(k));_t(L)}catch{_t([])}},[t,a,$?.anchorSelect]),_(()=>{ve.current&&(ue(ve),Ct(w))},[w]);const ae=(vt=$?.content)!==null&&vt!==void 0?vt:X,He=ie&&Object.keys(Oe.tooltipStyles).length>0;return to(e,()=>({open:v=>{if(v?.anchorSelect)try{document.querySelector(v.anchorSelect)}catch{return void console.warn(`[react-tooltip] "${v.anchorSelect}" is not a valid CSS selector`)}yt(v??null),v?.delay?Ct(v.delay):z(!0)},close:v=>{v?.delay?tt(v.delay):z(!1)},activeAnchor:j,place:Oe.place,isOpen:!!(Ue&&!g&&ae&&He)})),Ue&&!g&&ae?J.createElement(h,{id:t,role:Kt,className:_0("react-tooltip",it.tooltip,n0.tooltip,n0[s],n,`react-tooltip__place-${Oe.place}`,it[He?"show":"closing"],He?"react-tooltip__show":"react-tooltip__closing",p==="fixed"&&it.fixed,x&&it.clickable),onTransitionEnd:v=>{ue(wt),ie||v.propertyName!=="opacity"||(De(!1),yt(null),T?.())},style:{...be,...Oe.tooltipStyles,opacity:It!==void 0&&He?It:void 0},ref:ee},ae,J.createElement(h,{className:_0("react-tooltip-arrow",it.arrow,n0.arrow,o,C&&it.noArrow),style:{...Oe.tooltipArrowStyles,background:Pt?`linear-gradient(to right bottom, transparent 50%, ${Pt} 50%)`:void 0,"--rt-arrow-size":`${qe}px`},ref:Je})):null},ps=({content:e})=>J.createElement("span",{dangerouslySetInnerHTML:{__html:e}}),fs=J.forwardRef(({id:e,anchorId:t,anchorSelect:n,content:o,html:s,render:i,className:a,classNameArrow:l,variant:c="dark",place:d="top",offset:f=10,wrapper:p="div",children:u=null,events:h=["hover"],openOnClick:w=!1,positionStrategy:m="absolute",middlewares:y,delayShow:g=0,delayHide:C=0,float:x=!1,hidden:N=!1,noArrow:F=!1,clickable:U=!1,closeOnEsc:R=!1,closeOnScroll:ce=!1,closeOnResize:re=!1,openEvents:se,closeEvents:be,globalCloseEvents:de,imperativeModeOnly:Ae=!1,style:T,position:oe,isOpen:X,defaultIsOpen:V=!1,disableStyleInjection:D=!1,border:Q,opacity:Fe,arrowColor:j,arrowSize:Ke,setIsOpen:Ot,afterShow:It,afterHide:Pt,disableTooltip:qe,role:Kt="tooltip"},vt)=>{const[ee,Je]=I(o),[ve,Re]=I(s),[wt,Oe]=I(d),[qt,ie]=I(c),[Tt,Ue]=I(f),[De,$]=I(g),[yt,xt]=I(C),[bt,At]=I(x),[Bt,Xe]=I(N),[Ie,_t]=I(p),[$e,Qe]=I(h),[Jt,et]=I(m),[Ze,we]=I(null),[z,Gt]=I(null),Ct=Z(D),{anchorRefs:tt,activeAnchor:kt}=O2(e),nt=Y=>Y?.getAttributeNames().reduce((pe,A)=>{var E;return A.startsWith("data-tooltip-")&&(pe[A.replace(/^data-tooltip-/,"")]=(E=Y?.getAttribute(A))!==null&&E!==void 0?E:null),pe},{}),ot=Y=>{const pe={place:A=>{var E;Oe((E=A)!==null&&E!==void 0?E:d)},content:A=>{Je(A??o)},html:A=>{Re(A??s)},variant:A=>{var E;ie((E=A)!==null&&E!==void 0?E:c)},offset:A=>{Ue(A===null?f:Number(A))},wrapper:A=>{var E;_t((E=A)!==null&&E!==void 0?E:p)},events:A=>{const E=A?.split(" ");Qe(E??h)},"position-strategy":A=>{var E;et((E=A)!==null&&E!==void 0?E:m)},"delay-show":A=>{$(A===null?g:Number(A))},"delay-hide":A=>{xt(A===null?C:Number(A))},float:A=>{At(A===null?x:A==="true")},hidden:A=>{Xe(A===null?N:A==="true")},"class-name":A=>{we(A)}};Object.values(pe).forEach(A=>A(null)),Object.entries(Y).forEach(([A,E])=>{var ae;(ae=pe[A])===null||ae===void 0||ae.call(pe,E)})};_(()=>{Je(o)},[o]),_(()=>{Re(s)},[s]),_(()=>{Oe(d)},[d]),_(()=>{ie(c)},[c]),_(()=>{Ue(f)},[f]),_(()=>{$(g)},[g]),_(()=>{xt(C)},[C]),_(()=>{At(x)},[x]),_(()=>{Xe(N)},[N]),_(()=>{et(m)},[m]),_(()=>{Ct.current!==D&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[D]),_(()=>{typeof window<"u"&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:D==="core",disableBase:D}}))},[]),_(()=>{var Y;const pe=new Set(tt);let A=n;if(!A&&e&&(A=`[data-tooltip-id='${e.replace(/'/g,"\\'")}']`),A)try{document.querySelectorAll(A).forEach(k=>{pe.add({current:k})})}catch{console.warn(`[react-tooltip] "${A}" is not a valid CSS selector`)}const E=document.querySelector(`[id='${t}']`);if(E&&pe.add({current:E}),!pe.size)return()=>null;const ae=(Y=z??E)!==null&&Y!==void 0?Y:kt.current,He=new MutationObserver(k=>{k.forEach(L=>{var W;if(!ae||L.type!=="attributes"||!(!((W=L.attributeName)===null||W===void 0)&&W.startsWith("data-tooltip-")))return;const q=nt(ae);ot(q)})}),v={attributes:!0,childList:!1,subtree:!1};if(ae){const k=nt(ae);ot(k),He.observe(ae,v)}return()=>{He.disconnect()}},[tt,kt,z,t,n]),_(()=>{T?.border&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),Q&&!Yn("border",`${Q}`)&&console.warn(`[react-tooltip] "${Q}" is not a valid \`border\`.`),T?.opacity&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),Fe&&!Yn("opacity",`${Fe}`)&&console.warn(`[react-tooltip] "${Fe}" is not a valid \`opacity\`.`)},[]);let rt=u;const St=Z(null);if(i){const Y=i({content:z?.getAttribute("data-tooltip-content")||ee||null,activeAnchor:z});rt=Y?J.createElement("div",{ref:St,className:"react-tooltip-content-wrapper"},Y):null}else ee&&(rt=ee);ve&&(rt=J.createElement(ps,{content:ve}));const Ut={forwardRef:vt,id:e,anchorId:t,anchorSelect:n,className:_0(a,Ze),classNameArrow:l,content:rt,contentWrapperRef:St,place:wt,variant:qt,offset:Tt,wrapper:Ie,events:$e,openOnClick:w,positionStrategy:Jt,middlewares:y,delayShow:De,delayHide:yt,float:bt,hidden:Bt,noArrow:F,clickable:U,closeOnEsc:R,closeOnScroll:ce,closeOnResize:re,openEvents:se,closeEvents:be,globalCloseEvents:de,imperativeModeOnly:Ae,style:T,position:oe,isOpen:X,defaultIsOpen:V,border:Q,opacity:Fe,arrowColor:j,arrowSize:Ke,setIsOpen:Ot,afterShow:It,afterHide:Pt,disableTooltip:qe,activeAnchor:z,setActiveAnchor:Y=>Gt(Y),role:Kt};return J.createElement(ds,{...Ut})});typeof window<"u"&&window.addEventListener("react-tooltip-inject-styles",e=>{e.detail.disableCore||jn({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s;--rt-arrow-size:8px}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit;z-index:-1}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),e.detail.disableBase||jn({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:var(--rt-arrow-size);height:var(--rt-arrow-size)}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})});const I2=ne((e,t)=>{const{id:n,variant:o,content:s,children:i,className:a}=e,l=no(),c=n||l,d=typeof s=="string";return r(mt,{children:[r("div",{"data-tooltip-id":c,children:i}),r(fs,{ref:t,...e,className:k0("z-50",a||H("z-50 shadow-md",{"bg-font-secondary text-surface-primary":o==="dark","bg-surface-primary text-font-primary shadow-lg":o==="light","bg-success-default text-surface-primary":o==="error","bg-error-default text-surface-primary":o==="error","bg-info-default text-surface-primary":o==="warning"})),content:d?s:void 0,id:c,children:d?null:s})]})}),us=({isOpen:e,children:t})=>{const[n,o]=I(!1);return _(()=>{n!==e&&setTimeout(()=>{o(e)},1e3)},[n,e]),r("div",{className:H("block w-full","transition-all duration-300 ease-out",{"h-full":n,"h-0":!n}),children:t})},ms=({className:e="",style:t,children:n})=>r("div",{"aria-hidden":"true",className:k0(H("fixed inset-x-0 -bottom-3 z-50","box-border rounded-b-none rounded-t-xl bg-surface-primary dark:bg-neutral-7","block h-[38rem] w-full overflow-hidden","animate-slide-in-bottom"),e),...t&&{style:t},onClick:o=>{o.preventDefault(),o.stopPropagation()},children:n}),hs=({isOpen:e=!1,className:t,style:n,children:o})=>r(us,{isOpen:e,children:r(ms,{className:t,style:n,children:o})});function gs(e,t){const n=e,s=t-n+1,i=new Uint32Array(1);crypto.getRandomValues(i);const a=i[0]/2**32;return Math.floor(a*s+n)}const vs= window.__dynamic_base__+"/assets/banner-de.min-_O7gmKHE.png",ws= window.__dynamic_base__+"/assets/banner-en.min-Cfig8KFi.png",ys= window.__dynamic_base__+"/assets/banner-es.min-Cj47GKX5.png",xs= window.__dynamic_base__+"/assets/banner-fr.min-nVXy2RSl.png",bs= window.__dynamic_base__+"/assets/banner-id.min-CB7cfkg8.png",As= window.__dynamic_base__+"/assets/banner-it.min-CeMMxZRO.png",_s= window.__dynamic_base__+"/assets/banner-ja.min-golO6ccW.png",Cs= window.__dynamic_base__+"/assets/banner-ko.min-DwU6Wrt6.png",ks= window.__dynamic_base__+"/assets/banner-nl.min-CajjHKGo.png",Ss= window.__dynamic_base__+"/assets/banner-pt.min-DCaxYj5n.png",Ns= window.__dynamic_base__+"/assets/banner-th.min-zBd8C6u6.png",Ls= window.__dynamic_base__+"/assets/banner-tl.min-2zQP3xbT.png",Fs= window.__dynamic_base__+"/assets/banner-tw.min-Bozu4EKy.png",Es= window.__dynamic_base__+"/assets/banner-vi.min-5dCtFvSf.png",Ms="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABCCAMAAACWyYNNAAAAwFBMVEUAAABZG9xZG9xZGtxZG9xYG9tUFNdZGtxZHNxZG9xZGt1YG9pZG9xZG9zj/zTZ7eK5ueDr4/uCVOVuN+DBqfKXcOljKd6NYue2m+/WxvbLuPR4RuPBqvLg1PiCVOT18f2sje6hf+yZhN+HW+Wif+zf1Pi3m/B9TeSxq+Da8T7////Q3eGhkd9hKdGVf5OejYivqXPJ1FPS40mpnuBpMN+Jad5eIt2McJ2SaehyP+JqOMdqN8fb8T6Rd9+Rdt6McZ10klkjAAAADXRSTlMA/q+f30AQz65P32BfYw6YUQAAAptJREFUWMPtmYtu2jAUhjsuXbvth/o4tuOEQCCllJZy6X1tt73/W82OaRVCOhUJV2LiU5Tk55zwKRcbKRx8Gs1aA15o1H4sFYc1eKR+mEvq8ErNOo7hmaaR1OCZupF8hWcaRgLvfL5kcNkucDnxIZm0Sww8SM5PSpx7kJxYbu4KLg8Sd4naBXZVcmL5vXy08uBLsjyTX57vSWHjS3LnWeIe4cwOkLubPGBHB+OgXWLiQYLJ6gQ5AHZ1ql+yl+wl25f0GTtFwBjnQxZJxVgKQNusWfhWjQGuezYnp4hcdwDYLErdVZIel71uBBWE5mgxNElCReiHoTmaGJesq00ViAg6ZpLbbpF3K3O05MwUXXdsuyslDLjoBugGHYFUmMQ4RGCWDqEjLky2VQ1ok8l2C5NdFdz2i3TZTba7+kwKklAxCbM4iUJEPdMQO0n0mpXrth9IZhYRQoRv3ZWSIIjOZBIkgliY8L6OIkAmIVPK5rzKTNVe/pBBm2yrrhsm6zwrYap5d6UEFAOSOMBJuuR2C9lWXS5127za/b+Pk1f2kr1kE65m160li/nze5KYbt0W8e1Pu7k36w/z8NIqMJ6+I6H7exU/UpZBKepmNBqNHpW6xYeYtko8VEtURoqIUwbio8xIaJQR4UM8vZQl46ut35N5a43Z1iWLdcn11iWtCiol+Y+zGKo+03Gqh4EXSSjQ4UkoOlJF6Ean2IDrdceiUtKhfhrHqYBKjC5JsQGzdcm8WqJoSEhFcGbWEmyj4T4uO8bP1SNeJ8xKmNZpn212JvhTlkzfm7tkYVdiM6bjlfN48DNBPs0Xb0NkdoUdnur/KWnAM1/9v3t2b5+b8Mzxgf+3z3X3h4DPC/bl6PDA8b3W2NqXrvDtqHnwWfwFve9d1iRzHr4AAAAASUVORK5CYII=",Rs="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAF8AAAAYCAMAAACrz9ETAAABp1BMVEUAAAAAMYcAMYYAMIkAnN4AnN4AnN4AMIcAMIUAMIYAnN8AneEAMIcAMIcAMIgAnN8AnN4AM4sAltQAnN4AnN8AMIcAL4cAMIcAnN4AnN4AMIcAnN8AOYwANosAI4gAL4cAM4gAMYcAM4gAnd4And8ANYoANYcAouMAmt8ANIUAmt8AoeAAMIcAnN4AnN4AnN4AMYkAnN4AMogAMIcAMIcAM4gAMokAnd8AnN4AMokAm94AM4kAL4cAnuAAMIcAnOAAL4YAnN4ALYkAkskAn+AAnd4AL4YAnN4AMIYAnN4AMIcAMIgAnN4AMIcAL4cAMIYAnN4AnN8Am94AMIcAL4cANYoAMIcAL4cAMIcAmt4AnN4AnN4AnN4And4AnN8AMIcAnN4AL4gAL4cAnN0And4AneAAm90And8AnN0AOo4Amt8BM3wAnN0AbbgAnN0AnN8AnN4AL4YAPo4AktQALogAMIcAnN4BIWkAKHgAkdQAKXIALH8AlNcAis4Ad70Aa7IAUJsARYwAMXgBJG0AldgAktUAhskAVKMAQZQANIkAO4IALoIBKHDXDevHAAAAdXRSTlMA9b4Jh/vt14D5vxT87cjHqxAE+PTx6ePg1sSRGw0Hz5GIYU46NCAaFxMRC+/lzcS3tqmge3NvZ15dV1NHQD01KikXCgjy3by2s7KknJiOfnx2a2lZSkI4MC7q6dnRuK6inIiAY1lKRyUlH/nw3qWWi4daWU0nu90kAAADTUlEQVRIx82U51cTQRTFbzaBSAJp1FASDIGQ0IvSO0pRulLsvXedp2Dv9Y/2ziYTgoqHD3iOvw/JzuTlzr777i409QcM3SfxD7DUNs192G+qoyoHxwD2mXqHysWNfWZY7aCsAfvLIaV5luF5801sc+KgZiHGvV2ZWHZpFq8msJNelwukU5HnTw23HyOL86iyKSw7jF0Za5E0p/uRS1ObSDY+z7L6b4rrYBgtUoZ67MZQnmQoGUMOtyps/Rq3IltG/v0rCcAwWKBU18qlUhbs3kAPlauWq47w6wpyGCmx9avt+Lw3+m9F/CFTcph5XQMWWHAIiCTHg/TMySt+EL2I4L5I3g3ARf0eIJTyNiE0ORlCv0g+SxqUxshvvRTxwDDHOFUDD1kwgLU5d3l0PjbdXhuZa29fscc/296ZnBKpaASqqH8dI4EzFa1LPR3nXLgscpY1jxT5npH/9kk3C0MlH4ea5HARJzy+4lAaOuZDF/fZyjqNq50Is+NG77V8Dti7VJwehNDkgMgMNWrt+PzY2nr37u3rD8Jms/N1litVFI1SXp1fdfDasvQhnRhwqNJBOHl8edB7SuRU61kt7LqWR0/OtVFeLof8Ig8o0qXIm83NzY8vxKbDm41Ps8pgBanlo1VPuOhGDXM7j252M4wxE5+8gDfMf98ClrjVn2J8eoGgT5HXkiV/CIaTBTTGUVrm604GC1VhH0iBHgWO0aBBdncJqNPSJSUtbT2NcTah/71Br+IJBmrExOeVGMLxnU/2KmzWmKT6dBpK13kyb522+TiERcpTR9MrIo3gkTzHG+etJkx8Xgq5E/Zf6J3ENhf5yzhs1tMRdU4rdZQ2RSjOk/SJF9Lx0VwV+gKkWkWO4IpIawqIKfJ1U1g1hl+gdc3muoxWVc6Ws7hSZ39eRynGiyaOcaoJNjfoS7Gno4VibTqvgVAmPl/0aP2s2kEN1XxmsVCgiINuHgNZ5WJaH+Sl2nFT5EpPULgVmmGeTHw+C7mHX/VnLSuWXfRVut3nh2stazDzUnSPgiRmPJ7rpmhy2X8mfDwe8HiGJqo8Uxv6FWCRu0IW8Xec40kY+gqVOoE/MJFI4Xc8QuqwZ0Y5jYsR7Jmwdm0De4bxjzbgP+AnMjVFsQeKprIAAAAASUVORK5CYII=",Os="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAAAYCAMAAAAyNwimAAAAn1BMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////8Kd3m4AAAANHRSTlMAgL+QB++v3+f7YAoQcCscGPbry55F2kkTt6mFfNW7s6OWZ1Mw49B2IItZP8M8JE8O8To4SjE+2AAAAo1JREFUSMet1deyokAUheEFonTLkLOYcz5h1vs/22AfKJBS51z434i7wE/oKhplH2G/mxg4eFsryQcJD+9qwocd8K60st58ppu8a4q3531ESQPoaqTd2sUWnnVVZ9gnA78rnd0BQ/5krp7eP39K/uKX7fN7YGYY7ohcPQcWhuGcyRMeZx3mg6muz0bLbHU11DV3wOB2zpYJygwD9Ud9oJG927M0OX14BvCHVeMV5WZku0DRBWCTljOaCLGNHMQbEapFKcSuBtCjj+9BIkTY2+MohOIwFy3gsuAtv+ch7AK7EsjSzFgn9B1DJ22UV35JrwX04Z4yIxYMXGtCHm+PWwYN0Le29Yo6ww5ghNwC1a0skEoKCxhzjhr4NDmD6kBqOJKhUosGyF3JqvP6Hvhc0I8B5xAtRmqQk0N8bujUgDtlfw1celEvV4OQjOF+9d0GsGPWiUsLEIOZSX8H7HwGpq+AlJxYKbdQgDmYBeyvymNJYUoF2ORZ3WIDrHesK7QW8KXrauX3kpGHpQIQkkudwx8g0PVomQHfZJHhoAAk5CrhqQGEkfMnefgMuotcFpPXaqC+9LkB6kekGjNoBsMSZogGGFgBy4Jp4SHiAyCldJvBjKTdAYo2gC3JYwtYZtrY/lj/AbyIj4ArWQC7arAmN1kHSMklsKgGK3JitIB6g8mWGz4E0CPDXNaDCefoABiQ56msBpagjQaQ6sfjsS7JFrDWtCPq4sVo/F0P/OovnTQtRd1HNLfjarCXvtcCzsZo22c7Ha9aMsfrNEZQWbw1PpG/33B6IykcvMiKBjTd9pZ5GbNTgacZQv7n9e/5MryiKvXJvpXzvo2L5zl7C69b71tb3CVP5piY7ZKFh/f0Dyccj7ooarSdAAAAAElFTkSuQmCC",P2=({currency:e,language:t})=>{const{t:n}=P(),o=[{iconURL:Ms,title:n("common.refund_process_bar.coupon_payment_1")},{iconURL:Rs}],s=e==="JPY"?[...o,{iconURL:Os}]:o;return r("div",{className:H("top-15 absolute","flex items-center justify-center","w-full","font-bold text-white"),style:{fontFamily:"Source Han Sans",fontSize:`${t==="es"?"11px":"0.75rem"}`},children:[s.map(({title:i,iconURL:a},l)=>r(J.Fragment,{children:[r("div",{className:"flex items-center",children:[r("div",{className:"h-3.5",children:r("img",{alt:`payment_${i??"title"}_${a}`,className:"max-h-full max-w-full",src:a})}),i&&r("span",{className:"pl-0.5",children:i})]}),l!==s.length-1&&r("span",{className:"px-0.5",children:"/"})]},`${i??"title"}_${a}`)),r("span",{className:"pl-1",children:n("common.refund_process_bar.coupon_payment_2")})]})},Is=e=>{const{t}=P(),{visible:n,language:o,countdown:s,currency:i,refundRatio:a,onClose:l}=e;let c,d,f="16px",p="12px";o==="zh-TW"?(d=Fs,c=144):o==="en"?(d=ws,c=183,f="20px"):o==="ko"?(d=Cs,c=144):o==="es"?(d=ys,c=195,p="11px"):o==="it"?(d=As,c=199,p="11px",f="20px"):o==="fr"?(d=xs,c=195,f="20px"):o==="nl"?(d=ks,c=183,f="20px"):o==="pt"?(d=Ss,c=144,p="11px"):o==="de"?(d=vs,c=144,p="11px",f="20px"):o==="th"?(d=Ns,c=183,p="11px",f="20px"):o==="id"?(d=bs,c=183,p="11px",f="20px"):o==="vi"?(d=Es,c=183,p="11px",f="20px"):o==="tl"?(d=Ls,c=183,p="11px",f="20px"):(d=_s,c=198);const u=M(()=>s.split(""),[s]);return r("div",{className:`${H("fixed inset-0","flex items-center justify-center",{hidden:!n,block:n})} coupon-background`,style:{backgroundColor:"rgba(0,0,0,0.4)",zIndex:51},children:r("div",{className:"relative",children:[r("div",{style:{backgroundImage:`url(${d})`,backgroundSize:"contain",width:300,height:500,display:"flex",justifyContent:"space-around",alignItems:"flex-end"},children:[r(P2,{currency:i,language:o}),r("div",{style:{position:"absolute",top:150,left:90,color:"white",fontWeight:900,fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontSize:"48px",lineHeight:"48px",textAlign:"center",transform:"rotate(-7deg)"},children:[a,"%"]}),r("div",{style:{position:"absolute",bottom:172,right:199,color:"white",fontFamily:"monospace",fontWeight:"bold"},children:u()[0]}),r("div",{style:{position:"absolute",bottom:172,right:167,color:"white",fontFamily:"monospace",fontWeight:"bold"},children:u()[1]}),r("div",{style:{position:"absolute",bottom:172,right:124,color:"white",fontFamily:"monospace",fontWeight:"bold"},children:u()[3]}),r("div",{style:{position:"absolute",bottom:172,right:92,color:"white",fontFamily:"monospace",fontWeight:"bold"},children:u()[4]}),r("div",{style:{width:"268px",height:"90px",color:"white",alignItems:"end",display:"inline-grid",justifyItems:"center",fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontSize:`${p}`,lineHeight:"15px",marginBottom:`${f}`},children:r("ol",{style:{listStyle:"auto",paddingLeft:"1.2rem"},children:[r("li",{children:t("common.partial_refund_process_bar.coupon_text_1").replace("__N__",a.toString())}),r("li",{children:t("common.partial_refund_process_bar.coupon_text_2")}),r("li",{children:t("common.partial_refund_process_bar.coupon_text_3")})]})})]}),r("div",{style:{width:"300px",height:"56px",alignItems:"end",display:"inline-grid",justifyItems:"center"},children:r("div",{"aria-hidden":!0,style:{width:c,height:40,borderRadius:20,background:"linear-gradient(90deg, #FC4D42 0%, #F2E56F 100%)",color:"#222222",textAlign:"center",lineHeight:"37px",fontWeight:600,fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontSize:14,cursor:"pointer"},onClick:l,children:t("common.refund_process_bar.button_title")})})]})})},Ps="data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewBox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M9.41075%201.91073C9.73619%201.5853%2010.2638%201.5853%2010.5893%201.91073L13.0893%204.41073L11.9108%205.58925L10.8333%204.51183V9.16666H15.4882L14.4108%208.08925L15.5893%206.91073L18.0893%209.41073C18.4147%209.73617%2018.4147%2010.2638%2018.0893%2010.5892L15.5893%2013.0892L14.4108%2011.9107L15.4882%2010.8333H10.8333V15.4881L11.9108%2014.4107L13.0893%2015.5892L10.5893%2018.0892C10.2638%2018.4147%209.73619%2018.4147%209.41075%2018.0892L6.91075%2015.5892L8.08926%2014.4107L9.16667%2015.4881V10.8333H4.51185L5.58926%2011.9107L4.41075%2013.0892L1.91075%2010.5892C1.58531%2010.2638%201.58531%209.73617%201.91075%209.41073L4.41075%206.91073L5.58926%208.08925L4.51185%209.16666H9.16667V4.51183L8.08926%205.58925L6.91075%204.41073L9.41075%201.91073Z'%20fill='white'/%3e%3c/svg%3e",Ts="data:image/svg+xml,%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M8%2014C11.3137%2014%2014%2011.3137%2014%208C14%204.68629%2011.3137%202%208%202C4.68629%202%202%204.68629%202%208C2%2011.3137%204.68629%2014%208%2014Z'%20stroke='white'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M7.33334%208H8.00001V10.6667H8.66668'%20stroke='white'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M8%205.3335H8.00667'%20stroke='white'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e",Bs=e=>{const{dndDiv:t,showCoupon:n}=e,o=Z({x:0,y:0}),s=Z({x:0,y:0}),i=Z(0),a=Z(0),l=Z({x:0,y:0}),c=M(m=>{const{current:y}=t;if(!y)return;const{top:g,right:C}=m;y.style.top=`${g}px`,y.style.right=`${C}px`},[t]),d=M(m=>{m.preventDefault();const{current:y}=t;if(!y)return;const g=y.getBoundingClientRect();o.current={x:m.touches[0].pageX,y:m.touches[0].pageY},s.current={x:g.left,y:g.top},l.current={x:g.left,y:g.top},i.current=window.innerWidth-g.width,a.current=window.innerHeight-g.height-10},[t]),f=M(m=>{const y=m.touches[0].pageX,g=m.touches[0].pageY,C=y-o.current.x,x=g-o.current.y,N=Math.min(Math.max(s.current.x+C,0),i.current),F=Math.min(Math.max(s.current.y+x,0),a.current),{current:U}=t;if(!U)return;const R=U.getBoundingClientRect();c({top:F,right:window.innerWidth-N-R.width}),s.current={x:N,y:F},o.current={x:y,y:g}},[t,c]),p=M(()=>{const{current:m}=t;m&&Math.abs(s.current.x-l.current.x)<5&&Math.abs(s.current.y-l.current.y)<5&&n()},[t,n]),u=M(m=>{const y=m.pageX,g=m.pageY,C=y-o.current.x,x=g-o.current.y,N=Math.min(Math.max(s.current.x+C,0),i.current),F=Math.min(Math.max(s.current.y+x,0),a.current),{current:U}=t;if(!U)return;const R=U.getBoundingClientRect();c({top:F,right:window.innerWidth-N-R.width}),s.current={x:N,y:F},o.current={x:y,y:g}},[t,s,o,i,a,c]),h=M(()=>{const{current:m}=t;m&&(Math.abs(s.current.x-l.current.x)<5&&Math.abs(s.current.y-l.current.y)<5&&n(),window.onmousemove=null,window.onmouseup=null)},[t,n]),w=M(m=>{m.preventDefault();const{current:y}=t;if(!y)return;const g=y.getBoundingClientRect();o.current={x:m.pageX,y:m.pageY},s.current={x:g.left,y:g.top},l.current={x:g.left,y:g.top},i.current=window.innerWidth-g.width,a.current=window.innerHeight-g.height-10,window.onmousemove=u,window.onmouseup=h},[t,u,h]);_(()=>{const{current:m}=t;m&&(oo()?(m.ontouchstart=d,m.ontouchmove=f,m.ontouchend=p,m.ontouchcancel=p):(m.ondragstart=()=>!1,m.onmousedown=w))},[t,w,d,f,p])},Gs=e=>{const{visible:t,percent:n,text:o,showCoupon:s}=e,i=Z(null);Bs({dndDiv:i,showCoupon:s});const{i18n:a}=P();return r("div",{ref:i,className:H("flex items-center justify-center","fixed top-9 z-40 h-9 w-[359px]","px-2 py-0","rounded-xl","cursor-pointer",{block:t,hidden:!t}),style:{backgroundColor:"rgba(0, 0, 0, 0.5)",right:(window.innerWidth-359)/2},children:[r("img",{alt:"grip",className:"size-5",src:Ps}),r("div",{className:"relative mx-[5px] my-2 flex h-4 w-[295px] bg-white",style:{borderRadius:10},children:[r("div",{className:"absolute left-0 top-0 h-4",style:{background:"linear-gradient(90deg, #FC4D42 0%, #F2E56F 100%)",filter:"blur(8px)",width:`${n*100}%`,borderRadius:10}}),r("div",{className:"absolute left-0 top-0 h-4",style:{background:"linear-gradient(90deg, #FC4D42 0%, #F2E56F 100%)",width:`${n*100}%`,borderRadius:10}}),r("div",{className:"absolute left-0 top-0 size-full text-center leading-4 text-black",style:{fontSize:a.language==="es"?8:10,fontFamily:"Hiragino Sans"},children:o})]}),r("img",{alt:"info",className:"size-4",src:Ts})]})},Us= window.__dynamic_base__+"/assets/banner-de.min-CTI0ISGF.png",Ds= window.__dynamic_base__+"/assets/banner-en.min-BUY-3AJQ.png",$s= window.__dynamic_base__+"/assets/banner-es.min-DR7G-U6b.png",Zs= window.__dynamic_base__+"/assets/banner-fr.min-Ba8jkBqu.png",Hs= window.__dynamic_base__+"/assets/banner-id.min-BPpaYn_X.png",zs= window.__dynamic_base__+"/assets/banner-it.min-B_joiZFg.png",Vs= window.__dynamic_base__+"/assets/banner-ja.min-YGtINCO0.png",js= window.__dynamic_base__+"/assets/banner-ko.min-CHn-3RNk.png",Ws= window.__dynamic_base__+"/assets/banner-nl.min-DBXNR8FL.png",Ys= window.__dynamic_base__+"/assets/banner-pt.min-qHcs2r_j.png",Ks= window.__dynamic_base__+"/assets/banner-th.min-BWCwMTw7.png",qs= window.__dynamic_base__+"/assets/banner-tl.min-B_jbzarK.png",Js= window.__dynamic_base__+"/assets/banner-tw.min-DKtQ25sY.png",Xs= window.__dynamic_base__+"/assets/banner-vi.min-QnbZK9u9.png",Qs=e=>{const{t}=P(),{visible:n,language:o,currency:s,countdown:i,onClose:a}=e;let l,c;o==="zh-TW"?(c=Js,l=144):o==="en"?(c=Ds,l=183):o==="ko"?(c=js,l=144):o==="es"?(c=$s,l=195):o==="it"?(c=zs,l=199):o==="fr"?(c=Zs,l=195):o==="nl"?(c=Ws,l=183):o==="pt"?(c=Ys,l=144):o==="de"?(c=Us,l=144):o==="th"?(c=Ks,l=183):o==="id"?(c=Hs,l=183):o==="vi"?(c=Xs,l=183):o==="tl"?(c=qs,l=183):(c=Vs,l=198);const d=M(()=>i.split(""),[i]);return r("div",{className:`${H("flex items-center justify-center","fixed","inset-0",{block:n,hidden:!n})} coupon-background`,style:{backgroundColor:"rgba(0,0,0,0.4)",zIndex:51},children:r("div",{className:"relative",children:[r("img",{alt:"",src:c,style:{width:300,height:500}}),r(P2,{currency:s,language:o}),r("div",{className:"absolute bottom-[172px] right-[199px] font-bold text-white",style:{fontFamily:"monospace"},children:d()[0]}),r("div",{className:"absolute bottom-[172px] right-[167px] font-bold text-white",style:{fontFamily:"monospace"},children:d()[1]}),r("div",{className:"absolute bottom-[172px] right-[124px] font-bold text-white",style:{fontFamily:"monospace"},children:d()[3]}),r("div",{className:"absolute bottom-[172px] right-[92px] font-bold text-white",style:{fontFamily:"monospace"},children:d()[4]}),r("div",{style:{width:"300px",height:"56px",alignItems:"end",display:"inline-grid",justifyItems:"center"},children:r("div",{"aria-hidden":!0,style:{width:l,height:40,borderRadius:20,background:"linear-gradient(90deg, #FC4D42 0%, #F2E56F 100%)",color:"#222222",textAlign:"center",lineHeight:"37px",fontWeight:600,fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontSize:14,cursor:"pointer"},onClick:a,children:t("common.refund_process_bar.button_title")})})]})})},ei=(e,t,n)=>{const[o,s]=I(0),[i,a]=I("00:00"),l=Z(void 0),c=M(()=>{if(!t||!e)return;const f=J0(new Date),p=(t-f)/(t-e);s(p),t<=f&&(s(0),clearInterval(l.current),n?.())},[t,e,n]),d=M(()=>{if(!t||!e)return;const f=J0(new Date),p=t-f,u=Math.floor(p/60),h=p%60<10?`0${p%60}`:p%60;a(`0${u}:${h}`),t<=f&&(a("00:00"),clearInterval(l.current),n?.())},[e,t,n]);return _(()=>{e&&t&&!l.current&&(l.current=window.setInterval(()=>{c(),d()},1e3)),!e&&!t&&n?.()},[e,t,c,d,n]),_(()=>()=>{clearInterval(l.current)},[]),{percent:o,text:i}},ti=()=>{const e=Ye(),t=Z(void 0);_(()=>{const n=gs(20,29);return t.current=window.setTimeout(()=>{e(ro())},n*1e3),()=>{window.clearTimeout(t.current)}},[e])},ni=()=>{const e=Ye(),{t}=P(),{doCouponTrigger:n,refundStatus:o}=G(x=>x.refundCoupon),[s,i]=I(!1),[a,l]=I(!1),[c,d]=I(null);let f="ja";c&&(f=c.lang);let p="JPY";c&&(p=c.currency);const u=M(()=>{i(!1),l(!1)},[]),h=M(()=>{i(!0),l(!1)},[]),w=ei(o?.pay_window?.start_at,o?.pay_window?.end_at,u);ti();const m=M(x=>{x.target===x.currentTarget&&(i(!1),l(!0))},[]),y=M(async(x,N)=>{e(io(x)),x.pay_window&&x.refund_ratio&&(N==="v1"&&i(!0),ze({action:"p_first_pay_refund_show",data:{display_name:window.option.userId,campaign:{id:"first_pay_refund_campaign",name:"first_pay_refund_campaign"},providers:window.option.providers}}))},[e]);_(()=>{const x=N=>{if(N.data?.type){if(N.data.type==="refund_campaign_status"&&K0(N.data.refundCampaignStatus)){const F=N.data.refundCampaignStatus;y(F,"v1")}if(N.data.type==="refund_campaign_v2_status"&&K0(N.data.refundCampaignStatus)){const F=N.data.refundCampaignStatus;y(F,"v2")}}};return window.addEventListener("message",x,!1),()=>window.removeEventListener("message",x,!1)},[]),_(()=>{o?.pay_window||n&&o?.target_user&&console.log("game ignore doActive")},[n,o]),_(()=>{(async()=>{const N=await so();d(N)})()},[]);let g=100;o?.refund_ratio&&(g=o.refund_ratio);let C;return g===100?C=t("common.refund_process_bar.title").replace("__time__",w.text):C=t("common.partial_refund_process_bar.title").replace("__time__",w.text).replace("__N__",g.toString()),r(mt,{children:[r(Gs,{percent:w.percent||0,showCoupon:h,text:C,visible:a}),g===100?r(Qs,{countdown:w.text,currency:p,language:f,visible:s,onClose:m}):r(Is,{countdown:w.text,currency:p,language:f,refundRatio:g,visible:s,onClose:m})]})},me="MainPopupRouteClick";var K=(e=>(e.Desktop="Desktop",e.Mobile="Mobile",e))(K||{});const T2="RecommendClick",Me=e=>{const{children:t,name:n,taskId:o,campaignId:s,campaignName:i}=e;return r("div",{"aria-hidden":!0,onClick:()=>{ze({action:"p_click",data:{display_name:window.option.userId,click:o?`${n}:${o}`:`${n}`,campaign:{id:s,name:i},providers:window.option.providers}})},children:t})},oi=()=>{const{vipRank:e}=G(t=>t.mainPopup);return _(()=>{l2()},[]),e&&e>0?r(b2,{rank:e}):r(s2,{type:"colorful"})},ri=()=>{const e=()=>{c2()},t=()=>{const o=new URL(window.location.href).searchParams.get("lang"),s=`${ht.SHD_G123_WEB_URL}?lang=${o}`;S0("misc_logo_click",{data:{position:"nav_panel",device:"desktop"}}),window.open(s,"_blank")};return r("div",{className:H("flex flex-col items-center justify-center gap-y-4","mb-4","select-none"),children:[r("div",{onClick:t,className:"-my-1 py-1",children:r(E0,{})}),r(oi,{}),r("div",{className:H("flex items-center justify-center","text-xxs text-font-primary font-semibold"),children:[r("span",{className:"text-center",children:window.option.userId}),r(he,{className:"text-font-primary p-0!",icon:r(h2,{className:"scale-50!"}),size:"small",type:"link",onClick:e})]})]})},$t=({label:e,icon:t,highlightIcon:n,isHighlight:o=!1,hasBadge:s=!1,onSelect:i})=>r("div",{"aria-hidden":!0,className:H("relative","flex flex-col items-center justify-center gap-y-2 h-16","cursor-pointer"),onClick:()=>{i?.()},children:[r("div",{className:"relative size-6",children:r(y0,{offsetRight:0,offsetTop:0,show:s,children:o?n:t})}),r("span",{className:"text-font-primary select-none text-center text-xs font-semibold",children:e})]}),si=()=>{const{t:e}=P(),{recommendGames:t,currentRoute:n}=G(i=>i.mainPopup),o=!t?.length||t?.length<2,s=M(i=>{ke(i)},[]);return r("div",{className:"flex flex-col items-center justify-center gap-y-2",children:[!o&&r(Me,{name:`${me}:${S.Recommends}_${K.Desktop}`,children:r($t,{highlightIcon:r(w2,{}),icon:r(g2,{className:"text-font-primary"}),isHighlight:n===S.Recommends,label:e("common.recommends.title"),onSelect:()=>{s(S.Recommends)}})}),r(Me,{name:`${me}:${S.Link}_${K.Desktop}`,children:r($t,{highlightIcon:r(or,{}),icon:r(Yo,{}),isHighlight:n===S.Link,label:e("common.im_connect.title"),onSelect:()=>{ke(S.Link)}})}),r(Me,{name:`${me}:CustomerService_${K.Desktop}`,children:r($t,{highlightIcon:r(Qo,{}),icon:r(Vt,{}),label:e("common.service.title_short"),onSelect:()=>{M0(),Ge()}})}),r(Me,{name:`${me}:${S.Settings}_${K.Desktop}`,children:r($t,{highlightIcon:r(x2,{}),icon:r(v2,{}),isHighlight:n===S.Settings||n===S.Login,label:e("common.settings.title"),onSelect:()=>{ke(S.Settings)}})})]})},ii=()=>r("div",{className:"bg-surface-primary relative w-[7.5rem] rounded-lg px-2 py-6 grow-0 shrink-0",children:[r(ri,{}),r(si,{})]}),ai="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='42'%20height='42'%20fill='none'%3e%3ccircle%20cx='21'%20cy='21'%20r='21'%20fill='%23242629'/%3e%3cpath%20fill='%23fff'%20d='M20.856%2014.514c-.889%200-2.264-1.01-3.713-.974-1.911.025-3.664%201.108-4.65%202.825-1.985%203.445-.512%208.534%201.424%2011.334.95%201.363%202.07%202.897%203.555%202.848%201.424-.06%201.96-.925%203.688-.925%201.717%200%202.204.925%203.713.889%201.534-.025%202.508-1.388%203.446-2.764%201.083-1.582%201.534-3.116%201.558-3.201-.037-.013-2.983-1.145-3.02-4.554-.023-2.848%202.326-4.212%202.436-4.273-1.34-1.96-3.397-2.179-4.115-2.227-1.875-.147-3.445%201.022-4.322%201.022Zm3.165-2.873c.792-.95%201.315-2.276%201.169-3.591-1.132.048-2.496.755-3.311%201.704-.73.84-1.364%202.191-1.193%203.482%201.254.097%202.544-.645%203.335-1.595Z'/%3e%3c/svg%3e",li="data:image/svg+xml,%3csvg%20width='38'%20height='38'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23a)'%3e%3ccircle%20cx='19'%20cy='19'%20r='18'%20fill='%23BFBFBF'/%3e%3cpath%20d='M18.88%2013.44c-.77%200-1.94-.87-3.19-.84a4.7%204.7%200%200%200-3.98%202.43c-1.7%202.95-.44%207.31%201.22%209.71.81%201.17%201.77%202.48%203.05%202.44%201.22-.05%201.68-.8%203.16-.8%201.47%200%201.89.8%203.18.77%201.32-.02%202.15-1.19%202.95-2.37a10.48%2010.48%200%200%200%201.34-2.74%204.26%204.26%200%200%201-.5-7.57%204.52%204.52%200%200%200-3.53-1.9c-1.6-.13-2.95.87-3.7.87Zm2.71-2.46a4.22%204.22%200%200%200%201-3.08c-.97.04-2.14.65-2.84%201.46a4.03%204.03%200%200%200-1.02%202.98c1.08.09%202.18-.55%202.86-1.36'%20fill='%23fff'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20transform='translate(1%201)'%20d='M0%200h36v36H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",ci="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='32'%20height='32'%20fill='none'%3e%3cg%20clip-path='url(%23a)'%3e%3ccircle%20cx='16'%20cy='16'%20r='16'%20fill='%235865F2'/%3e%3cg%20clip-path='url(%23b)'%3e%3cpath%20d='M22.93%209.38a16.491%2016.491%200%200%200-4.07-1.263.062.062%200%200%200-.066.031c-.175.313-.37.72-.506%201.041a15.226%2015.226%200%200%200-4.573%200%2010.535%2010.535%200%200%200-.514-************%200%200%200-.066-.032%2016.447%2016.447%200%200%200-4.07%201.263.058.058%200%200%200-.028.023c-2.593%203.873-3.303%207.652-2.954%2011.383a.069.069%200%200%200%20.026.047%2016.586%2016.586%200%200%200%204.994%202.525.065.065%200%200%200%20.07-.023%2011.86%2011.86%200%200%200%201.022-1.662.063.063%200%200%200-.035-.088%2010.912%2010.912%200%200%201-1.56-.744.064.064%200%200%201-.006-.106c.104-.079.21-.16.31-.243a.062.062%200%200%201%20.064-.009c3.273%201.495%206.817%201.495%2010.051%200a.061.061%200%200%201%20.066.008c.*************.31.244a.064.064%200%200%201-.005.106c-.499.291-1.017.538-1.561.743a.064.064%200%200%200-.034.089%2013.3%2013.3%200%200%200%201.02%201.661.063.063%200%200%200%20.07.024%2016.532%2016.532%200%200%200%205.003-2.525.065.065%200%200%200%20.026-.046c.417-4.314-.699-8.061-2.957-11.383a.05.05%200%200%200-.026-.024Zm-10.247%209.134c-.985%200-1.797-.904-1.797-2.015%200-1.111.796-2.016%201.797-2.016%201.01%200%201.813.912%201.798%202.016%200%201.11-.796%202.015-1.798%202.015Zm6.646%200c-.986%200-1.797-.904-1.797-2.015%200-1.111.796-2.016%201.797-2.016%201.009%200%201.813.912%201.797%202.016%200%201.11-.788%202.015-1.797%202.015Z'%20fill='%23fff'/%3e%3c/g%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20d='M0%200h32v32H0z'/%3e%3c/clipPath%3e%3cclipPath%20id='b'%3e%3cpath%20fill='%23fff'%20transform='translate(6%208)'%20d='M0%200h20v15.493H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",B2="data:image/svg+xml,%3csvg%20width='36'%20height='36'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23a)'%3e%3ccircle%20cx='18'%20cy='18'%20r='18'%20fill='%23BFBFBF'/%3e%3cg%20clip-path='url(%23b)'%3e%3cpath%20d='M25.797%2010.55a18.556%2018.556%200%200%200-4.58-**********%200%200%200-.073.035c-.198.352-.417.81-.57%201.171a17.123%2017.123%200%200%200-5.144%200c-.154-.368-.38-.82-.58-1.171a.072.072%200%200%200-.073-.035%2018.505%2018.505%200%200%200-4.58%**************%200%200%200-.03.026c-2.917%204.358-3.716%208.609-3.324%2012.807.*************.03.053a18.658%2018.658%200%200%200%205.618%**************%200%200%200%20.079-.026c.432-.591.818-1.214%201.149-1.87a.071.071%200%200%200-.04-.099%2012.287%2012.287%200%200%201-1.754-.836.072.072%200%200%201-.007-.12%208.54%208.54%200%200%200%20.348-.273.07.07%200%200%201%20.073-.01c3.682%201.681%207.669%201.681%2011.307%200a.07.07%200%200%201%20.074.009c.**************.35.274.*************-.007.12-.56.327-1.143.604-1.756.835a.072.072%200%200%200-.038.1c.338.655.724%201.278%201.149%201.869.**************.079.027a18.597%2018.597%200%200%200%205.627-************%200%200%200%20.03-.052c.468-4.854-.787-9.07-3.328-12.807a.058.058%200%200%200-.029-.027ZM14.27%2020.828c-1.109%200-2.022-1.018-2.022-2.268%200-1.25.895-2.268%202.022-2.268%201.135%200%202.04%201.027%202.022%202.268%200%201.25-.896%202.268-2.022%202.268Zm7.476%200c-1.108%200-2.022-1.018-2.022-2.268%200-1.25.896-2.268%202.022-2.268%201.135%200%202.04%201.027%202.022%202.268%200%201.25-.887%202.268-2.022%202.268Z'%20fill='%23fff'/%3e%3c/g%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20d='M0%200h36v36H0z'/%3e%3c/clipPath%3e%3cclipPath%20id='b'%3e%3cpath%20fill='%23fff'%20transform='translate(6.75%209)'%20d='M0%200h22.5v17.43H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",di="data:image/svg+xml,%3csvg%20width='24'%20height='24'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23a)'%20fill-rule='evenodd'%20clip-rule='evenodd'%3e%3cpath%20d='M24%2012.27c0-.85-.08-1.67-.22-2.45H12.24v4.64h6.6a5.51%205.51%200%200%201-2.45%203.62v3.01h3.96C22.66%2019%2024%2015.93%2024%2012.27Z'%20fill='%234285F4'/%3e%3cpath%20d='M12.24%2024c3.31%200%206.08-1.07%208.1-2.9l-3.95-3.02a7.47%207.47%200%200%201-4.15%201.15%207.28%207.28%200%200%201-6.85-4.95H1.3v3.1A12.27%2012.27%200%200%200%2012.24%2024Z'%20fill='%2334A853'/%3e%3cpath%20d='M5.4%2014.28a7.08%207.08%200%200%201%200-4.56v-3.1H1.3a11.8%2011.8%200%200%200%200%2010.77l4.1-3.11Z'%20fill='%23FBBC05'/%3e%3cpath%20d='M12.24%204.77c1.8%200%203.42.6%204.69%201.8l3.5-3.44A11.9%2011.9%200%200%200%2012.25%200C7.46%200%203.32%202.69%201.3%206.61l4.1%203.11a7.28%207.28%200%200%201%206.84-4.95Z'%20fill='%23EA4335'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20d='M0%200h24v24H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",pi="data:image/svg+xml,%3csvg%20width='36'%20height='36'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23a)'%20fill-rule='evenodd'%20clip-rule='evenodd'%20fill='%23BFBFBF'%3e%3cpath%20d='M36%2018.4c0-1.27-.12-2.5-.33-3.67h-17.3v6.96h9.88a8.27%208.27%200%200%201-3.66%205.43v4.52h5.93C34%2028.5%2036%2023.89%2036%2018.4Z'/%3e%3cpath%20d='M18.37%2036c4.96%200%209.11-1.61%2012.15-4.36l-5.93-4.52a11.2%2011.2%200%200%201-6.22%201.72c-4.79%200-8.84-3.16-10.28-7.42H1.95v4.67A18.4%2018.4%200%200%200%2018.37%2036Z'/%3e%3cpath%20d='M8.09%2021.42a10.62%2010.62%200%200%201%200-6.84V9.92H1.95a17.7%2017.7%200%200%200%200%2016.17l6.14-4.67Z'/%3e%3cpath%20d='M18.37%207.16c2.7%200%205.11.9%207.02%202.7l5.27-5.17A17.85%2017.85%200%200%200%2018.36%200a18.4%2018.4%200%200%200-16.4%209.92l6.13%204.66a10.92%2010.92%200%200%201%2010.28-7.42Z'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20d='M0%200h36v36H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",fi="data:image/svg+xml,%3csvg%20width='24'%20height='24'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12'%20cy='12'%20r='12'%20fill='%23FFE812'/%3e%3cpath%20d='M12%205c-4.44%200-8.04%202.87-8.04%206.4%200%202.3%201.5%204.3%203.77%205.44-.12.43-.79%202.76-.82%202.94%200%200-.**********.08.05.19%200%20.19%200a43.3%2043.3%200%200%200%203.4-2.26c.**********%201.42.1%204.44%200%208.04-2.87%208.04-6.41S16.44%205%2012%205Z'%20fill='%23000'/%3e%3cpath%20d='M7.56%2013.65a.46.46%200%200%201-.47-.45V10.4h-.72a.46.46%200%200%201-.45-.46c0-.25.2-.46.45-.46h2.37c.26%200%***********.46%200%20.26-.2.46-.46.46h-.72v2.79c0%20.24-.2.45-.46.45Zm4.06-.01c-.2%200-.34-.08-.38-.2l-.23-.62H9.59l-.23.61c-.04.13-.19.2-.38.2a.7.7%200%200%201-.3-.06c-.12-.06-.25-.22-.1-.66l1.1-2.95a.7.7%200%200%201%20.62-.47c.**********.62.47l1.1%202.95c.**********-.1.66a.7.7%200%200%201-.3.07Zm-.86-1.65-.46-1.33L9.84%2012h.92Zm2.01%201.59a.44.44%200%200%201-.44-.43V9.96a.47.47%200%200%201%20.95%200v2.76h.98c.25%200%***********.43%200%20.23-.2.43-.44.43h-1.49Zm2.58.06a.47.47%200%200%201-.47-.47v-3.2c0-.27.21-.48.47-.48.25%200%***********.47v1.01l1.3-1.3c.06-.08.15-.11.25-.11.12%200%***********.***********.18.14.3%200%20.1-.03.2-.1.28l-1.06%201.07%201.14%201.53a.47.47%200%200%201%***********.47%200%200%201-.***********%200%200%201-.***********%200%200%201-.16-.14l-1.09-1.46-.16.17v1.02c0%20.12-.05.24-.13.33a.46.46%200%200%201-.33.14Z'%20fill='%23FFE812'/%3e%3c/svg%3e",G2="data:image/svg+xml,%3csvg%20width='24'%20height='24'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23a)'%3e%3ccircle%20cx='12'%20cy='12'%20r='12'%20fill='%23BFBFBF'/%3e%3cpath%20d='M12%205c-4.44%200-8.04%202.87-8.04%206.4%200%202.3%201.5%204.3%203.77%205.44-.12.43-.79%202.76-.82%202.94%200%200-.**********.08.05.19%200%20.19%200a43.3%2043.3%200%200%200%203.4-2.26c.**********%201.42.1%204.44%200%208.04-2.87%208.04-6.41S16.44%205%2012%205Z'%20fill='%23fff'/%3e%3cpath%20d='M7.56%2013.65a.46.46%200%200%201-.47-.45V10.4h-.72a.46.46%200%200%201-.45-.46c0-.25.2-.46.45-.46h2.37c.26%200%***********.46%200%20.26-.2.46-.46.46h-.72v2.79c0%20.24-.2.45-.46.45Zm4.06-.01c-.2%200-.34-.08-.38-.2l-.23-.62H9.59l-.23.61c-.04.13-.19.2-.38.2a.7.7%200%200%201-.3-.06c-.12-.06-.25-.22-.1-.66l1.1-2.95a.7.7%200%200%201%20.62-.47c.**********.62.47l1.1%202.95c.**********-.1.66a.7.7%200%200%201-.3.07Zm-.86-1.65-.46-1.33L9.84%2012h.92Zm2.01%201.59a.44.44%200%200%201-.44-.43V9.96a.47.47%200%200%201%20.95%200v2.76h.98c.25%200%***********.43%200%20.23-.2.43-.44.43h-1.49Zm2.58.06a.47.47%200%200%201-.47-.47v-3.2a.47.47%200%201%201%20.93%200v1l1.3-1.3c.06-.08.15-.11.25-.11.12%200%***********.***********.18.14.3%200%20.1-.03.2-.1.28l-1.06%201.07%201.14%201.53a.47.47%200%200%201%***********.47%200%200%201-.***********%200%200%201-.***********%200%200%201-.16-.14l-1.09-1.46-.16.17v1.02c0%20.12-.05.24-.13.33a.46.46%200%200%201-.33.14Z'%20fill='%23BFBFBF'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20d='M0%200h24v24H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",ui="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='42'%20height='42'%20fill='none'%3e%3ccircle%20cx='21'%20cy='21'%20r='21'%20fill='%2300B900'/%3e%3cg%20clip-path='url(%23a)'%3e%3cpath%20fill='%23fff'%20d='M34.97%2019.74C34.97%2013.49%2028.7%208.4%2021%208.4c-7.7%200-13.97%205.09-13.97%2011.34%200%205.6%204.97%2010.3%2011.68%2011.18.46.1%201.08.3%***********.**********%201.25l-.2%201.2c-.06.36-.28%201.39%201.21.76%201.5-.63%208.05-4.74%2010.98-8.11%202.02-2.23%202.99-4.48%202.99-6.98Z'/%3e%3cpath%20fill='%2300B900'%20d='M18.16%2016.72h-.98a.27.27%200%200%200-.27.27v6.09c0%***********.27.27h.98c.15%200%20.27-.12.27-.27v-6.1a.27.27%200%200%200-.27-.26Zm6.75%200h-.98a.27.27%200%200%200-.28.27v3.62l-2.79-3.77-.02-.03-.02-.02-.02-.01-.02-.02a.03.03%200%200%200-.02%200v-.01l-.02-.01-.03-.01h-.03l-.02-.01h-1a.27.27%200%200%200-.27.27v6.09c0%***********.27.27h.98c.15%200%20.27-.12.27-.27v-3.62l2.8%203.78a.21.21%200%200%200%20.06.06l.03.02h.02l.***********.07.01h.98c.15%200%20.27-.12.27-.27V17a.28.28%200%200%200-.27-.27Zm-9.11%205.1h-2.67V17a.27.27%200%200%200-.27-.27h-.98a.27.27%200%200%200-.27.27v6.09c0%***********.08.19a.28.28%200%200%200%20.2.08h3.9c.15%200%20.28-.12.28-.27v-.98a.27.27%200%200%200-.27-.28Zm14.52-3.58c.15%200%20.27-.12.27-.27v-.98a.27.27%200%200%200-.27-.27H26.4a.26.26%200%200%200-.19.07v.01a.28.28%200%200%200-.08.19v6.09c0%***********.08.19a.28.28%200%200%200%20.2.08h3.9c.16%200%20.28-.12.28-.27v-.98a.27.27%200%200%200-.27-.28h-2.67V20.8h2.67c.15%200%20.27-.12.27-.27v-.98a.27.27%200%200%200-.27-.27h-2.67v-1.03h2.67Z'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20d='M6.3%206.3h29.4v29.4H6.3z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",U2="data:image/svg+xml,%3csvg%20width='36'%20height='36'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M0%2018a18%2018%200%201%201%2036%200%2018%2018%200%200%201-36%200Z'%20fill='%23BFBFBF'/%3e%3cpath%20d='M18.3%208.4c-6.12%200-11.1%204.01-11.1%208.94%200%204.42%203.95%208.12%209.28%************.***********.***********.04%201l-.16.94c-.05.28-.22%***********%201.19-.5%206.4-3.74%208.72-6.4%201.6-1.76%202.38-3.53%202.38-5.5%200-4.94-4.98-8.95-11.1-8.95Z'%20fill='%23fff'/%3e%3cpath%20d='M15.21%2015c-.11%200-.21.1-.21.22v4.96c0%**********.21.22H16a.22.22%200%200%200%20.21-.22v-4.96a.22.22%200%200%200-.21-.22h-.78Zm6.16%200h-.8c-.13%200-.23.1-.23.22v2.95l-2.31-3.07a.24.24%200%200%200-.02-.02.2.2%200%200%200-.02-.02.17.17%200%200%200-.01-.01l-.02-.01-.02-.02h-.02a.12.12%200%200%200-.02-.01h-.05l-.01-.01h-.81c-.13%200-.23.1-.23.22v4.96c0%**********.23.22h.8a.22.22%200%200%200%20.23-.22v-2.95l2.32%203.08a.22.22%200%200%200%**********.1%200%200%200%20.02.01h.01a.21.21%200%200%200%20.02.01.23.23%200%200%200%20.06.01h.82a.22.22%200%200%200%20.22-.22v-4.96c0-.12-.1-.22-.22-.22Zm-7.19%204.16h-2.15v-3.94a.22.22%200%200%200-.22-.22h-.79a.22.22%200%200%200-.22.22v4.96a.22.22%200%200%200%20.06.16c.05.04.1.06.16.06h3.16c.12%200%20.22-.1.22-.22v-.8a.22.22%200%200%200-.22-.22Zm11.4-2.92a.22.22%200%200%200%20.22-.22v-.8a.22.22%200%200%200-.22-.22h-3.16a.22.22%200%200%200-.***********%200%200%200-.07.16v4.96a.22.22%200%200%200%***********.22%200%200%200%20.16.06h3.16a.22.22%200%200%200%20.22-.22v-.8a.22.22%200%200%200-.22-.22h-2.15v-.84h2.15c.12%200%20.22-.1.22-.22v-.8a.22.22%200%200%200-.22-.22h-2.15v-.84h2.15Z'%20fill='%23BFBFBF'/%3e%3c/svg%3e",mi="data:image/svg+xml,%3csvg%20width='32'%20height='32'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23a)'%3e%3cpath%20d='M16%200C6.99%200%200%206.6%200%2015.52c0%204.66%201.91%208.7%205.02%2011.***********.56.43.91l.09%202.85a1.28%201.28%200%200%200%201.8%201.13l3.17-1.4c.27-.12.57-.14.86-.06%201.46.4%203.01.61%204.63.61%209.01%200%2016-6.6%2016-15.52C32%206.61%2025.01%200%2016%200Z'%20fill='url(%23b)'/%3e%3cpath%20d='m6.4%2020.06%204.7-7.46a2.4%202.4%200%200%201%203.46-.64l3.74%202.8a.96.96%200%200%200%201.16%200l5.05-3.83c.67-.51%201.55.3%201.1%201.01l-4.7%207.46a2.4%202.4%200%200%201-3.47.64l-3.74-2.8a.96.96%200%200%200-1.16%200l-5.05%203.83c-.67.51-1.55-.3-1.1-1.01Z'%20fill='%23fff'/%3e%3c/g%3e%3cdefs%3e%3cradialGradient%20id='b'%20cx='0'%20cy='0'%20r='1'%20gradientUnits='userSpaceOnUse'%20gradientTransform='matrix(35.2%200%200%2035.2%205.36%2032)'%3e%3cstop%20stop-color='%2309F'/%3e%3cstop%20offset='.6'%20stop-color='%23A033FF'/%3e%3cstop%20offset='.9'%20stop-color='%23FF5280'/%3e%3cstop%20offset='1'%20stop-color='%23FF7061'/%3e%3c/radialGradient%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20d='M0%200h32v32H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",D2="data:image/svg+xml,%3csvg%20width='32'%20height='32'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23a)'%3e%3cpath%20d='M16%200C6.99%200%200%206.6%200%2015.52c0%204.66%201.91%208.7%205.02%2011.***********.56.43.91l.09%202.85a1.28%201.28%200%200%200%201.8%201.13l3.17-1.4c.27-.12.57-.14.86-.06%201.46.4%203.01.61%204.63.61%209.01%200%2016-6.6%2016-15.52C32%206.61%2025.01%200%2016%200Z'%20fill='%23BFBFBF'/%3e%3cpath%20d='m6.4%2020.06%204.7-7.46a2.4%202.4%200%200%201%203.46-.64l3.74%202.8a.96.96%200%200%200%201.16%200l5.05-3.83c.67-.51%201.55.3%201.1%201.01l-4.7%207.46a2.4%202.4%200%200%201-3.47.64l-3.74-2.8a.96.96%200%200%200-1.16%200l-5.05%203.83c-.67.51-1.55-.3-1.1-1.01Z'%20fill='%23fff'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20d='M0%200h32v32H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",hi="data:image/svg+xml,%3csvg%20width='24'%20height='24'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23a)'%3e%3cpath%20d='M24%2012a12%2012%200%201%201-24%200%2012%2012%200%200%201%2024%200Zm-9.993-5.64v6.033l-4.2-6.033H6.36v11.295h3.63v-6.051l4.2%206.036h3.46V6.36h-3.643Z'%20fill='%2300DE5A'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20d='M0%200h24v24H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",$2="data:image/svg+xml,%3csvg%20width='24'%20height='24'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23a)'%3e%3cpath%20d='M24%2012a12%2012%200%201%201-24%200%2012%2012%200%200%201%2024%200ZM14%206.36v6.03L9.8%206.36H6.37v11.3h3.63V11.6l4.2%206.04h3.46V6.36H14Z'%20fill='%23BFBFBF'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20d='M0%200h24v24H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",gi="data:image/svg+xml,%3csvg%20width='32'%20height='32'%20viewBox='0%200%2032%2032'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_674_3202)'%3e%3ccircle%20cx='16'%20cy='16'%20r='16'%20fill='black'/%3e%3c/g%3e%3cpath%20d='M14.997%2014.9406C13.947%2014.9406%2013.0599%2014.5548%2012.3357%2013.7881C11.6113%2013.0212%2011.2471%2012.082%2011.2471%2010.9704C11.2471%209.85857%2011.6113%208.91933%2012.3357%208.15269C13.0599%207.38577%2013.947%207%2014.997%207C16.047%207%2016.9341%207.38577%2017.6586%208.15269C18.3827%208.91933%2018.7471%209.85857%2018.7471%2010.9704C18.7471%2012.082%2018.3827%2013.0212%2017.6586%2013.7881C16.9341%2014.5548%2016.047%2014.9406%2014.997%2014.9406ZM22.2485%2025L20.997%2023.412V19.5189C20.3972%2019.2739%2019.9128%2018.8791%2019.5487%2018.339C19.1843%2017.7991%2019%2017.1956%2019%2016.5242C19%2015.6438%2019.2914%2014.8907%2019.8742%2014.2736C20.4571%2013.6564%2021.1643%2013.3479%2022%2013.3479C22.8357%2013.3479%2023.5429%2013.6564%2024.1257%2014.2736C24.7086%2014.8907%2025%2015.6394%2025%2016.5242C25%2017.1956%2024.8157%2017.7991%2024.4513%2018.339C24.0872%2018.8791%2023.6028%2019.2693%2023.0027%2019.5189V19.7048L24.0014%2020.762L23.0027%2021.8194L24.0014%2022.8765L22.2529%2024.9954L22.2485%2025ZM22%2018.1168C22.4158%2018.1168%2022.7715%2017.9625%2023.0629%2017.654C23.3543%2017.3453%2023.5%2016.9688%2023.5%2016.5286C23.5%2016.0886%2023.3543%2015.7118%2023.0629%2015.4034C22.7715%2015.0949%2022.4158%2014.9406%2022%2014.9406C21.5842%2014.9406%2021.2285%2015.0949%2020.9371%2015.4034C20.6457%2015.7118%2020.5%2016.0886%2020.5%2016.5286C20.5%2016.9688%2020.6457%2017.3453%2020.9371%2017.654C21.2285%2017.9625%2021.5842%2018.1168%2022%2018.1168ZM17.05%2017.2184C17.1315%2017.9399%2017.3456%2018.6022%2017.6886%2019.2058C18.0323%2019.8064%2018.4767%2020.3354%2019%2020.7666V23.9429H7V21.429C7%2020.83%207.15856%2020.2812%207.47567%2019.7865C7.7841%2019.3008%208.20643%2018.9084%208.70122%2018.6477C9.68289%2018.1168%2010.707%2017.7221%2011.7657%2017.4588C12.8261%2017.1939%2013.9119%2017.0599%2015.0014%2017.0594C15.3357%2017.0594%2015.6785%2017.0731%2016.0257%2017.1003C16.377%2017.1275%2016.7157%2017.1683%2017.05%2017.2184Z'%20fill='white'/%3e%3cdefs%3e%3cclipPath%20id='clip0_674_3202'%3e%3crect%20width='32'%20height='32'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",vi="data:image/svg+xml,%3csvg%20width='32'%20height='32'%20viewBox='0%200%2032%2032'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_674_3137)'%3e%3ccircle%20cx='16'%20cy='16'%20r='16'%20fill='%23BFBFBF'/%3e%3c/g%3e%3cpath%20d='M14.997%2014.9406C13.947%2014.9406%2013.0599%2014.5548%2012.3357%2013.7881C11.6113%2013.0212%2011.2471%2012.082%2011.2471%2010.9704C11.2471%209.85857%2011.6113%208.91933%2012.3357%208.15269C13.0599%207.38577%2013.947%207%2014.997%207C16.047%207%2016.9341%207.38577%2017.6586%208.15269C18.3827%208.91933%2018.7471%209.85857%2018.7471%2010.9704C18.7471%2012.082%2018.3827%2013.0212%2017.6586%2013.7881C16.9341%2014.5548%2016.047%2014.9406%2014.997%2014.9406ZM22.2485%2025L20.997%2023.412V19.5189C20.3972%2019.2739%2019.9128%2018.8791%2019.5487%2018.339C19.1843%2017.7991%2019%2017.1956%2019%2016.5242C19%2015.6438%2019.2914%2014.8907%2019.8742%2014.2736C20.4571%2013.6564%2021.1643%2013.3479%2022%2013.3479C22.8357%2013.3479%2023.5429%2013.6564%2024.1257%2014.2736C24.7086%2014.8907%2025%2015.6394%2025%2016.5242C25%2017.1956%2024.8157%2017.7991%2024.4513%2018.339C24.0872%2018.8791%2023.6028%2019.2693%2023.0027%2019.5189V19.7048L24.0014%2020.762L23.0027%2021.8194L24.0014%2022.8765L22.2529%2024.9954L22.2485%2025ZM22%2018.1168C22.4158%2018.1168%2022.7715%2017.9625%2023.0629%2017.654C23.3543%2017.3453%2023.5%2016.9688%2023.5%2016.5286C23.5%2016.0886%2023.3543%2015.7118%2023.0629%2015.4034C22.7715%2015.0949%2022.4158%2014.9406%2022%2014.9406C21.5842%2014.9406%2021.2285%2015.0949%2020.9371%2015.4034C20.6457%2015.7118%2020.5%2016.0886%2020.5%2016.5286C20.5%2016.9688%2020.6457%2017.3453%2020.9371%2017.654C21.2285%2017.9625%2021.5842%2018.1168%2022%2018.1168ZM17.05%2017.2184C17.1315%2017.9399%2017.3456%2018.6022%2017.6886%2019.2058C18.0323%2019.8064%2018.4767%2020.3354%2019%2020.7666V23.9429H7V21.429C7%2020.83%207.15856%2020.2812%207.47567%2019.7865C7.7841%2019.3008%208.20643%2018.9084%208.70122%2018.6477C9.68289%2018.1168%2010.707%2017.7221%2011.7657%2017.4588C12.8261%2017.1939%2013.9119%2017.0599%2015.0014%2017.0594C15.3357%2017.0594%2015.6785%2017.0731%2016.0257%2017.1003C16.377%2017.1275%2016.7157%2017.1683%2017.05%2017.2184Z'%20fill='white'/%3e%3cdefs%3e%3cclipPath%20id='clip0_674_3137'%3e%3crect%20width='32'%20height='32'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",wi="data:image/svg+xml,%3csvg%20width='24'%20height='25'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M12.005%200C5.385%200%20.002%205.38%200%2011.995a11.965%2011.965%200%200%200%201.835%206.384l.285.453L.908%2023.26l4.541-1.191.439.26A11.985%2011.985%200%200%200%2011.996%2024H12c6.614%200%2011.997-5.381%2012-11.996a11.919%2011.919%200%200%200-3.512-8.486A11.922%2011.922%200%200%200%2012.005%200Z'%20fill='url(%23a)'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M8.068%205.697c-.295-.663-.605-.676-.886-.688L6.428%205c-.263%200-.69.1-1.05.498C5.018%205.896%204%206.858%204%208.816s1.41%203.85%201.607%204.116c.197.266%202.724%204.411%206.725%206.007%203.325%201.325%204.002%201.061%204.724.995.722-.066%202.33-.962%202.657-1.892.328-.929.328-1.725.23-1.892-.099-.166-.361-.265-.755-.464-.394-.2-2.33-1.162-2.69-1.295-.36-.133-.623-.198-.886.2-.262.398-1.016%201.294-1.246%201.56-.23.265-.459.299-.853.1-.394-.2-1.662-.62-3.166-1.975-1.17-1.055-1.96-2.358-2.19-2.756-.23-.398-.024-.613.173-.812.177-.178.394-.464.591-.697.197-.232.262-.398.393-.663s.066-.498-.033-.697c-.098-.199-.863-2.167-1.213-2.954Z'%20fill='%23fff'/%3e%3cdefs%3e%3clinearGradient%20id='a'%20x1='11.756'%20y1='1.44'%20x2='11.877'%20y2='21.95'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%2357D163'/%3e%3cstop%20offset='1'%20stop-color='%2323B33A'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e",Z2="data:image/svg+xml,%3csvg%20width='32'%20height='32'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23a)'%3e%3cg%20clip-path='url(%23b)'%20fill='%23BFBFBF'%3e%3cpath%20d='M31.292%2015.279A15.347%2015.347%200%200%200%2016.046.492h-.075a15.35%2015.35%200%200%200-13.48%2022.712l-1.358%208.122a.118.118%200%200%200%20.117.134h.023l8.033-1.787a15.357%2015.357%200%200%200%206.651%201.513h.44a15.343%2015.343%200%200%200%2014.895-15.907ZM16.32%2028.517h-.363a12.689%2012.689%200%200%201-5.847-1.42l-.411-.215L4.25%2028.17l1.005-5.511-.233-.395A12.688%2012.688%200%200%201%2015.607%203.16h.369a12.684%2012.684%200%200%201%20.358%2025.361l-.014-.004Z'/%3e%3cpath%20d='M10.621%208.43a1.419%201.419%200%200%200-1.008.45c-.366.375-1.389%201.278-1.448%203.174-.06%201.896%201.265%203.771%201.45%204.034.186.263%202.533%204.358%206.4%206.02%202.272.978%203.268%201.146%203.914%201.146.266%200%20.467-.028.677-.04.708-.044%202.307-.862%202.655-1.753.349-.89.372-1.668.28-1.824-.091-.155-.343-.268-.723-.467-.38-.2-2.243-1.195-2.593-1.335-.13-.06-.27-.097-.413-.108a.573.573%200%200%200-.459.274c-.311.388-1.026%201.23-1.266%201.473a.57.57%200%200%201-.42.196%201.006%201.006%200%200%201-.422-.114%209.827%209.827%200%200%201-3.033-2.023%2011.629%2011.629%200%200%201-2.06-2.741c-.212-.392%200-.595.192-.778.193-.184.4-.438.6-.657.163-.188.3-.397.404-.623a.71.71%200%200%200-.01-.675c-.093-.196-.779-2.125-1.101-2.899-.262-.661-.573-.683-.845-.703a19.843%2019.843%200%200%200-.738-.031h-.033'/%3e%3c/g%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20d='M0%200h32v32H0z'/%3e%3c/clipPath%3e%3cclipPath%20id='b'%3e%3cpath%20fill='%23fff'%20d='M0%200h32v32H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",yi="data:image/svg+xml,%3csvg%20width='24'%20height='24'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23a)'%3e%3ccircle%20cx='12'%20cy='12'%20r='12'%20fill='%23000'/%3e%3cpath%20d='M16.23%206h1.99l-4.38%205.02%205.12%206.8h-4.02l-3.14-4.14-3.6%204.14H6.22l4.64-5.37L5.95%206h4.11l2.84%203.78L16.23%206Zm-.7%2010.64h1.1l-7.15-9.5H8.3l7.23%209.5Z'%20fill='%23fff'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20d='M0%200h24v24H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",xi="data:image/svg+xml,%3csvg%20width='36'%20height='36'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23a)'%3e%3ccircle%20cx='18'%20cy='18'%20r='18'%20fill='%23BFBFBF'/%3e%3cpath%20d='M24.34%209h2.99l-6.56%207.52%207.66%2010.2h-6.01l-4.72-6.2-5.4%206.2H9.33l6.95-8.04L8.93%209h6.17l4.25%205.66L24.34%209Zm-1.05%2015.96h1.66L14.23%2010.7h-1.79L23.3%2024.96Z'%20fill='%23fff'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='a'%3e%3cpath%20fill='%23fff'%20d='M0%200h36v36H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",lt={passkey:{grayIcon:vi,lightIcon:gi,label:"Passkey"},line_message:{grayIcon:U2,lightIcon:ui,label:"Line"},facebook_message:{grayIcon:D2,lightIcon:mi,label:"Messenger"},discord_message:{grayIcon:B2,lightIcon:ci,label:"Discord"},whatsapp_message:{grayIcon:Z2,lightIcon:wi,label:"Whatsapp"},kakao_message:{grayIcon:G2,lightIcon:fi,label:"Kakao"},naver_message:{grayIcon:$2,lightIcon:hi,label:"Naver"},twitter:{grayIcon:xi,lightIcon:yi,label:"X"},google:{grayIcon:pi,lightIcon:di,label:"Google"},apple:{grayIcon:li,lightIcon:ai,label:"Apple"}},G0=({imType:e,variant:t})=>{const{t:n}=P(),o=M(()=>{Lo(!1),M0({openSetting:!0})},[]);return r("div",{className:"flex flex-row items-center",children:[r("div",{children:[r("div",{children:n("common.im_connect.toast_bind_title",{imTypeName:e?lt[e].label:"SNS"})}),r("div",{children:n("common.im_connect.toast_desc")})]}),r(he,{type:"link",className:t==="dark"?"text-font-overlay":"",onClick:o,children:n("common.im_connect.toast_button_text")})]})},U0=e=>{const{isSns:t,isLinked:n,name:o,showChatIcon:s}=e,{t:i}=P(),a=()=>{if(n){t?pt.success(i("common.account.links.already_success",{snsName:o}),{variant:"dark"}):pt.success(r(G0,{imType:o,variant:"dark"}),{variant:"dark"});return}t2(n2.Profile,o,window.option.appId)},l=c=>{c.stopPropagation(),c.preventDefault()};return r("div",{className:"basis-1/3 h-[4.25rem] flex flex-col items-center justify-center",onClick:a,children:[r("div",{className:"relative",children:[r("img",{alt:"IM-tool-icon",className:"size-8",src:n?lt[o].lightIcon:lt[o].grayIcon}),n&&r(Po,{containerClassName:"absolute -top-[0.6rem] -right-[0.475rem] scale-[0.67]",style:{color:"rgba(19, 108, 114, 1)"}})]}),r("div",{className:"relative mt-2 flex items-center",children:[r("label",{className:"font-semibold text-xs",children:lt[o].label}),!t&&s&&r("div",{onClick:l,className:"absolute top-0 right-[-1.125rem] mt-[0.186rem]",children:r(I2,{content:i("common.im_connect.chat_tip",{imTypeName:lt[o].label}),id:`${o}-chat`,place:"bottom",style:{height:"2rem",fontSize:"0.625rem",lineHeight:"0.875rem",fontWeight:600,verticalAlign:"middle"},children:r(Vt,{containerClassName:"cursor-pointer",viewBox:"0 0 24 24",width:14,height:14,style:{color:"rgba(187, 187, 187, 1)"}})})})]})]})},D0=()=>{const{imConnectedStatusList:e}=G(s=>s.imConnect),{authProviders:t}=G(s=>s.mainPopup),[n,o]=I(null);return _(()=>{const s=["passkey"],i=["google","apple","twitter"],a=f=>({socialType:f,isSns:!0,isLinked:!!t?.includes(f)}),l=s.map(a),c=i.map(a),d=e.map(f=>({socialType:f.imType,isSns:!1,isLinked:f.isLinked}));o([...l,...d,...c])},[t,e]),{supportedSocialTypeConnectedStatus:n}},bi=()=>{const{supportedSocialTypeConnectedStatus:e}=D0(),{t}=P();return e?r("div",{className:"h-fit w-[25rem] select-none px-6 pb-6",children:[r("h1",{className:"pt-6 pb-[1.125rem] text-center font-semibold",children:t("common.im_connect.title")}),r("div",{className:"flex flex-wrap gap-y-8",children:e.map(n=>r(U0,{name:n.socialType,isSns:n.isSns,isLinked:n.isLinked,showChatIcon:!0},n.socialType))})]}):null},$0=()=>{const{authProviders:e}=G(n=>n.mainPopup);return{isLinked:e.length>0}},H2=()=>{const{t:e}=P(),{isLinked:t}=$0();return r("span",{className:"select-none text-xs",children:e(t?"common.account.login.linked_description":"common.account.login.unlinked_description")})},Xn=()=>r("div",{className:"w-[0.03125rem] h-2 mx-2 bg-gray-300"}),z2=e=>{const{containerClassName:t,platform:n}=e,{t:o,i18n:s}=P(),i=()=>{window.open(o("common.terms.link"),"_blank","noopener")},a=()=>{window.open("https://g123.jp/privacy_policy","_blank","noopener")},l=()=>{window.open("https://g123.jp/houchi-game/c/tokusyo?lang=ja","_blank","noopener")};return r(Me,{name:`${me}:${S.Terms}_${n}`,children:r("div",{className:`${t} w-full inline-flex justify-center items-center`,children:[r("span",{className:"text-neutral-400 text-xs font-light font-['Hiragino Sans']",onClick:i,children:o("common.terms.title")}),r(Xn,{}),r("span",{className:"text-neutral-400 text-xs font-light font-['Hiragino Sans']",onClick:a,children:o("common.terms.privacy_policy.title")}),s.language==="ja"&&r(mt,{children:[r(Xn,{}),r("span",{className:"text-neutral-400 text-xs font-light font-['Hiragino Sans']",onClick:l,children:o("common.terms.tokusyo.title")})]})]})})},Ai=()=>{const{t:e}=P(),t=[{name:"passkey",label:"Passkey"},{name:"line",label:"Line"},{name:"google",label:"Google"},{name:"twitter",label:"X"},{name:"facebook",label:"Facebook"},{name:"apple",label:"Apple"}],n=M(o=>{o2().login(o)},[]);return r("div",{className:"gap-md flex flex-col mb-10",children:[r("h2",{className:"font-semibold",children:e("common.account.login.title_short")}),r(H2,{}),r("div",{className:"flex justify-between",children:t.map(o=>r("div",{className:"w-15 gap-y-xs flex flex-col justify-center py-2",children:[r("div",{className:"mx-auto w-10",children:r(f2,{name:o.name,onClick:()=>{n(o.name)}})}),r("h3",{className:"text-xxs select-none text-center font-semibold",children:o.label})]},o.name))})]})},_i=()=>{const{t:e,i18n:t}=P();return r("div",{className:"mt-10 mb-9 gap-md flex flex-col",children:[r("h2",{className:"font-semibold",children:e("common.account.recovery.title")}),r("span",{className:"select-none text-xs",children:e("common.account.recovery.description")}),r(he,{size:"small",type:"stroke",onClick:()=>{window.open(`${ht.SHD_G123_GAME_URL}/game/findmyaccount?lang=${t.language}&appCode=${window.option.appId}`)},children:e("common.account.recovery.title_short")})]})},Qn=()=>{const{t:e}=P();return r("div",{className:"h-fit w-[25rem] select-none px-6 pb-6",children:[r("h1",{className:"pt-6 pb-5 text-center font-semibold",children:e("common.settings.title")}),r(Ai,{}),r(_i,{}),r(z2,{containerClassName:"border-t border-line-divider pt-5",platform:K.Desktop})]})};var Ft=(e=>(e.DEVELOPING="DEVELOPING",e.PREPARING="PREPARING",e.RESERVABLE="RESERVABLE",e.PUBLISHED="PUBLISHED",e.CLOSED="CLOSED",e.DELETED="DELETED",e))(Ft||{}),dt=(e=>(e.HOT="HOT",e.NEW="NEW",e.NORMAL="NORMAL",e))(dt||{});const Ci="data:image/svg+xml,%3csvg%20width='27'%20height='16'%20viewBox='0%200%2027%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M0%208C0%205.19974%200%203.79961%200.544967%202.73005C1.02433%201.78924%201.78924%201.02433%202.73005%200.544967C3.79961%200%205.19974%200%208%200H19C21.8003%200%2023.2004%200%2024.27%200.544967C25.2108%201.02433%2025.9757%201.78924%2026.455%202.73005C27%203.79961%2027%205.19974%2027%208C27%2010.8003%2027%2012.2004%2026.455%2013.27C25.9757%2014.2108%2025.2108%2014.9757%2024.27%2015.455C23.2004%2016%2021.8003%2016%2019%2016H8C5.19974%2016%203.79961%2016%202.73005%2015.455C1.78924%2014.9757%201.02433%2014.2108%200.544967%2013.27C0%2012.2004%200%2010.8003%200%208Z'%20fill='%23FF385C'/%3e%3cpath%20d='M4.70199%2011.5V4.5H6.02199V7.39H8.62199V4.5H9.94199V11.5H8.62199V8.59H6.02199V11.5H4.70199ZM13.9845%2011.64C13.1045%2011.64%2012.4045%2011.4%2011.8845%2010.92C11.3645%2010.4333%2011.1045%209.74%2011.1045%208.84V7.16C11.1045%206.26%2011.3645%205.57%2011.8845%205.09C12.4045%204.60333%2013.1045%204.36%2013.9845%204.36C14.8645%204.36%2015.5645%204.60333%2016.0845%205.09C16.6045%205.57%2016.8645%206.26%2016.8645%207.16V8.84C16.8645%209.74%2016.6045%2010.4333%2016.0845%2010.92C15.5645%2011.4%2014.8645%2011.64%2013.9845%2011.64ZM13.9845%2010.46C14.4778%2010.46%2014.8612%2010.3167%2015.1345%2010.03C15.4078%209.74333%2015.5445%209.36%2015.5445%208.88V7.12C15.5445%206.64%2015.4078%206.25667%2015.1345%205.97C14.8612%205.68333%2014.4778%205.54%2013.9845%205.54C13.4978%205.54%2013.1145%205.68333%2012.8345%205.97C12.5612%206.25667%2012.4245%206.64%2012.4245%207.12V8.88C12.4245%209.36%2012.5612%209.74333%2012.8345%2010.03C13.1145%2010.3167%2013.4978%2010.46%2013.9845%2010.46ZM19.3591%2011.5V5.7H17.3191V4.5H22.7191V5.7H20.6791V11.5H19.3591Z'%20fill='white'/%3e%3c/svg%3e",ki="data:image/svg+xml,%3csvg%20width='27'%20height='16'%20viewBox='0%200%2027%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M0%208C0%205.19974%200%203.79961%200.544967%202.73005C1.02433%201.78924%201.78924%201.02433%202.73005%200.544967C3.79961%200%205.19974%200%208%200H19C21.8003%200%2023.2004%200%2024.27%200.544967C25.2108%201.02433%2025.9757%201.78924%2026.455%202.73005C27%203.79961%2027%205.19974%2027%208C27%2010.8003%2027%2012.2004%2026.455%2013.27C25.9757%2014.2108%2025.2108%2014.9757%2024.27%2015.455C23.2004%2016%2021.8003%2016%2019%2016H8C5.19974%2016%203.79961%2016%202.73005%2015.455C1.78924%2014.9757%201.02433%2014.2108%200.544967%2013.27C0%2012.2004%200%2010.8003%200%208Z'%20fill='%23E3FF34'/%3e%3cpath%20d='M3.54965%2011.5V4.5H6.05965L7.44965%2010.6H7.62965V4.5H8.92965V11.5H6.41965L5.02965%205.4H4.84965V11.5H3.54965ZM10.2489%2011.5V4.5H14.7489V5.7H11.5689V7.37H14.4689V8.57H11.5689V10.3H14.8089V11.5H10.2489ZM16.346%2011.5L15.426%204.5H16.736L17.416%2010.58H17.596L18.476%204.5H20.756L21.636%2010.58H21.816L22.496%204.5H23.806L22.886%2011.5H20.606L19.706%205.28H19.526L18.626%2011.5H16.346Z'%20fill='black'/%3e%3c/svg%3e",V2=({iconUrl:e,highlightType:t})=>r("div",{className:"relative size-full overflow-hidden rounded-lg bg-gray-300",children:[r("img",{alt:"",className:"pointer-events-none absolute size-full",src:e}),t===dt.NEW&&r("img",{alt:"",className:"pointer-events-none absolute right-[3px] top-[3px]",src:ki}),t===dt.HOT&&r("img",{alt:"",className:"pointer-events-none absolute right-[3px] top-[3px]",src:Ci})]}),Si=({game:e,highlightType:t,platform:n})=>{const o=M(()=>{if(e.appId.toString()===window.option.appId.toString())window.open(e.gameUrl);else if(e.status.toString()===Ft.PUBLISHED||e.status.toString()===Ft.RESERVABLE){const s=new URL(e.gameUrl);s.searchParams.set("platform",`game-${window.option.appId}`),s.searchParams.set("utm_source","g123-game"),s.searchParams.set("utm_campaign","float-panel"),s.searchParams.set("utm_medium",`game-${window.option.appId}`),window.open(s.href)}ze({action:"p_click",data:{display_name:window.option.userId,click:`${T2}:${e.appId}_${n}`,campaign:{},providers:window.option.providers}})},[e.appId,e.gameUrl,e.status,n]);return r("div",{"aria-hidden":"true",className:`game-item ${H("flex items-center gap-x-3","h-[4.5rem] w-full","box-border border-b border-dashed border-gray-100")}`,role:"button",onClick:o,children:[r("div",{"aria-hidden":"true",className:"size-12 shrink-0",role:"button",children:r(V2,{highlightType:t,iconUrl:i2(e.iconUrl,["w-72","h-72"])})}),r("div",{className:"grow items-start justify-start",children:[r("h3",{className:"h-6 select-none text-sm font-bold",children:e.shortTitle}),r("p",{className:"min-h-6 select-none text-xs",children:e.descriptionShort})]})]})},Ni="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='42'%20height='8'%20fill='none'%3e%3cg%20fill='%23E3FF34'%20opacity='.6'%3e%3cpath%20d='M4%200h26l-4%208H0l4-8ZM32%200h6l-4%208h-6l4-8ZM40%200h2l-4%208h-2l4-8Z'/%3e%3c/g%3e%3c/svg%3e",j2=({className:e,children:t})=>r("div",{className:k0("relative mb-2 text-base font-semibold",e),children:[r("img",{alt:"underline",className:"absolute bottom-0.5 z-0",src:Ni}),r("h2",{className:"relative z-10 px-1",children:t})]}),W2=({games:e,platform:t})=>{const{t:n}=P(),{hotGameCodes:o,newGameCodes:s}=G(i=>i.mainPopup);return r("div",{children:[r(j2,{children:n("common.recommends.hot_games")}),r("div",{className:H("flex flex-1 flex-wrap justify-center","max-h-96 w-full"),children:e.map(i=>{let a=dt.NORMAL;return o.includes(i.appId)&&(a=dt.HOT),s.includes(i.appId)&&(a=dt.NEW),r(Si,{game:i,highlightType:a,platform:t},i.appId)})})]})},Li=({game:e,currentState:t,onCreatePreEntry:n,onCancelPreEntry:o})=>{const{t:s}=P(),i=!!t?.isRegistered,a=M(()=>{if(e.appId.toString()===window.option.appId.toString())window.open(e.gameUrl);else{const d=new URL(e.gameUrl);d.searchParams.set("platform",`game-${window.option.appId}`),d.searchParams.set("utm_source","g123-game"),d.searchParams.set("utm_campaign","float-panel"),d.searchParams.set("utm_medium",`game-${window.option.appId}`),window.open(d.href)}ze({action:"p_click",data:{display_name:window.option.userId,click:`${T2}:${e.appId}_${K.Desktop}`,campaign:{},providers:window.option.providers}})},[e.appId,e.gameUrl]),l=M(async()=>n(e.appId),[e.appId,n]),c=M(async()=>o(e.appId),[e.appId,o]);return r("div",{className:"box-border flex w-24 shrink-0 grow-0 basis-24 flex-col items-center justify-center",children:[r("div",{"aria-hidden":"true",className:`game-item ${H("relative","size-16","rounded-lg bg-gray-300")}`,role:"button",onClick:a,children:r(V2,{iconUrl:i2(e.iconUrl,["w-64","h-64"])})}),r("h3",{className:H("my-1 w-full","truncate text-center text-xs font-light","select-none"),children:e.shortTitle}),r(he,{size:"small",type:i?"secondary":"highlight",onClick:i?c:l,children:s(i?"common.actions.pre_register.done":"common.actions.pre_register.title")})]})},Y2=({games:e})=>{const{t,i18n:n}=P(),o=L0.useMediaLayout({maxWidth:F0}),s=G(u=>u.preEntry.popupPreRegisterationStatus),i=Z(null),a=Ye(),l=G(u=>u.preEntry.preEntryStateMap),c=M(async()=>{await Promise.allSettled(e.map(u=>a(fo({appId:u.appId}))))},[e,a]),d=M(u=>{i.current&&i.current.scrollBy(u,0)},[]),f=M(async u=>a(ao({appId:u,lang:n.language})),[a,n.language]),p=M(async u=>a(lo({appId:u,lang:n.language})),[a,n.language]);return _(()=>{s&&s?.after!==s?.before&&(s?.after?pt.success(t("common.actions.pre_register.success"),{variant:"dark"}):pt.success(t("common.actions.pre_register.cancel_success"),{variant:"dark"}),co.dispatch(po.actions.dismissPopupPreRegisterationStatus()))},[s,t]),_(()=>{c()},[c]),r("div",{className:"mb-5",children:[r("div",{className:"flex justify-center",children:[r(j2,{className:"flex-1",children:t("common.recommends.upcoming_games")}),!o&&r("div",{className:"flex gap-x-1",children:[r(he,{className:"p-0!",icon:r(u2,{}),size:"small",type:"text",onClick:()=>d(-100)}),r(he,{className:"p-0!",icon:r(m2,{}),size:"small",type:"text",onClick:()=>d(100)})]})]}),r("div",{ref:i,className:"scrollbar-none flex overflow-x-scroll scroll-smooth",children:l&&[...e.filter(u=>!l[u.appId]),...e.filter(u=>l[u.appId])].map(u=>r(Li,{game:u,currentState:l[u.appId],onCreatePreEntry:f,onCancelPreEntry:p},u.appId))})]})},Fi=({children:e})=>r("div",{className:"flex max-h-[40rem] w-[25rem] flex-col p-6",children:e}),Ei=()=>{const{t:e}=P();return r("div",{className:"mb-4 flex justify-center items-center",children:r("h1",{className:"relative flex select-none items-center justify-center -my-1 py-1",onClick:()=>{const o=new URL(window.location.href).searchParams.get("lang"),s=`${ht.SHD_G123_WEB_URL}?lang=${o}`;S0("misc_logo_click",{data:{position:"recommend_panel",device:"desktop"}}),window.open(s,"_blank")},children:[r(E0,{}),r("span",{className:"ml-1 select-none font-extrabold",children:e("common.recommends.title")})]})})},Mi=()=>{const{recommendGames:e,preregists:t}=G(n=>n.mainPopup);return r(Fi,{children:[r(Ei,{}),r("div",{className:"scrollbar-none overflow-y-scroll",children:[t&&t.length>0&&r(Y2,{games:t}),r(W2,{games:e?.filter(n=>n.status!==Ft.RESERVABLE)??[],platform:K.Desktop})]})]})},Ri=({children:e})=>{const{isOpen:t}=G(s=>s.mainPopup),[n,o]=I(!1);return _(()=>{n!==t&&setTimeout(()=>{o(t)},1e3)},[t,n]),r("div",{className:"transition-all duration-300 ease-out",children:e})},Oi=()=>{const{recommendGames:e,currentRoute:t}=G(o=>o.mainPopup),n=!e?.length||e?.length<2;return _(()=>{t===S.Recommends&&n&&ke(S.Link)}),r("div",{"aria-hidden":"true",className:H("animate-slide-in-left","fixed left-0 top-1/2 z-50 box-border","-mt-80 h-[40rem] w-[36rem]","flex flex-row items-center gap-x-4 px-4","overflow-hidden"),onClick:o=>{o.preventDefault(),o.stopPropagation()},children:[r(ii,{}),r("div",{className:"bg-surface-primary relative rounded-lg",children:[t===S.Login&&r(Qn,{}),t===S.Link&&r(bi,{}),t===S.Recommends&&!n&&r(Mi,{}),t===S.Settings&&r(Qn,{})]})]})},Ii=()=>r(Ri,{children:r(Oi,{})}),Z0=e=>{const{headerTitle:t,onClickClose:n,onClickBack:o}=e;return r("h1",{className:"relative my-[1.125rem] flex items-center justify-center",children:[o&&r("span",{className:"absolute left-0 flex items-center",children:r(he,{style:{padding:0},icon:r(u2,{className:"text-font-primary"}),type:"link",onClick:o})}),r("span",{className:"select-none font-extrabold",children:t}),n&&r("div",{className:"absolute right-0",children:r(he,{icon:r(N0,{className:"scale-[0.85] text-[#666]"}),size:"small",type:"secondary",onClick:n})})]})},Pi=()=>{const e=[{name:"passkey",label:"Passkey"},{name:"line",label:"Line"},{name:"google",label:"Google"},{name:"twitter",label:"X"},{name:"facebook",label:"Facebook"},{name:"apple",label:"Apple"}],t=M(n=>{o2().login(n)},[]);return r("div",{className:"mx-auto mt-4 flex w-72 flex-wrap justify-start",children:e.map(n=>r("div",{className:"box-content size-16 p-4",children:[r("div",{className:"mx-auto w-9",children:r(f2,{name:n.name,onClick:()=>{t(n.name)}})}),r("div",{className:"text-xxs w-full select-none pt-2 text-center font-semibold",children:n.label})]},n.name))})},Ti=()=>{const{t:e,i18n:t}=P(),{isLinked:n}=$0(),{isGuestClicked:o}=G(a=>a.mainPopup),s=()=>{window.open(`${ht.SHD_G123_GAME_URL}/game/findmyaccount?lang=${t.language}&appCode=${window.option.appId}`)},i=()=>{ke(S.Settings)};return r("div",{className:"px-4",children:[r(Z0,{headerTitle:e("common.account.login.title_short"),onClickBack:o||n?i:void 0,onClickClose:Ge}),r("div",{className:"bg-[#eef6ef] rounded-lg p-4 text-xs",children:r(H2,{})}),r(Pi,{}),r(Me,{name:`${me}:${S.Recovery}_${K.Mobile}`,children:r("div",{className:"mt-8 mx-auto  pt-9 w-[90%] flex justify-center border-t border-line-divider text-center text-teal-700 text-xs font-semibold font-['Hiragino Sans']",onClick:a=>{a.stopPropagation(),a.preventDefault(),s()},children:e("common.account.recovery.tip_title")})})]})},o0=({onSelect:e,children:t})=>r("div",{"aria-hidden":!0,className:H("relative","flex flex-1 flex-col items-center","cursor-pointer"),onClick:()=>{e?.()},children:t}),r0=({children:e})=>r("span",{className:"select-none text-xs font-extrabold",children:e}),Bi=()=>{const{recommendGames:e,currentRoute:t}=G(l=>l.mainPopup),n=!e?.length||e?.length<2,{t:o}=P(),{isUnread:s}=G(l=>l.cs),{isLinked:i}=$0(),a=M(l=>{ke(l)},[]);return _(()=>{t===S.Recommends&&n&&ke(S.Settings)}),r("div",{className:H("absolute bottom-0","bg-surface-primary border-t border-solid border-gray-100","w-full pt-3"),children:[r("div",{className:"flex gap-4",style:{WebkitTapHighlightColor:"transparent"},children:[!n&&r(o0,{...t===S.Recommends?{}:{onSelect:()=>{a(S.Recommends),ze({action:"p_click",data:{display_name:window.option.userId,click:`${me}:${S.Recommends}_${K.Mobile}`,campaign:{},providers:window.option.providers}})}},children:[t===S.Recommends?r(w2,{}):r(g2,{}),r(r0,{children:o("common.recommends.title")})]}),r(o0,{onSelect:()=>{M0(),Ge(),ze({action:"p_click",data:{display_name:window.option.userId,click:`${me}:CustomerService_${K.Mobile}`,campaign:{},providers:window.option.providers}})},children:[r(y0,{offsetRight:0,offsetTop:0,show:s,children:r(Vt,{})}),r(r0,{children:o("common.service.title_short")})]}),r(o0,{...t===S.Settings?{}:{onSelect:()=>{a(S.Settings),ze({action:"p_click",data:{display_name:window.option.userId,click:`${me}:${S.Settings}_${K.Mobile}`,campaign:{},providers:window.option.providers}})}},children:[t===S.Settings||t===S.Link||t===S.Login||t===S.Recommends&&n?r(x2,{}):r(y0,{offsetRight:0,offsetTop:0,show:!i,children:r(v2,{})}),r(r0,{children:o("common.settings.title")})]})]}),r("div",{className:"mx-auto mt-6"})]})},Gi=()=>{const{supportedSocialTypeConnectedStatus:e}=D0(),{t}=P(),n=()=>{Fo(!0),ke(S.Settings)};return r("div",{className:"px-4 w-full",children:[r(Z0,{headerTitle:t("common.account.links.link"),onClickBack:n,onClickClose:Ge}),r("div",{className:"flex flex-wrap rounded-xl py-4 gap-y-12",children:e?.map(o=>r(U0,{name:o.socialType,isSns:o.isSns,isLinked:o.isLinked,showChatIcon:!0},o.socialType))})]})},Ui=()=>{const{t:e}=P();return r("div",{className:"mb-6 mt-4 flex justify-center items-center",children:[r("h1",{className:"relative flex select-none items-center justify-center -my-1 py-1",onClick:()=>{const o=new URL(window.location.href).searchParams.get("lang"),s=`${ht.SHD_G123_WEB_URL}?lang=${o}`;S0("misc_logo_click",{data:{position:"recommend_panel",device:"mobile"}}),window.open(s,"_blank")},children:[r(E0,{}),r("span",{className:"ml-1 font-extrabold",children:e("common.recommends.title")})]}),r("div",{className:"absolute right-4 top-4",children:r(he,{icon:r(N0,{className:"scale-[0.85] text-[#666]"}),size:"small",type:"secondary",onClick:Ge})})]})},Di=()=>{const{recommendGames:e,preregists:t}=G(n=>n.mainPopup);return r("div",{className:"px-6",children:[r(Ui,{}),r("div",{className:"scrollbar-none h-[28rem] overflow-y-scroll",children:[t&&t.length>0&&r(Y2,{games:t}),r(W2,{games:e?.filter(n=>n.status!==Ft.RESERVABLE)??[],platform:K.Mobile})]})]})},$i=e=>{const{isSns:t,name:n}=e,{t:o}=P();return r("div",{className:"relative flex-1 w-[8.3125rem] h-[6.3125rem] bg-transparent",children:[r("div",{className:" absolute flex-1 flex justify-center w-full h-full items-center rounded-lg bg-white overflow-hidden",children:[!t&&r("div",{className:"absolute top-0 right-0 w-[5.25rem] h-[5.25rem] bg-[#fafafa] rounded-full transform translate-x-1/2 -translate-y-1/2"}),r(U0,{...e})]}),!t&&r("div",{className:"flex-1 absolute top-0 right-0 flex justify-end pr-2 pt-2 w-10 h-10 bg-transparent",children:r(I2,{content:o("common.im_connect.chat_tip",{imTypeName:lt[n].label}),id:`${n}-card-chat`,place:"top",style:{height:"2rem",fontSize:"0.625rem",lineHeight:"0.875rem",fontWeight:600,verticalAlign:"middle"},children:r(Vt,{viewBox:"0 0 24 24",width:14,height:14,style:{color:"rgba(187, 187, 187, 1)"}})})})]})},Zi=()=>{const{vipRank:e}=G(t=>t.mainPopup);return _(()=>{l2()},[]),e&&e>0?r(b2,{rank:e}):r(s2,{type:"colorful"})},Hi=()=>{const e=()=>{ke(S.Login)},t=M(()=>{c2()},[]);return r("div",{className:"flex items-center justify-between w-full",children:[r("div",{className:"flex items-center",children:[r(Zi,{}),r("div",{className:"flex grow items-center gap-x-1 pl-2",children:[r("span",{className:"font-secondary text-sm",children:window.option.userId}),r(he,{className:"bg-transparent text-[#A5A5A7] p-0! active:text-brand-secondary-secondary",icon:r(h2,{}),size:"small",onClick:t})]})]}),r(Me,{name:`${me}:${S.Login}_${K.Mobile}`,children:r(he,{size:"small",type:"secondary",onClick:e,children:"Not my ID?"})})]})},zi=()=>{const{supportedSocialTypeConnectedStatus:e}=D0();if(!e?.length)return null;const t=()=>{ke(S.Link)},n=e.slice(0,2);return r("div",{className:"mt-8 flex gap gap-x-3",children:[n.map(o=>r($i,{name:o.socialType,isSns:o.isSns,isLinked:o.isLinked},o.socialType)),r(Me,{name:`${me}:${S.Link}_${K.Mobile}`,children:r("div",{className:"h-[6.3125rem] px-md flex justify-center items-center bg-white rounded-lg",onClick:t,children:r(m2,{})})})]})},Vi=()=>{const e=t=>{t.preventDefault(),t.stopPropagation(),To(),Ge()};return r("div",{className:"w-full mt-8  border-line-divider border-t flex justify-center",children:r(Me,{name:`${me}:${S.Install}_${K.Mobile}`,children:r("div",{"aria-hidden":!0,className:"mt-7 w-[88px] px-3 py-1 rounded-full border border-black/5 flex justify-center",onClick:e,children:r(jo,{style:{color:"#666666"}})})})})},e2=()=>{const e=G(n=>n.pwa.isPwaInstallPromptReady),{t}=P();return r("div",{className:"px-4 w-full",children:[r(Z0,{headerTitle:t("common.settings.title"),onClickClose:Ge}),r(Hi,{}),r(zi,{}),d2.pwa&&uo()&&e&&r(Vi,{}),r(z2,{containerClassName:"fixed bottom-24 left-0",platform:K.Mobile})]})},ji=()=>{const{isOpen:e,recommendGames:t,currentRoute:n}=G(i=>i.mainPopup),o=t?.length&&t?.length<2,s=mo(()=>n===S.Settings||n===S.Link||n===S.Login,[n]);return r(mt,{children:[r(he,{className:"fixed right-4 top-4 py-0.5",icon:r(qo,{}),size:"small",style:{paddingLeft:"0.5rem"},onClick:()=>{ho("RELOAD_BUTTON_CLICKED",!0)},children:"reload"}),r(hs,{className:`h-[38rem] ${s?"bg-neutral-100":""}`,isOpen:e,children:[n===S.Recommends&&!o&&r(Di,{}),n===S.Recommends&&o&&r(e2,{}),n===S.Login&&r(Ti,{}),n===S.Settings&&r(e2,{}),n===S.Link&&r(Gi,{}),r(Bi,{})]})]})},Wi=()=>{const{isOpen:e}=G(o=>o.mainPopup),t=L0.useMediaLayout({maxWidth:F0}),n=Ye();return _(()=>(window.option.appId&&Eo(window.option.appId),Mo(),Ro(),Oo(),Io(),Bo(),n(go(window.option.appId)),()=>{n(vo())}),[n]),e?r("div",{"aria-hidden":"true",className:"fixed top-0 z-50 size-full bg-black/40",onClick:Ge,children:t?r(ji,{}):r(Ii,{})}):null},Yi={Chrome:/chrome\/([0-9.]+)(?!\s*edg)/i,Firefox:/firefox\/([0-9.]+)/i,Safari:/^((?!chrome|android).)*safari\/([0-9.]+)/i,Edge:/(?:edg|edge)\/([0-9.]+)/i,Opera:/opera\/([0-9.]+)/i,Brave:/brave\/([0-9.]+)/i,Vivaldi:/vivaldi\/([0-9.]+)/i},at={mobile:/mobile|android|iphone|ipad|windows phone/i,ios:/iphone|ipad|ipod/i,android:/android/i,mac:/macintosh|mac os x/i,windows:/windows/i,linux:/linux/i},s0={wechat:/micromessenger/i,qq:/qq/i,weibo:/weibo/i},Ki=()=>typeof window<"u"?window.matchMedia("(max-width: 767px)").matches:!1,qi=(()=>{let e=null;return()=>{if(e)return e;if(!navigator?.userAgent)throw new Error("navigator is undefined");const t=navigator.userAgent.toLowerCase(),n={browser:"",version:"",isMobile:at.mobile.test(t),isIOS:at.ios.test(t),isAndroid:at.android.test(t),isWechat:s0.wechat.test(t),isQQ:s0.qq.test(t),isWeibo:s0.weibo.test(t),isSafari:/^((?!chrome|android).)*safari/i.test(t),isChrome:/chrome\/(?!\s*edg)/i.test(t),isFirefox:/firefox/i.test(t),isEdge:/(?:edg|edge)/i.test(t),isOpera:/opera|OPR/i.test(t),isMac:at.mac.test(t),isWindows:at.windows.test(t),isLinux:at.linux.test(t)};for(const[o,s]of Object.entries(Yi)){const i=t.match(s);if(i){n.browser=o,n.version=i[1];break}}return e=n,e}})(),H0=qi,Fa=()=>H0().isSafari,Ea=()=>H0().isWindows,Ji=()=>H0().isMobile||Ki(),Xi=wo(()=>Be(()=>import("./game-a0e37d72-s2bF6hzN.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11])).then(e=>({default:e.default})));function Qi(){return Ji()||!d2.favorite?null:r(r2,{fallback:r("div",{}),children:r(Xi,{})})}function ea({dialogs:e}){const[t,n]=I(null),o=Z(e),s=async()=>{const a=o.current[0],l=o.current.slice(1);if(o.current=l,a){let c=!1;try{c=await a.show()}catch(d){console.error("Dialog frequency error: ",d)}c?n(a):s()}},i={finish:()=>{n(null),s()}};return _(()=>{s()},[]),t?t.render(i):null}const ta=({onDidMount:e,onUnMount:t})=>{const n=Z(e),o=Z(t);return _(()=>{n.current=e,o.current=t}),_(()=>(n.current&&n.current(),()=>{o.current&&o.current()}),[]),null};function na(){const{gameTranslatorVisible:e}=G(s=>s.dialog),t=Ye(),[n,o]=I(null);return r(mt,{children:[e&&r(_o,{onFinish:()=>{n&&n.finish(),t(q0())}}),r(ea,{dialogs:[{show:Co,render:s=>r(ta,{onDidMount:()=>{o(s),t(yo())},onUnMount:()=>{o(null),t(q0())}})},{show:()=>!0,render:s=>r(Qi,{})}]})]})}const oa={facebook_message:D2,line_message:U2,discord_message:B2,whatsapp_message:Z2,kakao_message:G2,naver_message:$2},ra=({type:e,onClick:t})=>r("img",{alt:e,className:H("size-full",{"cursor-pointer":!!t}),onClick:t,src:oa[e]}),sa=({content:e})=>r("div",{className:"w-full text-left p-4 bg-brand-primary-bg rounded-lg justify-center items-start gap-2.5 inline-flex",children:r("div",{className:"grow shrink basis-0 text-black text-xs font-light font-['Hiragino Sans'] leading-5",children:e})}),ia=()=>{const{imConnectedStatus:e,imPopupOpen:t,imPopupConnectedStatus:n,supportedImTypes:o}=G(f=>f.imConnect),s=L0.useMediaLayout({maxWidth:F0}),{t:i}=P(),a=Ye(),l=f=>{const{appId:p}=window.option;t2(n2.H5Page,f.imType,p)},c=M(()=>{a(i0(!1))},[a]);_(()=>{if(!t||n)return;const p=Object.values(e??{}).filter(h=>h?.isLinked);if(p.length<=0)return;const u=p.length>1?void 0:p[0]?.imType;pt.success(r(G0,{imType:u,variant:"dark"}),{variant:"dark"}),a(i0(!1))},[t,e,n,a]);const d=Object.values(e??{});return d.length<=0||d.some(f=>f?.isLinked)?null:r(p2,{open:t,title:i("common.im_connect.popup_title"),onClose:c,headerClassName:"h-12 px-3",contentClassName:s?"px-3 pb-6 pt-0":"px-6 pb-6 pt-0",className:"w-[400px]",footer:null,children:[r(sa,{content:i("common.im_connect.popup_desc")}),r("div",{className:H("items-center pt-4 pb-6 flex",(o?.length??0)>3?"justify-between":"justify-evenly",{"px-3":s}),children:o?.map(f=>{const p=e?.[f];return p?r("div",{className:"w-[68px] h-16 py-2 flex-col justify-start items-center gap-2 inline-flex cursor-pointer",children:[r("div",{className:"w-9 h-9 relative",onClick:()=>l(p),children:r(ra,{type:p.imType})}),r("div",{className:"text-center text-neutral-800 text-xs font-semibold font-['Hiragino Sans'] leading-none",children:xo[p.imType]})]},p.imType):null})})]})},aa=()=>{const{imPopupConnectedStatus:e}=G(n=>n.imConnect),t=Ye();return _(()=>{e?.after.isLinked&&(pt.success(r(G0,{imType:e.after.imType,variant:"dark"}),{variant:"dark"}),t(bo()),t(i0(!1)))},[e,t]),null},la=()=>{const{t:e,i18n:t}=P(),{isSupportedDevice:n}=G(s=>s.app);_(()=>{if(!n){document.body.style.overflow="hidden";const s=Array.from(document.querySelectorAll("script"));for(const d of s)d.remove();const i=Array.from(document.querySelectorAll("img"));for(const d of i)d.src="";const a=Array.from(document.querySelectorAll("video, audio"));for(const d of a)d instanceof HTMLMediaElement&&(d.pause(),d.remove());const l=Array.from(document.querySelectorAll("iframe"));for(const d of l)d.remove();const c=window.setTimeout(()=>{},0);for(let d=0;d<c;d++)window.clearTimeout(d),window.clearInterval(d);window.stop()}return()=>{document.body.style.overflow=""}},[n]);const o=()=>{let i=new URL(window.location.href).searchParams.get("lang");i=i&&/^[a-zA-Z0-9_-]+$/.test(i)?i:t.language;const l=new URL(ht.SHD_G123_WEB_URL);l.searchParams.set("lang",i),window.location.href=l.href};return n?null:r("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center",children:r(p2,{open:!0,headerClassName:"hidden",contentClassName:"p-8",className:"w-[400px] rounded-xl",footer:null,children:r("div",{className:"flex flex-col items-center text-center",children:[r("div",{className:"mb-6",children:r(y2,{width:48,height:48,viewBox:"0 0 24 24"})}),r("div",{className:"mb-8 text-base",children:e("not_supported_tip.description")}),r("button",{type:"button",onClick:o,className:"px-8 py-3 bg-[#E6FF00] text-black rounded-full font-medium hover:opacity-90 transition-opacity",children:e("not_supported_tip.go")})]})})})},ca=()=>{const{t:e}=P(),[t,n]=J.useState(!1);_(()=>{const i=a=>{a.data&&a.data.event==="RELEASE_NEW_CLIENT"&&n(!0)};return window.addEventListener("message",i),()=>{window.removeEventListener("message",i)}},[]);const o=()=>{window.location.reload()},s=()=>{n(!1)};return t?r("div",{className:"w-screen flex justify-center px-4",children:r("div",{className:"w-full md:w-auto md:min-w-[360px] flex justify-between rounded-xl bg-[rgba(255,255,255,0.96)] px-4 py-[13px] mt-4",children:[r("div",{className:"flex items-center gap gap-2 text-sm font-medium leading-snug",children:[r(y2,{className:"size-6"}),r("div",{className:"flex flex-wrap",children:[r("div",{className:"text-neutral-800",children:[e("version_update_tip.check"),","]}),r("div",{onClick:o,className:"text-[#1C64F2] cursor-pointer",children:[e("version_update_tip.refresh"),"!"]})]})]}),r("div",{onClick:s,className:"ml-2 flex items-center cursor-pointer",children:r(N0,{className:"scale-[0.85] text-[#666]"})})]})}):null},da="g123App",pa=J.lazy(()=>Be(()=>import("./game-828640fc-Cs6SrrRS.js"),__vite__mapDeps([12,1,2,3,4,8,5]))),fa=J.lazy(()=>Be(()=>import("./game-e80e6666-B6RRqfIv.js"),__vite__mapDeps([13,1,2,3,4,7,6,9,5]))),ua=J.lazy(()=>Be(()=>import("./game-c536211d-D56Zha_z.js"),__vite__mapDeps([14,1,2,3,4,6,7]))),ma=J.lazy(()=>Be(()=>import("./game-d999a8e8-BZTf8JuJ.js"),__vite__mapDeps([15,1,2,3,4,9,5,7,6,8]))),ha=J.lazy(()=>Be(()=>import("./game-cdefd992-Bz4r6sWt.js"),__vite__mapDeps([16,1,2,3,4,6,7,8,5,9]))),ga=J.lazy(()=>Be(()=>import("./game-ac023766-B4FMI-sS.js"),__vite__mapDeps([17,1,2,3,4,6,7,8,5,9]))),va=J.lazy(()=>Be(()=>import("./game-8e5b257f-Bpk5yijc.js"),__vite__mapDeps([18,1,2,3,4,9,5,7,6,8])));function wa(e,t){console.info("initial react app",e);let n=document.getElementsByClassName(da)[0];n||(n=document.createElement("div"),n.className="g123App",n.style.position="fixed",n.style.top="0",n.style.left="0",document.body.appendChild(n)),ko(n).render(r(So,{store:e,children:r(Do,{i18n:t,children:[r(r2,{fallback:r("div",{}),children:[r(pa,{}),r(fa,{}),r(ga,{}),r(va,{}),r(ua,{}),r(ma,{}),r(ha,{})]}),r(Wi,{}),r(Go,{}),r(Ao,{containerClassName:"top-[82px]!"}),r(ni,{}),r(ia,{}),r(aa,{}),r(ca,{}),r(la,{}),r(na,{})]})}))}const Ma=Object.freeze(Object.defineProperty({__proto__:null,loadReactApp:wa},Symbol.toStringTag,{value:"Module"}));export{Fa as a,Ea as i,Ma as r};
