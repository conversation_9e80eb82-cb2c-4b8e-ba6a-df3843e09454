[1, ["e3iP4Eh3xErovnnY1Y5Gx2@6c48a"], 0, [["sp.SkeletonData", ["_name", "_native", "_atlasText", "textureNames", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "ani_loading_fw", ".bin", "\nani_loading_fw.png\nsize: 908,908\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\neffect/Q_yun/Q_yun_00000\n  rotate: true\n  xy: 2, 531\n  size: 98, 303\n  orig: 134, 355\n  offset: 31, 26\n  index: -1\neffect/Q_yun/Q_yun_00002\n  rotate: true\n  xy: 2, 429\n  size: 100, 302\n  orig: 134, 355\n  offset: 30, 28\n  index: -1\neffect/Q_yun/Q_yun_00004\n  rotate: true\n  xy: 2, 327\n  size: 100, 299\n  orig: 134, 355\n  offset: 31, 30\n  index: -1\neffect/Q_yun/Q_yun_00006\n  rotate: true\n  xy: 610, 319\n  size: 101, 295\n  orig: 134, 355\n  offset: 31, 33\n  index: -1\neffect/Q_yun/Q_yun_00008\n  rotate: true\n  xy: 307, 626\n  size: 98, 292\n  orig: 134, 355\n  offset: 34, 34\n  index: -1\neffect/Q_yun/Q_yun_00010\n  rotate: true\n  xy: 613, 524\n  size: 100, 293\n  orig: 134, 355\n  offset: 33, 32\n  index: -1\neffect/Q_yun/Q_yun_00012\n  rotate: true\n  xy: 612, 422\n  size: 100, 294\n  orig: 134, 355\n  offset: 34, 31\n  index: -1\neffect/Q_yun/Q_yun_00014\n  rotate: true\n  xy: 2, 224\n  size: 101, 294\n  orig: 134, 355\n  offset: 33, 30\n  index: -1\neffect/Q_yun/Q_yun_00016\n  rotate: true\n  xy: 605, 216\n  size: 101, 294\n  orig: 134, 355\n  offset: 33, 29\n  index: -1\neffect/Q_yun/Q_yun_00018\n  rotate: true\n  xy: 2, 17\n  size: 101, 295\n  orig: 134, 355\n  offset: 33, 29\n  index: -1\neffect/Q_yun/Q_yun_00020\n  rotate: true\n  xy: 299, 10\n  size: 102, 300\n  orig: 134, 355\n  offset: 32, 30\n  index: -1\neffect/Q_yun/Q_yun_00022\n  rotate: true\n  xy: 601, 2\n  size: 102, 302\n  orig: 134, 355\n  offset: 32, 29\n  index: -1\neffect/Q_yun/Q_yun_00024\n  rotate: true\n  xy: 303, 324\n  size: 100, 305\n  orig: 134, 355\n  offset: 34, 26\n  index: -1\neffect/Q_yun/Q_yun_00026\n  rotate: true\n  xy: 298, 222\n  size: 100, 305\n  orig: 134, 355\n  offset: 33, 26\n  index: -1\neffect/Q_yun/Q_yun_00028\n  rotate: true\n  xy: 2, 120\n  size: 100, 306\n  orig: 134, 355\n  offset: 32, 24\n  index: -1\neffect/Q_yun/Q_yun_00030\n  rotate: true\n  xy: 310, 114\n  size: 100, 305\n  orig: 134, 355\n  offset: 32, 24\n  index: -1\neffect/Q_yun/Q_yun_00032\n  rotate: true\n  xy: 306, 426\n  size: 99, 304\n  orig: 134, 355\n  offset: 32, 24\n  index: -1\neffect/Q_yun/Q_yun_00034\n  rotate: true\n  xy: 307, 527\n  size: 97, 304\n  orig: 134, 355\n  offset: 33, 24\n  index: -1\neffect/Q_yun/Q_yun_00036\n  rotate: true\n  xy: 601, 626\n  size: 95, 304\n  orig: 134, 355\n  offset: 34, 24\n  index: -1\neffect/Q_yun/Q_yun_00038\n  rotate: true\n  xy: 2, 631\n  size: 93, 303\n  orig: 134, 355\n  offset: 34, 24\n  index: -1\neffect/Q_yun/Q_yun_00040\n  rotate: true\n  xy: 607, 723\n  size: 92, 298\n  orig: 134, 355\n  offset: 34, 27\n  index: -1\neffect/Q_yun/Q_yun_00042\n  rotate: true\n  xy: 300, 817\n  size: 89, 295\n  orig: 134, 355\n  offset: 35, 27\n  index: -1\neffect/Q_yun/Q_yun_00044\n  rotate: true\n  xy: 2, 818\n  size: 88, 296\n  orig: 134, 355\n  offset: 35, 27\n  index: -1\neffect/Q_yun/Q_yun_00046\n  rotate: true\n  xy: 597, 817\n  size: 89, 298\n  orig: 134, 355\n  offset: 34, 27\n  index: -1\neffect/Q_yun/Q_yun_00048\n  rotate: true\n  xy: 2, 726\n  size: 89, 300\n  orig: 134, 355\n  offset: 34, 25\n  index: -1\neffect/Q_yun/Q_yun_00050\n  rotate: true\n  xy: 304, 726\n  size: 89, 301\n  orig: 134, 355\n  offset: 34, 25\n  index: -1\neffect/lizi_00000\n  rotate: false\n  xy: 617, 106\n  size: 108, 108\n  orig: 108, 108\n  offset: 0, 0\n  index: -1\n", ["ani_loading_fw.png"], [0]], -1], 0, 0, [0], [-1], [0]]