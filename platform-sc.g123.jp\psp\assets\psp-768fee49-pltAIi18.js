const e={actions:{back:"返回",close:"關閉",delete:"刪除",cancel:"取消",confirm_close_ui:"要取消付款嗎？",confirm_close_ui_refund_campaign:"[全額退款] 活動馬上就要結束了. 要取消付款嗎？",confirm_close_ui_refund_campaign_n:"[__N__%退款] 活動馬上就要結束了. 要取消付款嗎？"},tips:{learn_more:"詳細",processing:"處理中",processing_please_wait:"處理中請稍候",age_verification:"不允許未滿20歲玩家購買, 請確認之後進行購買行為",deliver_delay:"商品交付可能會需要花費數分鐘。",mail_sent:"支付Email已經寄出。請遵循Email的指示進行付款。"}},t={result:"支付結果",order_no:"訂單編號",success:"下單成功",complete:"付款完成",email_success:"支付Email已經寄出。請遵循Email的指示進行付款。",pay:"付款",failed:"支付失敗",try_again:"重試一遍",try_other_method:"使用其他支付方法",contact_customer_service:"聯繫客服",contact_customer_service_sent_success:"已發送至客服，請稍等。",shipment_pending:"商品交付可能會需要花費數分鐘。",maintenance:{label:"維修中",message:"{{paymentMethod}}正在維修中。維修結束前請稍候。"},refund:{pay_refund_campaign:"以「全額退款」支付",pay_refund_campaign_n:"以「__N__%退款」支付",remain_tips_all:"全額退款 離活動結束還有",remain_tips_ratio:"{{ratio}}%退款 離活動結束還有",warning_not_meet_refund_amount_condition:"不符合退款金额条件"},method:{title:"支付方法",title_short:"支付方法",learn_more:"支付方法",applepay:{title:"Apple Pay"},googlepay:{title:"Google Pay"},stripe_creditcard:{title:"信用卡"},creditucc:{title:"信用卡"},creditcard:{title:"信用卡",card_number:"信用卡號碼",pay:"付款",new_card_pay:"付款",add_new_card:"登錄新信用卡",unbind_tips:"是否要刪除登錄的信用卡?",management:"信用卡管理",delete:"信用卡登錄移除",confirm_delete:"要刪除末尾碼 {{cardLastDigits}} 的信用卡嗎？",authentication_required:"需要進行安全認證。",error:{invalid_card_type:"信用卡類型無法使用",default:"請輸入正確的信用卡情報"},tips:{registered:"登録完成",security_code:"信用卡驗證碼",security_code_is_title:"關於信用卡驗證碼",security_code_is_content:"請輸入信用卡背面的「簽名欄」上紀錄的三位數字。",expiration_date:"有效日期",secured_and_encrypted:"為了交易安全, 已經進行了加密。"}},paypal:{title:"PayPal",pay:"付款",available:"PayPal上有效的支付方式",unbind_tips:"是否要刪除已經登錄的PayPal帳號的情報？",error:{payment_agreement_canceled:"請刪除登錄完成的PayPal帳號重新嘗試"}},paypay:{title:"PayPay",unbind_tips:"是否要刪除已經登錄的PayPay帳號的情報？"},amazon:{title:"Amazon Pay",pay:"付款",unbind_tips:"是否要解除一鍵付款？"},bitcash:{title:"BitCash"},paidy:{title:"稍後付款paidy",pay:"付款",unbind_tips:"是否要刪除已經登錄的paidy帳號的情報？",messages:{pay_next_month:"不管當月購買幾次、都能夠在隔月一次用下列方法進行付款",bank:"戶頭轉帳",convenience_store:"便利商店付款",bank_fee:"銀行轉帳"}},rakutenpay:{title:"樂天Pay"},alipay:{title:"支付寶",unbind_tips:"是否要解除一鍵付款？"},naverpay:{title:"NAVER Pay"},kakaopay:{title:"Kakao Pay"},wechatpay:{title:"微信支付"},jkopay:{title:"街口支付",scan_qr_code:"使用街口APP掃碼付款"},adyen_creditcard:{holderName:"J. Smith",cardNumber:"1234 5678 9012 3456",expiryDate:"MM/YY",expiryMonth:"MM",expiryYear:"YY",securityCodeThreeDigits:"3 位數",securityCodeFourDigits:"4 位數",password:"12"}}},a={support_network:"有效Plan: VISA、MasterCard、JCB、American Expres",stripe:{support_network:"有效Plan: VISA, MasterCard, American Express"},paypal:{title:"PayPal上有效的支付方式",desc1:"你可以透過PayPal進行多種支付，包括使用PayPal餘額，銀行賬戶（在部分國家和地區可使用），PayPal Credit，扣帳卡和信用卡（Visa, Master, JCB, AMEX, Discover, UnionPay） ",desc2:"你可以訪問PayPal官方網站查詢詳情： <0>https://www.paypal.com/us/webapps/mpp/country-worldwide</0>"},paidy:{title:"※關於使用Paidy隔月付款",desc1:"・每個月的帳單在月末結算後於下個月一號發送請款單、3號前會用Email・SMS等通知說明。",desc2:"・支付方法、便利商店付款（便利商店設置設備）、銀行轉帳及戶頭轉帳。",desc3:"・支付日期、便利商店付款及銀行轉帳的話是10號之前。戶頭轉帳的話12號*落款。但是1月・5月時會是20號*。",desc4:"・根據支付方法、每筆付款會要求手續費。便利商店支付會要求350日圓（含稅）、銀行匯款則由顧客承擔匯款手續費。戶頭轉帳的情況則不會發生手續費。",desc5:"・金融機関休息日的話、下一個營業日。"},jkopay:{title:"Jko Pay",desc1:"街口支付有效付款方式：「掃描條碼」",desc2:"請先開通街口帳戶並完成支付設定！",desc3:"支付設定可選擇連結銀行帳戶、直接使用街口帳戶餘額付款",desc4:"街口網站： <0>https://www.jkopay.com/instructions/pay.html </0>"},company_info:{name:{title:"販賣業者",content:"CTW 股份有限公司"},ceo:{title:"代表者",content:"董事長 ：佐々木龍一"},address:{title:"業者所在地",content:"〒106-0032 東京都港区六本木1−9−10 アークヒルズ仙石山森タワー"},contact:{title:"連絡方式",email:"Email <EMAIL>",phone:"電話號碼 050-1748-6333"},price:{title:"販賣價格",desc1:"在各Software購買頁面裡會顯示販賣價格（含消費稅）, 商品代金等費用",desc2:"Software下載時發生的網路費等用由顧客承擔。"},payment_method:{title:"支付方式說明",content:"支持信用卡(VISA、MASTER、JCB、AMEX)、PayPal、PayPay、楽天Pay、Nanaco Gift、Amazon Pay、Paidy隔月付款、BitCash、電信公司代付款等。"},payment_term:{title:"顯示支付期限",content:"立即付款預付（結算公司的通知時期或是落款時間會根據結算公司不同而不一樣、詳細請洽詢結算公司。）"},delivery_time:{title:"交付時期",content:"支付手續完成後、會在購買完成後的畫面顯示時交付。"},return_and_exchange:{title:"關於退款, 交換",desc1:"由於商品特性、無法受理顧客因自身原因想要退貨・交換。請見諒。",desc2:"※若是因為網路不穩, 斷線或是Software自身故障而無法使用的話、請洽客服。"}},FAQ:"FAQ"},i={common:e,payment:t,settlement:a};export{e as common,i as default,t as payment,a as settlement};
