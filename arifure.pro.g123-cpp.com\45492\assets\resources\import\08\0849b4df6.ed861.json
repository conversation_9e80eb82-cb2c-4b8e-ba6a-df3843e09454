[1, ["feECe+G6hKsq6VGmKVgOp7"], ["_effectAsset"], [["<PERSON>.<PERSON><PERSON><PERSON><PERSON>", ["_name", "_native"], 1], ["cc.Material", ["_name", "_props", "_states", "_defines"], -1], ["cc.Json<PERSON>set", ["_name", "json"], 1], ["cc.TextAsset", ["_name", "text"], 1], ["cc.EffectAsset", ["_name", "shaders", "techniques"], 0]], [[0, 0, 1, 3], [1, 0, 1, 2, 3, 5], [2, 0, 1, 3], [3, 0, 1, 3], [4, 0, 1, 2, 4]], [[[[0, "adventure_slot", ".bin"], -1], 0, 0, [], [], []], [[[0, "help", ".bin"], -1], 0, 0, [], [], []], [[[0, "guajidia<PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerplayeraiconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "hero_pifu_up", ".bin"], -1], 0, 0, [], [], []], [[[0, "daygiftcom", ".bin"], -1], 0, 0, [], [], []], [[[0, "yong<PERSON><PERSON><PERSON>_tequan", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "dtcom", ".bin"], -1], 0, 0, [], [], []], [[[0, "boss", ".bin"], -1], 0, 0, [], [], []], [[[0, "djq_gift", ".bin"], -1], 0, 0, [], [], []], [[[0, "strategy", ".bin"], -1], 0, 0, [], [], []], [[[0, "yuma<PERSON><PERSON>_horse", ".bin"], -1], 0, 0, [], [], []], [[[0, "hougong_anmo_prize", ".bin"], -1], 0, 0, [], [], []], [[[0, "attr", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_mofa_hp", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerherobaseconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonghui_rank_reward", ".bin"], -1], 0, 0, [], [], []], [[[0, "boss_text", ".bin"], -1], 0, 0, [], [], []], [[[0, "hongbao", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_buy", ".bin"], -1], 0, 0, [], [], []], [[[0, "weapon_jichu", ".bin"], -1], 0, 0, [], [], []], [[[0, "jijin_task", ".bin"], -1], 0, 0, [], [], []], [[[0, "legionmoster", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "slave_word", ".bin"], -1], 0, 0, [], [], []], [[[0, "tequan", ".bin"], -1], 0, 0, [], [], []], [[[0, "action", ".bin"], -1], 0, 0, [], [], []], [[[0, "tf_wuqi", ".bin"], -1], 0, 0, [], [], []], [[[0, "tequan_item", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerattributionconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "jinbiaosai_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "heroskin_exp", ".bin"], -1], 0, 0, [], [], []], [[[0, "shop", ".bin"], -1], 0, 0, [], [], []], [[[0, "yuanzheng_rank", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "yuma<PERSON><PERSON>_fanpai", ".bin"], -1], 0, 0, [], [], []], [[[0, "wuzi_zhanling", ".bin"], -1], 0, 0, [], [], []], [[[0, "migong_di<PERSON>ai", ".bin"], -1], 0, 0, [], [], []], [[[0, "pay_djq", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_slotreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "crossarena_placement", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_weapon_lv", ".bin"], -1], 0, 0, [], [], []], [[[0, "emoji", ".bin"], -1], 0, 0, [], [], []], [[[0, "crosssdsl_boss_difficulty", ".bin"], -1], 0, 0, [], [], []], [[[0, "cross_nszdz_lingdi", ".bin"], -1], 0, 0, [], [], []], [[[0, "shangjin_item", ".bin"], -1], 0, 0, [], [], []], [[[0, "tunhu<PERSON>_huangoushop", ".bin"], -1], 0, 0, [], [], []], [[[0, "s<PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "dianjin", ".bin"], -1], 0, 0, [], [], []], [[[0, "demon<PERSON>na_seasonaffix", ".bin"], -1], 0, 0, [], [], []], [[[0, "paiweisai_time", ".bin"], -1], 0, 0, [], [], []], [[[0, "demonarena_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "cross_nszdz_rank_gonghui", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "cross_nszdz_time", ".bin"], -1], 0, 0, [], [], []], [[[0, "paiweisai_chest", ".bin"], -1], 0, 0, [], [], []], [[[0, "tun<PERSON>o", ".bin"], -1], 0, 0, [], [], []], [[[0, "l<PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "buff_action", ".bin"], -1], 0, 0, [], [], []], [[[0, "ji<PERSON>uan_hero_pos", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonghui_dps_reward", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_weapon_fumo", ".bin"], -1], 0, 0, [], [], []], [[[0, "pay", ".bin"], -1], 0, 0, [], [], []], [[[0, "vip<PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "npc", ".bin"], -1], 0, 0, [], [], []], [[[0, "tccw_hero", ".bin"], -1], 0, 0, [], [], []], [[[0, "npcmap7", ".bin"], -1], 0, 0, [], [], []], [[[0, "dianjincom", ".bin"], -1], 0, 0, [], [], []], [[[0, "crossarena_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerrankrobotconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "guanghangong_event", ".bin"], -1], 0, 0, [], [], []], [[[0, "tanxian_map", ".bin"], -1], 0, 0, [], [], []], [[[0, "hg_keji_jc", ".bin"], -1], 0, 0, [], [], []], [[[0, "mofazhen_star", ".bin"], -1], 0, 0, [], [], []], [[[0, "migong_paiming", ".bin"], -1], 0, 0, [], [], []], [[[0, "guanghangong_timetable", ".bin"], -1], 0, 0, [], [], []], [[[0, "hougong_anmo_music", ".bin"], -1], 0, 0, [], [], []], [[[0, "bao<PERSON>_zeng<PERSON>qi_lv", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_mofa_sc", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_quest", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowertutorialconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "exmail", ".bin"], -1], 0, 0, [], [], []], [[[0, "libao_itts", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_herostar", ".bin"], -1], 0, 0, [], [], []], [[[0, "weapon_lv", ".bin"], -1], 0, 0, [], [], []], [[[0, "baoshi", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonghui_juanxianreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "mineral_attr", ".bin"], -1], 0, 0, [], [], []], [[[0, "shenshu_exp", ".bin"], -1], 0, 0, [], [], []], [[[0, "mofazhen_unlock", ".bin"], -1], 0, 0, [], [], []], [[[0, "tunhuoc<PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "demonarena_share", ".bin"], -1], 0, 0, [], [], []], [[[0, "tf_hero", ".bin"], -1], 0, 0, [], [], []], [[[0, "dengluzhanling_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "huodongrili_type", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowersummonconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "herogrow", ".bin"], -1], 0, 0, [], [], []], [[[0, "moshou_pass", ".bin"], -1], 0, 0, [], [], []], [[[0, "pata_team_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "fight_type", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerrankrewardsconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "jin<PERSON><PERSON><PERSON>_rankreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_main_lv", ".bin"], -1], 0, 0, [], [], []], [[[0, "boss_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerrankconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "migong_tqtishi", ".bin"], -1], 0, 0, [], [], []], [[[0, "crossyanchang_rank_gonghui", ".bin"], -1], 0, 0, [], [], []], [[[0, "lianjin_yaoji", ".bin"], -1], 0, 0, [], [], []], [[[0, "viprankgift_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_singleslot", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "yumajian_stage", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonglve_attr", ".bin"], -1], 0, 0, [], [], []], [[[0, "paiweisai_rankreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "heropifu_upreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "tslb", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonghui_helpreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "yong<PERSON><PERSON><PERSON>_boss", ".bin"], -1], 0, 0, [], [], []], [[[0, "crossyanchang_libao", ".bin"], -1], 0, 0, [], [], []], [[[0, "moshou", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "npcmap3", ".bin"], -1], 0, 0, [], [], []], [[[0, "guide", ".bin"], -1], 0, 0, [], [], []], [[[0, "mofazhen_pass", ".bin"], -1], 0, 0, [], [], []], [[[0, "mofazhen_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "lianjin_skill", ".bin"], -1], 0, 0, [], [], []], [[[0, "rankgift_content", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowernpcsummonconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "yumajian_shop", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_flower", ".bin"], -1], 0, 0, [], [], []], [[[0, "yuma<PERSON><PERSON>_selfreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "arena_battlepass", ".bin"], -1], 0, 0, [], [], []], [[[0, "tslb_lib", ".bin"], -1], 0, 0, [], [], []], [[[0, "dt_xingxiang", ".bin"], -1], 0, 0, [], [], []], [[[0, "heroskill", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowervfxconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "sgyjsection", ".bin"], -1], 0, 0, [], [], []], [[[0, "tccw_boss_zu", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerskilltargetconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "tccw_hero_lv", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonghui_bujiword", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "tccw_item", ".bin"], -1], 0, 0, [], [], []], [[[0, "title", ".bin"], -1], 0, 0, [], [], []], [[[0, "mofazhen_exp", ".bin"], -1], 0, 0, [], [], []], [[[0, "cross_nszdz_lingd", ".bin"], -1], 0, 0, [], [], []], [[[0, "npcmap2", ".bin"], -1], 0, 0, [], [], []], [[[0, "nongchang_libao", ".bin"], -1], 0, 0, [], [], []], [[[0, "paiweisai_rank", ".bin"], -1], 0, 0, [], [], []], [[[0, "qian<PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "yuanzheng_buff", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_field", ".bin"], -1], 0, 0, [], [], []], [[[0, "mofazhen_faqi", ".bin"], -1], 0, 0, [], [], []], [[[0, "npcmap9", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_trinket", ".bin"], -1], 0, 0, [], [], []], [[[0, "yong<PERSON><PERSON><PERSON>_dflevel", ".bin"], -1], 0, 0, [], [], []], [[[0, "wuzi_type", ".bin"], -1], 0, 0, [], [], []], [[[0, "skill_afteratk", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonglve_fuben", ".bin"], -1], 0, 0, [], [], []], [[[0, "equipreroll", ".bin"], -1], 0, 0, [], [], []], [[[0, "moshou_star", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_season", ".bin"], -1], 0, 0, [], [], []], [[[0, "chest_sequence", ".bin"], -1], 0, 0, [], [], []], [[[0, "dt_tiku", ".bin"], -1], 0, 0, [], [], []], [[[0, "mingzi", ".bin"], -1], 0, 0, [], [], []], [[[0, "tccw_guanqia", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_battlepass", ".bin"], -1], 0, 0, [], [], []], [[[0, "pata_double_boss", ".bin"], -1], 0, 0, [], [], []], [[[0, "leichong", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonghui_rizhi", ".bin"], -1], 0, 0, [], [], []], [[[0, "setattr", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_house", ".bin"], -1], 0, 0, [], [], []], [[[0, "map_xuanze", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerequipconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "g123attributes", ".bin"], -1], 0, 0, [], [], []], [[[0, "leitaisai_gift", ".bin"], -1], 0, 0, [], [], []], [[[0, "tccw_boss", ".bin"], -1], 0, 0, [], [], []], [[[0, "weekgift", ".bin"], -1], 0, 0, [], [], []], [[[0, "guajicom", ".bin"], -1], 0, 0, [], [], []], [[[0, "vip", ".bin"], -1], 0, 0, [], [], []], [[[0, "shangjin_fuben", ".bin"], -1], 0, 0, [], [], []], [[[0, "shenshu", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_diary", ".bin"], -1], 0, 0, [], [], []], [[[0, "destiny_chapter", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "destiny_task", ".bin"], -1], 0, 0, [], [], []], [[[0, "task", ".bin"], -1], 0, 0, [], [], []], [[[0, "herostarup", ".bin"], -1], 0, 0, [], [], []], [[[0, "npcmap12", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_singleslotreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "equiprankup", ".bin"], -1], 0, 0, [], [], []], [[[0, "biaoqingbaolibao", ".bin"], -1], 0, 0, [], [], []], [[[0, "shixing_attr", ".bin"], -1], 0, 0, [], [], []], [[[0, "cross_nszdz_libao", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_workshop", ".bin"], -1], 0, 0, [], [], []], [[[0, "shop_item", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerarenarewardsconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "hero", ".bin"], -1], 0, 0, [], [], []], [[[0, "pata_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_helptxt", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerseasonrewardsconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "slave_pro", ".bin"], -1], 0, 0, [], [], []], [[[0, "tccw_shuxing", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerplayerairuleconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_house_lv", ".bin"], -1], 0, 0, [], [], []], [[[0, "tunhuorank", ".bin"], -1], 0, 0, [], [], []], [[[0, "dt_task", ".bin"], -1], 0, 0, [], [], []], [[[0, "lianjin_level", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "heropifu_up", ".bin"], -1], 0, 0, [], [], []], [[[0, "demonarena_rankreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_mofa_zl", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_turntable", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonghui_juanxian", ".bin"], -1], 0, 0, [], [], []], [[[0, "crossyan<PERSON>_jingli", ".bin"], -1], 0, 0, [], [], []], [[[0, "b<PERSON><PERSON>_z<PERSON><PERSON><PERSON>_zhanling", ".bin"], -1], 0, 0, [], [], []], [[[0, "crossarena_rank", ".bin"], -1], 0, 0, [], [], []], [[[0, "item", ".bin"], -1], 0, 0, [], [], []], [[[0, "cross_nszdz_user_jifen", ".bin"], -1], 0, 0, [], [], []], [[[0, "chest", ".bin"], -1], 0, 0, [], [], []], [[[0, "artifact", ".bin"], -1], 0, 0, [], [], []], [[[0, "jijinshangcheng_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_house_txt", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_handbook", ".bin"], -1], 0, 0, [], [], []], [[[0, "giftcom", ".bin"], -1], 0, 0, [], [], []], [[[0, "baoshi_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerheroconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "wuzi_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "hg_keji_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "crosssdsl_fuzhou", ".bin"], -1], 0, 0, [], [], []], [[[0, "lianjin_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "guajiextra", ".bin"], -1], 0, 0, [], [], []], [[[0, "paiweisai_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "npcmap5", ".bin"], -1], 0, 0, [], [], []], [[[0, "hougong_anmo_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "demonarena_brawlreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "slave_record", ".bin"], -1], 0, 0, [], [], []], [[[0, "weapon_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "shangjin_shop", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_event", ".bin"], -1], 0, 0, [], [], []], [[[0, "pata_team_boss", ".bin"], -1], 0, 0, [], [], []], [[[0, "duanzao_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "npcmap6", ".bin"], -1], 0, 0, [], [], []], [[[0, "crossyanchang_lingdi", ".bin"], -1], 0, 0, [], [], []], [[[0, "pata_double_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "kffz_wanfa", ".bin"], -1], 0, 0, [], [], []], [[[0, "crossyanchang_rank_geren", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_turntablereward", ".bin"], -1], 0, 0, [], [], []], [[[0, "guajilevel", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_lv", ".bin"], -1], 0, 0, [], [], []], [[[0, "yuanzheng", ".bin"], -1], 0, 0, [], [], []], [[[0, "fuben", ".bin"], -1], 0, 0, [], [], []], [[[0, "migong_di<PERSON><PERSON>zu", ".bin"], -1], 0, 0, [], [], []], [[[0, "lianjin_diaoluo", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerbaseconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "monthgift", ".bin"], -1], 0, 0, [], [], []], [[[0, "yumajian_map", ".bin"], -1], 0, 0, [], [], []], [[[0, "gong<PERSON>_buji", ".bin"], -1], 0, 0, [], [], []], [[[0, "npcmap1", ".bin"], -1], 0, 0, [], [], []], [[[0, "skill_bag", ".bin"], -1], 0, 0, [], [], []], [[[0, "yuma<PERSON><PERSON>_buff", ".bin"], -1], 0, 0, [], [], []], [[[0, "boss_z<PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "npcmap4", ".bin"], -1], 0, 0, [], [], []], [[[0, "shang<PERSON>_monster", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_handbook_p", ".bin"], -1], 0, 0, [], [], []], [[[0, "weapon_bskilllv", ".bin"], -1], 0, 0, [], [], []], [[[1, "spine-gray", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{}]]], 0, 0, [0], [0], [0]], [[[0, "jiayuan_speed", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "crossyanchang_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "matchthree", ".bin"], -1], 0, 0, [], [], []], [[[0, "meiliquhui_level", ".bin"], -1], 0, 0, [], [], []], [[[0, "slave_rank", ".bin"], -1], 0, 0, [], [], []], [[[0, "pifuhuodong", ".bin"], -1], 0, 0, [], [], []], [[[0, "kffz_guize", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_weapon_mofak<PERSON>shi", ".bin"], -1], 0, 0, [], [], []], [[[0, "hougong_qinre", ".bin"], -1], 0, 0, [], [], []], [[[0, "lianjin_usenum", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonghui_qizhi", ".bin"], -1], 0, 0, [], [], []], [[[0, "arena_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "shang<PERSON>_yaowu", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerhaloconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_herolevel", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "hougong_hgitem", ".bin"], -1], 0, 0, [], [], []], [[[0, "bizhong", ".bin"], -1], 0, 0, [], [], []], [[[0, "tanxian_prize", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_weapon_nihua", ".bin"], -1], 0, 0, [], [], []], [[[0, "equipattr", ".bin"], -1], 0, 0, [], [], []], [[[0, "yuma<PERSON><PERSON>_sharereward", ".bin"], -1], 0, 0, [], [], []], [[[0, "npcmap10", ".bin"], -1], 0, 0, [], [], []], [[[0, "demonarena_timetable", ".bin"], -1], 0, 0, [], [], []], [[[0, "tunhuolibao", ".bin"], -1], 0, 0, [], [], []], [[[0, "hg_keji_gj", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_chapter", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_convert", ".bin"], -1], 0, 0, [], [], []], [[[0, "destiny_item", ".bin"], -1], 0, 0, [], [], []], [[[0, "equipextra", ".bin"], -1], 0, 0, [], [], []], [[[0, "mineral_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "hg_anmo_prize", ".bin"], -1], 0, 0, [], [], []], [[[0, "arena_prize", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_slottrainbuild", ".bin"], -1], 0, 0, [], [], []], [[[0, "skill_buff", ".bin"], -1], 0, 0, [], [], []], [[[0, "fightcom", ".bin"], -1], 0, 0, [], [], []], [[[0, "duanzao_item", ".bin"], -1], 0, 0, [], [], []], [[[0, "jin<PERSON><PERSON>i_time", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerherolevelupconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "skill_desc", ".bin"], -1], 0, 0, [], [], []], [[[0, "shangjin_com", ".bin"], -1], 0, 0, [], [], []], [[[2, "localServer", [{"server_name": "最强 内网", "sid": 1, "url": "ws://********:9501/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 zp", "sid": 4034, "url": "ws://*********:8001/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 jzj", "sid": 12, "url": "ws://10.0.0.122:8001/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 cl2", "sid": 1002, "url": "ws://10.0.0.50:8001/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 ysr", "sid": 7086, "url": "ws://10.0.0.101:8001/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 国内外网", "sid": 1, "url": "ws://101.35.125.220:8011/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 zl", "sid": 1070, "url": "ws://10.0.0.32:1214/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 g123测试", "sid": 1, "url": "wss://arifure-slb.stg.g123-cpp.com/pfzy-game1/gateway_g123_2", "status": 0, "createrole": 0, "capacity": 100, "outside": 1}, {"server_name": "平凡 kst", "sid": 1001, "url": "ws://10.0.1.76:7001/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 kirito", "sid": 108126, "url": "ws://10.0.0.226:8001/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 mxf", "sid": 85, "url": "ws://10.0.0.85:8234/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 lcj", "sid": 7999, "url": "ws://10.0.0.39:8012/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 gunan", "sid": 12138, "url": "ws://10.0.0.46:9001/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 25", "sid": 12139, "url": "ws://10.0.0.46:8001/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 wh2", "sid": 6, "url": "ws://10.0.1.182:8369/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 cw", "sid": 1087, "url": "ws://10.0.1.112:8001/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 cw2", "sid": 2087, "url": "ws://10.0.1.112:8101/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 g123测试2", "sid": 2, "url": "wss://arifure-slb.stg.g123-cpp.com/pfzy-game2/gateway_g123", "status": 0, "createrole": 0, "capacity": 100, "outside": 1}, {"server_name": "xinGeGe", "sid": 2036, "url": "ws://10.0.1.208:8001/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 g123正式服", "sid": 1, "url": "wss://arifure-slb.pro.g123-cpp.com/pfzy-game1/gateway_g123_2", "status": 0, "createrole": 0, "capacity": 100, "outside": 1}, {"server_name": "平凡 cw2", "sid": 2087, "url": "ws://10.0.1.112:8101/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 wnp", "sid": 666, "url": "ws://10.0.0.153:8001/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 wh合服1", "sid": 5, "url": "ws://10.0.1.182:8751/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 wh合服2", "sid": 6, "url": "ws://10.0.1.182:8751/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 盐场1区", "sid": 5, "url": "ws://10.0.0.50:8001/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 盐场2区", "sid": 6, "url": "ws://10.0.0.50:8002/gateway", "status": 0, "createrole": 0, "capacity": 100}, {"server_name": "平凡 盐场3区", "sid": 7, "url": "ws://10.0.0.50:8003/gateway", "status": 0, "createrole": 0, "capacity": 100}]]], 0, 0, [], [], []], [[[0, "tanxian_levelreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "ji<PERSON><PERSON>_zhanling", ".bin"], -1], 0, 0, [], [], []], [[[0, "comconf", ".bin"], -1], 0, 0, [], [], []], [[[0, "gong<PERSON>_qiming", ".bin"], -1], 0, 0, [], [], []], [[[0, "cross_nszdz_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "duanzao_level", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_mofa_open", ".bin"], -1], 0, 0, [], [], []], [[[0, "skill_passive", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonghui_help", ".bin"], -1], 0, 0, [], [], []], [[[0, "strategyline", ".bin"], -1], 0, 0, [], [], []], [[[0, "moshou_affix", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_mofa_table", ".bin"], -1], 0, 0, [], [], []], [[[0, "legionchapter", ".bin"], -1], 0, 0, [], [], []], [[[0, "legioncom", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonghui_zhiwei", ".bin"], -1], 0, 0, [], [], []], [[[0, "cheng<PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "g123item", ".bin"], -1], 0, 0, [], [], []], [[[0, "ji<PERSON>uan_huoban_jc", ".bin"], -1], 0, 0, [], [], []], [[[0, "pata_level", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_skillrandom", ".bin"], -1], 0, 0, [], [], []], [[[0, "guanghangong_sharereward", ".bin"], -1], 0, 0, [], [], []], [[[0, "arena_seasonaffix", ".bin"], -1], 0, 0, [], [], []], [[[0, "chest_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "boss_recommend_hero", ".bin"], -1], 0, 0, [], [], []], [[[0, "daygift", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_up", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "guang<PERSON><PERSON>_rabbit", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_weapon_skill", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_mofa_kj", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonghui_boss", ".bin"], -1], 0, 0, [], [], []], [[[0, "yuyan", ".bin"], -1], 0, 0, [], [], []], [[[0, "crosssdsl_choujiang", ".bin"], -1], 0, 0, [], [], []], [[[0, "shouchong", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowershowconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_tequan", ".bin"], -1], 0, 0, [], [], []], [[[0, "friend", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_payshop", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "xiangqian_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "monster", ".bin"], -1], 0, 0, [], [], []], [[[0, "shop_mode", ".bin"], -1], 0, 0, [], [], []], [[[0, "rankgift_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "extbuy", ".bin"], -1], 0, 0, [], [], []], [[[0, "yong<PERSON><PERSON><PERSON>_bossreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "hougong_anmo_main", ".bin"], -1], 0, 0, [], [], []], [[[0, "migong_libao", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_mofa_zs", ".bin"], -1], 0, 0, [], [], []], [[[0, "tccw_skill", ".bin"], -1], 0, 0, [], [], []], [[[0, "mofazhen", ".bin"], -1], 0, 0, [], [], []], [[[0, "arena_dan", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_lv", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_cardflip", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "qiandaocom", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerspineresconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "guajiprize", ".bin"], -1], 0, 0, [], [], []], [[[0, "crossarena_robot", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_mofa_stype", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON>_hero_jc_star", ".bin"], -1], 0, 0, [], [], []], [[[0, "viplevel", ".bin"], -1], 0, 0, [], [], []], [[[0, "duanzao_guding", ".bin"], -1], 0, 0, [], [], []], [[[0, "stjl_boss", ".bin"], -1], 0, 0, [], [], []], [[[0, "rerollcost", ".bin"], -1], 0, 0, [], [], []], [[[0, "demonarena_gift", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_surprisebuild", ".bin"], -1], 0, 0, [], [], []], [[[0, "starup_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "ji<PERSON><PERSON>_hero_jc_lv", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerplayeraibaseconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "battle_buff", ".bin"], -1], 0, 0, [], [], []], [[[0, "cross_nszdz_jingli", ".bin"], -1], 0, 0, [], [], []], [[[0, "starup_yc", ".bin"], -1], 0, 0, [], [], []], [[[0, "guanghangong_fanpai", ".bin"], -1], 0, 0, [], [], []], [[[0, "cj<PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "qipao", ".bin"], -1], 0, 0, [], [], []], [[[0, "legionbuff", ".bin"], -1], 0, 0, [], [], []], [[[0, "rank", ".bin"], -1], 0, 0, [], [], []], [[[0, "mofazhen_attr", ".bin"], -1], 0, 0, [], [], []], [[[0, "zhongjishilian_level", ".bin"], -1], 0, 0, [], [], []], [[[0, "shangjin_buff", ".bin"], -1], 0, 0, [], [], []], [[[0, "xiang<PERSON><PERSON>_attr", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "crosssdsl_boss", ".bin"], -1], 0, 0, [], [], []], [[[0, "chat", ".bin"], -1], 0, 0, [], [], []], [[[0, "head", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "meiliquhui_bz", ".bin"], -1], 0, 0, [], [], []], [[[0, "crosssdsl_choujiang_jindu", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "tanxian_npcmap", ".bin"], -1], 0, 0, [], [], []], [[[0, "ji<PERSON>uan_hero_jc", ".bin"], -1], 0, 0, [], [], []], [[[0, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "crossyanchang_time", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_seed", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_eventpoint", ".bin"], -1], 0, 0, [], [], []], [[[0, "tujian_level", ".bin"], -1], 0, 0, [], [], []], [[[0, "yumajian_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "jijin_level", ".bin"], -1], 0, 0, [], [], []], [[[0, "shenshujitan_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "jin<PERSON><PERSON><PERSON>_quest", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerrankbaseconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "moshou_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "npcmap11", ".bin"], -1], 0, 0, [], [], []], [[[0, "moshou_libao", ".bin"], -1], 0, 0, [], [], []], [[[0, "slave_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_skin", ".bin"], -1], 0, 0, [], [], []], [[[0, "sound", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_payslot", ".bin"], -1], 0, 0, [], [], []], [[[0, "slave_fudan", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_monster", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowernpcconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "hero_halo", ".bin"], -1], 0, 0, [], [], []], [[[0, "mofazhen_zhuanhua", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_payslotreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "boss_dps_reward", ".bin"], -1], 0, 0, [], [], []], [[[0, "fuben_name", ".bin"], -1], 0, 0, [], [], []], [[[0, "recruitcom", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_level", ".bin"], -1], 0, 0, [], [], []], [[[0, "jin<PERSON><PERSON>i_question", ".bin"], -1], 0, 0, [], [], []], [[[0, "qidayqd", ".bin"], -1], 0, 0, [], [], []], [[[0, "bao<PERSON>_zeng<PERSON><PERSON>_up", ".bin"], -1], 0, 0, [], [], []], [[[0, "yong<PERSON><PERSON><PERSON>_task", ".bin"], -1], 0, 0, [], [], []], [[[0, "skill_atk", ".bin"], -1], 0, 0, [], [], []], [[[0, "tccw_skill_lv", ".bin"], -1], 0, 0, [], [], []], [[[0, "skill_mianban", ".bin"], -1], 0, 0, [], [], []], [[[0, "slave_unlock", ".bin"], -1], 0, 0, [], [], []], [[[0, "userskin_exp", ".bin"], -1], 0, 0, [], [], []], [[[0, "wuzi", ".bin"], -1], 0, 0, [], [], []], [[[0, "kuaisuguaji", ".bin"], -1], 0, 0, [], [], []], [[[0, "equipcom", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerskillconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "npcmap8", ".bin"], -1], 0, 0, [], [], []], [[[0, "jdreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "legioninvasion", ".bin"], -1], 0, 0, [], [], []], [[[0, "z<PERSON><PERSON>talevel", ".bin"], -1], 0, 0, [], [], []], [[[0, "slave_hudong", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_achievement", ".bin"], -1], 0, 0, [], [], []], [[[0, "paiweisai_quest", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_weapon_jinjieshuxing", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletoweraiconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "skill_tips", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonghui_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "skill_ly", ".bin"], -1], 0, 0, [], [], []], [[[0, "hg_anmo_music", ".bin"], -1], 0, 0, [], [], []], [[[0, "hero_exp", ".bin"], -1], 0, 0, [], [], []], [[[0, "bao<PERSON>_zeng<PERSON>qi_libao", ".bin"], -1], 0, 0, [], [], []], [[[0, "jiayuan_libao", ".bin"], -1], 0, 0, [], [], []], [[[0, "leitaisai_reward", ".bin"], -1], 0, 0, [], [], []], [[[0, "moshou_levelup", ".bin"], -1], 0, 0, [], [], []], [[[0, "shixing_level", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerskillbuffconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "gonghui_lv", ".bin"], -1], 0, 0, [], [], []], [[[0, "destiny_level", ".bin"], -1], 0, 0, [], [], []], [[[0, "crosssdsl_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "opencond", ".bin"], -1], 0, 0, [], [], []], [[[0, "lianjin_tequan", ".bin"], -1], 0, 0, [], [], []], [[[0, "paiweisai_duty", ".bin"], -1], 0, 0, [], [], []], [[[0, "gongnengyugao", ".bin"], -1], 0, 0, [], [], []], [[[0, "huiz<PERSON>", ".bin"], -1], 0, 0, [], [], []], [[[0, "table", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_skill", ".bin"], -1], 0, 0, [], [], []], [[[0, "guanghangong_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "mi<PERSON>_zhanling", ".bin"], -1], 0, 0, [], [], []], [[[0, "7dayqd", ".bin"], -1], 0, 0, [], [], []], [[[0, "guanghangong_selfreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "artifact_exp", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_shop", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletoweraibaseconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "guanghangong_map", ".bin"], -1], 0, 0, [], [], []], [[[0, "tequanka", ".bin"], -1], 0, 0, [], [], []], [[[3, "arifure-currency-tier", "cny,jpy,usd,aud,gbp,cad,twd,eur,chf,czk,dkk,hkd,huf,nok,nzd,pln,sek,sgd,thb,php,mxn,ils,rub,krw,vnd,idr,clp,myr,brl,aed,bdt,pen,ron,sar,inr,isk,bgn,ars,mop\n0.10,1.00,0.01,0.05,0.01,0.05,1.00,0.01,0.05,0.50,0.50,0.10,5.00,1.00,0.10,0.19,0.10,0.05,1.00,1.00,0.20,0.05,1.00,10.00,500.00,1000.00,10.00,0.05,0.05,0.25,1.00,0.05,0.05,0.05,1.00,2.00,0.05,5.00,1.00\n0.50,10.00,0.09,0.19,0.09,0.19,3.00,0.09,0.09,2.00,0.99,1.00,30.00,1.79,0.19,0.39,0.79,0.15,2.49,4.09,1.39,0.29,5.49,90.00,2100.00,1800.00,60.00,0.39,0.39,0.69,7.49,0.29,0.39,0.29,5.99,10.00,0.19,60.00,1.49\n1.00,20.00,0.19,0.29,0.19,0.29,5.00,0.19,0.19,4.00,1.29,2.00,55.00,2.09,0.29,0.69,1.49,0.19,5.09,8.09,2.79,0.59,10.99,190.00,4200.00,2500.00,125.00,0.69,0.79,0.99,15.49,0.59,0.69,0.59,11.99,20.00,0.29,120.00,1.79\n3.00,50.00,0.39,0.59,0.29,0.49,11.00,0.39,0.39,9.00,2.49,3.00,135.00,3.69,0.59,1.59,3.79,0.49,12.99,20.49,6.99,1.29,27.99,480.00,10500.00,5800.00,310.00,1.69,1.79,1.39,37.99,1.39,1.69,1.39,29.99,50.00,0.69,300.00,2.89\n6.00,100.00,0.79,1.09,0.59,0.99,22.00,0.69,0.69,17.00,5.09,6.00,270.00,7.49,1.19,3.09,7.49,0.99,25.99,40.99,13.99,2.59,54.99,950.00,21000.00,11500.00,620.00,3.29,3.49,2.69,75.99,2.69,3.29,2.79,58.99,100.00,1.29,600.00,5.69\n8.00,130.00,0.99,1.49,0.79,1.29,28.00,0.89,0.89,22.00,6.49,8.00,350.00,9.49,1.49,4.09,9.99,1.29,33.99,52.99,17.99,3.39,71.99,1240.00,27300.00,15000.00,800.00,4.09,4.69,3.49,97.99,3.49,4.29,3.49,76.99,130.00,1.69,780.00,7.49\n12.00,180.00,1.39,1.99,1.09,1.79,40.00,1.29,1.29,30.00,9.09,10.00,480.00,13.49,2.09,5.49,13.99,1.79,46.99,72.99,24.99,4.49,98.99,1710.00,37800.00,20700.00,1110.00,5.99,6.49,4.89,135.99,4.79,5.99,4.99,105.99,180.00,2.39,1080.00,10.49\n18.00,280.00,2.09,3.09,1.69,2.79,60.00,1.99,1.89,47.00,14.49,17.00,740.00,20.99,3.29,8.99,21.49,2.79,71.99,112.99,38.99,7.09,153.99,2660.00,58700.00,32200.00,1720.00,9.09,10.09,7.49,210.99,7.49,9.09,7.49,164.99,280.00,3.69,1680.00,15.99\n30.00,480.00,3.49,5.09,2.89,4.69,105.00,3.29,3.29,80.00,24.99,28.00,1270.00,35.99,5.49,14.99,36.99,4.79,122.99,192.99,65.99,12.49,263.99,4560.00,100600.00,55200.00,2940.00,15.49,17.49,12.99,361.99,12.99,15.99,13.49,281.99,470.00,6.09,2880.00,27.99\n36.00,560.00,4.09,6.09,3.29,5.49,120.00,3.89,3.79,93.00,28.99,32.00,1480.00,41.99,6.49,17.99,42.99,5.49,143.99,224.99,76.99,14.49,307.99,5320.00,117400.00,64400.00,3430.00,18.49,20.49,15.09,421.99,14.99,18.49,15.49,328.99,550.00,7.09,3360.00,31.99\n45.00,720.00,5.29,7.99,4.29,7.09,160.00,4.99,4.89,120.00,36.99,40.00,1900.00,53.99,8.49,22.99,54.99,7.09,184.99,288.99,97.99,18.99,395.99,6840.00,150900.00,82800.00,4410.00,23.49,25.99,19.49,542.99,19.49,23.99,19.99,422.99,710.00,9.49,4320.00,40.99\n60.00,960.00,7.09,10.49,5.49,9.49,210.00,6.49,6.49,160.00,48.99,54.00,2530.00,70.99,11.49,29.99,72.99,9.49,245.99,384.99,130.99,24.99,526.99,9120.00,201100.00,110400.00,5870.00,31.49,34.99,25.99,722.99,25.99,31.99,26.99,563.99,940.00,12.49,5760.00,54.99\n68.00,1080.00,7.99,11.99,6.49,10.49,240.00,7.49,7.09,180.00,54.99,60.00,2850.00,79.99,12.99,33.99,81.99,10.99,276.99,432.99,146.99,27.99,592.99,10250.00,226300.00,124200.00,6610.00,34.99,38.99,28.99,813.99,28.99,35.99,29.99,633.99,1060.00,13.99,6480.00,61.99\n78.00,1190.00,8.49,12.99,7.09,11.49,260.00,8.09,7.99,198.00,60.99,68.00,3140.00,87.99,13.99,37.99,89.99,11.99,304.99,476.99,161.99,30.99,653.99,11300.00,249300.00,136800.00,7280.00,38.99,42.99,31.99,895.99,31.99,38.99,32.99,698.99,1170.00,15.49,7140.00,67.99\n88.00,1360.00,9.99,14.99,8.09,13.49,300.00,9.49,9.09,226.00,69.99,78.00,3580.00,100.99,15.99,42.99,102.99,13.49,348.99,544.99,184.99,34.99,746.99,12910.00,284900.00,156400.00,8320.00,43.99,48.99,36.99,1023.99,36.99,44.99,37.99,797.99,1340.00,17.99,8160.00,77.99\n98.00,1520.00,11.09,16.49,8.99,14.99,330.00,10.49,10.49,253.00,77.99,88.00,4000.00,112.99,17.99,47.99,114.99,15.49,389.99,608.99,206.99,39.99,834.99,14430.00,318400.00,174700.00,9300.00,49.99,54.99,40.99,1144.99,40.99,49.99,41.99,891.99,1490.00,19.99,9120.00,86.99\n128.00,1980.00,14.49,21.99,11.99,19.49,430.00,13.49,13.49,329.00,100.99,118.00,5220.00,145.99,23.49,61.99,149.99,19.99,507.99,792.99,269.99,50.99,1086.99,18800.00,414800.00,227600.00,12110.00,64.99,71.99,53.99,1490.99,52.99,64.99,54.99,1161.99,1940.00,25.99,11880.00,112.99\n168.00,2560.00,18.99,27.99,15.49,24.99,550.00,17.99,17.49,426.00,130.99,148.00,6740.00,188.99,30.49,79.99,193.99,25.99,655.99,1024.99,348.99,65.99,1404.99,24300.00,536300.00,294300.00,15660.00,82.99,91.99,68.99,1927.99,68.99,83.99,70.99,1501.99,2510.00,33.99,15360.00,145.99\n198.00,2980.00,21.99,32.99,17.99,28.99,640.00,20.49,20.49,495.00,151.99,178.00,7850.00,219.99,35.99,93.99,225.99,29.99,763.99,1192.99,405.99,76.99,1635.99,28290.00,624300.00,342500.00,18230.00,96.99,106.99,79.99,2243.99,79.99,97.99,81.99,1748.99,2920.00,38.99,17880.00,169.99\n248.00,3730.00,27.99,40.99,21.99,36.99,800.00,25.99,25.49,620.00,189.99,218.00,9820.00,274.99,43.99,116.99,281.99,37.99,955.99,1492.99,507.99,95.99,2046.99,35400.00,781400.00,428700.00,22810.00,120.99,133.99,100.99,2808.99,99.99,122.99,102.99,2188.99,3660.00,48.99,22380.00,211.99\n258.00,3880.00,28.99,41.99,22.99,37.99,840.00,26.99,26.49,645.00,197.99,228.00,10210.00,286.99,45.99,121.99,293.99,38.99,993.99,1552.99,527.99,99.99,2129.99,36830.00,812800.00,446000.00,23730.00,125.99,139.99,104.99,2921.99,103.99,126.99,106.99,2276.99,3800.00,50.99,23280.00,220.99\n328.00,4980.00,36.99,53.99,29.99,48.99,1070.00,34.99,33.99,827.00,253.99,288.00,13110.00,367.99,58.99,155.99,376.99,49.99,1275.99,1993.99,677.99,127.99,2732.99,47270.00,1043200.00,572400.00,30460.00,161.99,178.99,133.99,3749.99,132.99,162.99,136.99,2921.99,4880.00,64.99,29880.00,282.99\n398.00,6360.00,46.99,68.99,37.99,61.99,1370.00,43.99,42.99,1056.00,323.99,368.00,16740.00,468.99,74.99,198.99,480.99,63.99,1629.99,2545.99,864.99,163.99,3490.99,60360.00,1332300.00,731000.00,38890.00,205.99,228.99,170.99,4788.99,169.99,208.99,174.99,3731.99,6230.00,82.99,38160.00,361.99\n498.00,7960.00,58.99,86.99,46.99,77.99,1710.00,54.99,53.99,1322.00,404.99,458.00,20950.00,586.99,93.99,248.99,601.99,79.99,2038.99,3185.99,1082.99,204.99,4368.99,75550.00,1667500.00,914900.00,48680.00,257.99,285.99,213.99,5992.99,211.99,260.99,218.99,4670.99,7800.00,102.99,47760.00,452.99\n648.00,9800.00,71.99,106.99,57.99,95.99,2100.00,66.99,65.99,1628.00,498.99,568.00,25790.00,722.99,115.99,305.99,740.99,97.99,2510.99,3922.99,1332.99,251.99,5378.99,93010.00,2053000.00,1126400.00,59930.00,316.99,351.99,262.99,7378.99,260.99,320.99,268.99,5749.99,9600.00,126.99,58800.00,556.99\n758.00,11460.00,83.99,124.99,67.99,111.99,2460.00,78.99,76.99,1903.00,582.99,658.00,30160.00,844.99,134.99,357.99,865.99,113.99,2935.99,4586.99,1557.99,294.99,6289.99,108760.00,2400700.00,1317200.00,70080.00,370.99,410.99,307.99,8628.99,305.99,374.99,314.99,6723.99,11230.00,148.99,68760.00,651.99\n820.00,12400.00,90.99,134.99,72.99,120.99,2660.00,84.99,83.99,2059.00,630.99,708.00,32630.00,914.99,145.99,387.99,936.99,123.99,3176.99,4962.99,1685.99,318.99,6804.99,117680.00,2597600.00,1425200.00,75820.00,400.99,444.99,332.99,9335.99,330.99,405.99,339.99,7275.99,12150.00,160.99,74400.00,704.99\n1000.00,15600.00,114.99,168.99,91.99,151.99,3350.00,106.99,104.99,2591.00,793.99,898.00,41050.00,1150.99,183.99,487.99,1178.99,154.99,3995.99,6243.99,2120.99,400.99,8560.99,148050.00,3268000.00,1793100.00,95390.00,504.99,559.99,418.99,11744.99,415.99,510.99,427.99,9152.99,15280.00,201.99,93600.00,886.99\n1298.00,20880.00,152.99,225.99,122.99,202.99,4480.00,142.99,140.99,3467.00,1061.99,1198.00,54950.00,1539.99,245.99,651.99,1577.99,207.99,5348.99,8357.99,2838.99,536.99,11458.99,198170.00,4374100.00,2400000.00,127680.00,675.99,748.99,560.99,15720.99,556.99,682.99,572.99,12250.99,20450.00,269.99,125280.00,1186.99\n1998.00,32600.00,238.99,352.99,191.99,316.99,6990.00,222.99,218.99,5413.00,1657.99,1868.00,85790.00,2403.99,383.99,1017.99,2463.99,323.99,8350.99,13047.99,4431.99,837.99,17890.99,309400.00,6829300.00,3747100.00,199350.00,1053.99,1169.99,874.99,24543.99,868.99,1066.99,893.99,19126.99,31930.00,421.99,195600.00,1852.99\n2000.00,32800.00,239.99,354.99,192.99,318.99,7030.00,224.99,220.99,5446.00,1667.99,1878.00,86310.00,2418.99,385.99,1024.99,2478.99,325.99,8401.99,13127.99,4459.99,842.99,17999.99,311300.00,6871200.00,3770100.00,200570.00,1060.99,1176.99,879.99,24694.99,873.99,1072.99,898.99,19243.99,32120.00,423.99,196800.00,1863.99\n2598.00,42900.00,313.99,464.99,252.99,416.99,9190.00,293.99,287.99,7123.00,2181.99,2458.00,112890.00,3162.99,504.99,1339.99,3241.99,426.99,10988.99,17170.99,5832.99,1102.99,23542.99,407160.00,8987100.00,4931000.00,262330.00,1386.99,1538.99,1150.99,32298.99,1142.99,1403.99,1175.99,25169.99,42010.00,554.99,257400.00,2437.99\n3648.00,60620.00,443.99,655.99,356.99,589.99,12990.00,414.99,406.99,10065.00,3081.99,3478.00,159520.00,4468.99,712.99,1892.99,4580.99,601.99,15527.99,24262.99,8241.99,1557.99,33267.99,575340.00,12699200.00,6967800.00,370690.00,1959.99,2173.99,1625.99,45639.99,1614.99,1982.99,1660.99,35565.99,59360.00,783.99,363720.00,3443.99\n6000.00,99700.00,728.99,1078.99,586.99,969.99,21360.00,680.99,668.99,16554.00,5068.99,5708.00,262360.00,7349.99,1171.99,3112.99,7533.99,990.99,25537.99,39903.99,13554.99,2561.99,54713.99,946250.00,20886100.00,11459700.00,609670.00,3223.99,3575.99,2673.99,75062.99,2655.99,3261.99,2731.99,58493.99,97630.00,1288.99,598200.00,5663.99\n"]], 0, 0, [], [], []], [[[0, "leitaisai_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "fuben_com", ".bin"], -1], 0, 0, [], [], []], [[[0, "viprankgift_content", ".bin"], -1], 0, 0, [], [], []], [[[0, "hougonglevel", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_boss", ".bin"], -1], 0, 0, [], [], []], [[[0, "cross_nszdz_buff", ".bin"], -1], 0, 0, [], [], []], [[[0, "doubletowerrewardsbattleconfig", ".bin"], -1], 0, 0, [], [], []], [[[0, "mofazhen_type", ".bin"], -1], 0, 0, [], [], []], [[[0, "arena_robot", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_herogrow", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_eventtype", ".bin"], -1], 0, 0, [], [], []], [[[0, "user_main", ".bin"], -1], 0, 0, [], [], []], [[[0, "jin<PERSON>osai_pilao", ".bin"], -1], 0, 0, [], [], []], [[[0, "zhongzutacom", ".bin"], -1], 0, 0, [], [], []], [[[0, "rankgift", ".bin"], -1], 0, 0, [], [], []], [[[0, "pao<PERSON>ng", ".bin"], -1], 0, 0, [], [], []], [[[0, "adventure_bossreward", ".bin"], -1], 0, 0, [], [], []], [[[0, "pata_word", ".bin"], -1], 0, 0, [], [], []], [[[0, "itemaward", ".bin"], -1], 0, 0, [], [], []], [[[4, "../resources/effect/spine-gray", [{"hash": 3732946092, "name": "../resources/effect/spine-gray|sprite-vs:vert|sprite-fs:frag", "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 1, "defines": []}, {"name": "a_color", "format": 44, "location": 2, "defines": []}, {"name": "a_color2", "format": 44, "location": 3, "defines": ["TWO_COLORED"]}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#if USE_LOCAL\n  layout(std140) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n#endif\nin vec3 a_position;\nin vec2 a_texCoord;\nin vec4 a_color;\nout vec4 v_light;\nout vec2 uv0;\n#if TWO_COLORED\n  in vec4 a_color2;\n  out vec4 v_dark;\n#endif\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  #if USE_LOCAL\n    pos = cc_matWorld * pos;\n  #endif\n  pos = cc_matViewProj * pos;\n  uv0 = a_texCoord;\n  v_light = a_color;\n  #if TWO_COLORED\n    v_dark = a_color2;\n  #endif\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  layout(std140) uniform ALPHA_TEST_DATA {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n    if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n    if (alpha < alphaThreshold) discard;\n  #endif\n}\nin vec4 v_light;\n#if TWO_COLORED\n  in vec4 v_dark;\n#endif\nin vec2 uv0;\nuniform sampler2D cc_spriteTexture;\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if TWO_COLORED\n    vec4 texColor = vec4(1, 1, 1, 1);\n    texColor *= texture(cc_spriteTexture, uv0);\n     o.a = texColor.a * v_light.a;\n    o.rgb = ((texColor.a - 1.0) * v_dark.a + 1.0 - texColor.rgb) * v_dark.rgb + texColor.rgb * v_light.rgb;\n  #else\n    o *= texture(cc_spriteTexture, uv0);\n    o *= v_light;\n  #endif\n  float gray  = 0.2126 * o.r + 0.7152 * o.g + 0.0722 * o.b;\n      o.r = o.g = o.b = gray;\n  ALPHA_TEST(o);\n  return o;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision highp float;\nuniform highp mat4 cc_matViewProj;\n#if USE_LOCAL\n  uniform highp mat4 cc_matWorld;\n#endif\nattribute vec3 a_position;\nattribute vec2 a_texCoord;\nattribute vec4 a_color;\nvarying vec4 v_light;\nvarying vec2 uv0;\n#if TWO_COLORED\n  attribute vec4 a_color2;\n  varying vec4 v_dark;\n#endif\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  #if USE_LOCAL\n    pos = cc_matWorld * pos;\n  #endif\n  pos = cc_matViewProj * pos;\n  uv0 = a_texCoord;\n  v_light = a_color;\n  #if TWO_COLORED\n    v_dark = a_color2;\n  #endif\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n      uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n    if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n    if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 v_light;\n#if TWO_COLORED\n  varying vec4 v_dark;\n#endif\nvarying vec2 uv0;\nuniform sampler2D cc_spriteTexture;\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if TWO_COLORED\n    vec4 texColor = vec4(1, 1, 1, 1);\n    texColor *= texture2D(cc_spriteTexture, uv0);\n     o.a = texColor.a * v_light.a;\n    o.rgb = ((texColor.a - 1.0) * v_dark.a + 1.0 - texColor.rgb) * v_dark.rgb + texColor.rgb * v_light.rgb;\n  #else\n    o *= texture2D(cc_spriteTexture, uv0);\n    o *= v_light;\n  #endif\n  float gray  = 0.2126 * o.r + 0.7152 * o.g + 0.0722 * o.b;\n      o.r = o.g = o.b = gray;\n  ALPHA_TEST(o);\n  return o;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "defines": []}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 1}}, "defines": [{"name": "USE_LOCAL", "type": "boolean"}, {"name": "TWO_COLORED", "type": "boolean"}, {"name": "USE_ALPHA_TEST", "type": "boolean"}]}], [{"passes": [{"program": "../resources/effect/spine-gray|sprite-vs:vert|sprite-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"alphaThreshold": {"type": 13, "value": [0.5]}}}]}]]], 0, 0, [], [], []], [[[0, "top_resource", ".bin"], -1], 0, 0, [], [], []], [[[0, "geren_rank_reward", ".bin"], -1], 0, 0, [], [], []], [[[0, "tiaozhuan", ".bin"], -1], 0, 0, [], [], []], [[[0, "huodongrili_time", ".bin"], -1], 0, 0, [], [], []], [[[0, "guanghangong_shop", ".bin"], -1], 0, 0, [], [], []]]]