[1, ["34TV88DN5P7p3jnyKkufkk@f9941", "974jf/rEhCOarFlmlqkpgN@f9941", "b8rN0eouZOHalKp5eGDhlA@f9941", "853LBnMBpLOpcNLPDRSSVU@f9941", "17BnYZ3/pA8KrOZEVE6eOr@f9941", "ecW0D+YP1OsImHAaa34Gh7@f9941", "39gRBB/uFMBI1x5z/Y6k7l@f9941", "4fUNowHzZOnJTYWdmvBTZv@f9941", "ccgUVR1ztC9LB9yfcekGJT@f9941", "9fd+/mbRtPr7Wcy6DZk/wT@f9941", "50OTGHWEdKspGnziAq/Yek@f9941", "cbGWUQkKVBqY0i1aYZvJWx", "54pRRePjVOzZTyWtQutuQ9@f9941", "39ZOeyslZEHI6AwqqUPs3K@f9941", "4699Waa7NDw7UXHf+KwpZq@f9941", "eawsEN/MdNnKHmcZeNJiMR@f9941", "2ayVG8hJpBarfrxV6wAK97@f9941", "51h3QGaatLBJ7v6lOM4rgg@f9941", "e6R2OG6ixI3Jo0z8lhjOpX@f9941", "e9KfuGwrFNZZ7/hN+fQnz3@f9941", "22K4fMbidEb7XJ8om+Fp0n", "74skn2AdZDMrx5cSCC2cZm@f9941", "17Kf1dBR1CIYycXhRcHn1t@f9941", "82sGOQyflHzo3ackoZ0KNV@f9941", "48vou14VJJGJFIKaITIqdO@f9941", "b4UnfG681LJrMCxKxWEoQ1@f9941", "57/j+fauNIK4MhDKvcIXhG@f9941", "11DlhWQuhF/IJTbN81zXbX@f9941", "f1KiPEuSRDIqJgPZgkKPHo@f9941", "e6sd5wPXVJvaxJsVLq4zbF@f9941", "ffalqt7ANHq5wtbrUUXGWR@f9941", "cfs5S8Ot9JnZxlap4FDQgP", "4bvUePSW1My72lVB0hkqFh", "3cpsx0HnhOJK7aDYcBnMue@f9941", "6fFZrfe0JE+ZDOkjJK78bT@f9941", "66QU1v0+BParz5yVS18q/w@f9941", "e8D07I8ixDw4MZi0HVAkv2@f9941", "43fsk9e1JNjrpMNVexTbrJ", "cdUdfMjZJCzpoI6xsn127c@f9941", "b4K/PJwF5Oob5TOtr8PuG/", "68Zui2hoNLLrkC/3PE/4Hu", "ce2syMqX1FZK8vqwF9KcgR@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941", "7d3U4kR6pOIYAFMM9ga/dV@f9941", "d7d8vve4ZKMYDKfgIERoMB@f9941", "62TqFn4d1Nf7IF9cDmVxOu", "36aqYGyCdGvphHV5guU9q1", "999Nn8Fq9Nm4BZlO1ZHqQ6", "8aKRLXL69DPJhKtH3Wxtq1", "4bsIMdzW9DV5xECLt/ZVKr@f9941", "cb0VD7JatJ2YO6y6rcgJxj@f9941", "440+IRP9RLjbazSNufcBvX@f9941", "f4qNAekBdHRZym3st53P3X@f9941", "f0YQ25pP5EuLXbFiXCyrzr@f9941", "3bR+VT7cFFlKV1g4Y3m/Wn@f9941", "8dtNogTpVJtIArqPIfu/Vr@f9941", "b11uvx+I9Mv5aWsZwIAlVp@f9941", "c4WgrRJNlEjb6Tp7UQmJjB@f9941", "a2glWTJTpM2alHykxnhy3U@f9941", "ec2Em/pnhN15m0kgsMzkli@f9941", "68pYHyzgRLz4S7oPwvMKmN@f9941", "ad5dF75QRJmaB2fanBPP10@f9941", "84hl9oWd9EBZE7kFyjRRAG@f9941", "dcvEqf8kBEnqSi+uZD7WsW@f9941", "ce1Mx3GwJNyacJ5pBxQj4x@f9941", "d4y6T7Ys9LOb1AAKAjW69D@f9941", "48gsSNKplB5ocBn7hEu/jP@f9941", "bb9canxF5P35AK3q0ZhM6c@f9941", "efM3UbWGREioL5NhcqsCKn@f9941", "7dePS4P8xCWIlnfW0yoDh7@f9941", "72uW2nbvRBbYx2BSAIXOFT@f9941", "aef1UMYo5Iu64U04vIna8U@f9941", "b3h9LtuFJPq5yNhiBfEYDR@f9941", "c4tB7dCbhFWphgqKTPSAwc@f9941", "4aYgc4oKZMPY1X26wr3YIA@f9941", "fdsHUofqxHKI5LU2Yl56Ry@f9941", "2bZCCbmu5O3IVXm6+Ixx6r@f9941", "32jSqm1x9D3KFri5WF4V4I@f9941", "da/QjvJJxEi4pI3WdiVyDq@f9941", "22d/5Qt+VAV7S03ge6cim4@f9941", "1c0XaxUCdG+5SZVcz117zC@f9941", "d4lwQpthRA/rM3tXdDoppm@f9941", "d9kXEyjQ5OS6iU0A52aZZD@f9941", "ccIYNe4NZGGZMywFk4uCcK@f9941", "8cuuDref1IM7b2lym1umtR@f9941", "b7o2Se0VlCboeDKRZfaziy@f9941", "01kSg+CjVNC4lU/KGApFME@f9941", "e8CJL6ZStJJ5y8cILtRvHH@f9941", "f6dZAQJelOm5RkYl91fFhD@f9941", "386FymfBdAm7Ne6nIIQkso@f9941", "04UtJgHbZKLbCEJKwD/4f7@6c48a", "06x+PNawxH5bMwPSiOzsKo@f9941", "04UtJgHbZKLbCEJKwD/4f7@f9941", "a4rEewtvVC77ozW4ISPmEd@f9941", "8829DhdD5GArlKykkOv0Cd@f9941", "381sh3A5JNL57C84ovXdS/@f9941", "d4Fj+GkHhA8bZZFASTQThD@f9941", "4cWCCHzChG+LDzStm2ipGn@f9941", "a4QFN+HdJHApxkSRUFoT2x@f9941", "41c7QQ/vBK4q8dIh69UUC9@f9941", "32bhK74TpCYrhlKHPjfk4k@f9941", "f60Y7i+GZGVYHF5qSPc1kM@f9941", "18HfYaJr9MTZGMb/qK0Juy@f9941", "23lXmuLNRNSrRwhMWwHplL", "c4EBO7QPdIZZybXGF+UwBe", "12eBv/OCtAn7mUSTwsKyEp@f9941", "60JjOrGjhCo4cYwT225T5i", "a8PVzHl01EXZ8HqK7YMMDS", "7dj5uJT9FMn6OrOOx83tfK@f9941", "a9nqHeL2ZLo7tCRZOtBSQu@f9941", "bbETP+byhBN4TeISsx9XKY@f9941", "55lmbq019GbYTNhlYp79sE@f9941", "c69/1J7Y9CU4Njtd5e0g5w", "75fRLAw8lADqOyLJ74lKiO@f9941", "96IpwjVq9OG5dNb/LsYx9T@f9941", "ceEb9fdq9Lu4Ozf2Bc3vSn@f9941", "b2jchlp5JM9aDDtlcrqovg@f9941", "a7R5RZ8L9P9Jv/Gpxdze5e@f9941", "952G/v33hDxbq9Xgbnbb/j@f9941", "0fFp+a60NJa7dgAalTSMX0@f9941", "c0GOA9SIdBbIlKZ7CVdHSD@f9941", "7b9eEukopHBYj9E5xpauyH@f9941", "45ZinsoNNHFavdB5J2HRzD@f9941", "7ae5OgfvVCXICBpIY6jfd9@f9941", "acvyU3aGhM0L2Lx933eC5+@f9941", "7fjlhbNO9GG4QAYC3kLlpS@f9941", "79LKch54xNvaVkTB/b2e6o@f9941", "1dQaC3kh5NsKHEuVEzlXt1@f9941", "a2wtSHnrJCBqaFoXZ2V/hZ@f9941", "0f79OENwlBlJ+taRfehoSZ@f9941", "1ef2nvtFBCCL33lxTSXy7m@f9941", "4cq1PmTfxB0qjFk1pneFuV@f9941", "aeM7Q4O+NHOZhun9Wqr7mi@f9941", "11+vO+FURJ3o6DYilMgfx8@f9941", "d5ughqB/tCQLrBt4cUegKV@f9941", "95Rm4DJLtLBYWlrAJ6hZsO", "36w2doIfJMB6p9E3IIh8Gm@f9941", "11l0ZShjZNeJtqIB4JhgFh@f9941", "35K2GKsU1PILiTgFODZoBt@f9941", "a3en2XD5BBIK0pX/B8uUCb@f9941", "aetT+LZ4RDP7pMw3fyOfnX", "2f/oRnBmxDOb2L/2WYSIn1@f9941", "42TpcvuVhO84YqIf0xG7LB@f9941", "9crx8zCoxE1bqoih0QnRtM", "9fbcwLUnBDj7npDjBAY9hE@f9941", "04gASTk7dGVKDgpPxY5evU@f9941", "0dTU4vv49Nur6oJai/BHho@f9941", "d7Adp79D1AOqYrTxyGICVT", "b3Bnn4jkZNxrxVQNu1EnWi", "fchYVKjHNP8ah6aEcsGnj8@f9941", "bbZ+WBRaJIObSBAR0ndE96@f9941", "1buUFU4s1B4boLnOJf22rl", "ebRkZlkxFKCrsNYqVDzZl2@f9941", "00gwbKl8pEio8B6oehO20J@f9941", "8dVAB35EtBirTcLiRB4+0t", "d8DA9a1MJHQp/kUeaGXxL9", "6abQg4BWFNWYPrgmIcHPBp", "f7axYcz55F6o2AegJ/nnDV", "8dbkhG1ehN9aboaoBJlsil", "2ad/oOp+VEa5jqZ4+gzhm4@f9941", "055x8OrAxLo54ed0d3L4ny@f9941", "40++ZBIeJDKID2oG0ujiBm", "e3+5PDDOtHA6zwFPZ5RfZw@f9941", "666be684RM2pg0CVJkRU+V@f9941", "40ksEng9JLbaUttsW9YPz3@f9941", "440FE6jqREu4vV+D7lQizA@f9941", "e7ePiWma1JDYoFZarE3/wH", "34TV88DN5P7p3jnyKkufkk@6c48a", "4fUNowHzZOnJTYWdmvBTZv@6c48a", "95EkngnxZFbYuFpsqVTaFr@6c48a", "a4QFN+HdJHApxkSRUFoT2x@6c48a", "06fCs0T65CUpBq6S6/BB1O@f9941", "083LJLTkhBvoppzRLO/jE1", "86ge5ZzyJIfJyPnk9AdqmP", "ceEb9fdq9Lu4Ozf2Bc3vSn@6c48a", "f1KiPEuSRDIqJgPZgkKPHo@6c48a"], ["node", "_spriteFrame", "_skeletonData", "_parent", "root", "_target", "asset", "_textureSource", "_barSprite", "data", "targetInfo", "value", "spriteFrame", "_tmxFile", "material", "_content", "_font"], [["cc.Label", ["_actualFontSize", "_string", "_isBold", "_fontSize", "_enableOutline", "_outlineWidth", "_lineHeight", "_overflow", "_enableWrapText", "_horizontalAlign", "_enableShadow", "_shadowBlur", "_verticalAlign", "_isSystemFontUsed", "_spacingX", "node", "__prefab", "_color", "_outlineColor", "_shadowColor", "_shadowOffset", "_font"], -12, 1, 4, 5, 5, 5, 5, 6], ["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos", "_lscale", "_euler", "_lrot"], -2, 4, 9, 1, 2, 5, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_useGrayscale", "_isTrimmedMode", "node", "__prefab", "_spriteFrame", "_color"], -1, 1, 4, 6, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_affectedByScale", "_spacingY", "_spacingX", "_enabled", "_verticalDirection", "_paddingTop", "_paddingBottom", "node", "__prefab"], -6, 1, 4], ["cc.Widget", ["_alignFlags", "_originalHeight", "_originalWidth", "_bottom", "_top", "_enabled", "_left", "_right", "node", "__prefab"], -5, 1, 4], "cc.SpriteFrame", ["sp.Skeleton", ["_premultipliedAlpha", "_preCacheMode", "defaultSkin", "defaultAnimation", "loop", "node", "__prefab", "_skeletonData"], -2, 1, 4, 6], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "_enabled", "node", "__prefab", "_target", "_normalColor"], 0, 1, 4, 1, 5], ["StatusData", ["status", "fileId", "label_font", "gradient_material", "grayscale", "active", "rotation", "scale", "size", "color", "spriteFrame", "position"], -3, 5, 5, 5, 5, 6, 5], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedChil<PERSON>n", "mountedComponents"], 2, 1, 9, 9, 9], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.TiledMap", ["_enableCulling", "node", "__prefab", "_tmxFile"], 2, 1, 4, 6], ["cc.Mask", ["_enabled", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["_enabled", "node", "__prefab", "_fillColor"], 2, 1, 4, 5], ["<PERSON><PERSON>", ["elastic", "_enabled", "inertia", "node", "__prefab", "_content"], 0, 1, 4, 1], ["cc.T<PERSON><PERSON>ayer", ["node", "__prefab", "_color"], 3, 1, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["011c8MZ++JCbqPChKjiX2MO", ["_statusIndex", "currStatusName", "statusNameArray", "node", "__prefab", "statusNodes", "statusData"], 0, 1, 4, 2, 9], ["cc.ProgressBar", ["_totalLength", "_progress", "node", "__prefab", "_barSprite"], 1, 1, 4, 1], ["7563fP+WuxL1p5rM8Q2NGlY", ["node", "__prefab", "spfs"], 3, 1, 4, 3], ["cc.TargetInfo", ["localID"], 2], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4], ["80801awInNCm55tEhtITSKB", ["mix1", "mix2", "mix3", "mix4", "node", "__prefab", "color", "material"], -1, 1, 4, 12, 6], ["cc.UIOpacity", ["_opacity", "node", "__prefab"], 2, 1, 4], ["cc.MountedChildrenInfo", ["targetInfo", "nodes"], 3, 4, 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 6], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 6], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 2], ["0ffbcbl89xEj5yYYH4SQgf+", ["i18n_string", "i18n_params", "node", "__prefab"], 1, 1, 4], ["cc.RichText", ["_lineHeight", "_string", "_horizontalAlign", "_verticalAlign", "_fontSize", "node", "__prefab", "_fontColor"], -2, 1, 4, 5], ["cc.TiledMapAsset", ["_name", "tmxXmlStr", "spriteFrameNames", "tsxFileNames", "tsxFiles", "spriteFrames", "spriteFrameSizes"], -1, 3, 3, 12], ["cc.TextAsset", ["_name", "text"], 1]], [[18, 0, 2], [21, 0, 1, 2, 3, 4, 5, 5], [7, 0, 1, 2, 1], [2, 4, 5, 6, 1], [1, 0, 1, 7, 6, 5, 9, 10, 3], [7, 0, 1, 2, 3, 1], [1, 0, 1, 7, 8, 6, 5, 9, 3], [26, 0, 2], [1, 0, 1, 7, 6, 5, 3], [1, 0, 1, 7, 6, 5, 10, 3], [2, 4, 5, 1], [25, 0, 1, 2, 1], [27, 0, 1, 2, 2], [1, 0, 1, 7, 6, 5, 9, 3], [34, 0, 1, 2, 2], [32, 0, 1, 2, 2], [1, 0, 2, 1, 7, 8, 6, 5, 9, 4], [1, 0, 1, 7, 8, 6, 5, 3], [2, 0, 4, 5, 6, 2], [1, 0, 1, 8, 6, 5, 9, 3], [2, 1, 0, 4, 5, 6, 3], [0, 1, 0, 3, 6, 2, 4, 5, 15, 16, 18, 8], [31, 0, 1, 2, 3], [1, 0, 2, 1, 7, 6, 5, 9, 4], [6, 2, 3, 0, 1, 5, 6, 7, 5], [1, 0, 2, 1, 7, 6, 5, 4], [1, 0, 2, 1, 7, 8, 6, 5, 4], [33, 0, 1, 2, 3], [1, 0, 1, 8, 6, 5, 3], [1, 3, 4, 7, 5, 3], [22, 0, 1, 2, 3, 4, 5, 4], [8, 0, 1, 3, 4, 5, 3], [16, 0, 1, 1], [4, 0, 2, 1, 8, 9, 4], [4, 5, 2, 1, 8, 9, 4], [0, 1, 0, 3, 6, 2, 4, 5, 15, 16, 8], [10, 0, 1, 2, 2], [17, 0, 2], [11, 0, 1, 2, 3, 4, 5, 3], [24, 0, 1, 2, 3, 4, 3], [0, 1, 0, 3, 2, 4, 5, 15, 16, 17, 7], [0, 1, 0, 3, 6, 2, 4, 5, 15, 16, 17, 8], [0, 1, 0, 3, 2, 4, 5, 15, 16, 7], [1, 0, 1, 7, 8, 6, 5, 9, 10, 3], [2, 1, 0, 4, 5, 3], [2, 1, 4, 5, 6, 2], [6, 0, 1, 5, 6, 7, 3], [7, 0, 1, 1], [4, 0, 8, 9, 2], [4, 0, 4, 8, 9, 3], [3, 1, 4, 2, 9, 10, 4], [3, 0, 1, 9, 10, 3], [2, 4, 5, 7, 6, 1], [1, 0, 2, 1, 8, 6, 5, 9, 4], [1, 0, 2, 1, 7, 6, 5, 9, 10, 4], [2, 1, 0, 4, 5, 7, 6, 3], [4, 0, 3, 8, 9, 3], [3, 5, 0, 1, 4, 9, 10, 5], [3, 0, 1, 4, 9, 10, 4], [3, 0, 1, 4, 2, 9, 10, 5], [8, 0, 1, 3, 4, 3], [0, 1, 0, 3, 7, 8, 2, 4, 5, 15, 16, 17, 9], [0, 1, 0, 3, 6, 2, 10, 11, 15, 16, 17, 19, 20, 8], [0, 1, 0, 6, 7, 8, 2, 4, 5, 15, 16, 17, 9], [6, 0, 1, 4, 5, 6, 7, 4], [6, 0, 1, 5, 6, 3], [38, 0, 1, 2, 3, 3], [41, 0, 1, 3], [1, 0, 2, 1, 7, 6, 5, 10, 4], [2, 0, 4, 5, 2], [2, 1, 0, 2, 4, 5, 7, 6, 4], [4, 0, 6, 7, 2, 8, 9, 5], [19, 0, 1, 1], [3, 0, 1, 3, 2, 9, 10, 5], [3, 0, 1, 3, 6, 2, 9, 10, 6], [3, 0, 1, 3, 9, 10, 4], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3, 2], [0, 1, 0, 3, 2, 15, 16, 5], [0, 1, 0, 3, 6, 7, 2, 4, 5, 15, 16, 18, 9], [0, 1, 9, 0, 3, 2, 15, 16, 17, 6], [0, 1, 9, 0, 3, 8, 2, 4, 5, 15, 16, 9], [0, 1, 0, 6, 2, 15, 16, 5], [0, 1, 0, 3, 6, 2, 15, 16, 17, 6], [28, 0, 1, 2, 3, 4, 5, 6, 7, 5], [6, 2, 0, 1, 5, 6, 7, 4], [35, 0, 1, 2, 2], [36, 0, 1, 2, 2], [39, 0, 1, 2, 3, 4, 5, 6, 7, 6], [40, 0, 1, 2, 3, 4, 5, 6, 5], [1, 0, 1, 8, 6, 5, 9, 10, 3], [1, 0, 2, 1, 7, 8, 6, 5, 10, 4], [1, 0, 2, 1, 7, 8, 6, 5, 9, 11, 4], [1, 0, 1, 7, 6, 5, 9, 12, 11, 3], [1, 0, 1, 7, 6, 5, 12, 11, 3], [1, 0, 1, 8, 6, 5, 10, 3], [11, 0, 1, 2, 3, 4, 5, 6, 3], [7, 0, 1, 3, 1], [2, 0, 4, 5, 7, 6, 2], [2, 0, 3, 4, 5, 3], [4, 0, 4, 3, 1, 8, 9, 5], [4, 0, 4, 3, 2, 1, 8, 9, 6], [4, 0, 1, 8, 9, 3], [4, 0, 3, 1, 8, 9, 4], [4, 5, 0, 2, 1, 8, 9, 5], [20, 0, 1, 2, 3, 4, 5, 4], [3, 5, 0, 1, 3, 2, 9, 10, 6], [3, 0, 1, 7, 8, 3, 9, 10, 6], [3, 0, 1, 2, 9, 10, 4], [3, 0, 4, 9, 10, 3], [12, 1, 2, 3, 1], [12, 0, 1, 2, 3, 2], [13, 1, 2, 1], [14, 1, 2, 3, 1], [8, 2, 0, 1, 3, 4, 4], [8, 2, 0, 1, 3, 4, 6, 5, 4], [15, 0, 3, 4, 5, 2], [15, 1, 0, 2, 3, 4, 5, 4], [23, 0, 1, 2, 3, 4, 5, 6, 4], [9, 0, 1, 2, 3, 11, 6, 7, 8, 9, 10, 5], [9, 0, 1, 4, 2, 3, 6, 7, 8, 9, 10, 6], [9, 0, 1, 4, 2, 3, 11, 6, 7, 8, 9, 10, 6], [9, 0, 1, 5, 4, 2, 3, 6, 7, 8, 9, 10, 7], [0, 1, 0, 3, 6, 7, 8, 2, 4, 5, 15, 16, 18, 10], [0, 1, 0, 6, 7, 8, 2, 4, 5, 15, 16, 18, 9], [0, 1, 9, 0, 3, 7, 8, 2, 15, 16, 8], [0, 1, 9, 0, 3, 2, 15, 16, 6], [0, 1, 0, 6, 2, 15, 16, 17, 5], [0, 1, 12, 0, 3, 6, 13, 14, 2, 4, 5, 15, 16, 18, 21, 11], [0, 1, 0, 3, 2, 15, 16, 17, 5], [0, 1, 9, 0, 3, 7, 8, 2, 4, 5, 15, 16, 10], [0, 0, 15, 16, 2], [0, 1, 0, 3, 7, 8, 2, 15, 16, 7], [0, 1, 0, 6, 7, 2, 4, 5, 15, 16, 18, 8], [29, 0, 1, 2, 2], [10, 0, 1, 3, 2, 2], [10, 0, 1, 4, 2, 2], [30, 0, 1, 1], [37, 0, 1, 1], [16, 0, 1, 2, 1]], [[[{"name": "mapbg_002", "rect": {"x": 0, "y": 0, "width": 1117, "height": 241}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1117, "height": 241}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-558.5, -120.5, 0, 558.5, -120.5, 0, -558.5, 120.5, 0, 558.5, 120.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 241, 1117, 241, 0, 0, 1117, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -558.5, "y": -120.5, "z": 0}, "maxPos": {"x": 558.5, "y": 120.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [7], [90]], [[[37, "gvg_ghg_main"], [28, "gvg_ghg_main", 33554432, [-16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35], [[2, -12, [0, "35MvXfwqpFtZPh7cUwC+Ge"], [5, 640, 1280]], [55, 1, 0, -13, [0, "2dV3rtOZJGR5kK65hBuX2L"], [4, 4288049443], 166], [33, 45, 640, 1280, -14, [0, "63/3mCJClGRL9OKnur7CtF"]], [72, -15, [0, "95Gzuwh5NJMZ8mFbIGrYY+"]]], [105, "c46/YsCPVOJYA4mWEpNYRx", null, null, -11, 0, [-1, -2, -3, -4, -5, -6, -7, -8, -9, -10]]], [90, "mapbg", 33554432, [-38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49], [[2, -36, [0, "8eYY3lWe1FD4XRQfo9noga"], [5, 6700, 4300]], [44, 1, 0, -37, [0, "92+vyZ72VPxqhvg5nM+wIB"]]], [1, "5c3fX66jFMd5enJzO39OM3", null, null, null, 1, 0], [1, 0, -30, 0], [1, 1.04, 1.04, 1]], [28, "content", 33554432, [2, -51, -52, -53, -54, -55, -56, -57, -58, -59], [[2, -50, [0, "86RVz2oPVMvZU7978GHaTJ"], [5, 6700, 4200]]], [1, "73NlN3WXhOUrly2SPgfsDk", null, null, null, 1, 0]], [53, "gvg_ghg_grid_me", false, 33554432, [-62, -63, -64, -65, -66, -67, -68, -69, -70], [[2, -60, [0, "88OUjXBEhKtaON/FE8YY/x"], [5, 139.1, 163.6]], [34, false, 640, 1280, -61, [0, "2f3DNUkhNLJ7VNWnXR7+3j"]]], [1, "c5iMgLHg1Go6YJ9jj1Ddy8", null, null, null, 1, 0], [1, -799.495, 0, 0]], [53, "gvg_ghg_grid_select", false, 33554432, [-73, -74, -75, -76, -77, -78, -79, -80, -81], [[2, -71, [0, "1dpXavTgZD2KvrDvStKcAq"], [5, 139.1, 163.6]], [34, false, 640, 1280, -72, [0, "7blC7FZTtIaoXkhqqcR3Se"]]], [1, "65nu/ycMhOTLzF8XjVabgr", null, null, null, 1, 0], [1, -1031.602, 202, 0]], [6, "bot_ui", 33554432, 1, [-85, -86, -87, -88, -89, -90, -91, -92], [[2, -82, [0, "35SLZs5VZPPbt9ohvfASjB"], [5, 640, 464.244]], [10, -83, [0, "a24hes46FGOrsptfSVEt3l"]], [48, 4, -84, [0, "b12Mi0+0pAUbIQw6izZgH0"]]], [1, "c3m3iVMKdNzaSXKGCazS46", null, null, null, 1, 0], [1, 0, -407.878, 0]], [19, "layout_right", 33554432, [-97, -98, -99, -100], [[5, -93, [0, "ecvIW5zd9PkIQWK/HZdagm"], [5, 250, 381], [0, 0.5, 1]], [69, 0, -94, [0, "4bvZTB5zZA7p2bz8e25BvP"]], [73, 1, 2, 2, true, -95, [0, "e6TXwYKvhL2YUZpTn5CBap"]], [49, 1, 7.803, -96, [0, "bekZxpSnxJ/I+pRTVIpQTK"]]], [1, "0d+xtWusZBhJUpT/izqXfx", null, null, null, 1, 0], [1, -190.966, -7.803, 0]], [53, "item_zy1", false, 33554432, [-103, -104, -105, -106, -107, -108], [[2, -101, [0, "e7ryJOmoBJvIfYGuKq8NQR"], [5, 246, 45]], [3, -102, [0, "1esfcvrSVO/5sTOpLbMM84"], 129]], [1, "3fvN/caBFBKJoO6xUnPAr2", null, null, null, 1, 0], [1, 0, -54.5, 0]], [17, "ghz", 33554432, 3, [-111, -112, -113, -114, -115], [[2, -109, [0, "3dCgA9gkpFvKMvrAEOECvu"], [5, 6626, 4225]], [110, -110, [0, "53VyqYRVhJj4/fY25fwPiZ"], 12]], [1, "6dUeZyms1Mb7wjiRFS2tmR", null, null, null, 1, 0]], [6, "btn_right", 33554432, 6, [-118, -119, -120, -121, -122], [[5, -116, [0, "f5KJJyV6pEKK1nJnGzBB9n"], [5, 100, 448], [0, 0.5, 0]], [74, 1, 2, 20, 0, true, -117, [0, "81Mkp1W/BN0J53HfZ1gheZ"]]], [1, "06nuJO+alEwYx5CufVaChQ", null, null, null, 1, 0], [1, 270.642, 55.871, 0]], [19, "layout_chat", 33554432, [-127, -128, -129], [[5, -123, [0, "205MYPfhtLuL4yY+opNaka"], [5, 562.757, 36], [0, 0, 0.5]], [50, 1, 2, true, -124, [0, "83fhRsS1tHzJuhyrN37FBZ"]], [112, -125, [0, "4f+U+rkcBF2K2ZfhEvH9AU"]], [113, -126, [0, "23qZCkpOZEtYgZx4d26F4Y"], [4, 16777215]]], [1, "baz7/Z0zVMWYJf7ex9D8ge", null, null, null, 1, 0], [1, -265.012, 0, 0]], [6, "btn_hc", 33554432, 6, [-134, -135, -136], [[2, -130, [0, "afEe7i/19AWKAej//5rKib"], [5, 128, 212]], [3, -131, [0, "9ePzDavXFNc4I7JypWp2vQ"], 105], [31, 3, 0.9, -133, [0, "71ItqqhzJOOLV+G/xNJQtl"], -132]], [1, "e3OIQaVCtIYZ0vQ6aYspRJ", null, null, null, 1, 0], [1, 243.453, -80.368, 0]], [16, "fighthead", false, 33554432, 6, [-140, -141, -142, -143], [[2, -137, [0, "2fkLQ8lLhAxaOpm0b/PUGm"], [5, 333.8, 115]], [57, false, 1, 1, -41.1, -138, [0, "c1EN3Iii9Cl5ByFr//AUwM"]], [56, 4, 280.116, -139, [0, "b04avhMMxHpqkIyHniq2r9"]]], [1, "deiBL17ONOJaOiIWnZ53Gg", null, null, null, 1, 0], [1, 0, 105.49399999999997, 0]], [6, "item_map", 33554432, 7, [-146, -147, -148, -149, -150], [[5, -144, [0, "72P3JQoghEVq5CmT1zgcSE"], [5, 246, 240], [0, 0.5, 1]], [20, 1, 0, -145, [0, "a4F0L65hNG6pSfRHQTO9eL"], 121]], [1, "46jgNkiDdD2L1fZR5Fxc/M", null, null, null, 1, 0], [1, 0, -107, 0]], [19, "smallMap", 33554432, [-153, -154, -155, -156, -157], [[2, -151, [0, "b5Av7NmbZM3q3SCupsJNWa"], [5, 6626, 4225]], [111, false, -152, [0, "87FUy2v5NLv7PvFi90k3LN"], 117]], [1, "ebAAV2mZ1MxbIdugzD+KyD", null, null, null, 1, 0], [1, 177.579, 0, 0]], [16, "fightheadstart", false, 33554432, 1, [-161, -162, -163, -164], [[2, -158, [0, "bfIvfZoM1KboUjv1HeI97v"], [5, 333.8, 115]], [57, false, 1, 1, -41.1, -159, [0, "99MyZCeRhLM7MtVu6bEKFR"]], [100, 1, 501.091, 663.909, 115, -160, [0, "1bs7NRjQxCA4Tb5Wn2xvAL"]]], [1, "c9IswKJPtDkbDHhARQYmMJ", null, null, null, 1, 0], [1, 0, 761.409, 0]], [17, "mapScroll", 33554432, 1, [3], [[2, -165, [0, "b0CFpc/eBM5YM1ik0WhtOE"], [5, 640, 1080]], [116, false, -166, [0, "4dbBzqXd9H6qVpEXOYjF00"], 3], [76, false, -167, [0, "e7u8Be1w5J2qkX9O3YLo3R"]], [77, false, -168, [0, "c9kjTgtONOLIJdfQL1iXNu"], [4, 16777215]], [101, 45, 100, 100, 640, 700, -169, [0, "4bYGzjow9CrYVoAZ2LOziC"]]], [1, "41s1jmvWVPDJT18mOCuTtR", null, null, null, 1, 0]], [26, "nr_tips_revive", false, 33554432, 1, [-173, -174, -175], [[2, -170, [0, "0diyF5N09F+Lng/CL4enbX"], [5, 640, 1280]], [102, 5, 1280, -171, [0, "03lM7RO+xOKLPtRMV27pJC"]], [72, -172, [0, "281kxduOpM5q7WhSujSCZH"]]], [1, "b9sivvI3BCrZ/sELerqYI0", null, null, null, 1, 0]], [6, "btn_fh", 33554432, 18, [-179, -180, -181], [[2, -176, [0, "15nhrj6fNOyKUTk1N7yPGN"], [5, 509, 131.5]], [18, 0, -177, [0, "04EypvXBNJ6Zji5wGtug/r"], 67], [56, 4, 38.498000000000054, -178, [0, "8auSQZH+1DZKFPoL5qfINv"]]], [1, "ceAlj7zrNG+IhXQko/qJbz", null, null, null, 1, 0], [1, 0, -535.752, 0]], [6, "btn_gh1", 33554432, 6, [-186, -187], [[2, -182, [0, "688/Y6i/BGCrtteqd6Cq8Z"], [5, 128, 212]], [3, -183, [0, "3fLb7Y4V9P6LYaybUShqAY"], 69], [31, 3, 0.9, -185, [0, "00nNlULmFAwJ8ZhrDYwg6P"], -184]], [1, "81+FqQWypJzYlz2LUtst+g", null, null, null, 1, 0], [1, -250.128, -80.368, 0]], [6, "btn_left", 33554432, 6, [-190, -191, -192, -193], [[5, -188, [0, "75JBm+jJJDqaMDAogDlBnH"], [5, 100, 293], [0, 0.5, 0]], [74, 1, 2, 20, 0, true, -189, [0, "78nfmwoTxJQb3u5nYkSvZ5"]]], [1, "e4N9OELgBOtpOXzbTZlJdQ", null, null, null, 1, 0], [1, -260.349, 56.478, 0]], [6, "bot_chat", 33554432, 6, [-199, 11], [[2, -194, [0, "109mmuFSVH54ukmMrZ8R1E"], [5, 640, 31]], [3, -195, [0, "e04nxerV1AgJtrsTQcM/L2"], 82], [56, 4, 5, -196, [0, "dd1PV8DwNHrqHu/qLrHybB"]], [31, 3, 0.9, -198, [0, "eb7aB3y9JHv7XM8vSBR1r4"], -197]], [1, "b9bFIjazpJh7T7eIDNqbsJ", null, null, null, 1, 0], [1, 0, -211.622, 0]], [19, "img_gvg_di20", 33554432, [-205, -206], [[2, -200, [0, "f3+pwbHFBN6bj7Re1jZBq7"], [5, 89, 32]], [45, 1, -201, [0, "95Zdo7CQ9NKK0W2PZ2gt8L"], 85], [118, 1, "close", ["open", "close"], -204, [0, "14g4q2XO5OXItFr8xi9oHv"], [-202, -203], [[119, "open", "7fPn7Iw2BIkathu8Ltm+PH", null, null, [1, 17.5, 0, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 44, 22], [1, 114, 222, 173], 86], [120, "open", "76KQLJ2h9KgqUhG2iAxdRx", true, null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 79, 22], [1, 0, 81, 44], 87], [121, "close", "7fPn7Iw2BIkathu8Ltm+PH", true, null, null, [1, -17.5, 0, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 44, 22], [1, 173, 172, 173], 88], [122, "close", "76KQLJ2h9KgqUhG2iAxdRx", false, true, null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 79, 22], [1, 0, 81, 44], 89]]]], [1, "27O4bMLCRLH4KsFL3jyLAn", null, null, null, 1, 0], [1, -62.546, 0.77, 0]], [26, "item_zddl1", false, 33554432, 1, [-209, -210, -211, -212], [[2, -207, [0, "d9gxVKpCdBwqcv9/ogfJkx"], [5, 184, 54]], [18, 0, -208, [0, "b6nn/tvN9L574VI1CcbLwx"], 145]], [1, "f567mC7R1K9oljzOf3bBlF", null, null, null, 1, 0]], [16, "fighthead1", false, 33554432, 1, [-215, -216, -217, -218], [[2, -213, [0, "dbyH6NqIVMdb32dUd2GgeT"], [5, 333.8, 115]], [57, false, 1, 1, -41.1, -214, [0, "168BBtjrJKdog871mS/NUM"]]], [1, "eark3BzDdL2791ielWnNlq", null, null, null, 1, 0], [1, 0, 354.3080000000001, 0]], [17, "gridMap", 33554432, 3, [4, 5, -221], [[2, -219, [0, "cazSWeQa5EB5HtO524PmYi"], [5, 6700, 4200]], [33, 45, 3476, 3961.5, -220, [0, "1eOh0f6MpFrJ9Ykcm4s069"]]], [1, "76FXwDq+9JIKVLkArqrrC7", null, null, null, 1, 0]], [43, "item_head", 33554432, 4, [-224, -225, -226], [[2, -222, [0, "77906I+EpLu7a5vg1NiMXX"], [5, 43, 43]], [10, -223, [0, "875ez2nJVIAK/FeXe/341I"]]], [1, "f8vTaRvuFANb6gBAOI59uy", null, null, null, 1, 0], [1, -53.863, 39.648, 0], [1, 0.8, 0.8, 1]], [6, "layout_dot", 33554432, 4, [-229, -230, -231], [[2, -227, [0, "5325kU4PpPgY/YCBqrgLl1"], [5, 57, 20]], [58, 1, 1, 3, -228, [0, "0bgqNSZ0xPPq3tbw2GNu76"]]], [1, "87MynbfSdMIJGF/8n4WY32", null, null, null, 1, 0], [1, 0, -37.606, 0]], [19, "fightPro", 33554432, [-236, -237], [[2, -232, [0, "7dsC8SUDdAgJb/GlUsP8U6"], [5, 127, 10]], [20, 1, 0, -233, [0, "4erwQ20KVJ4ZNCKHi2oUfh"], 32], [39, 125, 0.4, -235, [0, "acLjIoX1hGe5Bzbn1sT5AY"], -234]], [1, "a04PMyenhK5pr7DuDb8lYi", null, null, null, 1, 0], [1, 0, -29.588, 0]], [6, "layout_dot", 33554432, 5, [-240, -241, -242], [[2, -238, [0, "53gQcY+BhLgLkb1qdy0gAX"], [5, 57, 20]], [58, 1, 1, 3, -239, [0, "43Krv4napO3rg0FFq+LUnF"]]], [1, "55hUKCeytHGJnvNOLx/n3K", null, null, null, 1, 0], [1, 0, -37.606, 0]], [19, "fightPro", 33554432, [-247, -248], [[2, -243, [0, "38n/zdd9FJXLEgFdD2qfL+"], [5, 127, 10]], [20, 1, 0, -244, [0, "64jrbAqCNJKbEwDNA6xNfB"], 54], [39, 125, 0.4, -246, [0, "1ftsbLDMZCo62BPxHS5p79"], -245]], [1, "1agGxVA+1J+oMOYGIZ6cwm", null, null, null, 1, 0], [1, 0, -29.588, 0]], [6, "btn_dw", 33554432, 21, [-252, -253], [[2, -249, [0, "067sk1VvREZ6HLzE8hUg11"], [5, 80, 85]], [31, 3, 0.9, -251, [0, "65oYbDdVlJV5SFdtvmfxwB"], -250]], [1, "c4aBXPEwpPnYPXA/Q5pGp2", null, null, null, 1, 0], [1, 0, 145.5, 0]], [6, "btn_zb", 33554432, 21, [-257, -258], [[2, -254, [0, "48WL5PtCtHQZhNS17DQAu6"], [5, 80, 85]], [31, 3, 0.9, -256, [0, "430OmelgJCBbA3Bn/4Sg9m"], -255]], [1, "54Bqnoj75DJIOmrWuTp4ls", null, null, null, 1, 0], [1, 0, 250.5, 0]], [6, "btn_zb1", 33554432, 10, [-262, -263], [[2, -259, [0, "4dSj1a85NDlrB/a+fvKLGg"], [5, 80, 85]], [31, 3, 0.9, -261, [0, "3c3VJCpcJPtbsm9+sgInzI"], -260]], [1, "57P2iNh5ZPfoUVZuBQOgDk", null, null, null, 1, 0], [1, -10.221, 42.5, 0]], [6, "btn_ph", 33554432, 10, [-267, -268], [[2, -264, [0, "dfnI+F2KtANoIOyup+uit0"], [5, 80, 85]], [31, 3, 0.9, -266, [0, "53pUDOwAlBL4vatewAOWeR"], -265]], [1, "358HfEwshD276ur3/Ki9q5", null, null, null, 1, 0], [1, -10.221, 147.5, 0]], [6, "btn_gz", 33554432, 10, [-272, -273], [[2, -269, [0, "1bquTo6dlM1qupXzE1hDwm"], [5, 80, 85]], [31, 3, 0.9, -271, [0, "e6+6FKwuRFhKRI42wd8HpJ"], -270]], [1, "caloqziUhKxb3nHjmbD6/J", null, null, null, 1, 0], [1, -10.221, 252.5, 0]], [6, "btn_cs", 33554432, 10, [-277, -278], [[2, -274, [0, "b7dO5e0CJJq73sD9+35XwE"], [5, 80, 85]], [31, 3, 0.9, -276, [0, "1eKS+0wGJGxaBUJPcysDeF"], -275]], [1, "4bcIgpCRFIV7brBo6oslBI", null, null, null, 1, 0], [1, -10.221, 357.5, 0]], [6, "img_ghg_di1", 33554432, 6, [-282, -283], [[2, -279, [0, "42y5muxslFuId1GH8t6vA7"], [5, 509, 89]], [3, -280, [0, "82XavJrV9K9LQDUnrUPphC"], 95], [11, -281, [0, "38JSp+KfBKjbeygPb5qC5m"], [96, 97, 98]]], [1, "b78qNAgkFK2oerfFblEhtu", null, null, null, 1, 0], [1, 0, -127.849, 0]], [6, "btn_auto", 33554432, 38, [23, -287], [[2, -284, [0, "a02HRYvWtIV4B3LTvgY2Uw"], [5, 340, 57]], [20, 1, 0, -285, [0, "3bD12NltFB1pPPZlAnbHqI"], 90], [31, 3, 0.9, -286, [0, "31Z+RwcO9CsKiAzerJK+vY"], 23]], [1, "60awoe7BRAbou4jKpGbXum", null, null, null, 1, 0], [1, 0, 66.944, 0]], [17, "item_model1", 33554432, 7, [-291, -292], [[5, -288, [0, "53E/jQ98FJEbsFTM8CFDtM"], [5, 246, 105], [0, 0.5, 1]], [10, -289, [0, "acR+U10itNn6WBE/EGIXtg"]], [106, false, 1, 2, -1, true, -290, [0, "869pbw/wZKA4WKgGdSSp7f"]]], [1, "7aXsq5OINFK68FIzNaVZtU", null, null, null, 1, 0]], [16, "item_map_sq", false, 33554432, 7, [-295, -296, -297], [[5, -293, [0, "76stXXsA1LXKWeZDWoD1Qp"], [5, 246, 30], [0, 0.5, 1]], [45, 1, -294, [0, "05F3JdPjBKibeqIDJlsL8U"], 114]], [1, "a6jrSjxOFOhJH+XnZ0n02N", null, null, null, 1, 0], [1, 0, -107, 0]], [28, "scrollv", 33554432, [-303], [[2, -298, [0, "5en0uNoU5MkpiqcQ7gwv9p"], [5, 6700, 4300]], [117, false, false, false, -300, [0, "80E0MdOrVCBpXhG9Sg3kno"], -299], [76, false, -301, [0, "02a711aI9NKqmgNQNp+Zhi"]], [77, false, -302, [0, "a86Rth/flEk4+bU7809YN3"], [4, 16777215]]], [1, "5ctRgYcZtIwoGx79aTeQlc", null, null, null, 1, 0]], [6, "item_ziyuan", 33554432, 7, [-306, 8, -307], [[5, -304, [0, "0enGiNdLlJZ6A/8nY9Pb+2"], [5, 246, 32], [0, 0.5, 1]], [51, 1, 2, -305, [0, "3f9snxqzBGkYytMSUBf8/d"]]], [1, "9djfmpePVDs7EKEHne8KzG", null, null, null, 1, 0], [1, 0, -349, 0]], [16, "item_zy2", false, 33554432, 43, [-310, -311, -312], [[2, -308, [0, "f8g9TR6LFJgK1oodbjFVQY"], [5, 246, 45]], [3, -309, [0, "1cl6hdtWFC85mqjy1ISj/V"], 134]], [1, "8dBgQ2qi5Fx57S5RqBnPEB", null, null, null, 1, 0], [1, 0, -54.5, 0]], [19, "resource_panel", 33554432, [-317], [[5, -313, [0, "e5S2tR4C5KmIjqoEVZS2hW"], [5, 203, 116.9], [0, 0.5, 1]], [20, 1, 0, -314, [0, "6dzSyKnf1IlrolsG4dlMEY"], 136], [48, 33, -315, [0, "e5XMiJjNpBd771h0pW0AHL"]], [107, 1, 2, 40.9, 40, 9.5, -316, [0, "ddARrEkJVEoaztD4AoarOy"]]], [1, "3cVndW1btLW6hznK9EM8L8", null, null, null, 1, 0], [1, 218.7, 0, 0]], [16, "sign", false, 33554432, 4, [-321], [[5, -318, [0, "77ys9g6aJCg4iyHxUs487A"], [5, 107, 123], [0, 0.5, 1]], [3, -319, [0, "e7zxtgnOBA6JBL7aFAVENZ"], 22], [11, -320, [0, "7fLZIJ8nlHprz0LiM26lmi"], [23, 24, 25]]], [1, "70INj6nVpOYI0ug98YYIcD", null, null, null, 1, 0], [1, 0, 150.983, 0]], [19, "fight", 33554432, [-323, 29, -324], [[2, -322, [0, "3b2ybtZJxMcqHbRj6Yk+CN"], [5, 190, 83.60000000000002]]], [1, "c2F6dk4TNAk4iIHNE/praD", null, null, null, 1, 0], [1, 0, -53.486, 0]], [17, "layout_btn", 33554432, 47, [-327, -328], [[2, -325, [0, "65qhXEG4FPDZwSgtw5SW/y"], [5, 89, 100]], [51, 1, 1, -326, [0, "2dGKcITFJBlrwB9MHlT8UR"]]], [1, "e6jcKal9lB74rgY1rCEcXu", null, null, null, 1, 0]], [16, "sign", false, 33554432, 5, [-332], [[5, -329, [0, "9eXXGpTqNAfrjTmIEaW9qz"], [5, 107, 123], [0, 0.5, 1]], [3, -330, [0, "deQtbPv8RDeZrBCVNQ9c5+"], 42], [11, -331, [0, "8dzy0FUvpN+oZI5xYh05B7"], [43, 44, 45]]], [1, "c6dmHFZDxOzbxqGTwdJO2X", null, null, null, 1, 0], [1, 0, 150.983, 0]], [19, "fight", 33554432, [-334, 31, -335], [[2, -333, [0, "30Z0hR28tDZ6cVMVvFh4D3"], [5, 190, 83.60000000000002]]], [1, "0eVY4mQUNFfJOxpvL5T1RR", null, null, null, 1, 0], [1, 0, -53.486, 0]], [26, "layout_btn", false, 33554432, 50, [-338, -339], [[2, -336, [0, "836r+/UYdPEYyYVoSOAnI4"], [5, 177, 100]], [51, 1, 1, -337, [0, "7fZfj+EQlLyLmWqTrZZfg8"]]], [1, "110IK+CZdPBYLHbXbiVSK6", null, null, null, 1, 0]], [19, "layout_fh", 33554432, [-342, -343], [[2, -340, [0, "cakPZTe9BKh5ZiK9NneWpa"], [5, 100, 80.2]], [73, 1, 2, -8, true, -341, [0, "7amA1xHVBIqa+Nm9m2AXwE"]]], [1, "2aHVeWnYhD8pGbuFqdXSdF", null, null, null, 1, 0], [1, 0, -5, 0]], [7, ["03XfDFaqNOdp6SCwdhIQpD"]], [6, "btn_sd", 33554432, 21, [-347], [[2, -344, [0, "5cwOePJ0dMnaQygMVfrxNr"], [5, 80, 83]], [18, 0, -345, [0, "49u6NkUipKO5ItdfwRakwL"], 70], [60, 3, 0.9, -346, [0, "f3DFBdJdBKubUsFDwiFTCZ"]]], [1, "deUC6SK+xHSYfn6ivQbvxD", null, null, null, 1, 0], [1, 0, 41.5, 0]], [13, "btn_sl1", 33554432, 10, [[5, -348, [0, "90nqk1VZhB5pCVkfsR7xT6"], [5, 96, 28], [0, 0.5, 1.1]], [3, -349, [0, "94pWVLRkJFkYAWYxiogWuW"], 78], [60, 3, 0.9, -350, [0, "fedSKymxpFjYM089JGYaf+"]], [11, -351, [0, "bdihIUDMlKupCgmcoXwq5f"], [79, 80]]], [1, "a2K4s70ANNdJV1SLUSFxv6", null, null, null, 1, 0], [1, -10.221, 450.8, 0]], [13, "btn_ty_lt1", 33554432, 22, [[2, -352, [0, "deYTF+lb9NvZmkAjsWCeHK"], [5, 30, 19]], [3, -353, [0, "65Snc9hnFPjJDQXXGNE89a"], 81], [114, false, 3, 0.9, -354, [0, "32s+d6YzhGX7dX47L/4YMK"]]], [1, "0axDx4oKNF77uH+HUwBF9h", null, null, null, 1, 0], [1, -289.798, 0, 0]], [25, "bg", false, 33554432, 23, [[2, -355, [0, "77zLxUZ7dD8oDrPCJwPRpT"], [5, 79, 22]], [70, 1, 0, true, -356, [0, "b6c6MWSglADYTzsDSmpvei"], [4, 4281094400], 83], [71, 40, 5, 5, 40, -357, [0, "39o9Du+BtAirvOOaEX6+E2"]]], [1, "76KQLJ2h9KgqUhG2iAxdRx", null, null, null, 1, 0]], [13, "btn_open", 33554432, 23, [[2, -358, [0, "bb99yGvARIJ5PsVXZtt065"], [5, 44, 22]], [70, 1, 0, true, -359, [0, "6aYTih9gpDfYAC07fmV7Zu"], [4, 4289572013], 84], [71, 40, 5, 40, 40, -360, [0, "feBp9khnJK0YX1yO5GMFOE"]]], [1, "7fPn7Iw2BIkathu8Ltm+PH", null, null, null, 1, 0], [1, -17.5, 0, 0]], [7, ["03XfDFaqNOdp6SCwdhIQpD"]], [16, "img_ghg_di2", false, 33554432, 6, [-363, -364], [[2, -361, [0, "66Fob0YN1F/LuP6IKJtXMG"], [5, 397, 35]], [3, -362, [0, "45z8v+bx1NN6q7I4JLHenj"], 99]], [1, "3en+V0mj9OCbpWnqlOXtos", null, null, null, 1, 0], [1, 0, -58.679, 0]], [17, "lay_sysj", 33554432, 60, [-367, -368], [[2, -365, [0, "fcGTf1cl9DwIvt+3omIpIu"], [5, 220.05859375, 30]], [108, 1, 1, true, -366, [0, "caP7RoPs9OT5crdCcR7iDa"]]], [1, "af5r+H/a5Lw4mdcxLMi6tV", null, null, null, 1, 0]], [17, "gongda", 33554432, 13, [-371, -372], [[2, -369, [0, "7eIdRWxAVBR6EHoJNMAvIt"], [5, 246, 65]], [3, -370, [0, "31J7ExwspJ4bvm4kYDMRdr"], 106]], [1, "622q03MJ9NYK9O81GiwZZe", null, null, null, 1, 0]], [6, "item_head_l", 33554432, 13, [-375, -376], [[2, -373, [0, "1bKaINfYdDe5sfpybCSaXP"], [5, 85, 116]], [10, -374, [0, "43Z6unlo1PM6LXCgF/bZLy"]]], [1, "c4cu2MrQ1Em5qmaOJc6y32", null, null, null, 1, 0], [1, -124.4, 0, 0]], [6, "item_head_r", 33554432, 13, [-379, -380], [[2, -377, [0, "e4ozwkFvZLqJ1qxvuY6tW+"], [5, 85, 116]], [10, -378, [0, "c5IL38fN5Fwq1ht7pJafLQ"]]], [1, "6d0XzyOMFPQ4xN0rVMjwZC", null, null, null, 1, 0], [1, 124.4, 0, 0]], [6, "top", 33554432, 1, [7, 45], [[5, -381, [0, "5eLrye/WlPNpfqC16mE592"], [5, 640.4, 462.6], [0, 0.5, 1]], [48, 1, -382, [0, "0eyi+BQA9OMJwXbdwqWxgT"]]], [1, "76ob0kRrdPr5L8pR2bOn8M", null, null, null, 1, 0], [1, 0, 640, 0]], [6, "countDown", 33554432, 40, [-385, -386], [[2, -383, [0, "df+oZ9hglJ3YQeJwZ9abMh"], [5, 246, 70]], [3, -384, [0, "25sMVInMpPIKQMzinSgG0z"], 111]], [1, "85Pioa3nRIi4N07jPfS4ZY", null, null, null, 1, 0], [1, 0, -70, 0]], [6, "Layout", 33554432, 66, [-389, -390], [[2, -387, [0, "e8mskoDA1OEqzg+W/l187H"], [5, 31, 30]], [59, 1, 1, 6, true, -388, [0, "e2idV0qtBAALHPXSTCHhxu"]]], [1, "0f/p5gGzZI+LaFAAlgHlHT", null, null, null, 1, 0], [1, 0, -10.864, 0]], [4, "txt_map_sq", 33554432, 41, [[5, -391, [0, "c4AmWGrUFNZavwbw8joua4"], [5, 0, 50.4], [0, 0, 0.5]], [78, "", 36, 36, true, -392, [0, "a6dGGL6chEpoYeGNRb0mlE"]], [49, 1, 1.3659999999999997, -393, [0, "fbZoUttPVAtYjFVEhvCRrE"]], [12, "gvg_ghg_tip_036", -394, [0, "9cAoMbHMtKpIwVpuueV24H"]]], [1, "7eXhV4SylD2r0wwhoYsUHr", null, null, null, 1, 0], [1, -80.345, -13.966, 0], [1, 0.5, 0.5, 1]], [4, "txt_gn1", 33554432, 14, [[5, -395, [0, "70V/jkj7VFe7boBHCcgYoD"], [5, 0, 50.4], [0, 0, 0.5]], [78, "", 36, 36, true, -396, [0, "dciwJA6jhBdaQSiFzWwL5T"]], [49, 1, 2, -397, [0, "1cI9gikOFHFJbuxt8EZvgx"]], [12, "gvg_ghg_tip_036", -398, [0, "14dV8sEdtL2LEL+gkaFzIn"]]], [1, "4cVkax7wFH5Yxv5HaGezqC", null, null, null, 1, 0], [1, -80.345, -14.6, 0], [1, 0.5, 0.5, 1]], [43, "ssmap", 33554432, 14, [42, -400, -401], [[2, -399, [0, "77HqJUCrREg6KANitiUgf1"], [5, 6700, 4300]]], [1, "5cc0F092dGxKpKOldgbOHx", null, null, null, 1, 0], [1, -1, -126.163, 0], [1, 0.032, 0.032, 1]], [7, ["79BcNar8tDmZucBHLjoa8/"]], [7, ["c46/YsCPVOJYA4mWEpNYRx"]], [19, "tips_popup1", 33554432, [-404, -405], [[2, -402, [0, "ba4zYxr49Ei6ckpOSJ3BTP"], [5, 640, 126]], [3, -403, [0, "daRLdFv9FBBLRvVF278gQG"], 140]], [1, "1atqctxOhN9bNTEDA3d90O", null, null, null, 1, 0], [1, 0, -65.33, 0]], [4, "txt_model1", 33554432, 73, [[2, -406, [0, "02xPmH+stICqgNAEgS5nIx"], [5, 264, 96.2]], [35, "探索海底", 64, 64, 70, true, true, 4, -407, [0, "d3XGk4xZFI+6etEaiK5k0Q"]], [84, 0.26, 0.78, 1, 1, -408, [0, "f32sjYygJLDZ5ODR6GQHCM"], [[[4, 4294959247], [4, 4294951980]], 8, 8], 139], [12, "gvg_ghg_tip_231", -409, [0, "f8iN0tBe1J/4AXZif2rlyM"]]], [1, "ccCNEGTJ1NsaujpPmUsQ4O", null, null, null, 1, 0], [1, 0, 23.255999999999972, 0], [1, 0.5, 0.5, 1]], [28, "tips_popup2", 33554432, [-412, -413], [[2, -410, [0, "35WfjNn7xBPbFOIdJ0Rism"], [5, 640, 126]], [3, -411, [0, "5etNAgSXJIE4WxEWOSPOOX"], 141]], [1, "73N8q85XVIhLiAJc5coPlQ", null, null, null, 1, 0]], [28, "icon", 33554432, [-417], [[47, -414, [0, "8fFzeXvcpJC7p7aGiTA8bq"]], [55, 1, 0, -415, [0, "7agjJ+YOFAvKvKY6EN5nbJ"], [4, 4281019383], 142], [134, 100, -416, [0, "989vv0bphKvJbuhGnvglzA"]]], [1, "5epWRxSWtPvJ3jFhF4evVn", null, null, null, 1, 0]], [7, ["c46/YsCPVOJYA4mWEpNYRx"]], [19, "sign", 33554432, [-421], [[5, -418, [0, "39n3WyzINCV6Dlla/nqPZF"], [5, 107, 123], [0, 0.5, 1]], [3, -419, [0, "06y/ijuI5DsJwDZzRVd17Q"], 150], [11, -420, [0, "d3lvaD34RG6Lf3HWYdqUoJ"], [151, 152, 153]]], [1, "441REWL1RJiKQhTkV66GX4", null, null, null, 1, 0], [1, 0, 98.983, 0]], [91, "item_red", false, 33554432, 1, [-424, -425], [[2, -422, [0, "79PBgCx9dKXIowzPu5tBQL"], [5, 26, 26]], [52, -423, [0, "c4VhUeCy1GZ4tfY9nud7Bm"], [4, 4279045619], 156]], [1, "1dkYenQ5hKzZLWomZB1Dsh", null, null, null, 1, 0], [1, 0.4, 0.4, 1]], [7, ["c46/YsCPVOJYA4mWEpNYRx"]], [17, "gongda", 33554432, 16, [-428, -429], [[2, -426, [0, "8clOg6WmxDTKU5Hds/bys5"], [5, 246, 65]], [3, -427, [0, "04pv38Cz5LYok/TDD3qhtl"], 158]], [1, "19X0BCMfRLbZhU03jJ0+E6", null, null, null, 1, 0]], [6, "item_head_l", 33554432, 16, [-432, -433], [[2, -430, [0, "e9do6R/DhNn4b5TUao8SMe"], [5, 85, 116]], [10, -431, [0, "83bottz/9IbbLoj03raw7L"]]], [1, "2ecJ9T8iVGGop/h12lIUpb", null, null, null, 1, 0], [1, -124.4, 0, 0]], [6, "item_head_r", 33554432, 16, [-436, -437], [[2, -434, [0, "bdS3SBPpRFzZjIS7fjL43Q"], [5, 85, 116]], [10, -435, [0, "7d5udf7j1FV7uX3AkDqlW6"]]], [1, "02phKJMg1LQI3O2ZQc1/Pq", null, null, null, 1, 0], [1, 124.4, 0, 0]], [92, "tips_newmonster", false, 33554432, 1, [-440, -441], [[5, -438, [0, "b5w0oAoWZMz40606gCCxep"], [5, 812.3881225585938, 1284], [0, 0.500002779829885, 0.632398753894081]], [24, "default", "idle", false, 0, -439, [0, "42rGR7E65LrqJjcAHRJstk"], 162]], [1, "9fjCKZDE1FkIyHI9QrMBKQ", null, null, null, 1, 0], [1, 11.615, 84.485, 0], [1, 0, 0, -8.140407933488985e-06]], [26, "gongda", false, 33554432, 25, [-444, -445], [[2, -442, [0, "85/Ba/SApMyYz1OsYk+B2w"], [5, 246, 65]], [3, -443, [0, "c1KgNM8qNP2qHYUzZiucn3"], 163]], [1, "36/TvFYCZNm7giLVILXLkC", null, null, null, 1, 0]], [6, "item_head_l", 33554432, 25, [-448, -449], [[2, -446, [0, "a518iSS5VH9oURnIjHbbRf"], [5, 85, 116]], [10, -447, [0, "11nzY4kFVIhplXoQ+FLBLz"]]], [1, "90eUaoCopOd6NCfPUELZLi", null, null, null, 1, 0], [1, -124.4, 0, 0]], [6, "item_head_r", 33554432, 25, [-452, -453], [[2, -450, [0, "ec3VyR7PZPa6fgll+CLDb3"], [5, 85, 116]], [10, -451, [0, "37DoYQGLBFGa5q7q3mXydP"]]], [1, "442qUgbBZB24d5vL9Bluua", null, null, null, 1, 0], [1, 124.4, 0, 0]], [4, "mapbg_04", 33554432, 2, [[2, -454, [0, "4ffWOg4UtMJotOuIE5v1ew"], [5, 207, 716]], [3, -455, [0, "48+SPaXexLWKHonjRaBme8"], 3], [103, 33, 2868, 716, -456, [0, "424yDLjNBCXYxQ/T7Q+C/z"]]], [1, "5bQS7UJDBAup3LdxrxY8hI", null, null, null, 1, 0], [1, 3143, 1434, 0], [1, 2, 2, 1]], [4, "mapbg_06", 33554432, 2, [[2, -457, [0, "f9nba/XDxN1oF42THBXGAq"], [5, 185, 716]], [3, -458, [0, "caPY5QidlDwqyDU0KmxTO5"], 5], [48, 36, -459, [0, "67uEpWNShIlYTEHHkbHf8k"]]], [1, "6egO/5vRFH05lynT9bsp9c", null, null, null, 1, 0], [1, 3165, -1434, 0], [1, 2, 2, 1]], [4, "mapbg_12", 33554432, 2, [[2, -460, [0, "742aREE95D2bC90CA+q0WB"], [5, 197, 717]], [18, 0, -461, [0, "f4FwTCD41BR4ritW54gAGc"], 11], [48, 9, -462, [0, "addBsJE+5NmYrgCbIIohxi"]]], [1, "7aSKZjTxlIjL8zH+lnLgGm", null, null, null, 1, 0], [1, -3153, 1433, 0], [1, 2, 2, 1]], [8, "img_yuan_di1", 33554432, 27, [[2, -463, [0, "17PbLYIJxB5ozXRyGzYLz/"], [5, 43, 43]], [3, -464, [0, "68uUmz7YRDJrEafW/JfjaZ"], 16], [11, -465, [0, "9dyzKl08dLEIfhVGo6JhU0"], [17, 18]]], [1, "19r0FTbGlPZZY0+SP6wIQv", null, null, null, 1, 0]], [8, "img_yuan_quan1", 33554432, 27, [[2, -466, [0, "23rDBs2JhBxpHuEhDBBLef"], [5, 47, 47]], [3, -467, [0, "bc5u5RAblPhrFhRY2EjMwP"], 19], [11, -468, [0, "02QgnWEl9KXbwd6cW2ySRO"], [20, 21]]], [1, "ac/IRehqhES70HgKIz9BgJ", null, null, null, 1, 0]], [16, "layout_head", false, 33554432, 4, [-471], [[2, -469, [0, "73TXlDWoRDnogCd5GIBWa3"], [5, 126.75, 48]], [50, 1, 3, true, -470, [0, "1718tVoGFLUKcfI7aZu12P"]]], [1, "e1ywJ4wZxCM6M2srA0Kqo8", null, null, null, 1, 0], [1, 0, -6.598, 0]], [17, "mapitem", 33554432, 4, [47], [[2, -472, [0, "5aLLcjZolLy7Dm2Fp17/oq"], [5, 200, 200]], [10, -473, [0, "1044qmF+RDl5ENQnEB3ziS"]]], [1, "72D5Ky0cRIl5HDLEbRThaI", null, null, null, 1, 0]], [16, "lay_xx", false, 33554432, 47, [-476], [[2, -474, [0, "50uVHejuhLJp2RRSVZVny8"], [5, 151, 33]], [20, 1, 0, -475, [0, "9fROTv/dpHV5MI3cnWuAdw"], 29]], [1, "52OYLN6jVLtJfFeVohvXr/", null, null, null, 1, 0], [1, 0, 2.185, 0]], [38, "Bar", 33554432, 29, [[[5, -477, [0, "68QERG5IJJfLTP3d/rTI7Q"], [5, 50, 8], [0, 0, 0.5]], -478, [11, -479, [0, "bdATsQJ5hNvrqLCiC/GO4q"], [30, 31]]], 4, 1, 4], [1, "f3JwuaaTtPOrbeOOFvWDd6", null, null, null, 1, 0], [1, -62.5, 0, 0]], [17, "btn_ty_lq_1", 33554432, 48, [-482], [[2, -480, [0, "9fV8akHTZOA62I2aHgHrLR"], [5, 89, 91]], [3, -481, [0, "94/z+ahctHM5L8HtUaPfYr"], 33]], [1, "b8BQYP1CZGTqDr9yVefKSK", null, null, null, 1, 0]], [9, "txt_xq", 33554432, 97, [[2, -483, [0, "bfbgTdlYBKt5n8sjg49dQr"], [5, 94, 63.96]], [21, "详情", 44, 44, 46, true, true, 3, -484, [0, "06vI7OlBpJ1LEhtzfelU3/"], [4, 4279388555]], [12, "sdsl_58", -485, [0, "c3MpR6QdlKEbtNM7HwnrPu"]]], [1, "628boJmRxN6ITFjLsekQKF", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [16, "btn_ty_lq_2", false, 33554432, 48, [-488], [[2, -486, [0, "09N/zdBdZJhaW6YEv1qHNB"], [5, 88, 90]], [3, -487, [0, "9dW6LRpZhE/IB2kLm0QwDz"], 34]], [1, "92DVOTCQFEsowBasx6KaKF", null, null, null, 1, 0], [1, 44.5, 0, 0]], [9, "txt_xq", 33554432, 99, [[2, -489, [0, "fc8CiLilFPW5IOcZ+xzfwZ"], [5, 94, 63.96]], [21, "前往", 44, 44, 46, true, true, 3, -490, [0, "cbMaRhlWFECpzkyoPB+Ls5"], [4, 4282867204]], [12, "task_3", -491, [0, "1aVzd/Gg1M7a8zEKj64GgW"]]], [1, "99El7rBx1Jzr0g8I7CUf3S", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [23, "car", false, 33554432, 5, [[2, -492, [0, "d9b3B/NXVB3qZ9dfXTnLJc"], [5, 121, 115]], [3, -493, [0, "feAKOIRyxKc7kv4nRaOXDB"], 36], [11, -494, [0, "fbvBLHz5pCDIRdatOfUqJE"], [37, 38]]], [1, "71a0YcW89L3LD5EgGKU6k9", null, null, null, 1, 0], [1, 0, 29.028, 0]], [25, "img_ghg_map_di1", false, 33554432, 5, [[2, -495, [0, "0cY0ppy15CnbYsPI3I9/QD"], [5, 169, 136]], [3, -496, [0, "b0vv70lrtDxq71UbjMolGm"], 39], [11, -497, [0, "58Tg26hTRNYrjApQkkRULu"], [40, 41]]], [1, "bc5zO1PT1M84pzX/FPNpFm", null, null, null, 1, 0]], [6, "layout_head", 33554432, 5, [-500], [[2, -498, [0, "52aCggK2NDe6Stcso2i/8C"], [5, 126.75, 48]], [50, 1, 3, true, -499, [0, "f6g3vXqadM2bBD6dH0hBS0"]]], [1, "56nYZWMthFwJLpS1lvHsg6", null, null, null, 1, 0], [1, 0, -6.598, 0]], [17, "mapitem", 33554432, 5, [50], [[2, -501, [0, "3dGSNok4ZDo6iYcQ9CVFrP"], [5, 200, 200]], [10, -502, [0, "26xIOd8WJJn5NrPsoV8rLO"]]], [1, "bf35A/9oFIPqCKJQdvIyye", null, null, null, 1, 0]], [16, "lay_xx", false, 33554432, 50, [-505], [[2, -503, [0, "54sU6lmEFHmLX17RH5cb8h"], [5, 151, 33]], [20, 1, 0, -504, [0, "9cip3BXzVE5LvCajgOB2Zs"], 51]], [1, "c9ZwnMCV9KjJoeE8u+a5Lq", null, null, null, 1, 0], [1, 0, 2.185, 0]], [38, "Bar", 33554432, 31, [[[5, -506, [0, "d2An9ZjXxDDLPkARG1lVOr"], [5, 50, 8], [0, 0, 0.5]], -507, [11, -508, [0, "bahONQIolL4KfZAEp+8Qq7"], [52, 53]]], 4, 1, 4], [1, "f0I0RLdBJHrb1C0jrEO+rH", null, null, null, 1, 0], [1, -62.5, 0, 0]], [6, "btn_ty_lq_1", 33554432, 51, [-511], [[2, -509, [0, "192F96+5pDoKho3ZyJHg7l"], [5, 89, 91]], [3, -510, [0, "f8RbCi1CFByoSOkNE4k7AU"], 55]], [1, "05lJI9XhxP+brl5zE7h/OY", null, null, null, 1, 0], [1, -44, 0, 0]], [9, "txt_xq", 33554432, 107, [[2, -512, [0, "33+CuqRh9NUILFbyFinLQB"], [5, 94, 63.96]], [21, "详情", 44, 44, 46, true, true, 3, -513, [0, "f1n/WJEoxHhZfC2B1wa4x6"], [4, 4279388555]], [12, "sdsl_58", -514, [0, "934324JmREc4j9NmPx5VpS"]]], [1, "e0jBPEwUFKD6xj2iJj4scW", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [6, "btn_ty_lq_2", 33554432, 51, [-517], [[2, -515, [0, "deNobYokxNKYCCXIR5gJXw"], [5, 88, 90]], [3, -516, [0, "95N0HrKrBDip0tPKiNSwXk"], 56]], [1, "e4S9KoHmRKUI22aw6eVmOq", null, null, null, 1, 0], [1, 44.5, 0, 0]], [9, "txt_xq", 33554432, 109, [[2, -518, [0, "e5NvDufuBHYqVt+wwP/HRO"], [5, 94, 63.96]], [21, "前往", 44, 44, 46, true, true, 3, -519, [0, "49+EAY6itOb5zd6yn024GJ"], [4, 4282867204]], [12, "task_3", -520, [0, "f0npPghSpHV6t0JMXi8Cms"]]], [1, "0ag+2+Hh5ALI1i6wBDGm2G", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [29, 0, {}, 26, [30, "c46/YsCPVOJYA4mWEpNYRx", null, null, -522, [135, "b4x954vxJD05yzWuTpKSQA", 1, [[137, [7, ["c46/YsCPVOJYA4mWEpNYRx"]], [-521]]], [[22, "gvg_ghg_end", ["_name"], [7, ["c46/YsCPVOJYA4mWEpNYRx"]]], [15, ["_lpos"], [7, ["c46/YsCPVOJYA4mWEpNYRx"]], [1, 0, 0, 0]], [15, ["_lrot"], [7, ["c46/YsCPVOJYA4mWEpNYRx"]], [3, 0, 0, 0, 1]], [15, ["_euler"], [7, ["c46/YsCPVOJYA4mWEpNYRx"]], [1, 0, 0, 0]], [22, false, ["_active"], [7, ["c46/YsCPVOJYA4mWEpNYRx"]]], [22, false, ["_active"], [7, ["e9Ypf4YzJAEbdz9/l8X1OV"]]], [22, true, ["_enabled"], [7, ["dcycM1EdZNdLvBmNO43KYe"]]], [22, true, ["_active"], [7, ["61OxAOQw5G44QmTyegEl1E"]]], [22, false, ["_active"], [7, ["13fAExDjFATYoINBp2Ebye"]]]]], 58]], [6, "icon_cl", 33554432, 18, [52], [[2, -523, [0, "42/MI1Fl9FYp7/fQN91fyM"], [5, 162, 166]], [3, -524, [0, "b7u7H6B2JOFYUsgCf5KccI"], 61]], [1, "a113ciKudOk5jS3+UBXUQr", null, null, null, 1, 0], [1, 0, 180.048, 0]], [29, 0, {}, 19, [30, "03XfDFaqNOdp6SCwdhIQpD", null, null, -528, [136, "fciIqyHc1JQoUz+8CXKdyO", 1, [[138, [7, ["5cvq8qCo5I7pXjdLcFwDsb"]], [-527]]], [[27, "btn_ty1", ["_name"], 53], [14, ["_lpos"], 53, [1, 0, 0, 0]], [14, ["_lrot"], 53, [3, 0, 0, 0, 1]], [14, ["_euler"], 53, [1, 0, 0, 0]], [14, ["_lscale"], 53, [1, 1.1, 1.1, 1]], [14, ["_outlineColor"], -525, [4, 4282867204]], [86, ["_spriteFrame"], [7, ["43Z+zpMHNH95/fghPESQqh"]], 63], [27, "复活", ["_string"], -526]]], 62]], [6, "img_mask_di1", 33554432, 19, [-531], [[2, -529, [0, "9bBTF5yAZMzYCYybAllN0D"], [5, 254.2, 30]], [18, 0, -530, [0, "2ew+U5V5FOfIO6pwVUIjFX"], 64]], [1, "79MEmMsZJLdIj78sBEST5P", null, null, null, 1, 0], [1, 0, -49.801, 0]], [9, "txt_fhcsyw", 33554432, 114, [[2, -532, [0, "39zf7zf3pDGa+vFE0XZWYW"], [5, 330, 56.4]], [40, "免费复活次数已用完", 36, 36, true, true, 3, -533, [0, "8clpBhgBBOT570JEDSZyq5"], [4, 4282598655]], [12, "gvg_ghg_tip_255", -534, [0, "78/ZMn/5xMY4CQuWRlU+/h"]]], [1, "b8i08tmoRKVLwbCOBcwfz2", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [6, "img_ghg_di9", 33554432, 19, [-537], [[2, -535, [0, "f07RCszrtO1qovBtNcmWOF"], [5, 94, 26]], [3, -536, [0, "db99cKsoNArZoPmuakajbs"], 66]], [1, "2azS2JjJRDaKSDVNHV0umz", null, null, null, 1, 0], [1, 0, 55.553, 0]], [4, "txt_gh1", 33554432, 20, [[2, -538, [0, "61aXHH7AJDQo8CztG3ZCGA"], [5, 270, 69]], [79, "解放者的赐福", 44, 44, 50, 3, true, true, 3, -539, [0, "2dh21owzpGn6kESZ2WfUVn"], [4, 4278453288]], [12, "ymj_tip_053", -540, [0, "45ytlqjK1EHaNS40tPnmQI"]]], [1, "25eOzpDvxEd6hPWFNjRTBq", null, null, null, 1, 0], [1, 0, -45.777, 0], [1, 0.5, 0.5, 1]], [4, "txt_sd", 33554432, 54, [[2, -541, [0, "28tIrTmoRLhqpOper37BUy"], [5, 235, 61.44]], [123, "商店", 44, 44, 44, 3, false, true, true, 3, -542, [0, "a7KUM5TANKBqUL6T9Qd9DA"], [4, 4281213474]], [12, "shop_1", -543, [0, "70hVS4bZZFurMs5Umg3WTM"]]], [1, "c5OyJNhIlJa7u75NxkZj6P", null, null, null, 1, 0], [1, 0, -28.765, 0], [1, 0.5, 0.5, 1]], [4, "txt_dw", 33554432, 32, [[2, -544, [0, "7djp/ddItLjokqpu9N4SOb"], [5, 98, 69]], [21, "标记", 46, 46, 50, true, true, 3, -545, [0, "48BWLKc0FMn6K/Fx214w5x"], [4, 4281213474]], [12, "gvg_ghg_tip_201", -546, [0, "99rXKs2FhPS4kEQzxZJhML"]]], [1, "42bDM9eaZIJ7VtDFu/AThU", null, null, null, 1, 0], [1, 0, -29, 0], [1, 0.5, 0.5, 1]], [4, "txt_zb", 33554432, 33, [[2, -547, [0, "beAwpBgQFGRK6josJfEaeQ"], [5, 98, 69]], [21, "定位", 46, 46, 50, true, true, 3, -548, [0, "ffdnFgI6RPVbQPIHfcqY63"], [4, 4281213474]], [12, "gvg_yc_txt87", -549, [0, "c2Ns3mp4dFQZlmLRdeHsvP"]]], [1, "b9ecdf3GVKAJ4WBLc1N7NW", null, null, null, 1, 0], [1, 0, -29, 0], [1, 0.5, 0.5, 1]], [4, "txt_zb1", 33554432, 34, [[2, -550, [0, "48CAffmNJIG4tbodO4CSyo"], [5, 6, 69]], [21, "", 46, 46, 50, true, true, 3, -551, [0, "74iSuyReJKBZDMoX1b7PNZ"], [4, 4281213474]], [12, "jinbiaosai_33", -552, [0, "16fU3ACHZL6IwGYLuZ1zv/"]]], [1, "281iV1gGdBkr/vj2YiM2Z8", null, null, null, 1, 0], [1, 0, -29, 0], [1, 0.5, 0.5, 1]], [4, "txt_ph", 33554432, 35, [[2, -553, [0, "11V1Plbl1F05CYoxkDlG2f"], [5, 6, 69]], [21, "", 46, 46, 50, true, true, 3, -554, [0, "052ZiaCqdFD796sXq5U5pt"], [4, 4281213474]], [12, "gonghui_46", -555, [0, "65ldXtsXFJM6WWzzDfUN+G"]]], [1, "ae2U4LKc9PNZFcCFjdKwJ2", null, null, null, 1, 0], [1, 0, -29, 0], [1, 0.5, 0.5, 1]], [4, "txt_gz", 33554432, 36, [[2, -556, [0, "3c8HaY241PMaIHtw8YoqHC"], [5, 98, 69]], [21, "规则", 46, 46, 50, true, true, 3, -557, [0, "d57dhmyDtGV73fXkoFg6TH"], [4, 4281213474]], [12, "gvg_yc_txt89", -558, [0, "72jwdqr2ZH9ptLm+y6h7/I"]]], [1, "758fg8bT1Ky4AAZU5yLeao", null, null, null, 1, 0], [1, 0, -29, 0], [1, 0.5, 0.5, 1]], [4, "txt_fh", 33554432, 37, [[2, -559, [0, "43mLm5fvlG1JFt8mdEAbPH"], [5, 6, 69]], [21, "", 46, 46, 50, true, true, 3, -560, [0, "b53/ZEaiBJnb/P0w3rVrho"], [4, 4281213474]], [12, "gvg_yc_txt88", -561, [0, "1atGbYM0VCGKRR9YHx5uPT"]]], [1, "bcp3KpbJhMUIaiIlqqDpVf", null, null, null, 1, 0], [1, 0, -29, 0], [1, 0.5, 0.5, 1]], [9, "txt_chat0", 33554432, 11, [[5, -562, [0, "546T7DeYBIwaodBJymbPyB"], [5, 0, 50.4], [0, 0, 0.5]], [80, "", 0, 36, 36, true, -563, [0, "b4rCuBkhVCTZXn3MllRg2a"], [4, 4290117376]], [12, "tx_lt_sj", -564, [0, "80B24Is5tM4oPTE2ywMd51"]]], [1, "361+oXkEVPwoAbcKcxdDKp", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [4, "Label", 33554432, 39, [[2, -565, [0, "60maNsHjxHi6HATuEhi8Eu"], [5, 253.1, 61.44]], [124, "", 40, 44, 2, false, true, true, 3, -566, [0, "9eIhvmUwNM3qLJUvMW1Zhc"], [4, 4280229916]], [12, "gvg_ghg_tip_037", -567, [0, "26O9Gkk5JGpriOKP8TLPcO"]]], [1, "67bQ7Z9yhFoJd4Dq/jo06Y", null, null, null, 1, 0], [1, 45.904, 0, 0], [1, 0.5, 0.5, 1]], [13, "img_hc1", 33554432, 12, [[2, -568, [0, "b3MuM00tNM4bBTdfSBKCMU"], [5, 121, 115]], [3, -569, [0, "dbv0ujpvhBk4Jg5+4ZhE0C"], 100], [11, -570, [0, "80tce5/BlI7Z7uSfO415PY"], [101, 102]]], [1, "26SH/s/95LUqFfa13rHGzU", null, null, null, 1, 0], [1, 1.192, 38.997, 0]], [16, "djs", false, 33554432, 12, [-572, -573], [[2, -571, [0, "e8Vp0HyQNE/pMwr2z8IIzh"], [5, 121, 115]]], [1, "9fPFQDlDtK66eyZTX5AhlA", null, null, null, 1, 0], [1, 1.238, 38.997, 0]], [26, "jibai", false, 33554432, 13, [-576], [[2, -574, [0, "310hH75MZCdZbgIE9LMqE6"], [5, 208, 88]], [3, -575, [0, "44TuNAWiBGvJXsLHBIC2pp"], 108]], [1, "b8a+47qIlPKZNOm+brbXsY", null, null, null, 1, 0]], [8, "gvg_yc_jb", 33554432, 129, [[2, -577, [0, "53S3i3jJdI1oe62cP+PDKE"], [5, 152, 38]], [3, -578, [0, "6acXcfUWZA/Y6nWNxba41f"], 107], [66, "gvg_yc_jb", "gvg_ghg_main", -579, [0, "f85waoSNJFS5J52ZZu4IcQ"]]], [1, "05dd7FXnJNcYEId3KfabKE", null, null, null, 1, 0]], [13, "icon_kd1", 33554432, 41, [[2, -580, [0, "694JYR+qFAEoODz38SIzNf"], [5, 22, 24]], [3, -581, [0, "a6IW6QFfBHDZY+f+I96JFR"], 112], [49, 1, 4.936, -582, [0, "d5x8jgFSZA8Zzrnp3K0rLD"]]], [1, "d0bgFaKg5DeaQOmmIhV4BO", null, null, null, 1, 0], [1, 104.018, -16.936, 0]], [23, "icon_kd", false, 33554432, 14, [[2, -583, [0, "b1Ywa9iMRMCYHS/HrpBvTh"], [5, 22, 24]], [3, -584, [0, "cc1seXgrZIUqEJfSe3+Tbb"], 115], [49, 1, 4.936, -585, [0, "6fww8kI21MTrnWw1qiR4uX"]]], [1, "d8tpbOP9xJS7Oz8Ss/nqIl", null, null, null, 1, 0], [1, 104.018, -16.936, 0]], [17, "scon", 33554432, 42, [15], [[2, -586, [0, "d7lITOKkhM56zHHRDRTDhb"], [5, 6626, 4225]]], [1, "f02v0f189GBaY04DEKHDpz", null, null, null, 1, 0]], [43, "img_kuang", 33554432, 70, [-589], [[2, -587, [0, "9e2zM1PJpL4Lj7hfxmujFQ"], [5, 23, 39]], [3, -588, [0, "3eTX7SIwJDPac2UEo1kHtD"], 119]], [1, "a1ao8oFv9KlJOShiTtTM1M", null, null, null, 1, 0], [1, -38.19999999999891, 1125.1375000000007, 0], [1, 25, 25, 1]], [6, "zyzb_title_di", 33554432, 43, [-592], [[2, -590, [0, "78uq6iQwREsr+HPhdKe7DD"], [5, 246, 32]], [3, -591, [0, "819Gf+DYtNnb5eJAopgjej"], 122]], [1, "92SpnMrF5GW65+fq5zRasC", null, null, null, 1, 0], [1, 0, -16, 0]], [4, "txt_zyzb", 33554432, 135, [[5, -593, [0, "d0kQ0F96pHy5vDBvd9HneL"], [5, 440.3, 50.4], [0, 0, 0.5]], [125, "资源账本", 0, 36, 36, 2, false, true, -594, [0, "7dg/I5XqlCh4bycFGCz3Vc"]], [12, "gvg_ghg_tip_232", -595, [0, "b5efTzr21A3JsWkiTk9sXU"]]], [1, "da+WIJZLpG34tw4d+uSZE/", null, null, null, 1, 0], [1, -109.388, 0, 0], [1, 0.5, 0.5, 1]], [25, "img_ghg_di12", false, 33554432, 8, [[2, -596, [0, "0emrOjgxdMgY4TRGToucnE"], [5, 254, 53]], [3, -597, [0, "99xu45CH5GJKcfXJcu/WRj"], 123], [11, -598, [0, "d0ktDb0DZP2rRatvYS/6aa"], [124, 125]]], [1, "8aN7JzPKNNdKMsqnN9kznR", null, null, null, 1, 0]], [4, "txt_zy1", 33554432, 8, [[5, -599, [0, "0eHLocqGtCKq68Rm8NLVrG"], [5, 150, 56.4], [0, 0, 0.5]], [81, "提交资源", 0, 36, 36, false, true, true, 3, -600, [0, "3bE8BmeE1PcITJKEYF0I6L"]], [12, "gvg_ghg_tip_233", -601, [0, "0cNJ5Lz2ZDnaYJRZvj3LyB"]]], [1, "233C29Ww5HY4y6czyzpz3Z", null, null, null, 1, 0], [1, -109.388, 0, 0], [1, 0.5, 0.5, 1]], [25, "img_ghg_di13", false, 33554432, 44, [[2, -602, [0, "fcfGrTHS9NnqVPYzppC1jj"], [5, 254, 53]], [3, -603, [0, "123YiAZx1CvIJoW6k5gmxB"], 130], [11, -604, [0, "0ce8knNuRKEKcaLvGbCw8T"], [131, 132]]], [1, "77Y9p1/WRNQ7GdXBi1xDRm", null, null, null, 1, 0]], [9, "txt_zy_jz1", 33554432, 44, [[2, -605, [0, "d1qY5JRxdAB4DcpWwvB8CC"], [5, 150, 56.4]], [81, "强敌来袭", 0, 36, 36, false, true, true, 3, -606, [0, "2eBbbsY1dBQpQMR8x0bD+p"]], [12, "gvg_ghg_tip_234", -607, [0, "31hv7XRxhE/pk4X7FULQ+A"]]], [1, "07Mx5OZ75LHpAILDxCOXuM", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [16, "nr_tips_countDown", false, 33554432, 1, [-609, 73], [[2, -608, [0, "bfZSfKVGlBbJcWaoxqGPSQ"], [5, 640, 270]]], [1, "89OoVRgU1E7oOVQCie4X3D", null, null, null, 1, 0], [1, 0, 175.501, 0]], [6, "icon_cl2", 33554432, 141, [-612], [[2, -610, [0, "22GIv+70dPJJTcQQmFDPcI"], [5, 140, 144]], [18, 0, -611, [0, "7bsFcm0tpLiLhg/zD8Upx7"], 138]], [1, "7aCUOsW5lI6J8R/vbsNJub", null, null, null, 1, 0], [1, 0, 67.851, 0]], [16, "buildIcon", false, 33554432, 1, [76, -614], [[47, -613, [0, "e5aKZvs5RGl6LlyKrTw4KD"]]], [1, "f0FjrjJdRG7a52Eyk5PWNR", null, null, null, 1, 0], [1, -122.30000000000001, -104.61599999999999, 0]], [7, ["c46/YsCPVOJYA4mWEpNYRx"]], [26, "gvg_ghg_biaoji", false, 33554432, 1, [78], [[2, -615, [0, "97AcpWePZKmoVOLp2pnoc9"], [5, 139.1, 163.6]], [34, false, 640, 1280, -616, [0, "d4cOrnVHxOWYDXs1UJQ3w5"]]], [1, "d743vSb8FCuJiA6o466Y2E", null, null, null, 1, 0]], [4, "txt_fqtz", 33554432, 81, [[2, -617, [0, "75HROahdxFr6savpDHbgZQ"], [5, 334.4, 56.4]], [61, "向你发起挑战！", 36, 36, 2, false, true, true, 3, -618, [0, "ddEwKM7kdFFo0vKSyRq+lX"], [4, 4280999422]], [12, "gvg_ghg_tip_039", -619, [0, "b3YcRgjPlPHpyiSNm5ZUcR"]]], [1, "0c17mxE9ZAELpVT3UP8Wth", null, null, null, 1, 0], [1, -1, -16.198, 0], [1, 0.5, 0.5, 1]], [26, "jibai", false, 33554432, 16, [-622], [[2, -620, [0, "5bpyPtGsVGfYdZvDBDRCf6"], [5, 208, 88]], [3, -621, [0, "29psV+kKdABJ0M9OtMcdbp"], 160]], [1, "87QUXWDAFMJ6fmc44xLM+5", null, null, null, 1, 0]], [8, "gvg_yc_jb", 33554432, 147, [[2, -623, [0, "6f+ajdbAxN5b3N8JbYRp51"], [5, 65, 36]], [3, -624, [0, "b25lJAY09On4/5+TdoQ7tw"], 159], [66, "gvg_yc_jb", "gvg_yc_main", -625, [0, "a7ZLXEBT5DNYCcX1qnrTst"]]], [1, "17XQRVkX1KzJMnrzjij/I2", null, null, null, 1, 0]], [4, "txt_monster_djs", 33554432, 84, [[2, -626, [0, "3758/qP8ZCe4MSDv8MDZUS"], [5, 584, 96.2]], [35, "攻占荷鲁夏安全模式", 64, 64, 70, true, true, 4, -627, [0, "2cUvbsQ5FE8oQE5C3TkqJM"]], [84, 0.26, 0.82, 1, 1, -628, [0, "2cFRjBbnxGPJTS0hJ5ZNoa"], [[[4, 4288146944], [4, 4289198327]], 8, 8], 161]], [1, "9c5yh0SclHIrRoDOjvxD2z", null, null, null, 1, 0], [1, 0, 19.961, 0], [1, 0.5, 0.5, 1]], [4, "txt_fqtz", 33554432, 85, [[2, -629, [0, "daHEwgd3pHL7fFTyWP3U1R"], [5, 334.4, 56.4]], [61, "向你发起挑战！", 36, 36, 2, false, true, true, 3, -630, [0, "7bJStCvGhOo5fN287NaJIE"], [4, 4280999422]], [12, "gvg_ghg_tip_039", -631, [0, "b7mQe5B25HYb1yQGoTyzW3"]]], [1, "98a12MOLNO/Y2Gm5Vdq42V", null, null, null, 1, 0], [1, -1, -16.198, 0], [1, 0.5, 0.5, 1]], [17, "jibai", 33554432, 25, [-634], [[2, -632, [0, "91Q12LpO9BHK/xuDHqniIK"], [5, 208, 88]], [3, -633, [0, "fbiCw0duRBOaijErpNiS5m"], 165]], [1, "4bFuP9ssJElKMsZ8YK0Lje", null, null, null, 1, 0]], [8, "gvg_yc_jb", 33554432, 151, [[2, -635, [0, "40HBWWfQZKG4WF9Xpk6xVL"], [5, 65, 36]], [3, -636, [0, "5byk6enpdCEpKycVEFpqrY"], 164], [66, "gvg_yc_jb", "gvg_ghg_main", -637, [0, "b1MDgaaUJMiLpd2lmeU9pL"]]], [1, "ca9YEZ2eRBwLUlO+D16IZl", null, null, null, 1, 0]], [4, "mapbg_01", 33554432, 2, [[2, -638, [0, "0bpI5R3R5ALYL0twmeqQRG"], [5, 920, 272]], [3, -639, [0, "38fVCltsRLfI3yEeNAy/2v"], 0]], [1, "d0DXcwQYtFC79wvo6lnqzb", null, null, null, 1, 0], [1, -2036.5, 1878, 0], [1, 2, 2, 1]], [4, "mapbg_02", 33554432, 2, [[2, -640, [0, "47smF/mgtKYoAg3RdeIqua"], [5, 1117, 241]], [3, -641, [0, "feAgejaEdBzJnteeT0D/xF"], 1]], [1, "4ejMZi+91BBKNG50JhuoRl", null, null, null, 1, 0], [1, 0.4999999999998863, 1909, 0], [1, 2, 2, 1]], [4, "mapbg_03", 33554432, 2, [[2, -642, [0, "6bunnMNMBL9KWdbwW9O6Ag"], [5, 910, 214]], [18, 0, -643, [0, "3bRWUJnv5HP6hcD0qB3jIS"], 2]], [1, "bd8cbzhAtGaLFMfrygyU2i", null, null, null, 1, 0], [1, 2026.5, 1936, 0], [1, 2, 2, 1]], [4, "mapbg_05", 33554432, 2, [[2, -644, [0, "66E38ZCJ1GwYbh0nprZrU2"], [5, 200, 718]], [18, 0, -645, [0, "f2wQN2AQdN/bVJ7+h65AVm"], 4]], [1, "05yr7+JMxM/LI+uEEh1bri", null, null, null, 1, 0], [1, 3149.5, 0, 0], [1, 2, 2, 1]], [4, "mapbg_07", 33554432, 2, [[2, -646, [0, "e9Btfi/0ROaYKGRaL9UQP9"], [5, 932, 149]], [18, 0, -647, [0, "56VbPLRjNAkbDPWiUfyF/o"], 6]], [1, "0csvmeQZNL3LB6yl/Y0McF", null, null, null, 1, 0], [1, 2049, -2001, 0], [1, 2, 2, 1]], [4, "mapbg_08", 33554432, 2, [[2, -648, [0, "2eVyXPkG1NkL533ke/EbP0"], [5, 1117, 152]], [3, -649, [0, "146lv+/phEjrL5V9JuxhcX"], 7]], [1, "a9T/OcXS5FRIl2GdWqt/u+", null, null, null, 1, 0], [1, 0.5, -1998, 0], [1, 2, 2, 1]], [4, "mapbg_09", 33554432, 2, [[2, -650, [0, "bfYQET0WZH0L/Ms6qc1B27"], [5, 944, 152]], [18, 0, -651, [0, "54LI0LIMxOA6nbn46v0+5Z"], 8]], [1, "1bF5Av+VpHsLhlKEF0W12N", null, null, null, 1, 0], [1, -2060.5, -1998, 0], [1, 2, 2, 1]], [4, "mapbg_10", 33554432, 2, [[2, -652, [0, "87DFF3/RZCuoHXQHA1py1r"], [5, 173, 716]], [3, -653, [0, "9dhNKfmChAmYpZsZdtHqMG"], 9]], [1, "adZMZcqiRA/b2BACp0whtN", null, null, null, 1, 0], [1, -3176.9999999999995, -1434, 0], [1, 2, 2, 1]], [4, "mapbg_11", 33554432, 2, [[2, -654, [0, "1eYvfGIz5G5JqUDFNdXe4m"], [5, 190, 717]], [3, -655, [0, "5dycb6eyVO7LLhWh6XAmSR"], 10]], [1, "aaEdty+NVAN7Oj2AQylkdg", null, null, null, 1, 0], [1, -3160, -1, 0], [1, 2, 2, 1]], [25, "dikuai0", false, 33554432, 9, [[2, -656, [0, "fatloUychEg4rZPXbi/Upt"], [5, 6626, 4225]], [32, -657, [0, "82GtarWD9A674rSCDv2/WU"]]], [1, "08OqCrEptC+Zt6LQvXIJs+", null, null, null, 1, 0]], [8, "black", 33554432, 9, [[2, -658, [0, "c2ZdU18eBNRJiaYCFjW05b"], [5, 6626, 4225]], [139, -659, [0, "53aWlQy1pFuaUnnqdqKSvz"], [4, 1782464083]]], [1, "ceQdWQNs5Gz6a5kNmv2NEq", null, null, null, 1, 0]], [8, "<PERSON><PERSON><PERSON>", 33554432, 9, [[2, -660, [0, "69m/ulw/5Dra7Kt3ozliQX"], [5, 6626, 4225]], [32, -661, [0, "5bCOBIdfJJdJTZR6JQBFqo"]]], [1, "03RabK2zRIvLmz3sryC3gK", null, null, null, 1, 0]], [8, "bigBuild", 33554432, 9, [[2, -662, [0, "3frU+SYlpLzZ48bTvDzl+b"], [5, 6626, 4225]], [32, -663, [0, "dabb6cVvJAG6I81Jk3c5Xy"]]], [1, "f5roOJuutKuZ9uTvBUwMfO", null, null, null, 1, 0]], [25, "mountain", false, 33554432, 9, [[2, -664, [0, "c5YCw07UZFSLsxdxaZwUt9"], [5, 6626, 4225]], [32, -665, [0, "d4O7bh59tAI5V5/O0V7Y/L"]]], [1, "36K6CJLF1Gzr14HpGS6uN4", null, null, null, 1, 0]], [8, "g<PERSON><PERSON>", 33554432, 3, [[2, -666, [0, "3cez8MzHVOYJcLbkEZYRAn"], [5, 6700, 4200]], [33, 45, 3476, 3961.5, -667, [0, "ba/PqrS8RCQIIsbZuRTyKB"]]], [1, "a6l8upBiJLJJU3Wd/xGS3s", null, null, null, 1, 0]], [8, "chusongMap", 33554432, 3, [[2, -668, [0, "dfgyNjRSpAEZ+I5t4pPN1s"], [5, 6700, 4200]], [33, 45, 3476, 3961.5, -669, [0, "d0dr4Sk89AurvkzzRDimsY"]]], [1, "61Ed8sUhFBP4tYFhjld2V2", null, null, null, 1, 0]], [8, "lineMap", 33554432, 3, [[2, -670, [0, "70x8116GtKIocUD+5GDhcG"], [5, 6700, 4200]], [33, 45, 3476, 3961.5, -671, [0, "db/sbXVR5DCKyPDlw+t++5"]]], [1, "7c03c12DdJsbaps3xxo9ps", null, null, null, 1, 0]], [8, "clickNode", 33554432, 3, [[2, -672, [0, "67S7aw5fVNzJ8xwoZrC+Zh"], [5, 6700, 4200]], [33, 45, 3476, 3961.5, -673, [0, "40j7aYzCVA1Z0lMkfA84g6"]]], [1, "f1yI1bh5pOUbKEisjHn+Ia", null, null, null, 1, 0]], [8, "gridMonster", 33554432, 3, [[2, -674, [0, "abnOzb7lxLhrzsQKPPN2pJ"], [5, 6700, 4200]], [33, 45, 3476, 3961.5, -675, [0, "b2fcvxGTdJroEyuaf8SJcB"]]], [1, "8c6fBBtlBJp7EI4oLMg84c", null, null, null, 1, 0]], [8, "gridOtherMap", 33554432, 3, [[2, -676, [0, "483VMjXf5Gkr5w7sAfN7U5"], [5, 6700, 4200]], [33, 45, 3476, 3961.5, -677, [0, "83dXn3g71HeaSjARQtEyKV"]]], [1, "157ia46NFJ34RK9/P4ptnn", null, null, null, 1, 0]], [8, "img_ghg_map_di3", 33554432, 4, [[2, -678, [0, "64m8uvHtdOUYDQrIeN6vQb"], [5, 175, 136]], [18, 0, -679, [0, "15tbAKsbZEHoixF/pssBv/"], 13]], [1, "73L1jKwnBLbqfER2fZMiZF", null, null, null, 1, 0]], [23, "ani_hc", false, 33554432, 4, [[5, -680, [0, "79RUisBWVAWqWkJCFEYc9P"], [5, 477.64520263671875, 696.6078491210938], [0, 0.563288527767154, 0.47391214558762235]], [46, false, 0, -681, [0, "bd2MsLJ/BL2JHpi37GlvvI"], 14]], [1, "4aUoIkdhZNxa3oj0lkKxoP", null, null, null, 1, 0], [1, 0, 12.029, 0]], [4, "car", 33554432, 4, [[5, -682, [0, "afHrnCordG/7trN8SO5fat"], [5, 178.00001525878906, 171], [0, 0.5672357051518725, 0.026550025270696272]], [46, false, 0, -683, [0, "33O1csa/xIeZwvXe2ELXMe"], 15]], [1, "879IP0wGBMWaCspwcybwOx", null, null, null, 1, 0], [1, 0, -47.972, 0], [1, 0.8, 0.8, 1]], [4, "txt_num", 33554432, 46, [[2, -684, [0, "66+hzkwSRJ6b4YMXmBrOmC"], [5, 6, 76.56]], [35, "", 52, 52, 56, true, true, 3, -685, [0, "a7kcGr7gNImr4dpfc9S1Zs"]]], [1, "4bUXGQLIpITruMLxb1pPM3", null, null, null, 1, 0], [1, 0, -52.817, 0], [1, 0.5, 0.5, 1]], [13, "img_dot1", 33554432, 28, [[2, -686, [0, "17Ep7voI9CxL9uiq+W79JZ"], [5, 17, 17]], [3, -687, [0, "0caxOBJZhISpIuqNy5ozVm"], 26]], [1, "a9pejZ2lZIFYiwK6ZLuhNh", null, null, null, 1, 0], [1, -20, 0, 0]], [8, "img_dot2", 33554432, 28, [[2, -688, [0, "c0d+FtPxhA26Z4NmFMvsRJ"], [5, 17, 17]], [3, -689, [0, "f6UTfYLLtMxpXFlVPvkaOG"], 27]], [1, "6d4wkmmXJLrotuMCCIb3xg", null, null, null, 1, 0]], [13, "img_dot3", 33554432, 28, [[2, -690, [0, "casqxk/29DyKb3kesULvkx"], [5, 17, 17]], [3, -691, [0, "83iJxdgH1GjpkGaN0tTMIX"], 28]], [1, "d3aSMDoixA/5jGMDQZwu3q", null, null, null, 1, 0], [1, 20, 0, 0]], [4, "ico_head1", 33554432, 93, [[2, -692, [0, "c4GEx2lFJG/aY6KObWI3c1"], [5, 115, 116]], [10, -693, [0, "90GNMs8mVDV6hOYHQZ31p+"]]], [1, "ebAX8IV1JEUq4lP+aH4alH", null, null, null, 1, 0], [1, -43.25, 0, 0], [1, 0.35, 0.35, 1]], [9, "sl", 33554432, 95, [[2, -694, [0, "37x3ryQGRJJLCA8tDscdMb"], [5, 290.0625, 53.88]], [41, "[4000服]公会名称", 36, 36, 38, true, true, 3, -695, [0, "f42usaDJtF/bhdj619bMzj"], [4, 4290117376]]], [1, "034l0XaQ9NlrZ0UPscc/Nv", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [9, "hp", 33554432, 29, [[2, -696, [0, "5cB5HPXv5HmK5k74dJ7jBT"], [5, 175.0625, 56.4]], [42, "122/121212", 32, 32, true, true, 3, -697, [0, "16XsxEcKJKcp5jTZhqsmRh"]]], [1, "aa8VY736FFqp03bx8lR+8p", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [23, "ani_fight", false, 33554432, 4, [[5, -698, [0, "13RgK3wrhLl7PHK4CP8uhK"], [5, 640.0000610351562, 1280], [0, 0.20191777211019782, 0.47884483337402345]], [46, false, 0, -699, [0, "2agx8CyTJPq52oiMPS9rhA"], 35]], [1, "8cie1GqpNJHq07QacaKJQd", null, null, null, 1, 0], [1, 0, 12.029, 0]], [4, "txt_num", 33554432, 49, [[2, -700, [0, "6fs8i7VLRCAKZN5bHVLttK"], [5, 6, 76.56]], [35, "", 52, 52, 56, true, true, 3, -701, [0, "1ct3FI6IVPxpXC3x3NvfiA"]]], [1, "58W7v+xbNINJkhGfSVM9cT", null, null, null, 1, 0], [1, 0, -52.817, 0], [1, 0.5, 0.5, 1]], [13, "img_dot1", 33554432, 30, [[2, -702, [0, "2efHvGWKJKhI08UgfH5PbG"], [5, 17, 17]], [3, -703, [0, "eb753CA8xJdLB5gLh9KIOZ"], 46]], [1, "f3PNjEDV1FPJpI0PqYHSBx", null, null, null, 1, 0], [1, -20, 0, 0]], [8, "img_dot2", 33554432, 30, [[2, -704, [0, "f8uWH9YCFGgZrA8wkUIqgO"], [5, 17, 17]], [3, -705, [0, "36lzalIKxNe6f9X4+YYLMT"], 47]], [1, "1e/zLk5VpG65B0JWmn/emM", null, null, null, 1, 0]], [13, "img_dot3", 33554432, 30, [[2, -706, [0, "effRzVoLpFm6qlfqJcBN1f"], [5, 17, 17]], [3, -707, [0, "df+qrcXztCnoR1Ddh3Wu7+"], 48]], [1, "2fdm8f5qFHoIPJUrFZmbnQ", null, null, null, 1, 0], [1, 20, 0, 0]], [8, "ani_click1", 33554432, 5, [[5, -708, [0, "78RiZDoH9C8Jdw3l5yGu56"], [5, 642, 1282], [0, 0.4470404984423676, 0.5062402496099844]], [64, false, 0, false, -709, [0, "b7ddblD9FFOIQMSjkxjdgH"], 49]], [1, "e5MvuexoJDYpGA7DwXFHV5", null, null, null, 1, 0]], [25, "ani_click2", false, 33554432, 5, [[5, -710, [0, "90tRRaAvhPtLa9hxKoCJ0D"], [5, 642, 1282], [0, 0.4470404984423676, 0.5062402496099844]], [64, false, 0, false, -711, [0, "55BYL21y1PKb1Lzc1xBsmz"], 50]], [1, "7aGJ3YUelEqZHHOwdEbs4v", null, null, null, 1, 0]], [4, "ico_head1", 33554432, 103, [[2, -712, [0, "1fAf4m2uhAsr0SPgR3gVcy"], [5, 115, 116]], [10, -713, [0, "7eKW75NQFI3YczGok8icxi"]]], [1, "4a9fEfwqlDM5HyD8XTaBXq", null, null, null, 1, 0], [1, -43.25, 0, 0], [1, 0.35, 0.35, 1]], [9, "sl", 33554432, 105, [[2, -714, [0, "0dQFsYc7JBILs/nMDEdmuz"], [5, 290.0625, 53.88]], [41, "[4000服]公会名称", 36, 36, 38, true, true, 3, -715, [0, "42psukAw9JaKVvz0qYp/bF"], [4, 4290117376]]], [1, "88rYuLyitA66+qCyINeu6i", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [9, "hp", 33554432, 31, [[2, -716, [0, "1b8+T/ohNPNatk4dQQ4fFI"], [5, 175.0625, 56.4]], [42, "122/121212", 32, 32, true, true, 3, -717, [0, "4dJx9hi0VEkYJmP0vNGCW7"]]], [1, "9cQkvAAnBDJrx5SHnBHBxh", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [23, "ani_fight", false, 33554432, 5, [[5, -718, [0, "79IskjT9pOYpHRsRW4VT6G"], [5, 640.0000610351562, 1280], [0, 0.20191777211019782, 0.47884483337402345]], [46, false, 0, -719, [0, "e1DoCUDi1A1bJHqNfA+ZIR"], 57]], [1, "c6yDgbGSJAgq08RBT4/0vU", null, null, null, 1, 0], [1, 0, 12.029, 0]], [25, "ani_click1", false, 33554432, 111, [[5, -720, [0, "2dhmQmOQpI5KZeNCgthzI2"], [5, 642, 1282], [0, 0.4470404984423676, 0.5062402496099844]], [64, false, 0, false, -721, [0, "2aJZeWe9VJU5h+WxvBVkYi"], 59]], [1, "752nBSGHhGrLdJB2onv15N", null, null, null, 1, 0]], [8, "gridBiaoji", 33554432, 3, [[2, -722, [0, "adM81ciwFMAbs2zCYJt+/p"], [5, 6750, 4300]], [104, false, 45, 3476, 3961.5, -723, [0, "8b0m+cmDZJiZa27v9TANv4"]]], [1, "2a4v8zryhAOqDS1VK/xN7+", null, null, null, 1, 0]], [8, "bg2", 33554432, 18, [[2, -724, [0, "71bkkIC1pLoq/2k3ZW9BA5"], [5, 640, 1440]], [55, 1, 0, -725, [0, "0bLwA76UFBTZLGgraz3ozG"], [4, 3422552064], 60]], [1, "2erQM7KmlKiL5IR7UR2/hm", null, null, null, 1, 0]], [4, "txt_zt", 33554432, 52, [[2, -726, [0, "34cT2dHeFFWpfzeEun+Fz5"], [5, 144, 88.2]], [62, "复活中", 48, 48, 70, true, true, 1, -727, [0, "27qa18vSJEiahq0/S/93B5"], [4, 4290117376], [4, 2348810240], [0, 0, -6]]], [1, "110sz6QTRAcJgMcL7iJDWa", null, null, null, 1, 0], [1, 0, 18.05, 0], [1, 0.5, 0.5, 1]], [4, "txt_fh_djs", 33554432, 52, [[2, -728, [0, "ebfWaHS4BIqoprx7hZgqrL"], [5, 106.7578125, 88.2]], [62, "10S", 60, 60, 70, true, true, 1, -729, [0, "dcVtkILOFCw73+KYDd0hoG"], [4, 4280540671], [4, 2348810240], [0, 0, -6]]], [1, "970pMiwoRN/pdOks38ZZ4c", null, null, null, 1, 0], [1, 0, -18.05, 0], [1, 0.5, 0.5, 1]], [96, "txt_tab_1", 33554432, 113, [[[2, -730, [0, "77Mt/A7RxGrJ0Q7zFDQxkY"], [5, 345, 80.64]], [79, "复活", 53, 52, 64, 2, true, true, 3, -731, [0, "bc648ctydDD5l0O0o5vV7i"], [4, 4282867204]], -732], 4, 4, 1], [1, "5cvq8qCo5I7pXjdLcFwDsb", null, null, null, 1, 0], [1, 0, 2, 0], [1, 0.5, 0.5, 1]], [29, 0, {}, 116, [30, "79BcNar8tDmZucBHLjoa8/", null, null, -733, [36, "d2dDe3lfBJXrnRnQV0ke2O", 1, [[22, "list_xh1", ["_name"], [7, ["79BcNar8tDmZucBHLjoa8/"]]], [15, ["_lpos"], [7, ["79BcNar8tDmZucBHLjoa8/"]], [1, 0, 0.03299999999998704, 0]], [15, ["_lrot"], [7, ["79BcNar8tDmZucBHLjoa8/"]], [3, 0, 0, 0, 1]], [15, ["_euler"], [7, ["79BcNar8tDmZucBHLjoa8/"]], [1, 0, 0, 0]], [22, "1", ["_string"], [7, ["0dpBBsahVNwKBm3pB6bLA5"]]], [15, ["_contentSize"], [7, ["feEue8VsVDB4gRggznsuay"]], [5, 28.24609375, 66.48]], [15, ["_color"], [7, ["0dpBBsahVNwKBm3pB6bLA5"]], [4, 4294967295]], [15, ["_lpos"], [7, ["5dxakxnn1IH5IOSWRbNW25"]], [1, -9.0615234375, 0, 0]], [15, ["_contentSize"], [7, ["47Cs6IYElOBK5jyf6MFKqL"]], [5, 49.123046875, 30]], [15, ["_outlineColor"], [7, ["0dpBBsahVNwKBm3pB6bLA5"]], [4, 4280098330]], [22, true, ["_active"], [7, ["5dxakxnn1IH5IOSWRbNW25"]]], [15, ["_lpos"], [7, ["ab6OaQyhFLSaBnxtHZuhoA"]], [1, 17.5, 0, 0]]]], 65]], [13, "img_gh1", 33554432, 20, [[2, -734, [0, "e9vh2ca3tBv5mKQVWK2tVw"], [5, 132, 104]], [3, -735, [0, "44lJVLJb1JRqaVUoAt5uI+"], 68]], [1, "6bMj1gMghLV6WJrseOCnFP", null, null, null, 1, 0], [1, 0, 28.215, 0]], [8, "img_dw", 33554432, 32, [[2, -736, [0, "e8T+5C8q1LAZvpaGHgCPEi"], [5, 80, 82]], [3, -737, [0, "b1HGFFhaNAio7n1HIa9joQ"], 71]], [1, "42KPFEqVBHw6fFRYJFxgbw", null, null, null, 1, 0]], [8, "img_zb", 33554432, 33, [[2, -738, [0, "06eBkuW2ZMRL2YO0fZVhF3"], [5, 80, 82]], [3, -739, [0, "79No2EIAFMAae5++/dy/Mc"], 72]], [1, "5eJhl+azJEZYqOmYfcHVtf", null, null, null, 1, 0]], [23, "btn_sf", false, 33554432, 21, [[2, -740, [0, "eaSYpycUJMXpbaaQnkXLva"], [5, 80, 82]], [3, -741, [0, "bcQNI9E8lI9aKQ46AMf5Yt"], 73]], [1, "94XB9M2dpH4ZsV7WVhTkBW", null, null, null, 1, 0], [1, 0, 251, 0]], [8, "img_zb1", 33554432, 34, [[2, -742, [0, "d3Oi/9NI9PKrNpGtFmcZpI"], [5, 80, 82]], [3, -743, [0, "624lyTF89AHLTbnYi/06kM"], 74]], [1, "475wLkqwlFv6HFA4SjVp2J", null, null, null, 1, 0]], [8, "img_ph", 33554432, 35, [[2, -744, [0, "34Nnh+yPhJT7S+JdE5gC3s"], [5, 80, 83]], [3, -745, [0, "d01urfH5RB65c90wMsnj6G"], 75]], [1, "0fs/+wHJpPZKqbWD44mdfO", null, null, null, 1, 0]], [8, "img_gz", 33554432, 36, [[2, -746, [0, "62CGzVImpPLpVHhZgkjsrN"], [5, 80, 82]], [3, -747, [0, "10aPV7PMxAE6uM8HB3sgDc"], 76]], [1, "d7b28HfZtLl61fCMq2x7go", null, null, null, 1, 0]], [8, "img_fh", 33554432, 37, [[2, -748, [0, "3cK9pmXZdCo5NEyZGYhBb2"], [5, 80, 82]], [3, -749, [0, "f6eL1gkABHUZXsM1ZK/njJ"], 77]], [1, "bbKQxNooFJbrhoCVPy+ypN", null, null, null, 1, 0]], [4, "txt_chat1", 33554432, 11, [[5, -750, [0, "92xW2XELNLn7TjxmWF/LF/"], [5, 0, 50.4], [0, 0, 0.5]], [80, "", 0, 36, 36, true, -751, [0, "5an2mlRwFC6qREv8BEcLjF"], [4, 4288806655]]], [1, "82z//yIXZPjIvEhRPpjnhL", null, null, null, 1, 0], [1, 2, 0, 0], [1, 0.5, 0.5, 1]], [4, "txt_chat2", 33554432, 11, [[5, -752, [0, "4f64+HCZ9Bg5RXt+qgJFEh"], [5, 0, 50.4], [0, 0, 0.5]], [126, "", 0, 36, 36, true, -753, [0, "d0sG1rybtDXIt1wJTmntqg"]]], [1, "02aRPkoA1MwJMfSCWvyL4t", null, null, null, 1, 0], [1, 4, 0, 0], [1, 0.5, 0.5, 1]], [29, 0, {}, 38, [30, "03XfDFaqNOdp6SCwdhIQpD", null, null, -757, [36, "61KSLyA5ZJh5BjjUS3aZHr", 1, [[27, "btn_ty1", ["_name"], 59], [14, ["_lpos"], 59, [1, 0, 0, 0]], [14, ["_lrot"], 59, [3, 0, 0, 0, 1]], [14, ["_euler"], 59, [1, 0, 0, 0]], [14, ["_lscale"], 59, [1, 1.1, 1.1, 1]], [15, ["_outlineColor"], [7, ["bc648ctydDD5l0O0o5vV7i"]], [4, 4279270831]], [86, ["_spriteFrame"], [7, ["43Z+zpMHNH95/fghPESQqh"]], 92], [87, ["spfs", "2"], -754, 93], [27, 4, ["spfs", "length"], -755], [87, ["spfs", "3"], -756, 94], [15, ["_contentSize"], [7, ["89Kmzp7M5JRJxKSraI3F2C"]], [5, 190, 66]]]], 91]], [7, ["2fM+Ep5oJCAbVEaTu9NhXO"]], [4, "txt_jlks", 33554432, 61, [[2, -758, [0, "fdWMRGzVJFE4CbC8ASZtK3"], [5, 280, 55.44]], [82, "距离开赛剩余：", 40, 44, true, -759, [0, "6be6IIHnZMB43z7pd/V45O"]]], [1, "61v8KBIMROn7ZpDaUl4y9v", null, null, null, 1, 0], [1, -40.029296875, 0, 0], [1, 0.5, 0.5, 1]], [4, "txt_sysj", 33554432, 61, [[2, -760, [0, "63FHtqeMBMso4HQD2iZ0XP"], [5, 160.1171875, 55.44]], [127, "00:00:02", 40, 44, true, -761, [0, "b3oGRK/WlJE6GNUTiznLpK"], [4, 4289855272]]], [1, "82sGpm5N5AHJEGqW2CJ+03", null, null, null, 1, 0], [1, 70, 0, 0], [1, 0.5, 0.5, 1]], [68, "txt_sjzy", false, 33554432, 60, [[2, -762, [0, "28P/CPGq9OnKQTgB+JgWgF"], [5, 440, 55.44]], [82, "收集资源，获得更高积分", 40, 44, true, -763, [0, "ad1z9Au+pHYo4llJflW84L"]]], [1, "cdlShj5UhO46abzipxyNOL", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [4, "txt_hc1", 33554432, 12, [[2, -764, [0, "3dBqh+1cRGZ6qt8A6mN39V"], [5, 94, 69]], [21, "回城", 44, 44, 50, true, true, 3, -765, [0, "72J+/deeBITqPs0KWA4LQE"], [4, 4278453288]]], [1, "8bJRrKPCNI16PqG4p+UWce", null, null, null, 1, 0], [1, 0, -45.777, 0], [1, 0.5, 0.5, 1]], [8, "img_hc_mask", 33554432, 128, [[2, -766, [0, "61Sv2Z1I9IWrYwOtzhEyAf"], [5, 121, 115]], [52, -767, [0, "dbHDaXfgFGjqyJWtFTcrwZ"], [4, 3019898880], 103]], [1, "edQ1+kgBFEBa2dLTDIOhME", null, null, null, 1, 0]], [4, "txt_hc_djs", 33554432, 128, [[2, -768, [0, "8aaTwc+qhOPqRFx0nyw4zz"], [5, 188.69, 101]], [128, "10s", 0, 58, 58, 101, false, -28, true, true, 3, -769, [0, "74+3iuz4tELZgogTc1g52O"], [4, 4278453288], 104]], [1, "5bRx/TJ1VFnoh/gUUEHbtd", null, null, null, 1, 0], [1, 6.1609999999999445, -9.233000000000004, 0], [1, 0.5, 0.5, 1]], [4, "txt_name3", 33554432, 62, [[2, -770, [0, "f0ezXKPF1LqZOvML2QYbVH"], [5, 311.3, 58.92]], [63, "玩家名称", 40, 42, 2, false, true, true, 3, -771, [0, "e0JJghY2BM+4XQY5vETOC/"], [4, 4282598655]]], [1, "979dOhrGlLN5oFfcAatLea", null, null, null, 1, 0], [1, -1, 13.063, 0], [1, 0.5, 0.5, 1]], [4, "txt_fqtz", 33554432, 62, [[2, -772, [0, "49wDjEnB1DVK425epDpfHH"], [5, 334.4, 56.4]], [61, "向你发起挑战！", 36, 36, 2, false, true, true, 3, -773, [0, "b4VREe4rtDsoYbjZa2wFAK"], [4, 4280999422]]], [1, "3dvtrlHbJCGamJhUyZGk9Q", null, null, null, 1, 0], [1, -1, -16.198, 0], [1, 0.5, 0.5, 1]], [9, "item_head2", 33554432, 63, [[2, -774, [0, "ecn2x4zqJLsIvaUgSVsair"], [5, 115, 116]], [10, -775, [0, "6cJP9PlpRJKrkMqQzBmr1L"]]], [1, "9bC6rq6AJC/7eG3UMIyq6K", null, null, null, 1, 0], [1, 0.7, 0.7, 1]], [4, "txt_name1", 33554432, 63, [[2, -776, [0, "5fgQFC9iRG64f50cVvp4ow"], [5, 150, 56.4]], [40, "玩家名称", 36, 36, true, true, 3, -777, [0, "2dyze42mVEyoo6S4u156Qp"], [4, 4290117376]]], [1, "c2mCSlENZPi5gtTn7Kb4wy", null, null, null, 1, 0], [1, 0, -37.643, 0], [1, 0.5, 0.5, 1]], [9, "item_head3", 33554432, 64, [[2, -778, [0, "1e51WDM4xIAalkdW+EKnS+"], [5, 115, 116]], [10, -779, [0, "e3MyISnFFFXJGaIE8QOtGV"]]], [1, "be9pSzOUFEG6fIWyUGsXv1", null, null, null, 1, 0], [1, 0.7, 0.7, 1]], [4, "txt_name2", 33554432, 64, [[2, -780, [0, "1cQuZkDKdMfZ5mXyWkdDCW"], [5, 150, 56.4]], [40, "玩家名称", 36, 36, true, true, 3, -781, [0, "b4Nk5/mk5NGan5uloBVbSH"], [4, 4282598655]]], [1, "01fZGkZcpElJW0Ut4HiSuA", null, null, null, 1, 0], [1, 0, -37.643, 0], [1, 0.5, 0.5, 1]], [13, "img_gvg_di12", 33554432, 40, [[2, -782, [0, "14QNxW2HFIDbZ0FJDFO+Xd"], [5, 246, 36]], [3, -783, [0, "6eP1d20NBLsrYwCAD3xiO2"], 109]], [1, "e9xUAqN35HlrIRp06FuzsS", null, null, null, 1, 0], [1, 0, -18, 0]], [4, "txt_model", 33554432, 66, [[2, -784, [0, "6bUZnyYV1Hxr2rDSJOOiv4"], [5, 0, 50.4]], [129, "", 36, 36, true, -785, [0, "07Ngs5QaNMMrRrktMrB7DT"], [4, 4290117376]]], [1, "f5XH5+t4VEv4iHwVcaDLS9", null, null, null, 1, 0], [1, 0, 19.106, 0], [1, 0.5, 0.5, 1]], [13, "img_djs1", 33554432, 67, [[2, -786, [0, "701Y9BI7BBx5V5DIosNhFX"], [5, 25, 25]], [98, 0, -787, [0, "aeLFcHDXlLQJmR6KvU6X1g"], [4, 4280999422], 110]], [1, "33S2D4LeNLY4AUYBY5D9kx", null, null, null, 1, 0], [1, -3, -1.8740000000000236, 0]], [4, "txt_time", 33554432, 67, [[2, -788, [0, "f8lxpJqPRAlLdGE2TvUsbW"], [5, 0, 63]], [83, "", 48, 48, 50, true, -789, [0, "edgGBuQJlKrJTmoPIO9U6w"], [4, 4280999422]]], [1, "c1qPpPnvlGUKA7RJHDjaBV", null, null, null, 1, 0], [1, 15.5, -2.2490000000000236, 0], [1, 0.5, 0.5, 1]], [93, "img_sjx3", 33554432, 41, [[2, -790, [0, "1cGPNuCNZHq5woj2l5zJ25"], [5, 24, 13]], [3, -791, [0, "bcuRIOv/VGMYNrbu0xKokn"], 113]], [1, "bbVJhhTexDObQSYkOefER3", null, null, null, 1, 0], [1, -100.433, -15.47199999999998, 0], [3, 0, 0, 1, 6.123233995736766e-17], [1, 180, 180, 7.016709298534876e-15]], [13, "img_sjx1", 33554432, 14, [[2, -792, [0, "5fRJxgmzFHTahPm/KRTJ0b"], [5, 24, 13]], [18, 0, -793, [0, "61OF4pYoBFpazQQapXqKVc"], 116]], [1, "b3zObN+VlN15LJu7aVtVV2", null, null, null, 1, 0], [1, -100.433, -15.47199999999998, 0]], [25, "dikuai0", false, 33554432, 15, [[2, -794, [0, "b5k0VfwqxEvYx6aLKNJT03"], [5, 6626, 4225]], [32, -795, [0, "b0x/wiBQdNk7pn1HDYwr/s"]]], [1, "a7zaK9NeVAX7gV5HwJw2oD", null, null, null, 1, 0]], [25, "black", false, 33554432, 15, [[2, -796, [0, "fesShT2NxOqYig8xs2ftjL"], [5, 6626, 4225]], [32, -797, [0, "d8FQN0FT1KFpJBkA+DM3Ki"]]], [1, "0cTrcb3sFMe47jis5aQZXn", null, null, null, 1, 0]], [8, "<PERSON><PERSON><PERSON>", 33554432, 15, [[2, -798, [0, "d0cAlH+9JEY5fL9bulp+0+"], [5, 6626, 4225]], [32, -799, [0, "494x47YS9MTIjO4z7ifJnR"]]], [1, "efBndIuyFEoK76D2rBLnfP", null, null, null, 1, 0]], [8, "bigBuild", 33554432, 15, [[2, -800, [0, "11bdAGnw5DULBIK9MLDKLD"], [5, 6626, 4225]], [32, -801, [0, "44NsywhVtL/74pkJhdlIWu"]]], [1, "9cHAUYrgNGCJEwn0Bpg80Y", null, null, null, 1, 0]], [25, "mountain", false, 33554432, 15, [[2, -802, [0, "98O09XGQBLULmo911dAjI9"], [5, 6626, 4225]], [32, -803, [0, "f1Mrk/dLFPp7r9GTwBmDE6"]]], [1, "adruWnNzVDDIdLldzoqggO", null, null, null, 1, 0]], [9, "ssmap-001", 33554432, 70, [[2, -804, [0, "dbA1XIAEhEwKty6LRbzFOJ"], [5, 663, 423]], [3, -805, [0, "b9FKCkLhVD7Z0Vteff0G/t"], 118]], [1, "059eYBQGlC/KstvsTE1icE", null, null, null, 1, 0], [1, 10, 10, 1]], [54, "ani_warn", false, 33554432, 14, [[5, -806, [0, "beJ1t7LIhBb5mRQMcgma1E"], [5, 642, 1282], [0, 0.20093457943925233, 0.8502340093603744]], [24, "default", "animation", false, 0, -807, [0, "c3pKnYLytG3K10WQn31P33"], 120]], [1, "65Bsx3ZyhPCJAz6AK40z+0", null, null, null, 1, 0], [1, -4.345, -125.6, 0], [1, 1, 2, 1]], [8, "ani_tj0", 33554432, 8, [[5, -808, [0, "6bfo/OZnVN+643upDpVlXO"], [5, 255.99998474121094, 55], [0, 0.49793729173763346, 0.49356689453125]], [46, false, 0, -809, [0, "36jHHHYtxKy7/amACfI/wA"], 126]], [1, "53SJa08JRL0rxhAEPUSFY6", null, null, null, 1, 0]], [8, "ani_tq1", 33554432, 8, [[5, -810, [0, "2a3q+XrlpAJ7FiAce9dqXW"], [5, 255.99998474121094, 55], [0, 0.49793729173763346, 0.49356689453125]], [46, false, 0, -811, [0, "905LvtO1hPQ4GBaSd5URVT"], 127]], [1, "25CQy7XVRIGal24twb8f6A", null, null, null, 1, 0]], [4, "txt_zy2", 33554432, 8, [[5, -812, [0, "02X0XkSdNKAIbEutThMeHA"], [5, 120.29599999999999, 56.4], [0, 1, 0.5]], [130, "+9", 2, 36, 36, 2, false, true, true, 3, -813, [0, "6am1kmdJlFXIgVt3jUpVbU"]]], [1, "63ct68pvFKXY3dDBZ/9Tkl", null, null, null, 1, 0], [1, 86.496, 0, 0], [1, 0.5, 0.5, 1]], [13, "icon_1", 33554432, 8, [[2, -814, [0, "feYQL3j85INpXMEyKACmab"], [5, 21, 32]], [3, -815, [0, "a1cqCEmzpMtp1QKhep0+WD"], 128]], [1, "89TmIbzD9ECoUD301Sfxzn", null, null, null, 1, 0], [1, 102.903, 0, 0]], [8, "ani_zyzb_jdthong", 33554432, 44, [[5, -816, [0, "7bQGBu0xFGyZwQKr1ycJFY"], [5, 255.99998474121094, 55], [0, 0.49793729173763346, 0.4935660275545987]], [24, "default", "atk", false, 0, -817, [0, "7fDF+1WgNDxrvp46rESw1o"], 133]], [1, "24/ud7GQBHEZNgFQh0045a", null, null, null, 1, 0]], [29, 0, {}, 45, [30, "79BcNar8tDmZucBHLjoa8/", null, null, -818, [36, "c2+z4eViNGa65wCfhYVubm", 1, [[27, "toper_db1", ["_name"], 71], [14, ["_lpos"], 71, [1, -2.855, -58.9, 0]], [14, ["_lrot"], 71, [3, 0, 0, 0, 1]], [14, ["_euler"], 71, [1, 0, 0, 0]], [14, ["_lscale"], 71, [1, 1, 1, 1]], [15, ["_contentSize"], [7, ["47Cs6IYElOBK5jyf6MFKqL"]], [5, 163, 36]], [22, 1, ["_sizeMode"], [7, ["3cmYghmZ9L97pT4HhVk6a3"]]], [15, ["_lpos"], [7, ["88d8E6w2pChYKlOW8Fov+D"]], [1, -60, 0, 0]], [15, ["_lpos"], [7, ["31HfnZx/lElLSw+YwYGJuK"]], [1, 64.758, 0, 0]], [15, ["_contentSize"], [7, ["fdHAuzPnZL+bpQbpq7029Y"]], [5, 170, 63.96]], [22, false, ["_active"], [7, ["02cZFiQMxHl4L3fDAeg6dD"]]], [22, null, ["_spriteFrame"], [7, ["acoYAJMeZKn6M0p1WlLrPa"]]], [22, true, ["_enabled"], [7, ["58mgakwUhCV5gHlf0RMKZt"]]]]], 135]], [29, 0, {}, 1, [30, "c46/YsCPVOJYA4mWEpNYRx", null, null, -819, [36, "029c53on1KuIYIy2N6M+te", 1, [[27, "gvg_ghg_grid", ["_name"], 72], [14, ["_lpos"], 72, [1, 0, 0, 0]], [14, ["_lrot"], 72, [3, 0, 0, 0, 1]], [14, ["_euler"], 72, [1, 0, 0, 0]], [27, false, ["_active"], 72]]], 137]], [9, "txt_djs_num", 33554432, 142, [[2, -820, [0, "e5iAM/WXNHfZtbly9Tr5j6"], [5, 111.23046875, 128.52]], [62, "10", 100, 100, 102, true, true, 1, -821, [0, "d9ekxfG1dInbRCjuaBLrMR"], [4, 4280540671], [4, 2348810240], [0, 0, -6]]], [1, "1fJNoIpe9Lq5rkIvL68fuZ", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [4, "txt_hdjf", 33554432, 73, [[2, -822, [0, "43XlsD9JxKK6AbFimrrBig"], [5, 935.775390625, 63]], [88, 50, "<outline color=#000000 width=4><b>尽量多的向大本营提交<color=#f24949>白鲸&蚌壳</color>获得更多积分</b></outline>", 1, 1, 44, -823, [0, "eeRLtCJptOz6VlQe8rNw0F"], [4, 4280999422]]], [1, "c9qmVgkLhLTr2Tv3A9+Xy9", null, null, null, 1, 0], [1, 0, -20.734, 0], [1, 0.5, 0.5, 1]], [16, "nr_tips1", false, 33554432, 1, [75], [[2, -824, [0, "e4YnBdJJNB4oHSX3CExiwX"], [5, 640, 126]]], [1, "e7e5HhXORPXIbz8Xr+hwe/", null, null, null, 1, 0], [1, 0, 175.501, 0]], [4, "txt_djs1", 33554432, 75, [[2, -825, [0, "c3l2C+xsZLR5Xv+fD5SeHY"], [5, 141.4765625, 121.4]], [41, "52s", 80, 80, 90, true, true, 4, -826, [0, "19EYeAbj1MQqNhF5uQyykg"], [4, 4290117376]]], [1, "aa/UmsOxFE7bkl5u3gwhLk", null, null, null, 1, 0], [1, 0, 23.255999999999972, 0], [1, 0.5, 0.5, 1]], [4, "txt_ms1", 33554432, 75, [[2, -827, [0, "47+7Gk/SVHd6u0S+HKf1zV"], [5, 240, 88.2]], [83, "即将结束", 60, 60, 70, true, -828, [0, "8bQKUCdw5IS5HTyTAY911W"], [4, 4281064958]]], [1, "65i/34MiVM76uURSkjfn3q", null, null, null, 1, 0], [1, 0, -20.734, 0], [1, 0.5, 0.5, 1]], [8, "bid", 33554432, 76, [[2, -829, [0, "77tV/sacJMfbEMASlglVbI"], [5, 84.51171875, 50.4]], [131, 40, -830, [0, "90q4gbx+tJorprwnG7nQ1G"]]], [1, "769DBiFetOlo/mns8k+L/N", null, null, null, 1, 0]], [29, 0, {}, 143, [30, "c46/YsCPVOJYA4mWEpNYRx", null, null, -831, [36, "b9VIhk9iJAOpT90cHdWB93", 1, [[27, "gvg_yc_jianzhu_item", ["_name"], 144], [14, ["_lpos"], 144, [1, 0, 0, 0]], [14, ["_lrot"], 144, [3, 0, 0, 0, 1]], [14, ["_euler"], 144, [1, 0, 0, 0]]]], 143]], [4, "item_head4", 33554432, 24, [[2, -832, [0, "87o98QzElGrJb1e5wl2/xS"], [5, 115, 116]], [10, -833, [0, "110/3Y3q5NCZpXT7vFWXVs"]]], [1, "f5vCIcEuxMCrwSd9TQFFv0", null, null, null, 1, 0], [1, -50, 0, 0], [1, 0.4, 0.4, 1]], [4, "img_vs1", 33554432, 24, [[2, -834, [0, "e0pDtW52lCYZ4P9Pg0irVt"], [5, 87, 84]], [3, -835, [0, "014E5SLBhExq1ignIIczrA"], 144]], [1, "3e1coW4RFIjp/TnhVfXeIC", null, null, null, 1, 0], [1, 2.5, 5.699999999999989, 0], [1, 0.5, 0.5, 1]], [4, "item_head5", 33554432, 24, [[2, -836, [0, "c8wTeXg8BBYoMjjWX4M/Hh"], [5, 115, 116]], [10, -837, [0, "4fH688dwFMmJQQ8CM6rbhW"]]], [1, "c9trgW5g9IbK6mFypLjPNX", null, null, null, 1, 0], [1, 59.478, 0, 0], [1, 0.4, 0.4, 1]], [4, "txt_time1", 33554432, 24, [[2, -838, [0, "03dOVlKh1IZ5huOwWBmoJO"], [5, 132, 50.4]], [132, "20:41", 36, 36, 2, false, true, -839, [0, "e3Ma1SaPlE5oXCRtLZNtk+"]]], [1, "95dvdsXdNB8YDjcjN8y38y", null, null, null, 1, 0], [1, 0, -13.028, 0], [1, 0.5, 0.5, 1]], [68, "item_head4", false, 33554432, 1, [[2, -840, [0, "0cOpXLOiBPoq+EkGfATTA/"], [5, 115, 116]], [10, -841, [0, "7aC5NAxeVPK5dv/4AiEqN1"]]], [1, "cbVRBFzGRFD7aoUDzFbbPp", null, null, null, 1, 0], [1, 0.4, 0.4, 1]], [26, "arrow", false, 33554432, 1, [-843], [[2, -842, [0, "69j4dCGnNLoqCCVFz7MgLv"], [5, 50, 101]]], [1, "97Kh9CbLhEoIDisHBReXIU", null, null, null, 1, 0]], [94, "img_jt", 33554432, 257, [[5, -844, [0, "d70Aym1RVIlrMoq3ztcBdn"], [5, 671, 1492], [0, 0.518628912071535, 0.4859249329758713]], [85, "default", false, 0, -845, [0, "f6Nw8dRSFKeLwzZodjJOmz"], 146]], [1, "cd2ztdoBlCLKLJzLEjUbri", null, null, null, 1, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 0, 0, -90]], [29, 0, {}, 1, [30, "c46/YsCPVOJYA4mWEpNYRx", null, null, -846, [36, "26J8HX8DxPdbKM1WRURLy8", 1, [[22, "gvg_ghg_monster", ["_name"], [7, ["c46/YsCPVOJYA4mWEpNYRx"]]], [15, ["_lpos"], [7, ["c46/YsCPVOJYA4mWEpNYRx"]], [1, 0, 0, 0]], [15, ["_lrot"], [7, ["c46/YsCPVOJYA4mWEpNYRx"]], [3, 0, 0, 0, 1]], [15, ["_euler"], [7, ["c46/YsCPVOJYA4mWEpNYRx"]], [1, 0, 0, 0]], [22, false, ["_active"], [7, ["c46/YsCPVOJYA4mWEpNYRx"]]]]], 147]], [29, 0, {}, 1, [30, "c46/YsCPVOJYA4mWEpNYRx", null, null, -847, [36, "45RTSpK/VKrIRcS2tDsbDI", 1, [[27, "gvg_ghg_role", ["_name"], 77], [14, ["_lpos"], 77, [1, 0, 0, 0]], [14, ["_lrot"], 77, [3, 0, 0, 0, 1]], [14, ["_euler"], 77, [1, 0, 0, 0]], [27, false, ["_active"], 77]]], 148]], [54, "ani_chuansong", false, 33554432, 1, [[5, -848, [0, "57U3mCINhErZB1DtZPBUs8"], [5, 354.76190185546875, 547.0000610351562], [0, 0.516267151421809, 0.6090309258518894]], [85, "default", false, 0, -849, [0, "13OTfH9rRJoYtsBYW7VmmN"], 149]], [1, "20J1OtUHxB7qu7pEEc666v", null, null, null, 1, 0], [1, -513.046, 1315.9569999999999, 0], [1, 0.5, 0.5, 1]], [4, "txt_num", 33554432, 78, [[2, -850, [0, "c6255+5HVAiZZxsuIj2INp"], [5, 6, 76.56]], [35, "", 52, 52, 56, true, true, 3, -851, [0, "1csNkhIVtHA6FXeBFiPtyG"]]], [1, "404y6rSJZNW43pTIrzrkp5", null, null, null, 1, 0], [1, 0, -52.817, 0], [1, 0.5, 0.5, 1]], [8, "base0", 33554432, 79, [[2, -852, [0, "54c921cjFKZq35dKqMxmmq"], [5, 400, 400]], [52, -853, [0, "6bzsPgsfVJcoy4nSpUXSIQ"], [4, 4278254110], 154]], [1, "b6+aW90UpPipBGS+g/UnrS", null, null, null, 1, 0]], [8, "base1", 33554432, 79, [[2, -854, [0, "76wnm2Dw1JaYtb6BhiQT8E"], [5, 400, 400]], [52, -855, [0, "40gCt5jMRHprJSUyuFRIyU"], [4, 4279045619], 155]], [1, "86owDDGHBC7ZsIyq2vCG6+", null, null, null, 1, 0]], [29, 0, {}, 1, [30, "c46/YsCPVOJYA4mWEpNYRx", null, null, -856, [36, "48Mu2oa+1IG5CyCVtCrnCM", 1, [[27, "gvg_ghg_guild", ["_name"], 80], [14, ["_lpos"], 80, [1, 0, 0, 0]], [14, ["_lrot"], 80, [3, 0, 0, 0, 1]], [14, ["_euler"], 80, [1, 0, 0, 0]], [27, false, ["_active"], 80]]], 157]], [4, "txt_name3", 33554432, 81, [[2, -857, [0, "1fVkepBRBAK6y50GAuuCim"], [5, 311.3, 58.92]], [63, "玩家名称", 40, 42, 2, false, true, true, 3, -858, [0, "22TjpjahJKf7lvvwKam0L8"], [4, 4282598655]]], [1, "971EnboSJEtqAEmQkIhXmL", null, null, null, 1, 0], [1, -1, 13.063, 0], [1, 0.5, 0.5, 1]], [9, "item_head2", 33554432, 82, [[2, -859, [0, "25ZJ2YWiNFWql/Xq4p7BLx"], [5, 115, 116]], [10, -860, [0, "2dfCE8YrhMaJQV/Gj/RCp2"]]], [1, "98cg2v2pJH+Ypb5kdABO2W", null, null, null, 1, 0], [1, 0.7, 0.7, 1]], [4, "txt_name1", 33554432, 82, [[2, -861, [0, "0dSXSAemlDAZYXIAwLlB5n"], [5, 150, 56.4]], [40, "玩家名称", 36, 36, true, true, 3, -862, [0, "98kxX3ExFC2IfvWM4k12bq"], [4, 4290117376]]], [1, "3bueSd1CNEJYFxR/s3A6qi", null, null, null, 1, 0], [1, 0, -37.643, 0], [1, 0.5, 0.5, 1]], [9, "item_head3", 33554432, 83, [[2, -863, [0, "30INItIxZEia7+EkPqH2XG"], [5, 115, 116]], [10, -864, [0, "f3gPGZSndPCamfi2i0C4Lt"]]], [1, "4fmXsNiFpPr7bPFmkF3paY", null, null, null, 1, 0], [1, 0.7, 0.7, 1]], [4, "txt_name2", 33554432, 83, [[2, -865, [0, "53Xc6rPKlP9bKljRdCevxV"], [5, 150, 56.4]], [40, "玩家名称", 36, 36, true, true, 3, -866, [0, "d28Z8OBmRPM4/ChrPqXrfA"], [4, 4282598655]]], [1, "70ZSlZJ5VMsoIG0IjH2ZL4", null, null, null, 1, 0], [1, 0, -37.643, 0], [1, 0.5, 0.5, 1]], [4, "txt_monster", 33554432, 84, [[2, -867, [0, "d3wHiEVdtOyYWOyKaC/QKt"], [5, 640, 63]], [88, 50, "<outline color=#000000 width=4><b>尽量多地占领<color=#f24949>皇庭</color>获得更多积分</b></outline>", 1, 1, 44, -868, [0, "3dZXRgaq5Oy5UL+vYfB6F5"], [4, 4280999422]]], [1, "15NAOOZchJYaSo5GYLTNnI", null, null, null, 1, 0], [1, 0, -24.029, 0], [1, 0.5, 0.5, 1]], [4, "txt_name3", 33554432, 85, [[2, -869, [0, "90GiqkT/pDKqxinmCWzpmf"], [5, 311.3, 58.92]], [63, "玩家名称", 40, 42, 2, false, true, true, 3, -870, [0, "a6SuQwMRlEI78e66saDy3c"], [4, 4282598655]]], [1, "e8jKqyQ19CQph+Gej8P9Eb", null, null, null, 1, 0], [1, -1, 13.063, 0], [1, 0.5, 0.5, 1]], [9, "item_head2", 33554432, 86, [[2, -871, [0, "6chxZ1NclNqak1U6AGWNcg"], [5, 115, 116]], [10, -872, [0, "8f1pK6DQ5AA5o+P6wg0omc"]]], [1, "c9E4NJCzJAQKnDhPO+a6rl", null, null, null, 1, 0], [1, 0.7, 0.7, 1]], [4, "txt_name1", 33554432, 86, [[2, -873, [0, "111rFoMadMwo1tV/HFSuKS"], [5, 6, 56.4]], [40, "", 36, 36, true, true, 3, -874, [0, "9cQcZfXSBAHYPlMdHplJAM"], [4, 4290117376]]], [1, "dbk/7CBmxO5bDdQQQCsy+N", null, null, null, 1, 0], [1, 0, -37.643, 0], [1, 0.5, 0.5, 1]], [9, "item_head3", 33554432, 87, [[2, -875, [0, "6dbMr/sDRMMpdTKpb8uyV1"], [5, 115, 116]], [10, -876, [0, "8af2q/j2VHj6VCI3kQAdpK"]]], [1, "19lfZgcoxEArqQivLszdle", null, null, null, 1, 0], [1, 0.7, 0.7, 1]], [4, "txt_name2", 33554432, 87, [[2, -877, [0, "5cPZmrXyFK/Z6KR5Atr335"], [5, 6, 56.4]], [40, "", 36, 36, true, true, 3, -878, [0, "711PGJ25VEPZSq8jRzjvEQ"], [4, 4282598655]]], [1, "a7bkdSDW9CrZPo3Xj6XcIC", null, null, null, 1, 0], [1, 0, -37.643, 0], [1, 0.5, 0.5, 1]], [9, "ico_head", 33554432, 27, [[2, -879, [0, "b8Wgc+NEVCdKqnAaxfQ7cn"], [5, 115, 116]]], [1, "b8qMNf+bNDNogrAK8Z1/3x", null, null, null, 1, 0], [1, 0.35, 0.35, 1]], [44, 1, 0, 96, [0, "6bRW06olNCzIjp8XmubSOB"]], [44, 1, 0, 106, [0, "b1FVXvf9NIb4R81PVFW7kq"]], [12, "gvg_tip_235", 199, [0, "2eV0QuQRtHF7mIzQWotEQC"]], [7, ["bc648ctydDD5l0O0o5vV7i"]], [13, "smapBuildBg", 33554432, 134, [[2, -880, [0, "36h+FP1epFSYp2suojjHTQ"], [5, 1008, 1166]]], [1, "b7kMxBhNhIvaA3PGOxEuLS", null, null, null, 1, 0], [1, 0, 2.2775000000001455, 0]]], 0, [0, -1, 265, 0, -2, 260, 0, -3, 259, 0, -4, 251, 0, -5, 244, 0, -6, 243, 0, -7, 211, 0, -8, 200, 0, -9, 113, 0, -10, 111, 0, 4, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 17, 0, -2, 18, 0, -3, 6, 0, -4, 65, 0, -5, 244, 0, -6, 141, 0, -7, 247, 0, -8, 143, 0, -9, 24, 0, -10, 256, 0, -11, 257, 0, -12, 259, 0, -13, 260, 0, -14, 261, 0, -15, 145, 0, -16, 79, 0, -17, 265, 0, -18, 16, 0, -19, 84, 0, -20, 25, 0, 0, 2, 0, 0, 2, 0, -1, 153, 0, -2, 154, 0, -3, 155, 0, -4, 88, 0, -5, 156, 0, -6, 89, 0, -7, 157, 0, -8, 158, 0, -9, 159, 0, -10, 160, 0, -11, 161, 0, -12, 90, 0, 0, 3, 0, -2, 9, 0, -3, 167, 0, -4, 168, 0, -5, 169, 0, -6, 170, 0, -7, 171, 0, -8, 172, 0, -9, 26, 0, -10, 195, 0, 0, 4, 0, 0, 4, 0, -1, 173, 0, -2, 174, 0, -3, 175, 0, -4, 27, 0, -5, 46, 0, -6, 28, 0, -7, 93, 0, -8, 94, 0, -9, 183, 0, 0, 5, 0, 0, 5, 0, -1, 101, 0, -2, 102, 0, -3, 49, 0, -4, 30, 0, -5, 188, 0, -6, 189, 0, -7, 103, 0, -8, 104, 0, -9, 193, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 20, 0, -2, 21, 0, -3, 10, 0, -4, 22, 0, -5, 38, 0, -6, 60, 0, -7, 12, 0, -8, 13, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 40, 0, -2, 41, 0, -3, 14, 0, -4, 43, 0, 0, 8, 0, 0, 8, 0, -1, 137, 0, -2, 238, 0, -3, 239, 0, -4, 138, 0, -5, 240, 0, -6, 241, 0, 0, 9, 0, 0, 9, 0, -1, 162, 0, -2, 163, 0, -3, 164, 0, -4, 165, 0, -5, 166, 0, 0, 10, 0, 0, 10, 0, -1, 34, 0, -2, 35, 0, -3, 36, 0, -4, 37, 0, -5, 55, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, -1, 125, 0, -2, 209, 0, -3, 210, 0, 0, 12, 0, 0, 12, 0, 5, 12, 0, 0, 12, 0, -1, 127, 0, -2, 216, 0, -3, 128, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, -1, 62, 0, -2, 129, 0, -3, 63, 0, -4, 64, 0, 0, 14, 0, 0, 14, 0, -1, 132, 0, -2, 230, 0, -3, 69, 0, -4, 70, 0, -5, 237, 0, 0, 15, 0, 0, 15, 0, -1, 231, 0, -2, 232, 0, -3, 233, 0, -4, 234, 0, -5, 235, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, -1, 81, 0, -2, 147, 0, -3, 82, 0, -4, 83, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, -1, 196, 0, -2, 112, 0, -3, 19, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, -1, 113, 0, -2, 114, 0, -3, 116, 0, 0, 20, 0, 0, 20, 0, 5, 20, 0, 0, 20, 0, -1, 201, 0, -2, 117, 0, 0, 21, 0, 0, 21, 0, -1, 54, 0, -2, 32, 0, -3, 33, 0, -4, 204, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 5, 56, 0, 0, 22, 0, -1, 56, 0, 0, 23, 0, 0, 23, 0, -1, 58, 0, -2, 57, 0, 0, 23, 0, -1, 57, 0, -2, 58, 0, 0, 24, 0, 0, 24, 0, -1, 252, 0, -2, 253, 0, -3, 254, 0, -4, 255, 0, 0, 25, 0, 0, 25, 0, -1, 85, 0, -2, 151, 0, -3, 86, 0, -4, 87, 0, 0, 26, 0, 0, 26, 0, -3, 111, 0, 0, 27, 0, 0, 27, 0, -1, 91, 0, -2, 277, 0, -3, 92, 0, 0, 28, 0, 0, 28, 0, -1, 177, 0, -2, 178, 0, -3, 179, 0, 0, 29, 0, 0, 29, 0, 8, 278, 0, 0, 29, 0, -1, 96, 0, -2, 182, 0, 0, 30, 0, 0, 30, 0, -1, 185, 0, -2, 186, 0, -3, 187, 0, 0, 31, 0, 0, 31, 0, 8, 279, 0, 0, 31, 0, -1, 106, 0, -2, 192, 0, 0, 32, 0, 5, 32, 0, 0, 32, 0, -1, 202, 0, -2, 119, 0, 0, 33, 0, 5, 33, 0, 0, 33, 0, -1, 203, 0, -2, 120, 0, 0, 34, 0, 5, 34, 0, 0, 34, 0, -1, 205, 0, -2, 121, 0, 0, 35, 0, 5, 35, 0, 0, 35, 0, -1, 206, 0, -2, 122, 0, 0, 36, 0, 5, 36, 0, 0, 36, 0, -1, 207, 0, -2, 123, 0, 0, 37, 0, 5, 37, 0, 0, 37, 0, -1, 208, 0, -2, 124, 0, 0, 38, 0, 0, 38, 0, 0, 38, 0, -1, 39, 0, -2, 211, 0, 0, 39, 0, 0, 39, 0, 0, 39, 0, -2, 126, 0, 0, 40, 0, 0, 40, 0, 0, 40, 0, -1, 225, 0, -2, 66, 0, 0, 41, 0, 0, 41, 0, -1, 68, 0, -2, 131, 0, -3, 229, 0, 0, 42, 0, 15, 133, 0, 0, 42, 0, 0, 42, 0, 0, 42, 0, -1, 133, 0, 0, 43, 0, 0, 43, 0, -1, 135, 0, -3, 44, 0, 0, 44, 0, 0, 44, 0, -1, 139, 0, -2, 242, 0, -3, 140, 0, 0, 45, 0, 0, 45, 0, 0, 45, 0, 0, 45, 0, -1, 243, 0, 0, 46, 0, 0, 46, 0, 0, 46, 0, -1, 176, 0, 0, 47, 0, -1, 95, 0, -3, 48, 0, 0, 48, 0, 0, 48, 0, -1, 97, 0, -2, 99, 0, 0, 49, 0, 0, 49, 0, 0, 49, 0, -1, 184, 0, 0, 50, 0, -1, 105, 0, -3, 51, 0, 0, 51, 0, 0, 51, 0, -1, 107, 0, -2, 109, 0, 0, 52, 0, 0, 52, 0, -1, 197, 0, -2, 198, 0, 0, 54, 0, 0, 54, 0, 0, 54, 0, -1, 118, 0, 0, 55, 0, 0, 55, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, 0, 57, 0, 0, 57, 0, 0, 58, 0, 0, 58, 0, 0, 58, 0, 0, 60, 0, 0, 60, 0, -1, 61, 0, -2, 215, 0, 0, 61, 0, 0, 61, 0, -1, 213, 0, -2, 214, 0, 0, 62, 0, 0, 62, 0, -1, 219, 0, -2, 220, 0, 0, 63, 0, 0, 63, 0, -1, 221, 0, -2, 222, 0, 0, 64, 0, 0, 64, 0, -1, 223, 0, -2, 224, 0, 0, 65, 0, 0, 65, 0, 0, 66, 0, 0, 66, 0, -1, 226, 0, -2, 67, 0, 0, 67, 0, 0, 67, 0, -1, 227, 0, -2, 228, 0, 0, 68, 0, 0, 68, 0, 0, 68, 0, 0, 68, 0, 0, 69, 0, 0, 69, 0, 0, 69, 0, 0, 69, 0, 0, 70, 0, -2, 236, 0, -3, 134, 0, 0, 73, 0, 0, 73, 0, -1, 74, 0, -2, 246, 0, 0, 74, 0, 0, 74, 0, 0, 74, 0, 0, 74, 0, 0, 75, 0, 0, 75, 0, -1, 248, 0, -2, 249, 0, 0, 76, 0, 0, 76, 0, 0, 76, 0, -1, 250, 0, 0, 78, 0, 0, 78, 0, 0, 78, 0, -1, 262, 0, 0, 79, 0, 0, 79, 0, -1, 263, 0, -2, 264, 0, 0, 81, 0, 0, 81, 0, -1, 266, 0, -2, 146, 0, 0, 82, 0, 0, 82, 0, -1, 267, 0, -2, 268, 0, 0, 83, 0, 0, 83, 0, -1, 269, 0, -2, 270, 0, 0, 84, 0, 0, 84, 0, -1, 149, 0, -2, 271, 0, 0, 85, 0, 0, 85, 0, -1, 272, 0, -2, 150, 0, 0, 86, 0, 0, 86, 0, -1, 273, 0, -2, 274, 0, 0, 87, 0, 0, 87, 0, -1, 275, 0, -2, 276, 0, 0, 88, 0, 0, 88, 0, 0, 88, 0, 0, 89, 0, 0, 89, 0, 0, 89, 0, 0, 90, 0, 0, 90, 0, 0, 90, 0, 0, 91, 0, 0, 91, 0, 0, 91, 0, 0, 92, 0, 0, 92, 0, 0, 92, 0, 0, 93, 0, 0, 93, 0, -1, 180, 0, 0, 94, 0, 0, 94, 0, 0, 95, 0, 0, 95, 0, -1, 181, 0, 0, 96, 0, -2, 278, 0, 0, 96, 0, 0, 97, 0, 0, 97, 0, -1, 98, 0, 0, 98, 0, 0, 98, 0, 0, 98, 0, 0, 99, 0, 0, 99, 0, -1, 100, 0, 0, 100, 0, 0, 100, 0, 0, 100, 0, 0, 101, 0, 0, 101, 0, 0, 101, 0, 0, 102, 0, 0, 102, 0, 0, 102, 0, 0, 103, 0, 0, 103, 0, -1, 190, 0, 0, 104, 0, 0, 104, 0, 0, 105, 0, 0, 105, 0, -1, 191, 0, 0, 106, 0, -2, 279, 0, 0, 106, 0, 0, 107, 0, 0, 107, 0, -1, 108, 0, 0, 108, 0, 0, 108, 0, 0, 108, 0, 0, 109, 0, 0, 109, 0, -1, 110, 0, 0, 110, 0, 0, 110, 0, 0, 110, 0, -1, 194, 0, 4, 111, 0, 0, 112, 0, 0, 112, 0, 10, 281, 0, 10, 281, 0, -1, 280, 0, 4, 113, 0, 0, 114, 0, 0, 114, 0, -1, 115, 0, 0, 115, 0, 0, 115, 0, 0, 115, 0, 0, 116, 0, 0, 116, 0, -1, 200, 0, 0, 117, 0, 0, 117, 0, 0, 117, 0, 0, 118, 0, 0, 118, 0, 0, 118, 0, 0, 119, 0, 0, 119, 0, 0, 119, 0, 0, 120, 0, 0, 120, 0, 0, 120, 0, 0, 121, 0, 0, 121, 0, 0, 121, 0, 0, 122, 0, 0, 122, 0, 0, 122, 0, 0, 123, 0, 0, 123, 0, 0, 123, 0, 0, 124, 0, 0, 124, 0, 0, 124, 0, 0, 125, 0, 0, 125, 0, 0, 125, 0, 0, 126, 0, 0, 126, 0, 0, 126, 0, 0, 127, 0, 0, 127, 0, 0, 127, 0, 0, 128, 0, -1, 217, 0, -2, 218, 0, 0, 129, 0, 0, 129, 0, -1, 130, 0, 0, 130, 0, 0, 130, 0, 0, 130, 0, 0, 131, 0, 0, 131, 0, 0, 131, 0, 0, 132, 0, 0, 132, 0, 0, 132, 0, 0, 133, 0, 0, 134, 0, 0, 134, 0, -1, 282, 0, 0, 135, 0, 0, 135, 0, -1, 136, 0, 0, 136, 0, 0, 136, 0, 0, 136, 0, 0, 137, 0, 0, 137, 0, 0, 137, 0, 0, 138, 0, 0, 138, 0, 0, 138, 0, 0, 139, 0, 0, 139, 0, 0, 139, 0, 0, 140, 0, 0, 140, 0, 0, 140, 0, 0, 141, 0, -1, 142, 0, 0, 142, 0, 0, 142, 0, -1, 245, 0, 0, 143, 0, -2, 251, 0, 0, 145, 0, 0, 145, 0, 0, 146, 0, 0, 146, 0, 0, 146, 0, 0, 147, 0, 0, 147, 0, -1, 148, 0, 0, 148, 0, 0, 148, 0, 0, 148, 0, 0, 149, 0, 0, 149, 0, 0, 149, 0, 0, 150, 0, 0, 150, 0, 0, 150, 0, 0, 151, 0, 0, 151, 0, -1, 152, 0, 0, 152, 0, 0, 152, 0, 0, 152, 0, 0, 153, 0, 0, 153, 0, 0, 154, 0, 0, 154, 0, 0, 155, 0, 0, 155, 0, 0, 156, 0, 0, 156, 0, 0, 157, 0, 0, 157, 0, 0, 158, 0, 0, 158, 0, 0, 159, 0, 0, 159, 0, 0, 160, 0, 0, 160, 0, 0, 161, 0, 0, 161, 0, 0, 162, 0, 0, 162, 0, 0, 163, 0, 0, 163, 0, 0, 164, 0, 0, 164, 0, 0, 165, 0, 0, 165, 0, 0, 166, 0, 0, 166, 0, 0, 167, 0, 0, 167, 0, 0, 168, 0, 0, 168, 0, 0, 169, 0, 0, 169, 0, 0, 170, 0, 0, 170, 0, 0, 171, 0, 0, 171, 0, 0, 172, 0, 0, 172, 0, 0, 173, 0, 0, 173, 0, 0, 174, 0, 0, 174, 0, 0, 175, 0, 0, 175, 0, 0, 176, 0, 0, 176, 0, 0, 177, 0, 0, 177, 0, 0, 178, 0, 0, 178, 0, 0, 179, 0, 0, 179, 0, 0, 180, 0, 0, 180, 0, 0, 181, 0, 0, 181, 0, 0, 182, 0, 0, 182, 0, 0, 183, 0, 0, 183, 0, 0, 184, 0, 0, 184, 0, 0, 185, 0, 0, 185, 0, 0, 186, 0, 0, 186, 0, 0, 187, 0, 0, 187, 0, 0, 188, 0, 0, 188, 0, 0, 189, 0, 0, 189, 0, 0, 190, 0, 0, 190, 0, 0, 191, 0, 0, 191, 0, 0, 192, 0, 0, 192, 0, 0, 193, 0, 0, 193, 0, 0, 194, 0, 0, 194, 0, 0, 195, 0, 0, 195, 0, 0, 196, 0, 0, 196, 0, 0, 197, 0, 0, 197, 0, 0, 198, 0, 0, 198, 0, 0, 199, 0, 0, 199, 0, -3, 280, 0, 4, 200, 0, 0, 201, 0, 0, 201, 0, 0, 202, 0, 0, 202, 0, 0, 203, 0, 0, 203, 0, 0, 204, 0, 0, 204, 0, 0, 205, 0, 0, 205, 0, 0, 206, 0, 0, 206, 0, 0, 207, 0, 0, 207, 0, 0, 208, 0, 0, 208, 0, 0, 209, 0, 0, 209, 0, 0, 210, 0, 0, 210, 0, 10, 212, 0, 10, 212, 0, 10, 212, 0, 4, 211, 0, 0, 213, 0, 0, 213, 0, 0, 214, 0, 0, 214, 0, 0, 215, 0, 0, 215, 0, 0, 216, 0, 0, 216, 0, 0, 217, 0, 0, 217, 0, 0, 218, 0, 0, 218, 0, 0, 219, 0, 0, 219, 0, 0, 220, 0, 0, 220, 0, 0, 221, 0, 0, 221, 0, 0, 222, 0, 0, 222, 0, 0, 223, 0, 0, 223, 0, 0, 224, 0, 0, 224, 0, 0, 225, 0, 0, 225, 0, 0, 226, 0, 0, 226, 0, 0, 227, 0, 0, 227, 0, 0, 228, 0, 0, 228, 0, 0, 229, 0, 0, 229, 0, 0, 230, 0, 0, 230, 0, 0, 231, 0, 0, 231, 0, 0, 232, 0, 0, 232, 0, 0, 233, 0, 0, 233, 0, 0, 234, 0, 0, 234, 0, 0, 235, 0, 0, 235, 0, 0, 236, 0, 0, 236, 0, 0, 237, 0, 0, 237, 0, 0, 238, 0, 0, 238, 0, 0, 239, 0, 0, 239, 0, 0, 240, 0, 0, 240, 0, 0, 241, 0, 0, 241, 0, 0, 242, 0, 0, 242, 0, 4, 243, 0, 4, 244, 0, 0, 245, 0, 0, 245, 0, 0, 246, 0, 0, 246, 0, 0, 247, 0, 0, 248, 0, 0, 248, 0, 0, 249, 0, 0, 249, 0, 0, 250, 0, 0, 250, 0, 4, 251, 0, 0, 252, 0, 0, 252, 0, 0, 253, 0, 0, 253, 0, 0, 254, 0, 0, 254, 0, 0, 255, 0, 0, 255, 0, 0, 256, 0, 0, 256, 0, 0, 257, 0, -1, 258, 0, 0, 258, 0, 0, 258, 0, 4, 259, 0, 4, 260, 0, 0, 261, 0, 0, 261, 0, 0, 262, 0, 0, 262, 0, 0, 263, 0, 0, 263, 0, 0, 264, 0, 0, 264, 0, 4, 265, 0, 0, 266, 0, 0, 266, 0, 0, 267, 0, 0, 267, 0, 0, 268, 0, 0, 268, 0, 0, 269, 0, 0, 269, 0, 0, 270, 0, 0, 270, 0, 0, 271, 0, 0, 271, 0, 0, 272, 0, 0, 272, 0, 0, 273, 0, 0, 273, 0, 0, 274, 0, 0, 274, 0, 0, 275, 0, 0, 275, 0, 0, 276, 0, 0, 276, 0, 0, 277, 0, 0, 282, 0, 9, 1, 2, 3, 3, 3, 3, 17, 4, 3, 26, 5, 3, 26, 7, 3, 65, 8, 3, 43, 11, 3, 22, 15, 3, 133, 23, 3, 39, 29, 3, 47, 31, 3, 50, 42, 3, 70, 45, 3, 65, 47, 3, 94, 50, 3, 104, 52, 3, 112, 73, 3, 141, 75, 3, 247, 76, 3, 143, 78, 3, 145, 880], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 278, 279], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 13, 1, 2, 2, 1, -1, -2, 1, -1, -2, 1, -1, -2, -3, 1, 1, 1, 1, -1, -2, 1, 1, 1, 2, 1, -1, -2, 1, -1, -2, 1, -1, -2, -3, 1, 1, 1, 2, 2, 1, -1, -2, 1, 1, 1, 2, 6, 2, 1, 1, 6, 11, 1, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, -1, -2, 1, 1, 1, 1, 1, 12, 12, 12, 12, 1, 6, 11, 11, 11, 1, -1, -2, -3, 1, 1, -1, -2, 1, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 13, 1, 1, 2, 1, 1, 1, -1, -2, 2, 2, 1, 1, 1, -1, -2, 2, 1, 6, 1, 6, 1, 14, 1, 1, 1, 6, 1, 1, 2, 6, 6, 2, 1, -1, -2, -3, 1, 1, 1, 6, 1, 1, 1, 14, 2, 1, 1, 1, 1, 1, 1], [91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 30, 104, 20, 2, 2, 13, 3, 3, 14, 4, 4, 15, 16, 1, 1, 1, 6, 0, 7, 5, 17, 18, 11, 12, 12, 105, 8, 8, 21, 4, 4, 15, 16, 1, 1, 1, 106, 31, 6, 0, 7, 5, 17, 18, 11, 107, 31, 108, 109, 32, 110, 111, 112, 113, 22, 114, 33, 115, 116, 117, 118, 119, 120, 121, 122, 34, 123, 34, 124, 125, 9, 9, 126, 9, 9, 9, 9, 127, 32, 128, 129, 130, 22, 22, 131, 132, 133, 12, 12, 134, 12, 135, 33, 23, 24, 25, 136, 137, 138, 35, 36, 139, 35, 36, 140, 141, 142, 143, 144, 145, 26, 26, 27, 37, 37, 146, 38, 27, 26, 27, 147, 38, 148, 149, 39, 150, 40, 41, 41, 42, 151, 152, 153, 154, 39, 155, 156, 4, 4, 15, 16, 28, 28, 28, 157, 23, 24, 25, 40, 158, 23, 24, 25, 42, 0, 0]], [[[37, "gvg_yc_jianzhu_item"], [28, "gvg_yc_jianzhu_item", 33554432, [-4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15], [[2, -2, [0, "5eVKgGkQJGTKK7nnMFiqcV"], [5, 139.1, 163.6]], [34, false, 640, 1280, -3, [0, "efd63sxOpGQ6J6JglfIuRR"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [17, "mapitem", 33554432, 1, [-18, -19, -20, -21], [[2, -16, [0, "37ASlOlpNJXaE3lsd7gN0Z"], [5, 200, 200]], [10, -17, [0, "bdq6Sul1pKVKPzBE7O87AQ"]]], [1, "93ay3636RNm5RU5zC0oNS5", null, null, null, 1, 0]], [6, "fight", 33554432, 2, [-23, -24, -25, -26], [[2, -22, [0, "37ZwabXeNFoJxs1iialuYn"], [5, 190, 83.60000000000002]]], [1, "48YJp2oHpJ652ahHPlCFh6", null, null, null, 1, 0], [1, 0, -53.486, 0]], [43, "item_head2", 33554432, 2, [-29, -30, -31], [[5, -27, [0, "7cSLBF171L56ctj0L7ovs8"], [5, 102.5, 100.8], [0, 0, 0.5]], [59, 1, 1, 4, true, -28, [0, "0c0i5FNzhPIr2DGurEYrju"]]], [1, "0durLq7CxJdpoj+IyGlcn4", null, null, null, 1, 0], [1, 70, 103.994, 0], [1, 0.35, 0.35, 1]], [26, "item_head1", false, 33554432, 1, [-34, -35, -36], [[2, -32, [0, "f3dyct/fxMBqHTT4ctLx8C"], [5, 43, 43]], [10, -33, [0, "60Cb5ykDFHrIvg2yXp6cHr"]]], [1, "c9BOyFbaVIA5g+wa3ljHjs", null, null, null, 1, 0]], [6, "fightNode", 33554432, 1, [-38, -39, -40], [[47, -37, [0, "43G6B6+3tC16BKhY0WtsYh"]]], [1, "87E3yEwLhC2b/BlRp8dZZV", null, null, null, 1, 0], [1, 0, 194.52700000000004, 0]], [6, "lay_xx", 33554432, 3, [-43, -44], [[2, -41, [0, "34G6CbGQxPUJKoqr6NH9fL"], [5, 151, 49]], [45, 1, -42, [0, "24Azn5zolBa6UPkYRZDLWV"], 8]], [1, "0c1BwGbt9KNYUSOl6/qBZB", null, null, null, 1, 0], [1, 0, 2.185, 0]], [6, "fightPro1", 33554432, 3, [-49], [[2, -45, [0, "8aTX8W14dFna4Mkbm52rKo"], [5, 127, 12]], [20, 1, 0, -46, [0, "8fxlFPdPpG4aA4WamXi8v2"], 9], [39, 125, 0.7, -48, [0, "abU59FF+NJ373eJzYFn3mI"], -47]], [1, "75hrcEb7ZAp77W3oaXmScm", null, null, null, 1, 0], [1, 0, -29.588, 0]], [16, "fightPro0", false, 33554432, 3, [-54], [[2, -50, [0, "d1snu4JjhNsoa8OVhwR4pJ"], [5, 127, 12]], [20, 1, 0, -51, [0, "c5aJWL+dtIEoBRSGHgtBjs"], 10], [39, 125, 0.7, -53, [0, "86DUKdHINOHZ/Eu0ZElMqV"], -52]], [1, "704de4NnNGZr+nsy7jW4sl", null, null, null, 1, 0], [1, 0, -29.588, 0]], [26, "Layout_line2", false, 33554432, 1, [-57, -58], [[2, -55, [0, "12cEeN+BJLOJXT55PW3vCV"], [5, 50, 66]], [75, 1, 2, -8, -56, [0, "2ejJUHr8RARIb5PnZUBFI9"]]], [1, "d2G8gpVSpFd4jh0IeTFPBr", null, null, null, 1, 0]], [26, "Layout_line1", false, 33554432, 1, [-61, -62], [[2, -59, [0, "a9QYucwB1DT6m171ePkqPk"], [5, 50, 65]], [75, 1, 2, -8, -60, [0, "42Gt7doctApp4GHyp7EnQA"]]], [1, "67wP0+u3BHS6qgvzoglMoA", null, null, null, 1, 0]], [13, "sign", 33554432, 1, [[5, -63, [0, "b23Imdd5JBvqDH7xJEM7m+"], [5, 97, 178], [0, 0.5, 1]], [18, 0, -64, [0, "49hj8wKPRL35AE6i3qRsaF"], 1], [11, -65, [0, "caa8PuRYFLKr906IfvumI2"], [2, 3]]], [1, "61OxAOQw5G44QmTyegEl1E", null, null, null, 1, 0], [1, 0, 194.527, 0]], [4, "txt_dby", 33554432, 1, [[2, -66, [0, "3d0Mq8K/FKHqNb5vufkOdX"], [5, 102, 56.4]], [42, "大本营", 32, 32, true, true, 3, -67, [0, "7fg5kyDKlOg7g15td61/WS"]], [12, "gvg_tip_233", -68, [0, "deygYaZktO9aNmtrgoxlbe"]]], [1, "09Zap5CqBFWagRoQMwJikq", null, null, null, 1, 0], [1, 0, 141.23300000000006, 0], [1, 0.5, 0.5, 1]], [4, "img_dot1", 33554432, 4, [[2, -69, [0, "a0+A4rOIJLWLbj8QTL8vd1"], [5, 21, 21]], [3, -70, [0, "68X5Wy7ztCEbgh1dZ/AwMG"], 11], [11, -71, [0, "a5HnObHo5DiJV8Phlu8Iw9"], [12, 13]]], [1, "37NK8DriVEFro9u2Mtp4LJ", null, null, null, 1, 0], [1, 15.75, -25.906, 0], [1, 1.5, 1.5, 1]], [4, "img_dot2", 33554432, 4, [[2, -72, [0, "f5UYB61llAa5inSV1k8VdN"], [5, 21, 21]], [3, -73, [0, "76vD1v9BlKyrs8On5627w6"], 14], [11, -74, [0, "b9RAdz5lFLebG7sqsVGKdq"], [15, 16]]], [1, "a3v6KlCntPh4kMEr7LNLhw", null, null, null, 1, 0], [1, 51.25, -25.906, 0], [1, 1.5, 1.5, 1]], [4, "img_dot3", 33554432, 4, [[2, -75, [0, "f1kaR+IRZAdq0jlgXF+YtI"], [5, 21, 21]], [3, -76, [0, "232hIQ5tVEGJfIHwpOX7sC"], 17], [11, -77, [0, "c5L58SKgpOIafrAd92+Rag"], [18, 19]]], [1, "c99V4TGgpORIijyRy3Wyrn", null, null, null, 1, 0], [1, 86.75, -25.906, 0], [1, 1.5, 1.5, 1]], [8, "img_yuan_di1", 33554432, 5, [[2, -78, [0, "5fHuoVCRxDy68xsihRl81A"], [5, 43, 43]], [3, -79, [0, "02qKWmAOBFvYozzixw+vNB"], 24], [11, -80, [0, "30TtdPH7NE8KuZmjMAIBdE"], [25, 26]]], [1, "3efJ1AJLlK5ao5lVsIspGJ", null, null, null, 1, 0]], [8, "img_yuan_quan1", 33554432, 5, [[2, -81, [0, "25qh7dy4ZFsam68ZrmGga/"], [5, 47, 47]], [3, -82, [0, "98jh2dYDFHTakBaWRRnqS3"], 27], [11, -83, [0, "dbCCprfMNP6ZQZNLMunJLr"], [28, 29]]], [1, "adEx5C7yBHioJFqMcn7/PH", null, null, null, 1, 0]], [23, "buff0", false, 33554432, 1, [[47, -84, [0, "63hVFQrMNCZb4G/A4n+OdW"]], [65, false, 0, -85, [0, "00cGPsMyhLOp5YxSfSASfn"]]], [1, "88C4LNVL5CW47AZvqS3Qwx", null, null, null, 1, 0], [1, 0, -50.468, 0]], [23, "img_light1", false, 33554432, 1, [[2, -86, [0, "c0/uvn9jlDXL4Si9OnTSKp"], [5, 175, 176]], [3, -87, [0, "bdsXkZzT9K6J9xT8fqbnj8"], 0]], [1, "6avyGCHYBD5pns9gueAvMI", null, null, null, 1, 0], [1, 0, 10.794999999999959, 0]], [13, "img_line5", 33554432, 6, [[2, -88, [0, "65mv9DgIhEC4Cm96E6kVsN"], [5, 65, 193]], [3, -89, [0, "73X8dWYW9MgLSNyn0nR2Xr"], 4]], [1, "84WpF0EDZMBKqmiE+Mz/X7", null, null, null, 1, 0], [1, 0.949, -92.714, 0]], [13, "ani_fight", 33554432, 6, [[5, -90, [0, "92QLZhusZB6ruR0OVfwQKI"], [5, 63, 66], [0, 0.49206349206349204, 0.48484848484848486]], [24, "default", "animation", false, 0, -91, [0, "aekasmA/lOUrJhGNzJ/Kst"], 5]], [1, "f4N6dVmLJMoafHEZtuYZX9", null, null, null, 1, 0], [1, 0, -30.174999999999955, 0]], [4, "ani_fight-001", 33554432, 6, [[5, -92, [0, "99eJuAfQ5GmohgMOTRUGil"], [5, 640.0000610351562, 1280], [0, 0.20191777211019782, 0.47884483337402345]], [24, "default", "animation", false, 0, -93, [0, "53I0Tm4d1OVI1+STo2d96I"], 6]], [1, "c5PX5++Q5NFo/ZK7BuXVZC", null, null, null, 1, 0], [1, 0, -167.308, 0], [1, 0.5, 0.5, 1]], [23, "ani_bh", false, 33554432, 1, [[2, -94, [0, "e96OF8PnNCQqlTzS2m8Vqd"], [5, 175, 176]], [3, -95, [0, "65vo1UnvdHYp1Rb8JJwNgx"], 7]], [1, "97bJEkliNNTpT+498EtDHB", null, null, null, 1, 0], [1, -2, 10.794999999999959, 0]], [4, "ani_fire", 33554432, 2, [[97, -96, [0, "3buMdNp7NJn6zWZgAI9kjT"], [0, 0.5, 0.4786932373046875]], [65, false, 0, -97, [0, "cdPHTtk2VE74mA/XkoC53N"]]], [1, "deXLus+VpI8KjGIt7TrgyZ", null, null, null, 1, 0], [1, 0, -34.7, 0], [1, 0.3, 1, 1]], [13, "team", 33554432, 2, [[2, -98, [0, "5bZmA8ee9NQqvgKInIc2Um"], [5, 8, 50]], [59, 1, 1, -8, true, -99, [0, "96GvJ4JfROzov2FgdnPtbk"]]], [1, "c7wuv5Z1ZMrLGEU4MprvvX", null, null, null, 1, 0], [1, 0, 103.994, 0]], [4, "jd", 33554432, 7, [[2, -100, [0, "97oCBKh4tD/7iW+fr23ZK/"], [5, 150, 53.88]], [35, "建筑名称", 36, 36, 38, true, true, 3, -101, [0, "31Bga12QVOHLu26EP2ngMO"]]], [1, "bfLjHZxBtG56hIW1v1JqFK", null, null, null, 1, 0], [1, 0, 11.03, 0], [1, 0.5, 0.5, 1]], [4, "sl", 33554432, 7, [[2, -102, [0, "01y7pQ63RDe7H/MN5TIfmn"], [5, 254.0625, 53.88]], [41, "[1201]公会名称", 36, 36, 38, true, true, 3, -103, [0, "6bgSZXN+pLhJxtLB7xbCmF"], [4, 4282113279]]], [1, "66n1i+p7VIVplFXeHIwR1g", null, null, null, 1, 0], [1, 0, -11.229, 0], [1, 0.5, 0.5, 1]], [38, "Bar1", 33554432, 8, [[[5, -104, [0, "57Hdl9DJxHWb3KmJBOdPAO"], [5, 87.5, 10], [0, 0, 0.5]], -105], 4, 1], [1, "f49ruyxARIOKsLZXCReZEN", null, null, null, 1, 0], [1, -62.5, 0, 0]], [38, "Bar0", 33554432, 9, [[[5, -106, [0, "6evptFEddLlohIqapuF5Ux"], [5, 87.5, 10], [0, 0, 0.5]], -107], 4, 1], [1, "b41qLLBGhJJ45mFM9v9S7c", null, null, null, 1, 0], [1, -62.5, 0, 0]], [4, "hp", 33554432, 3, [[2, -108, [0, "11G0XNEd5J+6siMzGySXzU"], [5, 175.0625, 56.4]], [42, "122/121212", 32, 32, true, true, 3, -109, [0, "3cqu/8jJtK24jIQM/mU92n"]]], [1, "dbKYAevLhOg6Tk1kFlMtDW", null, null, null, 1, 0], [1, 0, -29.587999999999965, 0], [1, 0.5, 0.5, 1]], [23, "buff1", false, 33554432, 1, [[47, -110, [0, "2aosW8odtFhb31ufHhWgp6"]], [65, false, 0, -111, [0, "6btj5YmoBKeYXwgihvDg2Z"]]], [1, "ccjNtYXhBOaZdswPhDU4PG", null, null, null, 1, 0], [1, 0, -2.991, 0]], [13, "img_yuan3", 33554432, 10, [[2, -112, [0, "d1yqZD+GVDJpg6V5Jt4P3A"], [5, 45, 44]], [3, -113, [0, "c8VfcT8SVE4ri+1pscgI1g"], 20]], [1, "f2fCQFlaxLLKDrqfIm5O31", null, null, null, 1, 0], [1, 0, 11, 0]], [13, "icon_arrow3", 33554432, 10, [[2, -114, [0, "ab4dD3vlFCA6aoE98bDWuM"], [5, 32, 30]], [45, 2, -115, [0, "9doUWt99FNSIvGETdparxm"], 21]], [1, "57i81X/gtExJ+8QBSKAoTp", null, null, null, 1, 0], [1, 0, -18, 0]], [13, "img_yuan1", 33554432, 11, [[2, -116, [0, "2fy3EMTM1PO7n/RKGP8fCK"], [5, 45, 44]], [3, -117, [0, "eajIX5XtBO6ZpPBK9/DQR5"], 22]], [1, "10khp2+fJNYJPO9YptaQC+", null, null, null, 1, 0], [1, 0, 10.5, 0]], [13, "icon_arrow1", 33554432, 11, [[2, -118, [0, "cf8eBSoB5JkINmyrYFBKee"], [5, 32, 29]], [45, 2, -119, [0, "97dJwsAS1GtqgsBS8DNRFs"], 23]], [1, "14O+obwKpAnrN9vk6PWtOz", null, null, null, 1, 0], [1, 0, -18, 0]], [13, "ani_pos", 33554432, 1, [[5, -120, [0, "76vV0+BNRB37APrxa2/yXe"], [5, 37.00000762939453, 43.0000114440918], [0, 0.4999975256022839, 0.014395046007011057]], [24, "default", "animation", false, 0, -121, [0, "65iGPByi5NZrK4r4AyS+b7"], 30]], [1, "f1mcEULD5PurttzKmePwkV", null, null, null, 1, 0], [1, 0, -0.052, 0]], [69, 0, 29, [0, "d6m2KkyCRFMbF2WJghQPSt"]], [99, 0, false, 30, [0, "4e/QUKtH9NFbePXTxsi4FE"]], [9, "ico_head", 33554432, 5, [[2, -122, [0, "43rSBKpEVB94h48GVXOPwv"], [5, 115, 116]]], [1, "01aFDatQ5DII8y5KBhi3vo", null, null, null, 1, 0], [1, 0.35, 0.35, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 19, 0, -2, 20, 0, -3, 12, 0, -4, 6, 0, -5, 13, 0, -6, 24, 0, -7, 2, 0, -8, 32, 0, -9, 10, 0, -10, 11, 0, -11, 5, 0, -12, 37, 0, 0, 2, 0, 0, 2, 0, -1, 25, 0, -2, 26, 0, -3, 3, 0, -4, 4, 0, 0, 3, 0, -1, 7, 0, -2, 8, 0, -3, 9, 0, -4, 31, 0, 0, 4, 0, 0, 4, 0, -1, 14, 0, -2, 15, 0, -3, 16, 0, 0, 5, 0, 0, 5, 0, -1, 17, 0, -2, 40, 0, -3, 18, 0, 0, 6, 0, -1, 21, 0, -2, 22, 0, -3, 23, 0, 0, 7, 0, 0, 7, 0, -1, 27, 0, -2, 28, 0, 0, 8, 0, 0, 8, 0, 8, 38, 0, 0, 8, 0, -1, 29, 0, 0, 9, 0, 0, 9, 0, 8, 39, 0, 0, 9, 0, -1, 30, 0, 0, 10, 0, 0, 10, 0, -1, 33, 0, -2, 34, 0, 0, 11, 0, 0, 11, 0, -1, 35, 0, -2, 36, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, -2, 38, 0, 0, 30, 0, -2, 39, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 40, 0, 9, 1, 122], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39], [1, 1, -1, -2, 1, 2, 2, 1, 1, 1, 1, 1, -1, -2, 1, -1, -2, 1, -1, -2, 1, 1, 1, 1, 1, -1, -2, 1, -1, -2, 2, 1, 1], [43, 44, 44, 159, 160, 161, 45, 43, 6, 5, 5, 10, 10, 29, 10, 10, 29, 10, 10, 29, 162, 163, 164, 165, 2, 2, 13, 3, 3, 14, 166, 7, 0]], [[[89, "map", "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<map version=\"1.10\" tiledversion=\"1.11.2\" orientation=\"hexagonal\" renderorder=\"left-down\" width=\"51\" height=\"32\" tilewidth=\"176\" tileheight=\"130\" infinite=\"0\" hexsidelength=\"82\" staggeraxis=\"x\" staggerindex=\"odd\" nextlayerid=\"9\" nextobjectid=\"1\">\r\n <tileset firstgid=\"1\" source=\"ground.tsx\"/>\r\n <tileset firstgid=\"46\" source=\"build1.tsx\"/>\r\n <tileset firstgid=\"49\" source=\"road.tsx\"/>\r\n <layer id=\"2\" name=\"dikuai0\" width=\"51\" height=\"32\" visible=\"0\">\r\n  <data encoding=\"base64\" compression=\"zlib\">\r\n   eJztWEkOwyAM5Nr1nkCzPKD//157QUKIxjEeY9rmEI0SnMGDxxIwOOfG9zP8ACI4AjHuhf+31BLxoRzfQssq/H8y0JJ6xTvaO1yMnHu9iaqLZ8RzcuLwcrVorL+0XhItVhq4eVjniMT826owh4ZHpw0toYN1rcE073xs6SRHCueNumj0ZPTWKXuv7e1/7X1Uj96J8TNonphv+livp0Zd0P3SmkfqMUsteb4atb4R4xeleXvw+TdoiR64Zu+ttHD3Y1b9cuzHcF5D8mrsx3rCPT4+zshYRJ+Ra++UOHHWd0qLcN3nAu9YwYP0wlMhnqMJwRUYc5biSnfjGnWR5FjyPYLvE74AfdKjBw==\r\n  </data>\r\n </layer>\r\n <layer id=\"5\" name=\"black\" width=\"51\" height=\"32\" opacity=\"0.5\" tintcolor=\"#5f5255\" offsetx=\"0\" offsety=\"5\">\r\n  <properties>\r\n   <property name=\"orderBy\" value=\"bottom\"/>\r\n  </properties>\r\n  <data encoding=\"base64\" compression=\"zlib\">\r\n   eJztz8ENADAMg8Cs0e4/aDsGRDwsvr4zM/fvLCjhQ5YslhI+ZMliKeFDliyWEj5kyWIp4UOWLJYSPmTJYinhQ5YslhI+ZNltebf3ooE=\r\n  </data>\r\n </layer>\r\n <layer id=\"6\" name=\"dikuai\" width=\"51\" height=\"32\">\r\n  <properties>\r\n   <property name=\"orderBy\" value=\"bottom\"/>\r\n  </properties>\r\n  <data encoding=\"base64\" compression=\"zlib\">\r\n   eJztWEkOwyAM5Nr1nkCzPKD//157QUKIQGyPgbQ5ICvBDB57LAGDMWb8juEHLALDFeatcD2CC5XrS9m/Rl1W4fqpAZdQK9aUtUO1HnOvNlF1sQR/SkwU3C0uJoONzr+0Xty6SPKEtAguR7Lxv1VhDw2NThkuroO8cmwYdzy3dBJjyc6Zumj0pNfWJfrm9va/9j6qR5+F+StoHx9vOFrnU6Mu6H6pjSPVWEsucbwatX4U5m9K+/ag8yNw8Rq4R9+1uFDPY6365TyP4bSGxNU4j/Vk9+j4vCNjLfqOzH1Tovi1flNahHmfE7gjAwephbeCP4UTAssR9kz5pd7GNeoiiTGlewTelv0AYLii7Q==\r\n  </data>\r\n </layer>\r\n <layer id=\"7\" name=\"bigBuild\" width=\"51\" height=\"32\">\r\n  <data encoding=\"base64\" compression=\"zlib\">\r\n   eJzt2FEKwCAMA1BPpN7/dLJ/N1JRltS8zyEYZtsNS7lXS7YPogafG47tHUbyMNUoQi2vWWborGGbkV/eZoxnzzr282fPZ2fNeruD605gr0f2fGZRGb77Gf/HbB+lWn6o5d2FrT/Z8ihSuIO98W78DwPB2QWj\r\n  </data>\r\n </layer>\r\n <layer id=\"4\" name=\"mountain\" width=\"51\" height=\"32\" visible=\"0\">\r\n  <data encoding=\"base64\" compression=\"zlib\">\r\n   eJztl1EOgCAMQ7kG97+o4c8YGdvKpOBeYmKQba2gQCn/pN4uTz80fjaa/KgXbR0LSL5ebEROTdwX46zFqqc+7tm9SGPPpH3EbnqTObCtHwgneWnU0tcqPYvUE5UjMvdbP+bxt+6DTvESVX8VM/dw2jZrHa8epI70T/Pmz+8lOYHnuj5a95nniUbnLl4anjNsIrPbu7Kc99m8efcObN8noicidpUehAgvs7HOUUu7tw4C8zn9AhcuEOY=\r\n  </data>\r\n </layer>\r\n</map>\r\n", ["destination1.png", "destination2.png", "destination3.png", "destination4.png", "destination5.png", "destination6.png", "destination7.png", "destination8.png", "destination9.png", "destination10.png", "destination11.png", "destination12.png", "destination13.png", "destination14.png", "destination15.png", "destination16.png", "ground1.png", "ground2.png", "obstacle1.png", "obstacle2.png", "start1.png", "start2.png", "start3.png", "start5.png", "start6.png", "start7.png", "start8.png", "start9.png", "start10.png", "start11.png", "start12.png", "start13.png", "start14.png", "start15.png", "start16.png", "start5.png", "start4.png", "build1.png", "build2.png", "hexin.png", "ground1_light.png", "ground2_light.png"], ["ground.tsx", "build1.tsx", "road.tsx"], [0, 1, 2], [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], [[[5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 142], [5, 176, 142], [5, 174, 210], [5, 175, 177], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 402, 369], [5, 353, 302], [5, 395, 415], [5, 176, 142], [5, 176, 142]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, -2, -3, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42], [46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 19, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 19, 83, 84, 85, 86, 87, 88]], [[{"name": "img_jdt2", "rect": {"x": 0, "y": 0, "width": 125, "height": 8}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 125, "height": 8}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-62.5, -4, 0, 62.5, -4, 0, -62.5, 4, 0, 62.5, 4, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 8, 125, 8, 0, 0, 125, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -62.5, "y": -4, "z": 0}, "maxPos": {"x": 62.5, "y": 4, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [7], [167]], [[[67, "ground", "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<tileset version=\"1.10\" tiledversion=\"1.11.2\" name=\"ground\" tilewidth=\"176\" tileheight=\"247\" tilecount=\"37\" columns=\"0\">\r\n <grid orientation=\"orthogonal\" width=\"1\" height=\"1\"/>\r\n <tile id=\"8\">\r\n  <image source=\"destination1.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"9\">\r\n  <image source=\"destination2.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"10\">\r\n  <image source=\"destination3.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"11\">\r\n  <image source=\"destination4.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"12\">\r\n  <image source=\"destination5.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"13\">\r\n  <image source=\"destination6.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"14\">\r\n  <image source=\"destination7.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"15\">\r\n  <image source=\"destination8.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"16\">\r\n  <image source=\"destination9.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"17\">\r\n  <image source=\"destination10.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"18\">\r\n  <image source=\"destination11.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"19\">\r\n  <image source=\"destination12.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"20\">\r\n  <image source=\"destination13.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"21\">\r\n  <image source=\"destination14.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"22\">\r\n  <image source=\"destination15.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"23\">\r\n  <image source=\"destination16.png\" width=\"176\" height=\"190\"/>\r\n </tile>\r\n <tile id=\"24\">\r\n  <image source=\"ground1.png\" width=\"176\" height=\"142\"/>\r\n </tile>\r\n <tile id=\"25\">\r\n  <image source=\"ground2.png\" width=\"176\" height=\"142\"/>\r\n </tile>\r\n <tile id=\"26\">\r\n  <image source=\"obstacle1.png\" width=\"174\" height=\"210\"/>\r\n </tile>\r\n <tile id=\"27\">\r\n  <image source=\"obstacle2.png\" width=\"175\" height=\"177\"/>\r\n </tile>\r\n <tile id=\"28\">\r\n  <image source=\"start1.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"29\">\r\n  <image source=\"start2.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"30\">\r\n  <image source=\"start3.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"31\">\r\n  <image source=\"start5.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"32\">\r\n  <image source=\"start6.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"33\">\r\n  <image source=\"start7.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"34\">\r\n  <image source=\"start8.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"35\">\r\n  <image source=\"start9.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"36\">\r\n  <image source=\"start10.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"37\">\r\n  <image source=\"start11.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"38\">\r\n  <image source=\"start12.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"39\">\r\n  <image source=\"start13.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"40\">\r\n  <image source=\"start14.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"41\">\r\n  <image source=\"start15.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"42\">\r\n  <image source=\"start16.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"43\">\r\n  <image source=\"start5.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n <tile id=\"44\">\r\n  <image source=\"start4.png\" width=\"171\" height=\"247\"/>\r\n </tile>\r\n</tileset>\r\n"]], 0, 0, [], [], []], [[{"name": "img_jdt3", "rect": {"x": 0, "y": 0, "width": 125, "height": 8}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 125, "height": 8}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-62.5, -4, 0, 62.5, -4, 0, -62.5, 4, 0, 62.5, 4, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 8, 125, 8, 0, 0, 125, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -62.5, "y": -4, "z": 0}, "maxPos": {"x": 62.5, "y": 4, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [7], [168]], [[[67, "road", "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<tileset version=\"1.10\" tiledversion=\"1.11.2\" name=\"road\" tilewidth=\"176\" tileheight=\"142\" tilecount=\"2\" columns=\"0\">\r\n <grid orientation=\"orthogonal\" width=\"1\" height=\"1\"/>\r\n <tile id=\"0\">\r\n  <image source=\"ground1_light.png\" width=\"176\" height=\"142\"/>\r\n </tile>\r\n <tile id=\"1\">\r\n  <image source=\"ground2_light.png\" width=\"176\" height=\"142\"/>\r\n </tile>\r\n</tileset>\r\n"]], 0, 0, [], [], []], [[{"name": "default_btn_disabled", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [7], [169]], [[[67, "build1", "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<tileset version=\"1.10\" tiledversion=\"1.11.2\" name=\"build1\" tilewidth=\"439\" tileheight=\"446\" tilecount=\"3\" columns=\"0\">\r\n <tileoffset x=\"-130\" y=\"130\"/>\r\n <grid orientation=\"orthogonal\" width=\"1\" height=\"1\"/>\r\n <tile id=\"0\">\r\n  <image source=\"build1.png\" width=\"437\" height=\"425\"/>\r\n </tile>\r\n <tile id=\"1\">\r\n  <image source=\"build2.png\" width=\"435\" height=\"327\"/>\r\n </tile>\r\n <tile id=\"2\">\r\n  <image source=\"hexin.png\" width=\"439\" height=\"446\"/>\r\n </tile>\r\n</tileset>\r\n"]], 0, 0, [], [], []], [[{"name": "mapbg_008", "rect": {"x": 0, "y": 0, "width": 1117, "height": 152}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1117, "height": 152}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-558.5, -76, 0, 558.5, -76, 0, -558.5, 76, 0, 558.5, 76, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 152, 1117, 152, 0, 0, 1117, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -558.5, "y": -76, "z": 0}, "maxPos": {"x": 558.5, "y": 76, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [7], [170]], [[[37, "gvg_ghg_grid"], [28, "gvg_ghg_grid", 33554432, [-4, -5, -6, -7, -8, -9, -10, -11, -12], [[2, -2, [0, "5eVKgGkQJGTKK7nnMFiqcV"], [5, 139.1, 163.6]], [34, false, 640, 1280, -3, [0, "efd63sxOpGQ6J6JglfIuRR"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [6, "layout_dot", 33554432, 1, [-15, -16, -17], [[2, -13, [0, "2eT0oKNZlNf7Ojazkt6Jdx"], [5, 57, 20]], [58, 1, 1, 3, -14, [0, "6acNq2aHBCV7mxAO4rfCVE"]]], [1, "eeUkcSHF9J9YvfFH8+JZrE", null, null, null, 1, 0], [1, 0, -37.606, 0]], [19, "fightPro", 33554432, [-22, -23], [[2, -18, [0, "d1snu4JjhNsoa8OVhwR4pJ"], [5, 127, 10]], [20, 1, 0, -19, [0, "c5aJWL+dtIEoBRSGHgtBjs"], 15], [39, 125, 0.4, -21, [0, "86DUKdHINOHZ/Eu0ZElMqV"], -20]], [1, "704de4NnNGZr+nsy7jW4sl", null, null, null, 1, 0], [1, 0, -29.588, 0]], [17, "item_head", 33554432, 1, [-26, -27, -28], [[2, -24, [0, "d07UzLTTRHf4IgfD2ws0BV"], [5, 43, 43]], [10, -25, [0, "8cszf2qDJNKLmZfXPI3BaD"]]], [1, "13fAExDjFATYoINBp2Ebye", null, null, null, 1, 0]], [6, "sign", 33554432, 1, [-32], [[5, -29, [0, "b23Imdd5JBvqDH7xJEM7m+"], [5, 107, 123], [0, 0.5, 1]], [3, -30, [0, "49hj8wKPRL35AE6i3qRsaF"], 5], [11, -31, [0, "caa8PuRYFLKr906IfvumI2"], [6, 7, 8]]], [1, "61OxAOQw5G44QmTyegEl1E", null, null, null, 1, 0], [1, 0, 150.983, 0]], [19, "fight", 33554432, [-34, 3, -35], [[2, -33, [0, "37ZwabXeNFoJxs1iialuYn"], [5, 190, 83.60000000000002]]], [1, "48YJp2oHpJ652ahHPlCFh6", null, null, null, 1, 0], [1, 0, -53.486, 0]], [26, "layout_btn", false, 33554432, 6, [-38, -39], [[2, -36, [0, "1adfna1b5HOIi4Wz/hQSx5"], [5, 177, 100]], [51, 1, 1, -37, [0, "b3tl2+pVdNvbKfgA0a70ZD"]]], [1, "55EBhKA71Ic5aQl+WsXWLV", null, null, null, 1, 0]], [8, "img_ghg_map_di1", 33554432, 1, [[2, -40, [0, "0asqbp709P/ZaxUg6qFInm"], [5, 169, 136]], [3, -41, [0, "dcycM1EdZNdLvBmNO43KYe"], 0], [11, -42, [0, "3aQsRUqzNEFqaEetRhvcY3"], [1, 2]]], [1, "b3kmVwUUBGyp+JcKypa50W", null, null, null, 1, 0]], [43, "car", 33554432, 1, [-45], [[5, -43, [0, "f23LppOT9PqYnLwK8buaa3"], [5, 178.00001525878906, 171], [0, 0.5672357051518725, 0.026550025270696272]], [24, "default", "idle", false, 0, -44, [0, "beDtxvaaZK+bzmewHUIzwU"], 4]], [1, "53fKb/rGFEDqs6KSh9bhCg", null, null, null, 1, 0], [1, 0, -47.972, 0], [1, 0.7, 0.7, 1]], [6, "layout_head", 33554432, 1, [-48], [[2, -46, [0, "95d4aHFZlPOoPSHAS0pqDo"], [5, 126.75, 48]], [50, 1, 3, true, -47, [0, "f4hhp/Y3FBVpiH+xaRl+xf"]]], [1, "d4hMZafWhKBr9kSeQY0hPE", null, null, null, 1, 0], [1, 0, -6.598, 0]], [17, "mapitem", 33554432, 1, [6], [[2, -49, [0, "37ASlOlpNJXaE3lsd7gN0Z"], [5, 200, 200]], [10, -50, [0, "bdq6Sul1pKVKPzBE7O87AQ"]]], [1, "93ay3636RNm5RU5zC0oNS5", null, null, null, 1, 0]], [16, "lay_xx", false, 33554432, 6, [-53], [[2, -51, [0, "34G6CbGQxPUJKoqr6NH9fL"], [5, 151, 33]], [20, 1, 0, -52, [0, "24Azn5zolBa6UPkYRZDLWV"], 12]], [1, "0c1BwGbt9KNYUSOl6/qBZB", null, null, null, 1, 0], [1, 0, 2.185, 0]], [38, "Bar", 33554432, 3, [[[5, -54, [0, "6evptFEddLlohIqapuF5Ux"], [5, 50, 8], [0, 0, 0.5]], -55, [11, -56, [0, "5eDV53T5tDfLGQ1DSXnDAl"], [13, 14]]], 4, 1, 4], [1, "b41qLLBGhJJ45mFM9v9S7c", null, null, null, 1, 0], [1, -62.5, 0, 0]], [6, "btn_ty_lq_1", 33554432, 7, [-59], [[2, -57, [0, "08lBcFRzxOKpLf0BjpvDHv"], [5, 89, 91]], [3, -58, [0, "14d/8SEIpDm4b/OLdCQl+S"], 16]], [1, "39p+o5wJtCdZAr+CPpz027", null, null, null, 1, 0], [1, -44, 0, 0]], [9, "txt_xq", 33554432, 14, [[2, -60, [0, "37iZt4OJpPwr0jKh0i890i"], [5, 94, 63.96]], [21, "详情", 44, 44, 46, true, true, 3, -61, [0, "4eY79nJqBMFo2P8pQVIXNL"], [4, 4279388555]], [12, "sdsl_58", -62, [0, "0a3KhUnFdIgqCjsbJV3T1K"]]], [1, "bb81SWyz5Alr5WWZhMV/hd", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [6, "btn_ty_lq_2", 33554432, 7, [-65], [[2, -63, [0, "2eFAWWTPVLbYI9LEUg42WZ"], [5, 88, 90]], [3, -64, [0, "70UibEADFC7ouTC/mvYZ7v"], 17]], [1, "8evyMAjShLTbXMTTmt5vUr", null, null, null, 1, 0], [1, 44.5, 0, 0]], [9, "txt_xq", 33554432, 16, [[2, -66, [0, "75qSyNrPtA1a8rnu8slkvb"], [5, 94, 63.96]], [21, "前往", 44, 44, 46, true, true, 3, -67, [0, "adrRwMN+JExK5kz9kGWQzK"], [4, 4282867204]], [12, "task_3", -68, [0, "63rO5OdpRHZpg3OWkhlehd"]]], [1, "614EOuX7FFhbchfybm4l6d", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [8, "img_yuan_di1", 33554432, 4, [[2, -69, [0, "03lSlXeaRBAKeG1vn6eFR2"], [5, 43, 43]], [3, -70, [0, "e0XvOGiZBCKpGmNKCWDCn1"], 19], [11, -71, [0, "ffsgTqhc9G4Zrs5VPnPVgU"], [20, 21]]], [1, "0addPLBkBDt4sjPdVz3fDJ", null, null, null, 1, 0]], [8, "img_yuan_quan1", 33554432, 4, [[2, -72, [0, "34oRypxjVIxZLtW+yaxGXg"], [5, 47, 47]], [3, -73, [0, "c2ElSbH1RHtqixwmFCXjiV"], 22], [11, -74, [0, "21ar9vR4JBrqiSgCBfbUpd"], [23, 24]]], [1, "fd2YMZROlFVYsbHBjGbgyj", null, null, null, 1, 0]], [25, "img_ghg_map_di3", false, 33554432, 1, [[2, -75, [0, "f9H5qM7pRBioSYrmJCBlDh"], [5, 175, 136]], [18, 0, -76, [0, "537ouba1RKW4Ow5MOILe+q"], 3]], [1, "beQnFZ9uVKRK0lxAGJx8jP", null, null, null, 1, 0]], [4, "txt_num", 33554432, 5, [[2, -77, [0, "3dzSVcoY5I66mamKjHXWOi"], [5, 6, 76.56]], [35, "", 52, 52, 56, true, true, 3, -78, [0, "61puVejBpM8KgYYioSfhvB"]]], [1, "cfgVTZMp9FKoCE/eIchIsx", null, null, null, 1, 0], [1, 0, -52.817, 0], [1, 0.5, 0.5, 1]], [13, "img_dot1", 33554432, 2, [[2, -79, [0, "043M5I1gdKa7TIOhedsKdd"], [5, 17, 17]], [3, -80, [0, "93wkDKlQlKuonxkPNT2n0E"], 9]], [1, "08HbNqE2ZHipfofNwV6yec", null, null, null, 1, 0], [1, -20, 0, 0]], [8, "img_dot2", 33554432, 2, [[2, -81, [0, "6d1h0jYMdHsZ/I1NMB4N3a"], [5, 17, 17]], [3, -82, [0, "a6H3sVhMJNmZXzEqIiC2UL"], 10]], [1, "2cdV9RVY5BbrpOO7lGSXKh", null, null, null, 1, 0]], [13, "img_dot3", 33554432, 2, [[2, -83, [0, "afcKrSw6NNk7UdUiuBB7ZB"], [5, 17, 17]], [3, -84, [0, "caR0Jg3tdPVrIZtk+T9MQg"], 11]], [1, "8fDAZ+KtxH+7DVGvkqghtO", null, null, null, 1, 0], [1, 20, 0, 0]], [4, "ico_head1", 33554432, 10, [[2, -85, [0, "4cafdPXG5PHayTeARVHRwk"], [5, 115, 116]], [10, -86, [0, "6eePtx4wtJPIeFPw/5JrbS"]]], [1, "985ggJk45GUL3nSIi+z+el", null, null, null, 1, 0], [1, -43.25, 0, 0], [1, 0.35, 0.35, 1]], [9, "sl", 33554432, 12, [[2, -87, [0, "01y7pQ63RDe7H/MN5TIfmn"], [5, 290.0625, 53.88]], [41, "[4000服]公会名称", 36, 36, 38, true, true, 3, -88, [0, "6bgSZXN+pLhJxtLB7xbCmF"], [4, 4290117376]]], [1, "66n1i+p7VIVplFXeHIwR1g", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [9, "hp", 33554432, 3, [[2, -89, [0, "11G0XNEd5J+6siMzGySXzU"], [5, 175.0625, 56.4]], [42, "122/121212", 32, 32, true, true, 3, -90, [0, "3cqu/8jJtK24jIQM/mU92n"]]], [1, "dbKYAevLhOg6Tk1kFlMtDW", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [23, "ani_fight", false, 33554432, 1, [[5, -91, [0, "20PttlMupItIJhMd1QTOeP"], [5, 640.0000610351562, 1280], [0, 0.20191777211019782, 0.47884483337402345]], [24, "default", "animation", false, 0, -92, [0, "756p8q43ZEyYwf5L8HGVwQ"], 18]], [1, "e9Ypf4YzJAEbdz9/l8X1OV", null, null, null, 1, 0], [1, 0, 12.029, 0]], [4, "head", 33554432, 9, [[2, -93, [0, "833gTap5ZJVoUQ6WSa7oRM"], [5, 121, 115]]], [1, "c2gjbO0p5FYKYdqPqrNXHj", null, null, null, 1, 0], [1, -56.546, 15.936, 0], [1, 0.5, 0.5, 1]], [44, 1, 0, 13, [0, "4e/QUKtH9NFbePXTxsi4FE"]], [9, "ico_head", 33554432, 4, [[2, -94, [0, "31rPuC7YNLMYpmGn3QwfH8"], [5, 115, 116]]], [1, "29WwEu/LtHp5q7ITKGcRKu", null, null, null, 1, 0], [1, 0.35, 0.35, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 8, 0, -2, 20, 0, -3, 9, 0, -4, 5, 0, -5, 2, 0, -6, 10, 0, -7, 11, 0, -8, 28, 0, -9, 4, 0, 0, 2, 0, 0, 2, 0, -1, 22, 0, -2, 23, 0, -3, 24, 0, 0, 3, 0, 0, 3, 0, 8, 30, 0, 0, 3, 0, -1, 13, 0, -2, 27, 0, 0, 4, 0, 0, 4, 0, -1, 18, 0, -2, 31, 0, -3, 19, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 21, 0, 0, 6, 0, -1, 12, 0, -3, 7, 0, 0, 7, 0, 0, 7, 0, -1, 14, 0, -2, 16, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, -1, 29, 0, 0, 10, 0, 0, 10, 0, -1, 25, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, -1, 26, 0, 0, 13, 0, -2, 30, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, -1, 15, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, -1, 17, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 31, 0, 9, 1, 3, 3, 6, 6, 3, 11, 94], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30], [1, -1, -2, 1, 2, 1, -1, -2, -3, 1, 1, 1, 1, -1, -2, 1, 1, 1, 2, 1, -1, -2, 1, -1, -2, 1], [8, 8, 21, 30, 20, 4, 4, 15, 16, 1, 1, 1, 6, 0, 7, 5, 17, 18, 11, 2, 2, 13, 3, 3, 14, 0]], [[[89, "smap", "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<map version=\"1.10\" tiledversion=\"1.11.2\" orientation=\"hexagonal\" renderorder=\"left-down\" width=\"51\" height=\"32\" tilewidth=\"176\" tileheight=\"130\" infinite=\"0\" hexsidelength=\"82\" staggeraxis=\"x\" staggerindex=\"odd\" nextlayerid=\"9\" nextobjectid=\"1\">\r\n <tileset firstgid=\"1\" source=\"ground.tsx\"/>\r\n <tileset firstgid=\"46\" source=\"build1.tsx\"/>\r\n <tileset firstgid=\"49\" source=\"road.tsx\"/>\r\n <layer id=\"2\" name=\"dikuai0\" width=\"51\" height=\"32\" visible=\"0\">\r\n  <data encoding=\"csv\">\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,28,25,26,25,26,25,26,25,27,25,26,25,26,25,26,25,28,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,29,25,26,25,26,25,26,25,26,25,26,25,29,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,33,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,30,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,27,25,26,27,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,27,26,25,27,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,27,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,27,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,27,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,33,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,30,26,25,26,25,\r\n25,26,28,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,28,26,25,\r\n25,26,25,26,32,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,31,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,27,25,9,25,27,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,14,25,26,25,26,25,26,25,10,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,25,25,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,27,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,27,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,13,25,26,25,26,25,26,25,11,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,25,12,25,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,33,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,30,26,25,26,25,\r\n25,26,28,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,28,26,25,\r\n25,26,25,26,32,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,31,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,27,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,27,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,27,25,26,27,27,25,26,25,26,25,26,25,26,25,27,25,26,25,26,25,26,25,26,25,27,27,26,25,27,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,32,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,31,25,26,27,26,26,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,45,25,26,25,26,25,26,25,26,25,26,25,45,25,26,25,26,25,26,25,26,26,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,26,26,25,26,25,26,25,26,25,28,26,26,25,26,25,26,25,26,25,26,26,26,25,26,25,28,25,26,25,26,26,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,26,26,25,26,25,26,25,26,25,26,26,26,25,26,25,26,25,27,25,26,26,26,25,26,25,26,25,26,25,26,26,26,25,26,25,26,25,26,25,26,25,26,25\r\n</data>\r\n </layer>\r\n <layer id=\"5\" name=\"black\" width=\"51\" height=\"32\" visible=\"0\" opacity=\"0.5\" tintcolor=\"#5f5255\">\r\n  <data encoding=\"csv\">\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25\r\n</data>\r\n </layer>\r\n <layer id=\"6\" name=\"dikuai\" width=\"51\" height=\"32\">\r\n  <data encoding=\"csv\">\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,28,25,26,25,26,25,26,25,27,25,26,25,26,25,26,25,28,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,25,25,26,25,26,25,26,25,26,25,26,25,29,25,26,25,26,25,26,25,26,25,26,25,29,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,33,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,30,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,27,25,26,27,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,27,26,25,27,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,27,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,0,26,25,26,25,26,27,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,27,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,33,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,30,26,25,26,25,\r\n25,26,28,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,28,26,25,\r\n25,26,25,26,32,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,31,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,27,25,9,25,27,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,14,25,26,25,26,25,26,25,10,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,25,25,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,27,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,27,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,13,25,26,25,26,25,26,25,11,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,25,12,25,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,33,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,30,26,25,26,25,\r\n25,26,28,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,28,26,25,\r\n25,26,25,26,32,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,31,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,27,27,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,27,27,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,27,25,26,27,27,25,26,25,26,25,26,25,26,25,27,25,26,25,26,25,26,25,26,25,27,27,26,25,27,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,27,26,25,32,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,31,25,26,27,26,26,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,26,25,45,25,26,25,26,25,26,25,26,25,26,25,45,25,26,25,26,25,26,25,26,26,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,26,26,25,26,25,26,25,26,25,28,26,26,25,26,25,26,25,26,25,26,26,26,25,26,25,28,25,26,25,26,26,26,25,26,25,26,25,26,25,26,25,26,25,\r\n25,26,25,26,25,26,25,26,26,26,25,26,25,26,25,26,25,26,26,26,25,26,25,26,25,27,25,26,26,26,25,26,25,26,25,26,25,26,26,26,25,26,25,26,25,26,25,26,25,26,25\r\n</data>\r\n </layer>\r\n <layer id=\"7\" name=\"bigBuild\" width=\"51\" height=\"32\">\r\n  <data encoding=\"csv\">\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,47,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,47,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,46,0,0,0,0,0,46,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,46,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,46,0,0,0,0,0,0,0,\r\n0,0,0,47,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,47,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,46,0,0,0,0,0,0,0,0,0,46,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,47,0,0,0,0,0,47,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,46,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,46,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,47,0,0,0,0,48,0,0,0,0,47,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,46,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,46,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,47,0,0,0,0,0,47,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,46,0,0,0,0,0,0,0,0,0,46,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,47,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,47,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,46,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,46,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,46,0,0,0,0,0,46,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,47,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,47,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0\r\n</data>\r\n </layer>\r\n <layer id=\"4\" name=\"mountain\" width=\"51\" height=\"32\" visible=\"0\">\r\n  <data encoding=\"csv\">\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,0,25,25,25,0,0,0,25,25,25,0,0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,0,0,0,0,0,0,25,25,25,0,0,0,25,25,25,0,0,0,0,0,0,0,0,25,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,0,0,0,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,\r\n0,0,0,25,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,25,0,0,0,\r\n0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,25,0,0,0,25,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,0,0,0,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,0,0,0,\r\n0,0,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,0,0,\r\n0,0,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,25,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,25,25,25,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,0,0,0,\r\n0,0,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,0,25,25,25,0,0,0,25,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,0,0,\r\n0,0,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,25,25,0,0,0,25,25,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,25,0,0,0,0,0,25,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,0,0,0,0,0,0,0,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,0,\r\n0,0,25,25,25,0,0,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,25,25,25,0,0,\r\n0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,\r\n0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,0,0,25,0,0,0,0,0,25,0,0,0,0,0,0,0,0,25,25,25,0,0,0,0,0,0,0,0,0,0,0,\r\n0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,0,0,0,0,0,0,0,0,0,0\r\n</data>\r\n </layer>\r\n</map>\r\n", ["destination1.png", "destination2.png", "destination3.png", "destination4.png", "destination5.png", "destination6.png", "destination7.png", "destination8.png", "destination9.png", "destination10.png", "destination11.png", "destination12.png", "destination13.png", "destination14.png", "destination15.png", "destination16.png", "ground1.png", "ground2.png", "obstacle1.png", "obstacle2.png", "start1.png", "start2.png", "start3.png", "start5.png", "start6.png", "start7.png", "start8.png", "start9.png", "start10.png", "start11.png", "start12.png", "start13.png", "start14.png", "start15.png", "start16.png", "start5.png", "start4.png", "build1.png", "build2.png", "hexin.png", "ground1_light.png", "ground2_light.png"], ["ground.tsx", "build1.tsx", "road.tsx"], [0, 1, 2], [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], [[[5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 190], [5, 176, 142], [5, 176, 142], [5, 174, 210], [5, 175, 177], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 171, 247], [5, 402, 369], [5, 353, 302], [5, 395, 415], [5, 176, 142], [5, 176, 142]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, -2, -3, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42], [46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 19, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 19, 83, 84, 85, 86, 87, 88]], [[[37, "toper_db"], [95, "toper_db", 33554432, [-6, -7, -8, -9], [[2, -2, [0, "47Cs6IYElOBK5jyf6MFKqL"], [5, 144, 32]], [109, 1, 4, -3, [0, "46wgLU6vlHt54vDxwmo6H/"]], [60, 3, 0.9, -4, [0, "d3tx27ihxAt4oikqrJ4eqb"]], [18, 0, -5, [0, "3cmYghmZ9L97pT4HhVk6a3"], 2]], [1, "79BcNar8tDmZucBHLjoa8/", null, null, null, -1, 0], [1, 0.9, 0.9, 1]], [13, "btn_jia_2", 33554432, 1, [[2, -10, [0, "74WxTEIhlEzqTwSdOqG0P2"], [5, 42, 43]], [45, 1, -11, [0, "ecpmZeUKhPrph4/wHxD6pE"], 1], [115, false, 3, 0.9, -13, [0, "58mgakwUhCV5gHlf0RMKZt"], [4, 4292269782], -12]], [1, "31HfnZx/lElLSw+YwYGJuK", null, null, null, 1, 0], [1, 54.608, 0, 0]], [6, "icon_token_di2", 33554432, 1, [-16], [[2, -14, [0, "1eaWQdYGNPAIayzca+UthR"], [5, 47, 48]], [10, -15, [0, "cd8+z3sDJLUbHcxkdJArBA"]]], [1, "88d8E6w2pChYKlOW8Fov+D", null, null, null, 1, 0], [1, -50.967, 0, 0]], [25, "icon_token_di1", false, 33554432, 1, [[2, -17, [0, "dagnivt9BEI5E+Wr//w+Hf"], [5, 144, 32]], [18, 0, -18, [0, "acoYAJMeZKn6M0p1WlLrPa"], 0]], [1, "02cZFiQMxHl4L3fDAeg6dD", null, null, null, 1, 0]], [4, "txt_db_sl1", 33554432, 1, [[2, -19, [0, "fdHAuzPnZL+bpQbpq7029Y"], [5, 140, 63.96]], [133, "", 41, 46, 2, true, true, 3, -20, [0, "d8dB/nSJZInrwALfM18oj4"], [4, 4278453288]]], [1, "baUPMRD2BJ4Ym8f0B9xs83", null, null, null, 1, 0], [1, -0.93, 0, 0], [1, 0.5, 0.5, 1]], [8, "token", 33554432, 3, [[2, -21, [0, "e3vxnFD/ZMfIXJKwhVkhf+"], [5, 34, 35]], [10, -22, [0, "0c2CkigdVJUrbvLAPm9ewG"]]], [1, "93o066nNhDMILDWL/GTgh9", null, null, null, 1, 0]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 3, 0, -4, 2, 0, 0, 2, 0, 0, 2, 0, 5, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, -1, 6, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 9, 1, 22], [0, 0, 0], [1, 1, 1], [89, 171, 89]], [[[37, "gvg_ghg_monster"], [28, "gvg_ghg_monster", 33554432, [-4, -5, -6, -7], [[2, -2, [0, "5eVKgGkQJGTKK7nnMFiqcV"], [5, 139.1, 163.6]], [34, false, 640, 1280, -3, [0, "efd63sxOpGQ6J6JglfIuRR"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [6, "fightPro", 33554432, 1, [-12, -13], [[2, -8, [0, "d1snu4JjhNsoa8OVhwR4pJ"], [5, 127, 10]], [20, 1, 0, -9, [0, "c5aJWL+dtIEoBRSGHgtBjs"], 5], [39, 125, 0.4, -11, [0, "86DUKdHINOHZ/Eu0ZElMqV"], -10]], [1, "704de4NnNGZr+nsy7jW4sl", null, null, null, 1, 0], [1, 0, -37.074, 0]], [38, "Bar", 33554432, 2, [[[5, -14, [0, "6evptFEddLlohIqapuF5Ux"], [5, 50, 8], [0, 0, 0.5]], -15, [11, -16, [0, "5eDV53T5tDfLGQ1DSXnDAl"], [3, 4]]], 4, 1, 4], [1, "b41qLLBGhJJ45mFM9v9S7c", null, null, null, 1, 0], [1, -62.5, 0, 0]], [4, "tree", 33554432, 1, [[5, -17, [0, "f23LppOT9PqYnLwK8buaa3"], [5, 851, 527], [0, 0.5069520190515754, 0.5956834362399194]], [24, "default", "idle", false, 0, -18, [0, "9eaFREhYRBhqfGJMxs1wcb"], 0]], [1, "53fKb/rGFEDqs6KSh9bhCg", null, null, null, 1, 0], [1, 0, 29.028, 0], [1, 0.8, 0.8, 1]], [4, "rabbit", 33554432, 1, [[5, -19, [0, "75pVnqHfNDiYcaFKgJ4DVY"], [5, 160.87655639648438, 75.91021728515625], [0, 0.5509215128680972, 0.2748245575948792]], [24, "default", "hit", false, 0, -20, [0, "06XVeNC/dCdo2nFMmVeiEw"], 1]], [1, "3baAbtv0hIvaih2zYS2FAA", null, null, null, 1, 0], [1, 0, -2.972, 0], [1, 0.8, 0.8, 1]], [23, "ani_fight", false, 33554432, 1, [[5, -21, [0, "baR8bOHP1Bo7NBKTgBEnrd"], [5, 640.0000610351562, 1280], [0, 0.20191777211019782, 0.47884483337402345]], [24, "default", "animation", false, 0, -22, [0, "00A8X7WDRFe76W4OyaSr2S"], 2]], [1, "79RY/hKl9O5oO1dElfcMSr", null, null, null, 1, 0], [1, 0, 12.029, 0]], [9, "hp", 33554432, 2, [[2, -23, [0, "11G0XNEd5J+6siMzGySXzU"], [5, 175.0625, 56.4]], [42, "122/121212", 32, 32, true, true, 3, -24, [0, "3cqu/8jJtK24jIQM/mU92n"]]], [1, "dbKYAevLhOg6Tk1kFlMtDW", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [44, 1, 0, 3, [0, "4e/QUKtH9NFbePXTxsi4FE"]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 2, 0, 0, 2, 0, 0, 2, 0, 8, 8, 0, 0, 2, 0, -1, 3, 0, -2, 7, 0, 0, 3, 0, -2, 8, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 9, 1, 24], [0, 0, 0, 0, 0, 0, 8], [2, 2, 2, -1, -2, 1, 1], [172, 173, 11, 0, 7, 5, 0]], [[{"name": "btn_bw", "rect": {"x": 0, "y": 0, "width": 80, "height": 82}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 80, "height": 82}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-40, -41, 0, 40, -41, 0, -40, 41, 0, 40, 41, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 82, 80, 82, 0, 0, 80, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -40, "y": -41, "z": 0}, "maxPos": {"x": 40, "y": 41, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [7], [174]], [[[37, "gvg_ghg_role"], [28, "gvg_ghg_role", 33554432, [-4, -5, -6, -7, -8, -9, -10, -11], [[2, -2, [0, "5eVKgGkQJGTKK7nnMFiqcV"], [5, 139.1, 163.6]], [34, false, 640, 1280, -3, [0, "efd63sxOpGQ6J6JglfIuRR"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [43, "item_head", 33554432, 1, [-14, -15, -16], [[2, -12, [0, "aaHg0X26NMn4IYtlwfseFb"], [5, 43, 43]], [10, -13, [0, "6cCtklaEhBap1GgQ5kvkUF"]]], [1, "abqjt/NEhM/pTxxMuFQDKm", null, null, null, 1, 0], [1, -69.912, 37.01200000000006, 0], [1, 0.8, 0.8, 1]], [19, "fightPro", 33554432, [-21, -22], [[2, -17, [0, "d1snu4JjhNsoa8OVhwR4pJ"], [5, 127, 10]], [20, 1, 0, -18, [0, "c5aJWL+dtIEoBRSGHgtBjs"], 13], [39, 125, 0.4, -20, [0, "86DUKdHINOHZ/Eu0ZElMqV"], -19]], [1, "704de4NnNGZr+nsy7jW4sl", null, null, null, 1, 0], [1, 0, -29.588, 0]], [16, "layout_btn", false, 33554432, 1, [-25, -26], [[2, -23, [0, "4c5GGb7+tJKoYKON5ymL1a"], [5, 177, 100]], [51, 1, 1, -24, [0, "f6ok+lJ1pLbKtQp56drRkS"]]], [1, "ebbRvVQqBC2bW2xwHFZrag", null, null, null, 1, 0], [1, 0, -64, 0]], [8, "img_ghg_map_di1", 33554432, 1, [[2, -27, [0, "0asqbp709P/ZaxUg6qFInm"], [5, 169, 136]], [3, -28, [0, "dcycM1EdZNdLvBmNO43KYe"], 0], [11, -29, [0, "3aQsRUqzNEFqaEetRhvcY3"], [1, 2]]], [1, "b3kmVwUUBGyp+JcKypa50W", null, null, null, 1, 0]], [8, "img_yuan_di1", 33554432, 2, [[2, -30, [0, "68K/I+PwRBZ5fdUfKwU8Fx"], [5, 43, 43]], [3, -31, [0, "44i0ayQAxNdLk3wftwG4yb"], 4], [11, -32, [0, "29Nz6YB7tDzIBx/SR9iSGh"], [5, 6]]], [1, "99P9ajikNNSJ6IXl/yMvgX", null, null, null, 1, 0]], [8, "img_yuan_quan1", 33554432, 2, [[2, -33, [0, "efptO+6cFKD5qUnzcH1Smw"], [5, 47, 47]], [3, -34, [0, "4enq9Wfn5HJ5RFxYd0jM9V"], 7], [11, -35, [0, "eczQzsTKdPjZwRBtfnCOy/"], [8, 9]]], [1, "08/uKPu0dNf5gWxn6fEogb", null, null, null, 1, 0]], [17, "mapitem", 33554432, 1, [-38], [[2, -36, [0, "37ASlOlpNJXaE3lsd7gN0Z"], [5, 200, 200]], [10, -37, [0, "bdq6Sul1pKVKPzBE7O87AQ"]]], [1, "93ay3636RNm5RU5zC0oNS5", null, null, null, 1, 0]], [6, "fight", 33554432, 8, [-40, 3], [[2, -39, [0, "37ZwabXeNFoJxs1iialuYn"], [5, 190, 83.60000000000002]]], [1, "48YJp2oHpJ652ahHPlCFh6", null, null, null, 1, 0], [1, 0, -53.486, 0]], [6, "lay_xx", 33554432, 9, [-43], [[2, -41, [0, "34G6CbGQxPUJKoqr6NH9fL"], [5, 151, 33]], [20, 1, 0, -42, [0, "24Azn5zolBa6UPkYRZDLWV"], 10]], [1, "0c1BwGbt9KNYUSOl6/qBZB", null, null, null, 1, 0], [1, 0, 2.185, 0]], [38, "Bar", 33554432, 3, [[[5, -44, [0, "6evptFEddLlohIqapuF5Ux"], [5, 50, 8], [0, 0, 0.5]], -45, [11, -46, [0, "5eDV53T5tDfLGQ1DSXnDAl"], [11, 12]]], 4, 1, 4], [1, "b41qLLBGhJJ45mFM9v9S7c", null, null, null, 1, 0], [1, -62.5, 0, 0]], [6, "layout_head", 33554432, 1, [-49], [[2, -47, [0, "95d4aHFZlPOoPSHAS0pqDo"], [5, 126.75, 48]], [50, 1, 3, true, -48, [0, "f4hhp/Y3FBVpiH+xaRl+xf"]]], [1, "d4hMZafWhKBr9kSeQY0hPE", null, null, null, 1, 0], [1, 0, -6.598, 0]], [6, "btn_ty_lq_1", 33554432, 4, [-52], [[2, -50, [0, "5brOe5k3lHMbKB5qGP4wBi"], [5, 89, 91]], [3, -51, [0, "27Vu2A6OJB050D4zsp3vy9"], 15]], [1, "11kl0iSQZAW6XlP3cSaKmn", null, null, null, 1, 0], [1, -44, 0, 0]], [9, "txt_xq", 33554432, 13, [[2, -53, [0, "32YeP/K85DeKzaWkVtEMyC"], [5, 94, 63.96]], [21, "详情", 44, 44, 46, true, true, 3, -54, [0, "c54JcmNc1KsrzqwPSmJexo"], [4, 4279388555]], [12, "sdsl_58", -55, [0, "bcPLUmwg9Mwrcfe0LW8ZBv"]]], [1, "69Ce7LHTFP3JG2Q57VMgPB", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [6, "btn_ty_lq_2", 33554432, 4, [-58], [[2, -56, [0, "9bJ9oei5dLlJKz85SUM5o0"], [5, 88, 90]], [3, -57, [0, "17LY0LVWdDeYLeAMDT3guG"], 16]], [1, "7cEhOKDxBIGYJVBTrqw6uK", null, null, null, 1, 0], [1, 44.5, 0, 0]], [9, "txt_xq", 33554432, 15, [[2, -59, [0, "445rNzvpNOpagPQ9i2m99X"], [5, 94, 63.96]], [21, "前往", 44, 44, 46, true, true, 3, -60, [0, "faWXTN1QZBvKyiUeYJeAas"], [4, 4282867204]], [12, "task_3", -61, [0, "4968xO1GZEuohprkIlalVI"]]], [1, "78Uo0rSHpNp6Wxu9iKMlY4", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [4, "car", 33554432, 1, [[5, -62, [0, "f23LppOT9PqYnLwK8buaa3"], [5, 178.00001525878906, 171], [0, 0.5672357051518725, 0.026550025270696272]], [24, "default", "idle", false, 0, -63, [0, "c1zehECaFDXquJPYMQ5uiM"], 3]], [1, "53fKb/rGFEDqs6KSh9bhCg", null, null, null, 1, 0], [1, 0, -47.972, 0], [1, 0.8, 0.8, 1]], [9, "sl", 33554432, 10, [[2, -64, [0, "01y7pQ63RDe7H/MN5TIfmn"], [5, 290.0625, 53.88]], [41, "[4000服]公会名称", 36, 36, 38, true, true, 3, -65, [0, "6bgSZXN+pLhJxtLB7xbCmF"], [4, 4290117376]]], [1, "66n1i+p7VIVplFXeHIwR1g", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [9, "hp", 33554432, 3, [[2, -66, [0, "11G0XNEd5J+6siMzGySXzU"], [5, 175.0625, 56.4]], [42, "122/121212", 32, 32, true, true, 3, -67, [0, "3cqu/8jJtK24jIQM/mU92n"]]], [1, "dbKYAevLhOg6Tk1kFlMtDW", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [4, "ico_head1", 33554432, 12, [[2, -68, [0, "4cafdPXG5PHayTeARVHRwk"], [5, 115, 116]], [10, -69, [0, "6eePtx4wtJPIeFPw/5JrbS"]]], [1, "985ggJk45GUL3nSIi+z+el", null, null, null, 1, 0], [1, -43.25, 0, 0], [1, 0.35, 0.35, 1]], [23, "ani_fight", false, 33554432, 1, [[5, -70, [0, "20PttlMupItIJhMd1QTOeP"], [5, 640.0000610351562, 1280], [0, 0.20191777211019782, 0.47884483337402345]], [24, "default", "animation", false, 0, -71, [0, "756p8q43ZEyYwf5L8HGVwQ"], 14]], [1, "e9Ypf4YzJAEbdz9/l8X1OV", null, null, null, 1, 0], [1, 0, 12.029, 0]], [23, "ani_hc", false, 33554432, 1, [[5, -72, [0, "c51zOD+W5AyYc8I3CcTmU9"], [5, 640.0000610351562, 1280], [0, 0.20191777211019782, 0.47884483337402345]], [24, "default", "animation", false, 0, -73, [0, "33e5mPxPhKPKAD0EztRCzz"], 17]], [1, "17Up+wvjZCsK4cLjDjOlZJ", null, null, null, 1, 0], [1, 0, 12.029, 0]], [9, "ico_head", 33554432, 2, [[2, -74, [0, "22AiaprMFHWKc+UoxITBhE"], [5, 115, 116]]], [1, "d1/tHPe7BJJLq7w/o0xU3U", null, null, null, 1, 0], [1, 0.35, 0.35, 1]], [44, 1, 0, 11, [0, "4e/QUKtH9NFbePXTxsi4FE"]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 5, 0, -2, 17, 0, -3, 2, 0, -4, 8, 0, -5, 12, 0, -6, 21, 0, -7, 4, 0, -8, 22, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 23, 0, -3, 7, 0, 0, 3, 0, 0, 3, 0, 8, 24, 0, 0, 3, 0, -1, 11, 0, -2, 19, 0, 0, 4, 0, 0, 4, 0, -1, 13, 0, -2, 15, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, -1, 10, 0, 0, 10, 0, 0, 10, 0, -1, 18, 0, 0, 11, 0, -2, 24, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, -1, 20, 0, 0, 13, 0, 0, 13, 0, -1, 14, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, -1, 16, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 9, 1, 3, 3, 9, 74], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24], [1, -1, -2, 2, 1, -1, -2, 1, -1, -2, 1, -1, -2, 1, 2, 1, 1, 2, 1], [8, 8, 21, 20, 2, 2, 13, 3, 3, 14, 6, 0, 7, 5, 11, 17, 18, 45, 0]], [[{"name": "default_radio_button_off", "rect": {"x": 3, "y": 3, "width": 26, "height": 26}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [13, 13, 13, 13], "vertices": {"rawPosition": [-13, -13, 0, 13, -13, 0, -13, 13, 0, 13, 13, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [3, 29, 29, 29, 3, 3, 29, 3], "nuv": [0.09375, 0.09375, 0.90625, 0.09375, 0.09375, 0.90625, 0.90625, 0.90625], "minPos": {"x": -13, "y": -13, "z": 0}, "maxPos": {"x": 13, "y": 13, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [7], [175]], [[[37, "gvg_ghg_guild"], [28, "gvg_ghg_guild", 33554432, [-4], [[2, -2, [0, "5eVKgGkQJGTKK7nnMFiqcV"], [5, 139.1, 163.6]], [34, false, 640, 1280, -3, [0, "efd63sxOpGQ6J6JglfIuRR"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [6, "lay_xx", 33554432, 1, [-7, -8], [[2, -5, [0, "21mFTBIFdM2aarQTQDLVpl"], [5, 151, 30]], [20, 1, 0, -6, [0, "cb3TvJCjFAF5Nge3K6sz+u"], 0]], [1, "45Yie4pzxHgq+xWiWRaOBJ", null, null, null, 1, 0], [1, 0, 2.185, 0]], [54, "jd", false, 33554432, 2, [[2, -9, [0, "e2rkXYEzdBh40MqFtbuteu"], [5, 150, 53.88]], [35, "建筑名称", 36, 36, 38, true, true, 3, -10, [0, "dc/DlvsoxEZIM3wgzC1UBR"]], [12, "gvg_ghg_tip_219", -11, [0, "59mJ9T9EZDf5WkRS2qzhV7"]]], [1, "cc0niReMpN67w5oncXxMd3", null, null, null, 1, 0], [1, 0, 11.03, 0], [1, 0.5, 0.5, 1]], [9, "ghname", 33554432, 2, [[2, -12, [0, "f2DNFdy4FGYbpRF0hpkb7S"], [5, 254.0625, 53.88]], [41, "[1201]公会名称", 36, 36, 38, true, true, 3, -13, [0, "90MHat4TBEvr3QYNvCStGT"], [4, 4282113279]]], [1, "86PIGHeslDc5CA90nUqCl2", null, null, null, 1, 0], [1, 0.5, 0.5, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 9, 1, 13], [0], [1], [6]]]]