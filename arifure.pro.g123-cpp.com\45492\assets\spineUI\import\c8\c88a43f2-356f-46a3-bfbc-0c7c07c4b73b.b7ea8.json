[1, ["fdnnwuvh5OCqGgqPk2AZQd@6c48a"], 0, [["sp.SkeletonData", ["_name", "_native", "_atlasText", "textureNames", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "ani_mh_p2_3", ".bin", "\nani_mh_p2_3.png\nsize: 1572,1572\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n4mh2_38_1\n  rotate: false\n  xy: 2, 973\n  size: 644, 597\n  orig: 644, 597\n  offset: 0, 0\n  index: -1\n4mh2_39\n  rotate: true\n  xy: 1470, 819\n  size: 52, 83\n  orig: 52, 83\n  offset: 0, 0\n  index: -1\n4mh2_40\n  rotate: true\n  xy: 1434, 496\n  size: 41, 79\n  orig: 41, 79\n  offset: 0, 0\n  index: -1\n4mh2_41\n  rotate: false\n  xy: 1440, 10\n  size: 68, 64\n  orig: 68, 64\n  offset: 0, 0\n  index: -1\n4mh2_42\n  rotate: false\n  xy: 550, 350\n  size: 78, 27\n  orig: 78, 27\n  offset: 0, 0\n  index: -1\n4mh2_43\n  rotate: true\n  xy: 1457, 239\n  size: 59, 39\n  orig: 59, 39\n  offset: 0, 0\n  index: -1\n4mh2_44\n  rotate: true\n  xy: 1470, 873\n  size: 91, 100\n  orig: 91, 100\n  offset: 0, 0\n  index: -1\n4mh2_45\n  rotate: true\n  xy: 1541, 1293\n  size: 66, 29\n  orig: 66, 29\n  offset: 0, 0\n  index: -1\n4mh2_46\n  rotate: false\n  xy: 1330, 507\n  size: 58, 28\n  orig: 58, 28\n  offset: 0, 0\n  index: -1\n4mh2_47\n  rotate: false\n  xy: 1515, 499\n  size: 37, 37\n  orig: 37, 37\n  offset: 0, 0\n  index: -1\n4mh2_48\n  rotate: true\n  xy: 754, 3\n  size: 18, 46\n  orig: 18, 46\n  offset: 0, 0\n  index: -1\n4mh2_49\n  rotate: false\n  xy: 1457, 300\n  size: 32, 61\n  orig: 32, 61\n  offset: 0, 0\n  index: -1\n4mh2_50\n  rotate: false\n  xy: 550, 101\n  size: 292, 239\n  orig: 324, 250\n  offset: 7, 0\n  index: -1\n4mh2_51\n  rotate: true\n  xy: 1513, 375\n  size: 64, 49\n  orig: 64, 49\n  offset: 0, 0\n  index: -1\n4mh2_52\n  rotate: true\n  xy: 1499, 441\n  size: 53, 64\n  orig: 53, 64\n  offset: 0, 0\n  index: -1\n4mh2_53\n  rotate: false\n  xy: 1532, 588\n  size: 38, 63\n  orig: 38, 63\n  offset: 0, 0\n  index: -1\n4mh2_54\n  rotate: true\n  xy: 1390, 505\n  size: 30, 33\n  orig: 33, 34\n  offset: 2, 1\n  index: -1\n4mh2_55\n  rotate: false\n  xy: 453, 124\n  size: 79, 59\n  orig: 79, 59\n  offset: 0, 0\n  index: -1\n4mh2_56\n  rotate: true\n  xy: 1478, 987\n  size: 89, 86\n  orig: 89, 86\n  offset: 0, 0\n  index: -1\n4mh2_57\n  rotate: true\n  xy: 1478, 1078\n  size: 125, 87\n  orig: 125, 87\n  offset: 0, 0\n  index: -1\n4mh2_58\n  rotate: true\n  xy: 910, 6\n  size: 42, 87\n  orig: 43, 87\n  offset: 1, 0\n  index: -1\n4mh2_59\n  rotate: false\n  xy: 1541, 1266\n  size: 29, 25\n  orig: 29, 25\n  offset: 0, 0\n  index: -1\n4mh2_60\n  rotate: false\n  xy: 1457, 214\n  size: 23, 23\n  orig: 23, 23\n  offset: 0, 0\n  index: -1\n4mh2_61\n  rotate: true\n  xy: 534, 164\n  size: 19, 14\n  orig: 21, 16\n  offset: 1, 1\n  index: -1\n4mh2_62\n  rotate: true\n  xy: 1003, 2\n  size: 97, 111\n  orig: 99, 113\n  offset: 1, 1\n  index: -1\n4mh2_63\n  rotate: false\n  xy: 1478, 966\n  size: 24, 19\n  orig: 24, 19\n  offset: 0, 0\n  index: -1\n4mh2_64\n  rotate: true\n  xy: 453, 101\n  size: 21, 29\n  orig: 21, 29\n  offset: 0, 0\n  index: -1\n4mh2_65\n  rotate: true\n  xy: 1279, 163\n  size: 182, 176\n  orig: 182, 176\n  offset: 0, 0\n  index: -1\n4mh2_65_1\n  rotate: false\n  xy: 1032, 347\n  size: 212, 155\n  orig: 212, 155\n  offset: 0, 0\n  index: -1\n4mh2_66\n  rotate: false\n  xy: 1116, 11\n  size: 171, 125\n  orig: 171, 125\n  offset: 0, 0\n  index: -1\n4mh2_66_1\n  rotate: true\n  xy: 1365, 651\n  size: 148, 137\n  orig: 148, 137\n  offset: 0, 0\n  index: -1\n4mh2_67\n  rotate: false\n  xy: 1103, 658\n  size: 260, 141\n  orig: 260, 141\n  offset: 0, 0\n  index: -1\n4mh2_67_1\n  rotate: false\n  xy: 1003, 101\n  size: 98, 239\n  orig: 98, 239\n  offset: 0, 0\n  index: -1\n4mh2_68\n  rotate: false\n  xy: 1469, 366\n  size: 42, 73\n  orig: 42, 73\n  offset: 0, 0\n  index: -1\n4mh2_69\n  rotate: false\n  xy: 1130, 966\n  size: 346, 237\n  orig: 346, 237\n  offset: 0, 0\n  index: -1\n4mh2_69_1\n  rotate: false\n  xy: 648, 1166\n  size: 469, 404\n  orig: 469, 404\n  offset: 0, 0\n  index: -1\n4mh2_70\n  rotate: false\n  xy: 1289, 16\n  size: 149, 145\n  orig: 149, 145\n  offset: 0, 0\n  index: -1\n4mh2_71\n  rotate: false\n  xy: 1103, 138\n  size: 174, 207\n  orig: 174, 207\n  offset: 0, 0\n  index: -1\n4mh2_72\n  rotate: false\n  xy: 1246, 347\n  size: 174, 155\n  orig: 174, 155\n  offset: 0, 0\n  index: -1\n4mh2_73\n  rotate: true\n  xy: 1532, 538\n  size: 48, 37\n  orig: 50, 39\n  offset: 1, 1\n  index: -1\n4mh2_74\n  rotate: false\n  xy: 1330, 537\n  size: 102, 112\n  orig: 104, 114\n  offset: 1, 1\n  index: -1\n4mh2_75\n  rotate: false\n  xy: 1434, 539\n  size: 96, 110\n  orig: 98, 112\n  offset: 1, 1\n  index: -1\n4mh2_76\n  rotate: false\n  xy: 754, 23\n  size: 88, 76\n  orig: 88, 77\n  offset: 0, 1\n  index: -1\n4mh2_77\n  rotate: false\n  xy: 844, 2\n  size: 64, 46\n  orig: 66, 48\n  offset: 1, 1\n  index: -1\n4mh2_78\n  rotate: false\n  xy: 1422, 441\n  size: 75, 53\n  orig: 77, 55\n  offset: 1, 1\n  index: -1\n4mh2_79\n  rotate: false\n  xy: 1504, 653\n  size: 64, 50\n  orig: 66, 52\n  offset: 1, 1\n  index: -1\n4mh2_80\n  rotate: true\n  xy: 1440, 76\n  size: 85, 48\n  orig: 87, 50\n  offset: 1, 1\n  index: -1\n4mh2_81\n  rotate: false\n  xy: 1032, 505\n  size: 47, 43\n  orig: 49, 45\n  offset: 1, 1\n  index: -1\n4mh2_82\n  rotate: true\n  xy: 1422, 363\n  size: 76, 45\n  orig: 78, 47\n  offset: 1, 1\n  index: -1\n4mh2_83\n  rotate: false\n  xy: 1504, 705\n  size: 50, 112\n  orig: 52, 114\n  offset: 1, 1\n  index: -1\n4mh2_84\n  rotate: false\n  xy: 1541, 1229\n  size: 25, 35\n  orig: 27, 37\n  offset: 1, 1\n  index: -1\n4mh2_85\n  rotate: true\n  xy: 1086, 504\n  size: 152, 242\n  orig: 154, 244\n  offset: 1, 1\n  index: -1\n4mh2_86_1\n  rotate: false\n  xy: 2, 379\n  size: 634, 592\n  orig: 637, 599\n  offset: 0, 0\n  index: -1\ntx/sdx/sdx_00000\n  rotate: false\n  xy: 638, 342\n  size: 392, 206\n  orig: 640, 360\n  offset: 248, 154\n  index: -1\ntx/sdx/sdx_00001\n  rotate: false\n  xy: 1119, 1361\n  size: 448, 209\n  orig: 640, 360\n  offset: 192, 151\n  index: -1\ntx/sdx/sdx_00002\n  rotate: false\n  xy: 638, 550\n  size: 446, 208\n  orig: 640, 360\n  offset: 194, 152\n  index: -1\ntx/sdx/sdx_00003\n  rotate: false\n  xy: 2, 12\n  size: 449, 171\n  orig: 640, 360\n  offset: 191, 189\n  index: -1\ntx/sdx/sdx_00004\n  rotate: false\n  xy: 638, 760\n  size: 463, 204\n  orig: 640, 360\n  offset: 177, 156\n  index: -1\ntx/sdx/sdx_00005\n  rotate: false\n  xy: 648, 966\n  size: 480, 198\n  orig: 640, 360\n  offset: 160, 162\n  index: -1\ntx/sdx/sdx_00006\n  rotate: true\n  xy: 844, 50\n  size: 290, 157\n  orig: 640, 360\n  offset: 350, 203\n  index: -1\ntx/sdx/sdx_00007\n  rotate: false\n  xy: 1119, 1205\n  size: 420, 154\n  orig: 640, 360\n  offset: 220, 206\n  index: -1\ntx/sdx/sdx_00008\n  rotate: false\n  xy: 453, 2\n  size: 299, 97\n  orig: 640, 360\n  offset: 341, 263\n  index: -1\ntx/sdx/sdx_00009\n  rotate: false\n  xy: 1103, 801\n  size: 365, 163\n  orig: 640, 360\n  offset: 275, 197\n  index: -1\ntx/sdx/sdx_00010\n  rotate: false\n  xy: 2, 185\n  size: 546, 192\n  orig: 640, 360\n  offset: 94, 168\n  index: -1\n", ["ani_mh_p2_3.png"], [0]], -1], 0, 0, [0], [-1], [0]]