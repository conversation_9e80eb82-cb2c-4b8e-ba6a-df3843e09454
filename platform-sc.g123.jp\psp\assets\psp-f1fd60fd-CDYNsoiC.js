import{_ as H}from"./psp-cd6a1b8d-CSw1A9l7.js";const x=t=>typeof t=="string",Se=()=>{let t,e;const r=new Promise((n,s)=>{t=n,e=s});return r.resolve=t,r.reject=e,r},Ut=t=>t==null?"":""+t,Pn=(t,e,r)=>{t.forEach(n=>{e[n]&&(r[n]=e[n])})},Nn=/###/g,Ht=t=>t&&t.indexOf("###")>-1?t.replace(Nn,"."):t,Kt=t=>!t||x(t),ke=(t,e,r)=>{const n=x(e)?e.split("."):e;let s=0;for(;s<n.length-1;){if(Kt(t))return{};const o=Ht(n[s]);!t[o]&&r&&(t[o]=new r),Object.prototype.hasOwnProperty.call(t,o)?t=t[o]:t={},++s}return Kt(t)?{}:{obj:t,k:Ht(n[s])}},Bt=(t,e,r)=>{const{obj:n,k:s}=ke(t,e,Object);if(n!==void 0||e.length===1){n[s]=r;return}let o=e[e.length-1],i=e.slice(0,e.length-1),a=ke(t,i,Object);for(;a.obj===void 0&&i.length;)o=`${i[i.length-1]}.${o}`,i=i.slice(0,i.length-1),a=ke(t,i,Object),a?.obj&&typeof a.obj[`${a.k}.${o}`]<"u"&&(a.obj=void 0);a.obj[`${a.k}.${o}`]=r},En=(t,e,r,n)=>{const{obj:s,k:o}=ke(t,e,Object);s[o]=s[o]||[],s[o].push(r)},Be=(t,e)=>{const{obj:r,k:n}=ke(t,e);if(r&&Object.prototype.hasOwnProperty.call(r,n))return r[n]},$n=(t,e,r)=>{const n=Be(t,r);return n!==void 0?n:Be(e,r)},Ir=(t,e,r)=>{for(const n in e)n!=="__proto__"&&n!=="constructor"&&(n in t?x(t[n])||t[n]instanceof String||x(e[n])||e[n]instanceof String?r&&(t[n]=e[n]):Ir(t[n],e[n],r):t[n]=e[n]);return t},pe=t=>t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var In={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const Tn=t=>x(t)?t.replace(/[&<>"'\/]/g,e=>In[e]):t;class An{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const r=this.regExpMap.get(e);if(r!==void 0)return r;const n=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,n),this.regExpQueue.push(e),n}}const jn=[" ",",","?","!",";"],Fn=new An(20),Mn=(t,e,r)=>{e=e||"",r=r||"";const n=jn.filter(i=>e.indexOf(i)<0&&r.indexOf(i)<0);if(n.length===0)return!0;const s=Fn.getRegExp(`(${n.map(i=>i==="?"?"\\?":i).join("|")})`);let o=!s.test(t);if(!o){const i=t.indexOf(r);i>0&&!s.test(t.substring(0,i))&&(o=!0)}return o},ot=(t,e,r=".")=>{if(!t)return;if(t[e])return Object.prototype.hasOwnProperty.call(t,e)?t[e]:void 0;const n=e.split(r);let s=t;for(let o=0;o<n.length;){if(!s||typeof s!="object")return;let i,a="";for(let l=o;l<n.length;++l)if(l!==o&&(a+=r),a+=n[l],i=s[a],i!==void 0){if(["string","number","boolean"].indexOf(typeof i)>-1&&l<n.length-1)continue;o+=l-o+1;break}s=i}return s},Le=t=>t?.replace("_","-"),Dn={type:"logger",log(t){this.output("log",t)},warn(t){this.output("warn",t)},error(t){this.output("error",t)},output(t,e){console?.[t]?.apply?.(console,e)}};class We{constructor(e,r={}){this.init(e,r)}init(e,r={}){this.prefix=r.prefix||"i18next:",this.logger=e||Dn,this.options=r,this.debug=r.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,r,n,s){return s&&!this.debug?null:(x(e[0])&&(e[0]=`${n}${this.prefix} ${e[0]}`),this.logger[r](e))}create(e){return new We(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new We(this.logger,e)}}var Q=new We;class Ye{constructor(){this.observers={}}on(e,r){return e.split(" ").forEach(n=>{this.observers[n]||(this.observers[n]=new Map);const s=this.observers[n].get(r)||0;this.observers[n].set(r,s+1)}),this}off(e,r){if(this.observers[e]){if(!r){delete this.observers[e];return}this.observers[e].delete(r)}}emit(e,...r){this.observers[e]&&Array.from(this.observers[e].entries()).forEach(([s,o])=>{for(let i=0;i<o;i++)s(...r)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([s,o])=>{for(let i=0;i<o;i++)s.apply(s,[e,...r])})}}class Wt extends Ye{constructor(e,r={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=r,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const r=this.options.ns.indexOf(e);r>-1&&this.options.ns.splice(r,1)}getResource(e,r,n,s={}){const o=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,i=s.ignoreJSONStructure!==void 0?s.ignoreJSONStructure:this.options.ignoreJSONStructure;let a;e.indexOf(".")>-1?a=e.split("."):(a=[e,r],n&&(Array.isArray(n)?a.push(...n):x(n)&&o?a.push(...n.split(o)):a.push(n)));const l=Be(this.data,a);return!l&&!r&&!n&&e.indexOf(".")>-1&&(e=a[0],r=a[1],n=a.slice(2).join(".")),l||!i||!x(n)?l:ot(this.data?.[e]?.[r],n,o)}addResource(e,r,n,s,o={silent:!1}){const i=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator;let a=[e,r];n&&(a=a.concat(i?n.split(i):n)),e.indexOf(".")>-1&&(a=e.split("."),s=r,r=a[1]),this.addNamespaces(r),Bt(this.data,a,s),o.silent||this.emit("added",e,r,n,s)}addResources(e,r,n,s={silent:!1}){for(const o in n)(x(n[o])||Array.isArray(n[o]))&&this.addResource(e,r,o,n[o],{silent:!0});s.silent||this.emit("added",e,r,n)}addResourceBundle(e,r,n,s,o,i={silent:!1,skipCopy:!1}){let a=[e,r];e.indexOf(".")>-1&&(a=e.split("."),s=n,n=r,r=a[1]),this.addNamespaces(r);let l=Be(this.data,a)||{};i.skipCopy||(n=JSON.parse(JSON.stringify(n))),s?Ir(l,n,o):l={...l,...n},Bt(this.data,a,l),i.silent||this.emit("added",e,r,n)}removeResourceBundle(e,r){this.hasResourceBundle(e,r)&&delete this.data[e][r],this.removeNamespaces(r),this.emit("removed",e,r)}hasResourceBundle(e,r){return this.getResource(e,r)!==void 0}getResourceBundle(e,r){return r||(r=this.options.defaultNS),this.getResource(e,r)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const r=this.getDataByLanguage(e);return!!(r&&Object.keys(r)||[]).find(s=>r[s]&&Object.keys(r[s]).length>0)}toJSON(){return this.data}}var Tr={processors:{},addPostProcessor(t){this.processors[t.name]=t},handle(t,e,r,n,s){return t.forEach(o=>{e=this.processors[o]?.process(e,r,n,s)??e}),e}};const Gt={},Jt=t=>!x(t)&&typeof t!="boolean"&&typeof t!="number";class Ge extends Ye{constructor(e,r={}){super(),Pn(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=r,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=Q.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,r={interpolation:{}}){const n={...r};return e==null?!1:this.resolve(e,n)?.res!==void 0}extractFromKey(e,r){let n=r.nsSeparator!==void 0?r.nsSeparator:this.options.nsSeparator;n===void 0&&(n=":");const s=r.keySeparator!==void 0?r.keySeparator:this.options.keySeparator;let o=r.ns||this.options.defaultNS||[];const i=n&&e.indexOf(n)>-1,a=!this.options.userDefinedKeySeparator&&!r.keySeparator&&!this.options.userDefinedNsSeparator&&!r.nsSeparator&&!Mn(e,n,s);if(i&&!a){const l=e.match(this.interpolator.nestingRegexp);if(l&&l.length>0)return{key:e,namespaces:x(o)?[o]:o};const c=e.split(n);(n!==s||n===s&&this.options.ns.indexOf(c[0])>-1)&&(o=c.shift()),e=c.join(s)}return{key:e,namespaces:x(o)?[o]:o}}translate(e,r,n){let s=typeof r=="object"?{...r}:r;if(typeof s!="object"&&this.options.overloadTranslationOptionHandler&&(s=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(s={...s}),s||(s={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const o=s.returnDetails!==void 0?s.returnDetails:this.options.returnDetails,i=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,{key:a,namespaces:l}=this.extractFromKey(e[e.length-1],s),c=l[l.length-1];let d=s.nsSeparator!==void 0?s.nsSeparator:this.options.nsSeparator;d===void 0&&(d=":");const u=s.lng||this.language,p=s.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(u?.toLowerCase()==="cimode")return p?o?{res:`${c}${d}${a}`,usedKey:a,exactUsedKey:a,usedLng:u,usedNS:c,usedParams:this.getUsedParamsDetails(s)}:`${c}${d}${a}`:o?{res:a,usedKey:a,exactUsedKey:a,usedLng:u,usedNS:c,usedParams:this.getUsedParamsDetails(s)}:a;const f=this.resolve(e,s);let h=f?.res;const b=f?.usedKey||a,m=f?.exactUsedKey||a,v=["[object Number]","[object Function]","[object RegExp]"],y=s.joinArrays!==void 0?s.joinArrays:this.options.joinArrays,P=!this.i18nFormat||this.i18nFormat.handleAsObject,w=s.count!==void 0&&!x(s.count),L=Ge.hasDefaultValue(s),k=w?this.pluralResolver.getSuffix(u,s.count,s):"",$=s.ordinal&&w?this.pluralResolver.getSuffix(u,s.count,{ordinal:!1}):"",Y=w&&!s.ordinal&&s.count===0,A=Y&&s[`defaultValue${this.options.pluralSeparator}zero`]||s[`defaultValue${k}`]||s[`defaultValue${$}`]||s.defaultValue;let D=h;P&&!h&&L&&(D=A);const U=Jt(D),G=Object.prototype.toString.apply(D);if(P&&D&&U&&v.indexOf(G)<0&&!(x(y)&&Array.isArray(D))){if(!s.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const V=this.options.returnedObjectHandler?this.options.returnedObjectHandler(b,D,{...s,ns:l}):`key '${a} (${this.language})' returned an object instead of string.`;return o?(f.res=V,f.usedParams=this.getUsedParamsDetails(s),f):V}if(i){const V=Array.isArray(D),C=V?[]:{},B=V?m:b;for(const T in D)if(Object.prototype.hasOwnProperty.call(D,T)){const z=`${B}${i}${T}`;L&&!h?C[T]=this.translate(z,{...s,defaultValue:Jt(A)?A[T]:void 0,joinArrays:!1,ns:l}):C[T]=this.translate(z,{...s,joinArrays:!1,ns:l}),C[T]===z&&(C[T]=D[T])}h=C}}else if(P&&x(y)&&Array.isArray(h))h=h.join(y),h&&(h=this.extendTranslation(h,e,s,n));else{let V=!1,C=!1;!this.isValidLookup(h)&&L&&(V=!0,h=A),this.isValidLookup(h)||(C=!0,h=a);const T=(s.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&C?void 0:h,z=L&&A!==h&&this.options.updateMissing;if(C||V||z){if(this.logger.log(z?"updateKey":"missingKey",u,c,a,z?A:h),i){const N=this.resolve(a,{...s,keySeparator:!1});N&&N.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let R=[];const ne=this.languageUtils.getFallbackCodes(this.options.fallbackLng,s.lng||this.language);if(this.options.saveMissingTo==="fallback"&&ne&&ne[0])for(let N=0;N<ne.length;N++)R.push(ne[N]);else this.options.saveMissingTo==="all"?R=this.languageUtils.toResolveHierarchy(s.lng||this.language):R.push(s.lng||this.language);const F=(N,W,se)=>{const we=L&&se!==h?se:T;this.options.missingKeyHandler?this.options.missingKeyHandler(N,c,W,we,z,s):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(N,c,W,we,z,s),this.emit("missingKey",N,c,W,h)};this.options.saveMissing&&(this.options.saveMissingPlurals&&w?R.forEach(N=>{const W=this.pluralResolver.getSuffixes(N,s);Y&&s[`defaultValue${this.options.pluralSeparator}zero`]&&W.indexOf(`${this.options.pluralSeparator}zero`)<0&&W.push(`${this.options.pluralSeparator}zero`),W.forEach(se=>{F([N],a+se,s[`defaultValue${se}`]||A)})}):F(R,a,A))}h=this.extendTranslation(h,e,s,f,n),C&&h===a&&this.options.appendNamespaceToMissingKey&&(h=`${c}${d}${a}`),(C||V)&&this.options.parseMissingKeyHandler&&(h=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${c}${d}${a}`:a,V?h:void 0,s))}return o?(f.res=h,f.usedParams=this.getUsedParamsDetails(s),f):h}extendTranslation(e,r,n,s,o){if(this.i18nFormat?.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...n},n.lng||this.language||s.usedLng,s.usedNS,s.usedKey,{resolved:s});else if(!n.skipInterpolation){n.interpolation&&this.interpolator.init({...n,interpolation:{...this.options.interpolation,...n.interpolation}});const l=x(e)&&(n?.interpolation?.skipOnVariables!==void 0?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let c;if(l){const u=e.match(this.interpolator.nestingRegexp);c=u&&u.length}let d=n.replace&&!x(n.replace)?n.replace:n;if(this.options.interpolation.defaultVariables&&(d={...this.options.interpolation.defaultVariables,...d}),e=this.interpolator.interpolate(e,d,n.lng||this.language||s.usedLng,n),l){const u=e.match(this.interpolator.nestingRegexp),p=u&&u.length;c<p&&(n.nest=!1)}!n.lng&&s&&s.res&&(n.lng=this.language||s.usedLng),n.nest!==!1&&(e=this.interpolator.nest(e,(...u)=>o?.[0]===u[0]&&!n.context?(this.logger.warn(`It seems you are nesting recursively key: ${u[0]} in key: ${r[0]}`),null):this.translate(...u,r),n)),n.interpolation&&this.interpolator.reset()}const i=n.postProcess||this.options.postProcess,a=x(i)?[i]:i;return e!=null&&a?.length&&n.applyPostProcessor!==!1&&(e=Tr.handle(a,e,r,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...s,usedParams:this.getUsedParamsDetails(n)},...n}:n,this)),e}resolve(e,r={}){let n,s,o,i,a;return x(e)&&(e=[e]),e.forEach(l=>{if(this.isValidLookup(n))return;const c=this.extractFromKey(l,r),d=c.key;s=d;let u=c.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));const p=r.count!==void 0&&!x(r.count),f=p&&!r.ordinal&&r.count===0,h=r.context!==void 0&&(x(r.context)||typeof r.context=="number")&&r.context!=="",b=r.lngs?r.lngs:this.languageUtils.toResolveHierarchy(r.lng||this.language,r.fallbackLng);u.forEach(m=>{this.isValidLookup(n)||(a=m,!Gt[`${b[0]}-${m}`]&&this.utils?.hasLoadedNamespace&&!this.utils?.hasLoadedNamespace(a)&&(Gt[`${b[0]}-${m}`]=!0,this.logger.warn(`key "${s}" for languages "${b.join(", ")}" won't get resolved as namespace "${a}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),b.forEach(v=>{if(this.isValidLookup(n))return;i=v;const y=[d];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(y,d,v,m,r);else{let w;p&&(w=this.pluralResolver.getSuffix(v,r.count,r));const L=`${this.options.pluralSeparator}zero`,k=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(p&&(y.push(d+w),r.ordinal&&w.indexOf(k)===0&&y.push(d+w.replace(k,this.options.pluralSeparator)),f&&y.push(d+L)),h){const $=`${d}${this.options.contextSeparator}${r.context}`;y.push($),p&&(y.push($+w),r.ordinal&&w.indexOf(k)===0&&y.push($+w.replace(k,this.options.pluralSeparator)),f&&y.push($+L))}}let P;for(;P=y.pop();)this.isValidLookup(n)||(o=P,n=this.getResource(v,m,P,r))}))})}),{res:n,usedKey:s,exactUsedKey:o,usedLng:i,usedNS:a}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,r,n,s={}){return this.i18nFormat?.getResource?this.i18nFormat.getResource(e,r,n,s):this.resourceStore.getResource(e,r,n,s)}getUsedParamsDetails(e={}){const r=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],n=e.replace&&!x(e.replace);let s=n?e.replace:e;if(n&&typeof e.count<"u"&&(s.count=e.count),this.options.interpolation.defaultVariables&&(s={...this.options.interpolation.defaultVariables,...s}),!n){s={...s};for(const o of r)delete s[o]}return s}static hasDefaultValue(e){const r="defaultValue";for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&r===n.substring(0,r.length)&&e[n]!==void 0)return!0;return!1}}class qt{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=Q.create("languageUtils")}getScriptPartFromCode(e){if(e=Le(e),!e||e.indexOf("-")<0)return null;const r=e.split("-");return r.length===2||(r.pop(),r[r.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(r.join("-"))}getLanguagePartFromCode(e){if(e=Le(e),!e||e.indexOf("-")<0)return e;const r=e.split("-");return this.formatLanguageCode(r[0])}formatLanguageCode(e){if(x(e)&&e.indexOf("-")>-1){let r;try{r=Intl.getCanonicalLocales(e)[0]}catch{}return r&&this.options.lowerCaseLng&&(r=r.toLowerCase()),r||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let r;return e.forEach(n=>{if(r)return;const s=this.formatLanguageCode(n);(!this.options.supportedLngs||this.isSupportedCode(s))&&(r=s)}),!r&&this.options.supportedLngs&&e.forEach(n=>{if(r)return;const s=this.getScriptPartFromCode(n);if(this.isSupportedCode(s))return r=s;const o=this.getLanguagePartFromCode(n);if(this.isSupportedCode(o))return r=o;r=this.options.supportedLngs.find(i=>{if(i===o)return i;if(!(i.indexOf("-")<0&&o.indexOf("-")<0)&&(i.indexOf("-")>0&&o.indexOf("-")<0&&i.substring(0,i.indexOf("-"))===o||i.indexOf(o)===0&&o.length>1))return i})}),r||(r=this.getFallbackCodes(this.options.fallbackLng)[0]),r}getFallbackCodes(e,r){if(!e)return[];if(typeof e=="function"&&(e=e(r)),x(e)&&(e=[e]),Array.isArray(e))return e;if(!r)return e.default||[];let n=e[r];return n||(n=e[this.getScriptPartFromCode(r)]),n||(n=e[this.formatLanguageCode(r)]),n||(n=e[this.getLanguagePartFromCode(r)]),n||(n=e.default),n||[]}toResolveHierarchy(e,r){const n=this.getFallbackCodes((r===!1?[]:r)||this.options.fallbackLng||[],e),s=[],o=i=>{i&&(this.isSupportedCode(i)?s.push(i):this.logger.warn(`rejecting language code not found in supportedLngs: ${i}`))};return x(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&o(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&o(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&o(this.getLanguagePartFromCode(e))):x(e)&&o(this.formatLanguageCode(e)),n.forEach(i=>{s.indexOf(i)<0&&o(this.formatLanguageCode(i))}),s}}const Yt={zero:0,one:1,two:2,few:3,many:4,other:5},Qt={select:t=>t===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class Vn{constructor(e,r={}){this.languageUtils=e,this.options=r,this.logger=Q.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,r){this.rules[e]=r}clearCache(){this.pluralRulesCache={}}getRule(e,r={}){const n=Le(e==="dev"?"en":e),s=r.ordinal?"ordinal":"cardinal",o=JSON.stringify({cleanedCode:n,type:s});if(o in this.pluralRulesCache)return this.pluralRulesCache[o];let i;try{i=new Intl.PluralRules(n,{type:s})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),Qt;if(!e.match(/-|_/))return Qt;const l=this.languageUtils.getLanguagePartFromCode(e);i=this.getRule(l,r)}return this.pluralRulesCache[o]=i,i}needsPlural(e,r={}){let n=this.getRule(e,r);return n||(n=this.getRule("dev",r)),n?.resolvedOptions().pluralCategories.length>1}getPluralFormsOfKey(e,r,n={}){return this.getSuffixes(e,n).map(s=>`${r}${s}`)}getSuffixes(e,r={}){let n=this.getRule(e,r);return n||(n=this.getRule("dev",r)),n?n.resolvedOptions().pluralCategories.sort((s,o)=>Yt[s]-Yt[o]).map(s=>`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${s}`):[]}getSuffix(e,r,n={}){const s=this.getRule(e,n);return s?`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${s.select(r)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",r,n))}}const Zt=(t,e,r,n=".",s=!0)=>{let o=$n(t,e,r);return!o&&s&&x(r)&&(o=ot(t,r,n),o===void 0&&(o=ot(e,r,n))),o},et=t=>t.replace(/\$/g,"$$$$");class zn{constructor(e={}){this.logger=Q.create("interpolator"),this.options=e,this.format=e?.interpolation?.format||(r=>r),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});const{escape:r,escapeValue:n,useRawValueToEscape:s,prefix:o,prefixEscaped:i,suffix:a,suffixEscaped:l,formatSeparator:c,unescapeSuffix:d,unescapePrefix:u,nestingPrefix:p,nestingPrefixEscaped:f,nestingSuffix:h,nestingSuffixEscaped:b,nestingOptionsSeparator:m,maxReplaces:v,alwaysFormat:y}=e.interpolation;this.escape=r!==void 0?r:Tn,this.escapeValue=n!==void 0?n:!0,this.useRawValueToEscape=s!==void 0?s:!1,this.prefix=o?pe(o):i||"{{",this.suffix=a?pe(a):l||"}}",this.formatSeparator=c||",",this.unescapePrefix=d?"":u||"-",this.unescapeSuffix=this.unescapePrefix?"":d||"",this.nestingPrefix=p?pe(p):f||pe("$t("),this.nestingSuffix=h?pe(h):b||pe(")"),this.nestingOptionsSeparator=m||",",this.maxReplaces=v||1e3,this.alwaysFormat=y!==void 0?y:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(r,n)=>r?.source===n?(r.lastIndex=0,r):new RegExp(n,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,r,n,s){let o,i,a;const l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},c=f=>{if(f.indexOf(this.formatSeparator)<0){const v=Zt(r,l,f,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(v,void 0,n,{...s,...r,interpolationkey:f}):v}const h=f.split(this.formatSeparator),b=h.shift().trim(),m=h.join(this.formatSeparator).trim();return this.format(Zt(r,l,b,this.options.keySeparator,this.options.ignoreJSONStructure),m,n,{...s,...r,interpolationkey:b})};this.resetRegExp();const d=s?.missingInterpolationHandler||this.options.missingInterpolationHandler,u=s?.interpolation?.skipOnVariables!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:f=>et(f)},{regex:this.regexp,safeValue:f=>this.escapeValue?et(this.escape(f)):et(f)}].forEach(f=>{for(a=0;o=f.regex.exec(e);){const h=o[1].trim();if(i=c(h),i===void 0)if(typeof d=="function"){const m=d(e,o,s);i=x(m)?m:""}else if(s&&Object.prototype.hasOwnProperty.call(s,h))i="";else if(u){i=o[0];continue}else this.logger.warn(`missed to pass in variable ${h} for interpolating ${e}`),i="";else!x(i)&&!this.useRawValueToEscape&&(i=Ut(i));const b=f.safeValue(i);if(e=e.replace(o[0],b),u?(f.regex.lastIndex+=i.length,f.regex.lastIndex-=o[0].length):f.regex.lastIndex=0,a++,a>=this.maxReplaces)break}}),e}nest(e,r,n={}){let s,o,i;const a=(l,c)=>{const d=this.nestingOptionsSeparator;if(l.indexOf(d)<0)return l;const u=l.split(new RegExp(`${d}[ ]*{`));let p=`{${u[1]}`;l=u[0],p=this.interpolate(p,i);const f=p.match(/'/g),h=p.match(/"/g);((f?.length??0)%2===0&&!h||h.length%2!==0)&&(p=p.replace(/'/g,'"'));try{i=JSON.parse(p),c&&(i={...c,...i})}catch(b){return this.logger.warn(`failed parsing options string in nesting for key ${l}`,b),`${l}${d}${p}`}return i.defaultValue&&i.defaultValue.indexOf(this.prefix)>-1&&delete i.defaultValue,l};for(;s=this.nestingRegexp.exec(e);){let l=[];i={...n},i=i.replace&&!x(i.replace)?i.replace:i,i.applyPostProcessor=!1,delete i.defaultValue;let c=!1;if(s[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(s[1])){const d=s[1].split(this.formatSeparator).map(u=>u.trim());s[1]=d.shift(),l=d,c=!0}if(o=r(a.call(this,s[1].trim(),i),i),o&&s[0]===e&&!x(o))return o;x(o)||(o=Ut(o)),o||(this.logger.warn(`missed to resolve ${s[1]} for nesting ${e}`),o=""),c&&(o=l.reduce((d,u)=>this.format(d,u,n.lng,{...n,interpolationkey:s[1].trim()}),o.trim())),e=e.replace(s[0],o),this.regexp.lastIndex=0}return e}}const Un=t=>{let e=t.toLowerCase().trim();const r={};if(t.indexOf("(")>-1){const n=t.split("(");e=n[0].toLowerCase().trim();const s=n[1].substring(0,n[1].length-1);e==="currency"&&s.indexOf(":")<0?r.currency||(r.currency=s.trim()):e==="relativetime"&&s.indexOf(":")<0?r.range||(r.range=s.trim()):s.split(";").forEach(i=>{if(i){const[a,...l]=i.split(":"),c=l.join(":").trim().replace(/^'+|'+$/g,""),d=a.trim();r[d]||(r[d]=c),c==="false"&&(r[d]=!1),c==="true"&&(r[d]=!0),isNaN(c)||(r[d]=parseInt(c,10))}})}return{formatName:e,formatOptions:r}},Xt=t=>{const e={};return(r,n,s)=>{let o=s;s&&s.interpolationkey&&s.formatParams&&s.formatParams[s.interpolationkey]&&s[s.interpolationkey]&&(o={...o,[s.interpolationkey]:void 0});const i=n+JSON.stringify(o);let a=e[i];return a||(a=t(Le(n),s),e[i]=a),a(r)}},Hn=t=>(e,r,n)=>t(Le(r),n)(e);class Kn{constructor(e={}){this.logger=Q.create("formatter"),this.options=e,this.init(e)}init(e,r={interpolation:{}}){this.formatSeparator=r.interpolation.formatSeparator||",";const n=r.cacheInBuiltFormats?Xt:Hn;this.formats={number:n((s,o)=>{const i=new Intl.NumberFormat(s,{...o});return a=>i.format(a)}),currency:n((s,o)=>{const i=new Intl.NumberFormat(s,{...o,style:"currency"});return a=>i.format(a)}),datetime:n((s,o)=>{const i=new Intl.DateTimeFormat(s,{...o});return a=>i.format(a)}),relativetime:n((s,o)=>{const i=new Intl.RelativeTimeFormat(s,{...o});return a=>i.format(a,o.range||"day")}),list:n((s,o)=>{const i=new Intl.ListFormat(s,{...o});return a=>i.format(a)})}}add(e,r){this.formats[e.toLowerCase().trim()]=r}addCached(e,r){this.formats[e.toLowerCase().trim()]=Xt(r)}format(e,r,n,s={}){const o=r.split(this.formatSeparator);if(o.length>1&&o[0].indexOf("(")>1&&o[0].indexOf(")")<0&&o.find(a=>a.indexOf(")")>-1)){const a=o.findIndex(l=>l.indexOf(")")>-1);o[0]=[o[0],...o.splice(1,a)].join(this.formatSeparator)}return o.reduce((a,l)=>{const{formatName:c,formatOptions:d}=Un(l);if(this.formats[c]){let u=a;try{const p=s?.formatParams?.[s.interpolationkey]||{},f=p.locale||p.lng||s.locale||s.lng||n;u=this.formats[c](a,f,{...d,...s,...p})}catch(p){this.logger.warn(p)}return u}else this.logger.warn(`there was no format function for ${c}`);return a},e)}}const Bn=(t,e)=>{t.pending[e]!==void 0&&(delete t.pending[e],t.pendingCount--)};class Wn extends Ye{constructor(e,r,n,s={}){super(),this.backend=e,this.store=r,this.services=n,this.languageUtils=n.languageUtils,this.options=s,this.logger=Q.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=s.maxParallelReads||10,this.readingCalls=0,this.maxRetries=s.maxRetries>=0?s.maxRetries:5,this.retryTimeout=s.retryTimeout>=1?s.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(n,s.backend,s)}queueLoad(e,r,n,s){const o={},i={},a={},l={};return e.forEach(c=>{let d=!0;r.forEach(u=>{const p=`${c}|${u}`;!n.reload&&this.store.hasResourceBundle(c,u)?this.state[p]=2:this.state[p]<0||(this.state[p]===1?i[p]===void 0&&(i[p]=!0):(this.state[p]=1,d=!1,i[p]===void 0&&(i[p]=!0),o[p]===void 0&&(o[p]=!0),l[u]===void 0&&(l[u]=!0)))}),d||(a[c]=!0)}),(Object.keys(o).length||Object.keys(i).length)&&this.queue.push({pending:i,pendingCount:Object.keys(i).length,loaded:{},errors:[],callback:s}),{toLoad:Object.keys(o),pending:Object.keys(i),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(l)}}loaded(e,r,n){const s=e.split("|"),o=s[0],i=s[1];r&&this.emit("failedLoading",o,i,r),!r&&n&&this.store.addResourceBundle(o,i,n,void 0,void 0,{skipCopy:!0}),this.state[e]=r?-1:2,r&&n&&(this.state[e]=0);const a={};this.queue.forEach(l=>{En(l.loaded,[o],i),Bn(l,e),r&&l.errors.push(r),l.pendingCount===0&&!l.done&&(Object.keys(l.loaded).forEach(c=>{a[c]||(a[c]={});const d=l.loaded[c];d.length&&d.forEach(u=>{a[c][u]===void 0&&(a[c][u]=!0)})}),l.done=!0,l.errors.length?l.callback(l.errors):l.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(l=>!l.done)}read(e,r,n,s=0,o=this.retryTimeout,i){if(!e.length)return i(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:r,fcName:n,tried:s,wait:o,callback:i});return}this.readingCalls++;const a=(c,d)=>{if(this.readingCalls--,this.waitingReads.length>0){const u=this.waitingReads.shift();this.read(u.lng,u.ns,u.fcName,u.tried,u.wait,u.callback)}if(c&&d&&s<this.maxRetries){setTimeout(()=>{this.read.call(this,e,r,n,s+1,o*2,i)},o);return}i(c,d)},l=this.backend[n].bind(this.backend);if(l.length===2){try{const c=l(e,r);c&&typeof c.then=="function"?c.then(d=>a(null,d)).catch(a):a(null,c)}catch(c){a(c)}return}return l(e,r,a)}prepareLoading(e,r,n={},s){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),s&&s();x(e)&&(e=this.languageUtils.toResolveHierarchy(e)),x(r)&&(r=[r]);const o=this.queueLoad(e,r,n,s);if(!o.toLoad.length)return o.pending.length||s(),null;o.toLoad.forEach(i=>{this.loadOne(i)})}load(e,r,n){this.prepareLoading(e,r,{},n)}reload(e,r,n){this.prepareLoading(e,r,{reload:!0},n)}loadOne(e,r=""){const n=e.split("|"),s=n[0],o=n[1];this.read(s,o,"read",void 0,void 0,(i,a)=>{i&&this.logger.warn(`${r}loading namespace ${o} for language ${s} failed`,i),!i&&a&&this.logger.log(`${r}loaded namespace ${o} for language ${s}`,a),this.loaded(e,i,a)})}saveMissing(e,r,n,s,o,i={},a=()=>{}){if(this.services?.utils?.hasLoadedNamespace&&!this.services?.utils?.hasLoadedNamespace(r)){this.logger.warn(`did not save key "${n}" as the namespace "${r}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(n==null||n==="")){if(this.backend?.create){const l={...i,isUpdate:o},c=this.backend.create.bind(this.backend);if(c.length<6)try{let d;c.length===5?d=c(e,r,n,s,l):d=c(e,r,n,s),d&&typeof d.then=="function"?d.then(u=>a(null,u)).catch(a):a(null,d)}catch(d){a(d)}else c(e,r,n,s,a,l)}!e||!e[0]||this.store.addResource(e[0],r,n,s)}}}const er=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:t=>{let e={};if(typeof t[1]=="object"&&(e=t[1]),x(t[1])&&(e.defaultValue=t[1]),x(t[2])&&(e.tDescription=t[2]),typeof t[2]=="object"||typeof t[3]=="object"){const r=t[3]||t[2];Object.keys(r).forEach(n=>{e[n]=r[n]})}return e},interpolation:{escapeValue:!0,format:t=>t,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),tr=t=>(x(t.ns)&&(t.ns=[t.ns]),x(t.fallbackLng)&&(t.fallbackLng=[t.fallbackLng]),x(t.fallbackNS)&&(t.fallbackNS=[t.fallbackNS]),t.supportedLngs?.indexOf?.("cimode")<0&&(t.supportedLngs=t.supportedLngs.concat(["cimode"])),typeof t.initImmediate=="boolean"&&(t.initAsync=t.initImmediate),t),De=()=>{},Gn=t=>{Object.getOwnPropertyNames(Object.getPrototypeOf(t)).forEach(r=>{typeof t[r]=="function"&&(t[r]=t[r].bind(t))})};class Re extends Ye{constructor(e={},r){if(super(),this.options=tr(e),this.services={},this.logger=Q,this.modules={external:[]},Gn(this),r&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,r),this;setTimeout(()=>{this.init(e,r)},0)}}init(e={},r){this.isInitializing=!0,typeof e=="function"&&(r=e,e={}),e.defaultNS==null&&e.ns&&(x(e.ns)?e.defaultNS=e.ns:e.ns.indexOf("translation")<0&&(e.defaultNS=e.ns[0]));const n=er();this.options={...n,...this.options,...tr(e)},this.options.interpolation={...n.interpolation,...this.options.interpolation},e.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=e.keySeparator),e.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=e.nsSeparator);const s=c=>c?typeof c=="function"?new c:c:null;if(!this.options.isClone){this.modules.logger?Q.init(s(this.modules.logger),this.options):Q.init(null,this.options);let c;this.modules.formatter?c=this.modules.formatter:c=Kn;const d=new qt(this.options);this.store=new Wt(this.options.resources,this.options);const u=this.services;u.logger=Q,u.resourceStore=this.store,u.languageUtils=d,u.pluralResolver=new Vn(d,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),c&&(!this.options.interpolation.format||this.options.interpolation.format===n.interpolation.format)&&(u.formatter=s(c),u.formatter.init(u,this.options),this.options.interpolation.format=u.formatter.format.bind(u.formatter)),u.interpolator=new zn(this.options),u.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},u.backendConnector=new Wn(s(this.modules.backend),u.resourceStore,u,this.options),u.backendConnector.on("*",(p,...f)=>{this.emit(p,...f)}),this.modules.languageDetector&&(u.languageDetector=s(this.modules.languageDetector),u.languageDetector.init&&u.languageDetector.init(u,this.options.detection,this.options)),this.modules.i18nFormat&&(u.i18nFormat=s(this.modules.i18nFormat),u.i18nFormat.init&&u.i18nFormat.init(this)),this.translator=new Ge(this.services,this.options),this.translator.on("*",(p,...f)=>{this.emit(p,...f)}),this.modules.external.forEach(p=>{p.init&&p.init(this)})}if(this.format=this.options.interpolation.format,r||(r=De),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const c=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);c.length>0&&c[0]!=="dev"&&(this.options.lng=c[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(c=>{this[c]=(...d)=>this.store[c](...d)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(c=>{this[c]=(...d)=>(this.store[c](...d),this)});const a=Se(),l=()=>{const c=(d,u)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(u),r(d,u)};if(this.languages&&!this.isInitialized)return c(null,this.t.bind(this));this.changeLanguage(this.options.lng,c)};return this.options.resources||!this.options.initAsync?l():setTimeout(l,0),a}loadResources(e,r=De){let n=r;const s=x(e)?e:this.language;if(typeof e=="function"&&(n=e),!this.options.resources||this.options.partialBundledLanguages){if(s?.toLowerCase()==="cimode"&&(!this.options.preload||this.options.preload.length===0))return n();const o=[],i=a=>{if(!a||a==="cimode")return;this.services.languageUtils.toResolveHierarchy(a).forEach(c=>{c!=="cimode"&&o.indexOf(c)<0&&o.push(c)})};s?i(s):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(l=>i(l)),this.options.preload?.forEach?.(a=>i(a)),this.services.backendConnector.load(o,this.options.ns,a=>{!a&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),n(a)})}else n(null)}reloadResources(e,r,n){const s=Se();return typeof e=="function"&&(n=e,e=void 0),typeof r=="function"&&(n=r,r=void 0),e||(e=this.languages),r||(r=this.options.ns),n||(n=De),this.services.backendConnector.reload(e,r,o=>{s.resolve(),n(o)}),s}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&Tr.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1)){for(let r=0;r<this.languages.length;r++){const n=this.languages[r];if(!(["cimode","dev"].indexOf(n)>-1)&&this.store.hasLanguageSomeTranslations(n)){this.resolvedLanguage=n;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,r){this.isLanguageChangingTo=e;const n=Se();this.emit("languageChanging",e);const s=a=>{this.language=a,this.languages=this.services.languageUtils.toResolveHierarchy(a),this.resolvedLanguage=void 0,this.setResolvedLanguage(a)},o=(a,l)=>{l?this.isLanguageChangingTo===e&&(s(l),this.translator.changeLanguage(l),this.isLanguageChangingTo=void 0,this.emit("languageChanged",l),this.logger.log("languageChanged",l)):this.isLanguageChangingTo=void 0,n.resolve((...c)=>this.t(...c)),r&&r(a,(...c)=>this.t(...c))},i=a=>{!e&&!a&&this.services.languageDetector&&(a=[]);const l=x(a)?a:a&&a[0],c=this.store.hasLanguageSomeTranslations(l)?l:this.services.languageUtils.getBestMatchFromCodes(x(a)?[a]:a);c&&(this.language||s(c),this.translator.language||this.translator.changeLanguage(c),this.services.languageDetector?.cacheUserLanguage?.(c)),this.loadResources(c,d=>{o(d,c)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?i(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(i):this.services.languageDetector.detect(i):i(e),n}getFixedT(e,r,n){const s=(o,i,...a)=>{let l;typeof i!="object"?l=this.options.overloadTranslationOptionHandler([o,i].concat(a)):l={...i},l.lng=l.lng||s.lng,l.lngs=l.lngs||s.lngs,l.ns=l.ns||s.ns,l.keyPrefix!==""&&(l.keyPrefix=l.keyPrefix||n||s.keyPrefix);const c=this.options.keySeparator||".";let d;return l.keyPrefix&&Array.isArray(o)?d=o.map(u=>`${l.keyPrefix}${c}${u}`):d=l.keyPrefix?`${l.keyPrefix}${c}${o}`:o,this.t(d,l)};return x(e)?s.lng=e:s.lngs=e,s.ns=r,s.keyPrefix=n,s}t(...e){return this.translator?.translate(...e)}exists(...e){return this.translator?.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,r={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const n=r.lng||this.resolvedLanguage||this.languages[0],s=this.options?this.options.fallbackLng:!1,o=this.languages[this.languages.length-1];if(n.toLowerCase()==="cimode")return!0;const i=(a,l)=>{const c=this.services.backendConnector.state[`${a}|${l}`];return c===-1||c===0||c===2};if(r.precheck){const a=r.precheck(this,i);if(a!==void 0)return a}return!!(this.hasResourceBundle(n,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||i(n,e)&&(!s||i(o,e)))}loadNamespaces(e,r){const n=Se();return this.options.ns?(x(e)&&(e=[e]),e.forEach(s=>{this.options.ns.indexOf(s)<0&&this.options.ns.push(s)}),this.loadResources(s=>{n.resolve(),r&&r(s)}),n):(r&&r(),Promise.resolve())}loadLanguages(e,r){const n=Se();x(e)&&(e=[e]);const s=this.options.preload||[],o=e.filter(i=>s.indexOf(i)<0&&this.services.languageUtils.isSupportedCode(i));return o.length?(this.options.preload=s.concat(o),this.loadResources(i=>{n.resolve(),r&&r(i)}),n):(r&&r(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages?.length>0?this.languages[0]:this.language)),!e)return"rtl";const r=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],n=this.services?.languageUtils||new qt(er());return r.indexOf(n.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(e={},r){return new Re(e,r)}cloneInstance(e={},r=De){const n=e.forkResourceStore;n&&delete e.forkResourceStore;const s={...this.options,...e,isClone:!0},o=new Re(s);if((e.debug!==void 0||e.prefix!==void 0)&&(o.logger=o.logger.clone(e)),["store","services","language"].forEach(a=>{o[a]=this[a]}),o.services={...this.services},o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},n){const a=Object.keys(this.store.data).reduce((l,c)=>(l[c]={...this.store.data[c]},l[c]=Object.keys(l[c]).reduce((d,u)=>(d[u]={...l[c][u]},d),l[c]),l),{});o.store=new Wt(a,s),o.services.resourceStore=o.store}return o.translator=new Ge(o.services,s),o.translator.on("*",(a,...l)=>{o.emit(a,...l)}),o.init(s,r),o.translator.options=s,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const K=Re.createInstance();K.createInstance=Re.createInstance;K.createInstance;K.dir;K.init;K.loadResources;K.reloadResources;K.use;K.changeLanguage;K.getFixedT;K.t;K.exists;K.setDefaultNamespace;K.hasLoadedNamespace;K.loadNamespaces;K.loadLanguages;var Ee,S,Ar,le,rr,jr,Fr,Mr,xt,it,at,Dr,Pe={},Vr=[],Jn=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,$e=Array.isArray;function Z(t,e){for(var r in e)t[r]=e[r];return t}function wt(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function j(t,e,r){var n,s,o,i={};for(o in e)o=="key"?n=e[o]:o=="ref"?s=e[o]:i[o]=e[o];if(arguments.length>2&&(i.children=arguments.length>3?Ee.call(arguments,2):r),typeof t=="function"&&t.defaultProps!=null)for(o in t.defaultProps)i[o]===void 0&&(i[o]=t.defaultProps[o]);return Ce(t,i,n,s,null)}function Ce(t,e,r,n,s){var o={type:t,props:e,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:s??++Ar,__i:-1,__u:0};return s==null&&S.vnode!=null&&S.vnode(o),o}function zr(){return{current:null}}function X(t){return t.children}function q(t,e){this.props=t,this.context=e}function _e(t,e){if(e==null)return t.__?_e(t.__,t.__i+1):null;for(var r;e<t.__k.length;e++)if((r=t.__k[e])!=null&&r.__e!=null)return r.__e;return typeof t.type=="function"?_e(t):null}function Ur(t){var e,r;if((t=t.__)!=null&&t.__c!=null){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if((r=t.__k[e])!=null&&r.__e!=null){t.__e=t.__c.base=r.__e;break}return Ur(t)}}function lt(t){(!t.__d&&(t.__d=!0)&&le.push(t)&&!Je.__r++||rr!=S.debounceRendering)&&((rr=S.debounceRendering)||jr)(Je)}function Je(){for(var t,e,r,n,s,o,i,a=1;le.length;)le.length>a&&le.sort(Fr),t=le.shift(),a=le.length,t.__d&&(r=void 0,s=(n=(e=t).__v).__e,o=[],i=[],e.__P&&((r=Z({},n)).__v=n.__v+1,S.vnode&&S.vnode(r),St(e.__P,r,n,e.__n,e.__P.namespaceURI,32&n.__u?[s]:null,o,s??_e(n),!!(32&n.__u),i),r.__v=n.__v,r.__.__k[r.__i]=r,Br(o,r,i),r.__e!=s&&Ur(r)));Je.__r=0}function Hr(t,e,r,n,s,o,i,a,l,c,d){var u,p,f,h,b,m,v=n&&n.__k||Vr,y=e.length;for(l=qn(r,e,v,l,y),u=0;u<y;u++)(f=r.__k[u])!=null&&(p=f.__i==-1?Pe:v[f.__i]||Pe,f.__i=u,m=St(t,f,p,s,o,i,a,l,c,d),h=f.__e,f.ref&&p.ref!=f.ref&&(p.ref&&kt(p.ref,null,f),d.push(f.ref,f.__c||h,f)),b==null&&h!=null&&(b=h),4&f.__u||p.__k===f.__k?l=Kr(f,l,t):typeof f.type=="function"&&m!==void 0?l=m:h&&(l=h.nextSibling),f.__u&=-7);return r.__e=b,l}function qn(t,e,r,n,s){var o,i,a,l,c,d=r.length,u=d,p=0;for(t.__k=new Array(s),o=0;o<s;o++)(i=e[o])!=null&&typeof i!="boolean"&&typeof i!="function"?(l=o+p,(i=t.__k[o]=typeof i=="string"||typeof i=="number"||typeof i=="bigint"||i.constructor==String?Ce(null,i,null,null,null):$e(i)?Ce(X,{children:i},null,null,null):i.constructor==null&&i.__b>0?Ce(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i).__=t,i.__b=t.__b+1,a=null,(c=i.__i=Yn(i,r,l,u))!=-1&&(u--,(a=r[c])&&(a.__u|=2)),a==null||a.__v==null?(c==-1&&(s>d?p--:s<d&&p++),typeof i.type!="function"&&(i.__u|=4)):c!=l&&(c==l-1?p--:c==l+1?p++:(c>l?p--:p++,i.__u|=4))):t.__k[o]=null;if(u)for(o=0;o<d;o++)(a=r[o])!=null&&(2&a.__u)==0&&(a.__e==n&&(n=_e(a)),Gr(a,a));return n}function Kr(t,e,r){var n,s;if(typeof t.type=="function"){for(n=t.__k,s=0;n&&s<n.length;s++)n[s]&&(n[s].__=t,e=Kr(n[s],e,r));return e}t.__e!=e&&(e&&t.type&&!r.contains(e)&&(e=_e(t)),r.insertBefore(t.__e,e||null),e=t.__e);do e=e&&e.nextSibling;while(e!=null&&e.nodeType==8);return e}function te(t,e){return e=e||[],t==null||typeof t=="boolean"||($e(t)?t.some(function(r){te(r,e)}):e.push(t)),e}function Yn(t,e,r,n){var s,o,i=t.key,a=t.type,l=e[r];if(l===null&&t.key==null||l&&i==l.key&&a==l.type&&(2&l.__u)==0)return r;if(n>(l!=null&&(2&l.__u)==0?1:0))for(s=r-1,o=r+1;s>=0||o<e.length;){if(s>=0){if((l=e[s])&&(2&l.__u)==0&&i==l.key&&a==l.type)return s;s--}if(o<e.length){if((l=e[o])&&(2&l.__u)==0&&i==l.key&&a==l.type)return o;o++}}return-1}function nr(t,e,r){e[0]=="-"?t.setProperty(e,r??""):t[e]=r==null?"":typeof r!="number"||Jn.test(e)?r:r+"px"}function Ve(t,e,r,n,s){var o,i;e:if(e=="style")if(typeof r=="string")t.style.cssText=r;else{if(typeof n=="string"&&(t.style.cssText=n=""),n)for(e in n)r&&e in r||nr(t.style,e,"");if(r)for(e in r)n&&r[e]==n[e]||nr(t.style,e,r[e])}else if(e[0]=="o"&&e[1]=="n")o=e!=(e=e.replace(Mr,"$1")),i=e.toLowerCase(),e=i in t||e=="onFocusOut"||e=="onFocusIn"?i.slice(2):e.slice(2),t.l||(t.l={}),t.l[e+o]=r,r?n?r.u=n.u:(r.u=xt,t.addEventListener(e,o?at:it,o)):t.removeEventListener(e,o?at:it,o);else{if(s=="http://www.w3.org/2000/svg")e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(e!="width"&&e!="height"&&e!="href"&&e!="list"&&e!="form"&&e!="tabIndex"&&e!="download"&&e!="rowSpan"&&e!="colSpan"&&e!="role"&&e!="popover"&&e in t)try{t[e]=r??"";break e}catch{}typeof r=="function"||(r==null||r===!1&&e[4]!="-"?t.removeAttribute(e):t.setAttribute(e,e=="popover"&&r==1?"":r))}}function sr(t){return function(e){if(this.l){var r=this.l[e.type+t];if(e.t==null)e.t=xt++;else if(e.t<r.u)return;return r(S.event?S.event(e):e)}}}function St(t,e,r,n,s,o,i,a,l,c){var d,u,p,f,h,b,m,v,y,P,w,L,k,$,Y,A,D,U=e.type;if(e.constructor!=null)return null;128&r.__u&&(l=!!(32&r.__u),o=[a=e.__e=r.__e]),(d=S.__b)&&d(e);e:if(typeof U=="function")try{if(v=e.props,y="prototype"in U&&U.prototype.render,P=(d=U.contextType)&&n[d.__c],w=d?P?P.props.value:d.__:n,r.__c?m=(u=e.__c=r.__c).__=u.__E:(y?e.__c=u=new U(v,w):(e.__c=u=new q(v,w),u.constructor=U,u.render=Zn),P&&P.sub(u),u.props=v,u.state||(u.state={}),u.context=w,u.__n=n,p=u.__d=!0,u.__h=[],u._sb=[]),y&&u.__s==null&&(u.__s=u.state),y&&U.getDerivedStateFromProps!=null&&(u.__s==u.state&&(u.__s=Z({},u.__s)),Z(u.__s,U.getDerivedStateFromProps(v,u.__s))),f=u.props,h=u.state,u.__v=e,p)y&&U.getDerivedStateFromProps==null&&u.componentWillMount!=null&&u.componentWillMount(),y&&u.componentDidMount!=null&&u.__h.push(u.componentDidMount);else{if(y&&U.getDerivedStateFromProps==null&&v!==f&&u.componentWillReceiveProps!=null&&u.componentWillReceiveProps(v,w),!u.__e&&u.shouldComponentUpdate!=null&&u.shouldComponentUpdate(v,u.__s,w)===!1||e.__v==r.__v){for(e.__v!=r.__v&&(u.props=v,u.state=u.__s,u.__d=!1),e.__e=r.__e,e.__k=r.__k,e.__k.some(function(G){G&&(G.__=e)}),L=0;L<u._sb.length;L++)u.__h.push(u._sb[L]);u._sb=[],u.__h.length&&i.push(u);break e}u.componentWillUpdate!=null&&u.componentWillUpdate(v,u.__s,w),y&&u.componentDidUpdate!=null&&u.__h.push(function(){u.componentDidUpdate(f,h,b)})}if(u.context=w,u.props=v,u.__P=t,u.__e=!1,k=S.__r,$=0,y){for(u.state=u.__s,u.__d=!1,k&&k(e),d=u.render(u.props,u.state,u.context),Y=0;Y<u._sb.length;Y++)u.__h.push(u._sb[Y]);u._sb=[]}else do u.__d=!1,k&&k(e),d=u.render(u.props,u.state,u.context),u.state=u.__s;while(u.__d&&++$<25);u.state=u.__s,u.getChildContext!=null&&(n=Z(Z({},n),u.getChildContext())),y&&!p&&u.getSnapshotBeforeUpdate!=null&&(b=u.getSnapshotBeforeUpdate(f,h)),A=d,d!=null&&d.type===X&&d.key==null&&(A=Wr(d.props.children)),a=Hr(t,$e(A)?A:[A],e,r,n,s,o,i,a,l,c),u.base=e.__e,e.__u&=-161,u.__h.length&&i.push(u),m&&(u.__E=u.__=null)}catch(G){if(e.__v=null,l||o!=null)if(G.then){for(e.__u|=l?160:128;a&&a.nodeType==8&&a.nextSibling;)a=a.nextSibling;o[o.indexOf(a)]=null,e.__e=a}else for(D=o.length;D--;)wt(o[D]);else e.__e=r.__e,e.__k=r.__k;S.__e(G,e,r)}else o==null&&e.__v==r.__v?(e.__k=r.__k,e.__e=r.__e):a=e.__e=Qn(r.__e,e,r,n,s,o,i,l,c);return(d=S.diffed)&&d(e),128&e.__u?void 0:a}function Br(t,e,r){for(var n=0;n<r.length;n++)kt(r[n],r[++n],r[++n]);S.__c&&S.__c(e,t),t.some(function(s){try{t=s.__h,s.__h=[],t.some(function(o){o.call(s)})}catch(o){S.__e(o,s.__v)}})}function Wr(t){return typeof t!="object"||t==null||t.__b&&t.__b>0?t:$e(t)?t.map(Wr):Z({},t)}function Qn(t,e,r,n,s,o,i,a,l){var c,d,u,p,f,h,b,m=r.props,v=e.props,y=e.type;if(y=="svg"?s="http://www.w3.org/2000/svg":y=="math"?s="http://www.w3.org/1998/Math/MathML":s||(s="http://www.w3.org/1999/xhtml"),o!=null){for(c=0;c<o.length;c++)if((f=o[c])&&"setAttribute"in f==!!y&&(y?f.localName==y:f.nodeType==3)){t=f,o[c]=null;break}}if(t==null){if(y==null)return document.createTextNode(v);t=document.createElementNS(s,y,v.is&&v),a&&(S.__m&&S.__m(e,o),a=!1),o=null}if(y==null)m===v||a&&t.data==v||(t.data=v);else{if(o=o&&Ee.call(t.childNodes),m=r.props||Pe,!a&&o!=null)for(m={},c=0;c<t.attributes.length;c++)m[(f=t.attributes[c]).name]=f.value;for(c in m)if(f=m[c],c!="children"){if(c=="dangerouslySetInnerHTML")u=f;else if(!(c in v)){if(c=="value"&&"defaultValue"in v||c=="checked"&&"defaultChecked"in v)continue;Ve(t,c,null,f,s)}}for(c in v)f=v[c],c=="children"?p=f:c=="dangerouslySetInnerHTML"?d=f:c=="value"?h=f:c=="checked"?b=f:a&&typeof f!="function"||m[c]===f||Ve(t,c,f,m[c],s);if(d)a||u&&(d.__html==u.__html||d.__html==t.innerHTML)||(t.innerHTML=d.__html),e.__k=[];else if(u&&(t.innerHTML=""),Hr(e.type=="template"?t.content:t,$e(p)?p:[p],e,r,n,y=="foreignObject"?"http://www.w3.org/1999/xhtml":s,o,i,o?o[0]:r.__k&&_e(r,0),a,l),o!=null)for(c=o.length;c--;)wt(o[c]);a||(c="value",y=="progress"&&h==null?t.removeAttribute("value"):h!=null&&(h!==t[c]||y=="progress"&&!h||y=="option"&&h!=m[c])&&Ve(t,c,h,m[c],s),c="checked",b!=null&&b!=t[c]&&Ve(t,c,b,m[c],s))}return t}function kt(t,e,r){try{if(typeof t=="function"){var n=typeof t.__u=="function";n&&t.__u(),n&&e==null||(t.__u=t(e))}else t.current=e}catch(s){S.__e(s,r)}}function Gr(t,e,r){var n,s;if(S.unmount&&S.unmount(t),(n=t.ref)&&(n.current&&n.current!=t.__e||kt(n,null,e)),(n=t.__c)!=null){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(o){S.__e(o,e)}n.base=n.__P=null}if(n=t.__k)for(s=0;s<n.length;s++)n[s]&&Gr(n[s],e,r||typeof t.type!="function");r||wt(t.__e),t.__c=t.__=t.__e=void 0}function Zn(t,e,r){return this.constructor(t,r)}function Ne(t,e,r){var n,s,o,i;e==document&&(e=document.documentElement),S.__&&S.__(t,e),s=(n=typeof r=="function")?null:r&&r.__k||e.__k,o=[],i=[],St(e,t=(!n&&r||e).__k=j(X,null,[t]),s||Pe,Pe,e.namespaceURI,!n&&r?[r]:s?null:e.firstChild?Ee.call(e.childNodes):null,o,!n&&r?r:s?s.__e:e.firstChild,n,i),Br(o,t,i)}function Jr(t,e){Ne(t,e,Jr)}function Xn(t,e,r){var n,s,o,i,a=Z({},t.props);for(o in t.type&&t.type.defaultProps&&(i=t.type.defaultProps),e)o=="key"?n=e[o]:o=="ref"?s=e[o]:a[o]=e[o]===void 0&&i!=null?i[o]:e[o];return arguments.length>2&&(a.children=arguments.length>3?Ee.call(arguments,2):r),Ce(t.type,a,n||t.key,s||t.ref,null)}function Ct(t){function e(r){var n,s;return this.getChildContext||(n=new Set,(s={})[e.__c]=this,this.getChildContext=function(){return s},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(o){this.props.value!=o.value&&n.forEach(function(i){i.__e=!0,lt(i)})},this.sub=function(o){n.add(o);var i=o.componentWillUnmount;o.componentWillUnmount=function(){n&&n.delete(o),i&&i.call(o)}}),r.children}return e.__c="__cC"+Dr++,e.__=t,e.Provider=e.__l=(e.Consumer=function(r,n){return r.children(n)}).contextType=e,e}Ee=Vr.slice,S={__e:function(t,e,r,n){for(var s,o,i;e=e.__;)if((s=e.__c)&&!s.__)try{if((o=s.constructor)&&o.getDerivedStateFromError!=null&&(s.setState(o.getDerivedStateFromError(t)),i=s.__d),s.componentDidCatch!=null&&(s.componentDidCatch(t,n||{}),i=s.__d),i)return s.__E=s}catch(a){t=a}throw t}},Ar=0,q.prototype.setState=function(t,e){var r;r=this.__s!=null&&this.__s!=this.state?this.__s:this.__s=Z({},this.state),typeof t=="function"&&(t=t(Z({},r),this.props)),t&&Z(r,t),t!=null&&this.__v&&(e&&this._sb.push(e),lt(this))},q.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),lt(this))},q.prototype.render=X,le=[],jr=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Fr=function(t,e){return t.__v.__b-e.__v.__b},Je.__r=0,Mr=/(PointerCapture)$|Capture$/i,xt=0,it=sr(!1),at=sr(!0),Dr=0;var re,E,tt,or,me=0,qr=[],I=S,ir=I.__b,ar=I.__r,lr=I.diffed,cr=I.__c,ur=I.unmount,dr=I.__;function de(t,e){I.__h&&I.__h(E,t,me||e),me=0;var r=E.__H||(E.__H={__:[],__h:[]});return t>=r.__.length&&r.__.push({}),r.__[t]}function be(t){return me=1,Qe(Yr,t)}function Qe(t,e,r){var n=de(re++,2);if(n.t=t,!n.__c&&(n.__=[r?r(e):Yr(void 0,e),function(a){var l=n.__N?n.__N[0]:n.__[0],c=n.t(l,a);l!==c&&(n.__N=[c,n.__[1]],n.__c.setState({}))}],n.__c=E,!E.__f)){var s=function(a,l,c){if(!n.__c.__H)return!0;var d=n.__c.__H.__.filter(function(p){return!!p.__c});if(d.every(function(p){return!p.__N}))return!o||o.call(this,a,l,c);var u=n.__c.props!==a;return d.forEach(function(p){if(p.__N){var f=p.__[0];p.__=p.__N,p.__N=void 0,f!==p.__[0]&&(u=!0)}}),o&&o.call(this,a,l,c)||u};E.__f=!0;var o=E.shouldComponentUpdate,i=E.componentWillUpdate;E.componentWillUpdate=function(a,l,c){if(this.__e){var d=o;o=void 0,s(a,l,c),o=d}i&&i.call(this,a,l,c)},E.shouldComponentUpdate=s}return n.__N||n.__}function ue(t,e){var r=de(re++,3);!I.__s&&Pt(r.__H,e)&&(r.__=t,r.u=e,E.__H.__h.push(r))}function ye(t,e){var r=de(re++,4);!I.__s&&Pt(r.__H,e)&&(r.__=t,r.u=e,E.__h.push(r))}function Ie(t){return me=5,J(function(){return{current:t}},[])}function Ot(t,e,r){me=6,ye(function(){if(typeof t=="function"){var n=t(e());return function(){t(null),n&&typeof n=="function"&&n()}}if(t)return t.current=e(),function(){return t.current=null}},r==null?r:r.concat(t))}function J(t,e){var r=de(re++,7);return Pt(r.__H,e)&&(r.__=t(),r.__H=e,r.__h=t),r.__}function Te(t,e){return me=8,J(function(){return t},e)}function Ze(t){var e=E.context[t.__c],r=de(re++,9);return r.c=t,e?(r.__==null&&(r.__=!0,e.sub(E)),e.props.value):t.__}function Lt(t,e){I.useDebugValue&&I.useDebugValue(e?e(t):t)}function es(t){var e=de(re++,10),r=be();return e.__=t,E.componentDidCatch||(E.componentDidCatch=function(n,s){e.__&&e.__(n,s),r[1](n)}),[r[0],function(){r[1](void 0)}]}function Rt(){var t=de(re++,11);if(!t.__){for(var e=E.__v;e!==null&&!e.__m&&e.__!==null;)e=e.__;var r=e.__m||(e.__m=[0,0]);t.__="P"+r[0]+"-"+r[1]++}return t.__}function ts(){for(var t;t=qr.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(Ue),t.__H.__h.forEach(ct),t.__H.__h=[]}catch(e){t.__H.__h=[],I.__e(e,t.__v)}}I.__b=function(t){E=null,ir&&ir(t)},I.__=function(t,e){t&&e.__k&&e.__k.__m&&(t.__m=e.__k.__m),dr&&dr(t,e)},I.__r=function(t){ar&&ar(t),re=0;var e=(E=t.__c).__H;e&&(tt===E?(e.__h=[],E.__h=[],e.__.forEach(function(r){r.__N&&(r.__=r.__N),r.u=r.__N=void 0})):(e.__h.forEach(Ue),e.__h.forEach(ct),e.__h=[],re=0)),tt=E},I.diffed=function(t){lr&&lr(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(qr.push(e)!==1&&or===I.requestAnimationFrame||((or=I.requestAnimationFrame)||rs)(ts)),e.__H.__.forEach(function(r){r.u&&(r.__H=r.u),r.u=void 0})),tt=E=null},I.__c=function(t,e){e.some(function(r){try{r.__h.forEach(Ue),r.__h=r.__h.filter(function(n){return!n.__||ct(n)})}catch(n){e.some(function(s){s.__h&&(s.__h=[])}),e=[],I.__e(n,r.__v)}}),cr&&cr(t,e)},I.unmount=function(t){ur&&ur(t);var e,r=t.__c;r&&r.__H&&(r.__H.__.forEach(function(n){try{Ue(n)}catch(s){e=s}}),r.__H=void 0,e&&I.__e(e,r.__v))};var fr=typeof requestAnimationFrame=="function";function rs(t){var e,r=function(){clearTimeout(n),fr&&cancelAnimationFrame(e),setTimeout(t)},n=setTimeout(r,35);fr&&(e=requestAnimationFrame(r))}function Ue(t){var e=E,r=t.__c;typeof r=="function"&&(t.__c=void 0,r()),E=e}function ct(t){var e=E;t.__c=t.__(),E=e}function Pt(t,e){return!t||t.length!==e.length||e.some(function(r,n){return r!==t[n]})}function Yr(t,e){return typeof e=="function"?e(t):e}function Qr(t,e){for(var r in e)t[r]=e[r];return t}function ut(t,e){for(var r in t)if(r!=="__source"&&!(r in e))return!0;for(var n in e)if(n!=="__source"&&t[n]!==e[n])return!0;return!1}function Nt(t,e){var r=e(),n=be({t:{__:r,u:e}}),s=n[0].t,o=n[1];return ye(function(){s.__=r,s.u=e,rt(s)&&o({t:s})},[t,r,e]),ue(function(){return rt(s)&&o({t:s}),t(function(){rt(s)&&o({t:s})})},[t]),r}function rt(t){var e,r,n=t.u,s=t.__;try{var o=n();return!((e=s)===(r=o)&&(e!==0||1/e==1/r)||e!=e&&r!=r)}catch{return!0}}function Et(t){t()}function $t(t){return t}function It(){return[!1,Et]}var Tt=ye;function qe(t,e){this.props=t,this.context=e}function Zr(t,e){function r(s){var o=this.props.ref,i=o==s.ref;return!i&&o&&(o.call?o(null):o.current=null),e?!e(this.props,s)||!i:ut(this.props,s)}function n(s){return this.shouldComponentUpdate=r,j(t,s)}return n.displayName="Memo("+(t.displayName||t.name)+")",n.prototype.isReactComponent=!0,n.__f=!0,n}(qe.prototype=new q).isPureReactComponent=!0,qe.prototype.shouldComponentUpdate=function(t,e){return ut(this.props,t)||ut(this.state,e)};var pr=S.__b;S.__b=function(t){t.type&&t.type.__f&&t.ref&&(t.props.ref=t.ref,t.ref=null),pr&&pr(t)};var ns=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function fe(t){function e(r){var n=Qr({},r);return delete n.ref,t(n,r.ref||null)}return e.$$typeof=ns,e.render=e,e.prototype.isReactComponent=e.__f=!0,e.displayName="ForwardRef("+(t.displayName||t.name)+")",e}var hr=function(t,e){return t==null?null:te(te(t).map(e))},Xr={map:hr,forEach:hr,count:function(t){return t?te(t).length:0},only:function(t){var e=te(t);if(e.length!==1)throw"Children.only";return e[0]},toArray:te},ss=S.__e;S.__e=function(t,e,r,n){if(t.then){for(var s,o=e;o=o.__;)if((s=o.__c)&&s.__c)return e.__e==null&&(e.__e=r.__e,e.__k=r.__k),s.__c(t,e)}ss(t,e,r,n)};var gr=S.unmount;function en(t,e,r){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(n){typeof n.__c=="function"&&n.__c()}),t.__c.__H=null),(t=Qr({},t)).__c!=null&&(t.__c.__P===r&&(t.__c.__P=e),t.__c.__e=!0,t.__c=null),t.__k=t.__k&&t.__k.map(function(n){return en(n,e,r)})),t}function tn(t,e,r){return t&&r&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(n){return tn(n,e,r)}),t.__c&&t.__c.__P===e&&(t.__e&&r.appendChild(t.__e),t.__c.__e=!0,t.__c.__P=r)),t}function Oe(){this.__u=0,this.o=null,this.__b=null}function rn(t){var e=t.__.__c;return e&&e.__a&&e.__a(t)}function nn(t){var e,r,n;function s(o){if(e||(e=t()).then(function(i){r=i.default||i},function(i){n=i}),n)throw n;if(!r)throw e;return j(r,o)}return s.displayName="Lazy",s.__f=!0,s}function ge(){this.i=null,this.l=null}S.unmount=function(t){var e=t.__c;e&&e.__R&&e.__R(),e&&32&t.__u&&(t.type=null),gr&&gr(t)},(Oe.prototype=new q).__c=function(t,e){var r=e.__c,n=this;n.o==null&&(n.o=[]),n.o.push(r);var s=rn(n.__v),o=!1,i=function(){o||(o=!0,r.__R=null,s?s(a):a())};r.__R=i;var a=function(){if(!--n.__u){if(n.state.__a){var l=n.state.__a;n.__v.__k[0]=tn(l,l.__c.__P,l.__c.__O)}var c;for(n.setState({__a:n.__b=null});c=n.o.pop();)c.forceUpdate()}};n.__u++||32&e.__u||n.setState({__a:n.__b=n.__v.__k[0]}),t.then(i,i)},Oe.prototype.componentWillUnmount=function(){this.o=[]},Oe.prototype.render=function(t,e){if(this.__b){if(this.__v.__k){var r=document.createElement("div"),n=this.__v.__k[0].__c;this.__v.__k[0]=en(this.__b,r,n.__O=n.__P)}this.__b=null}var s=e.__a&&j(X,null,t.fallback);return s&&(s.__u&=-33),[j(X,null,e.__a?null:t.children),s]};var _r=function(t,e,r){if(++r[1]===r[0]&&t.l.delete(e),t.props.revealOrder&&(t.props.revealOrder[0]!=="t"||!t.l.size))for(r=t.i;r;){for(;r.length>3;)r.pop()();if(r[1]<r[0])break;t.i=r=r[2]}};function os(t){return this.getChildContext=function(){return t.context},t.children}function is(t){var e=this,r=t.h;if(e.componentWillUnmount=function(){Ne(null,e.v),e.v=null,e.h=null},e.h&&e.h!==r&&e.componentWillUnmount(),!e.v){for(var n=e.__v;n!==null&&!n.__m&&n.__!==null;)n=n.__;e.h=r,e.v={nodeType:1,parentNode:r,childNodes:[],__k:{__m:n.__m},contains:function(){return!0},insertBefore:function(s,o){this.childNodes.push(s),e.h.insertBefore(s,o)},removeChild:function(s){this.childNodes.splice(this.childNodes.indexOf(s)>>>1,1),e.h.removeChild(s)}}}Ne(j(os,{context:e.context},t.__v),e.v)}function sn(t,e){var r=j(is,{__v:t,h:e});return r.containerInfo=e,r}(ge.prototype=new q).__a=function(t){var e=this,r=rn(e.__v),n=e.l.get(t);return n[0]++,function(s){var o=function(){e.props.revealOrder?(n.push(s),_r(e,t,n)):s()};r?r(o):o()}},ge.prototype.render=function(t){this.i=null,this.l=new Map;var e=te(t.children);t.revealOrder&&t.revealOrder[0]==="b"&&e.reverse();for(var r=e.length;r--;)this.l.set(e[r],this.i=[1,0,this.i]);return t.children},ge.prototype.componentDidUpdate=ge.prototype.componentDidMount=function(){var t=this;this.l.forEach(function(e,r){_r(t,r,e)})};var on=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,as=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,ls=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,cs=/[A-Z0-9]/g,us=typeof document<"u",ds=function(t){return(typeof Symbol<"u"&&typeof Symbol()=="symbol"?/fil|che|rad/:/fil|che|ra/).test(t)};function At(t,e,r){return e.__k==null&&(e.textContent=""),Ne(t,e),typeof r=="function"&&r(),t?t.__c:null}function an(t,e,r){return Jr(t,e),typeof r=="function"&&r(),t?t.__c:null}q.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(t){Object.defineProperty(q.prototype,t,{configurable:!0,get:function(){return this["UNSAFE_"+t]},set:function(e){Object.defineProperty(this,t,{configurable:!0,writable:!0,value:e})}})});var mr=S.event;function fs(){}function ps(){return this.cancelBubble}function hs(){return this.defaultPrevented}S.event=function(t){return mr&&(t=mr(t)),t.persist=fs,t.isPropagationStopped=ps,t.isDefaultPrevented=hs,t.nativeEvent=t};var jt,gs={enumerable:!1,configurable:!0,get:function(){return this.class}},br=S.vnode;S.vnode=function(t){typeof t.type=="string"&&function(e){var r=e.props,n=e.type,s={},o=n.indexOf("-")===-1;for(var i in r){var a=r[i];if(!(i==="value"&&"defaultValue"in r&&a==null||us&&i==="children"&&n==="noscript"||i==="class"||i==="className")){var l=i.toLowerCase();i==="defaultValue"&&"value"in r&&r.value==null?i="value":i==="download"&&a===!0?a="":l==="translate"&&a==="no"?a=!1:l[0]==="o"&&l[1]==="n"?l==="ondoubleclick"?i="ondblclick":l!=="onchange"||n!=="input"&&n!=="textarea"||ds(r.type)?l==="onfocus"?i="onfocusin":l==="onblur"?i="onfocusout":ls.test(i)&&(i=l):l=i="oninput":o&&as.test(i)?i=i.replace(cs,"-$&").toLowerCase():a===null&&(a=void 0),l==="oninput"&&s[i=l]&&(i="oninputCapture"),s[i]=a}}n=="select"&&s.multiple&&Array.isArray(s.value)&&(s.value=te(r.children).forEach(function(c){c.props.selected=s.value.indexOf(c.props.value)!=-1})),n=="select"&&s.defaultValue!=null&&(s.value=te(r.children).forEach(function(c){c.props.selected=s.multiple?s.defaultValue.indexOf(c.props.value)!=-1:s.defaultValue==c.props.value})),r.class&&!r.className?(s.class=r.class,Object.defineProperty(s,"className",gs)):(r.className&&!r.class||r.class&&r.className)&&(s.class=s.className=r.className),e.props=s}(t),t.$$typeof=on,br&&br(t)};var yr=S.__r;S.__r=function(t){yr&&yr(t),jt=t.__c};var vr=S.diffed;S.diffed=function(t){vr&&vr(t);var e=t.props,r=t.__e;r!=null&&t.type==="textarea"&&"value"in e&&e.value!==r.value&&(r.value=e.value==null?"":e.value),jt=null};var ln={ReactCurrentDispatcher:{current:{readContext:function(t){return jt.__n[t.__c].props.value},useCallback:Te,useContext:Ze,useDebugValue:Lt,useDeferredValue:$t,useEffect:ue,useId:Rt,useImperativeHandle:Ot,useInsertionEffect:Tt,useLayoutEffect:ye,useMemo:J,useReducer:Qe,useRef:Ie,useState:be,useSyncExternalStore:Nt,useTransition:It}}},_s="18.3.1";function cn(t){return j.bind(null,t)}function Ae(t){return!!t&&t.$$typeof===on}function un(t){return Ae(t)&&t.type===X}function dn(t){return!!t&&!!t.displayName&&(typeof t.displayName=="string"||t.displayName instanceof String)&&t.displayName.startsWith("Memo(")}function fn(t){return Ae(t)?Xn.apply(null,arguments):t}function Ft(t){return!!t.__k&&(Ne(null,t),!0)}function pn(t){return t&&(t.base||t.nodeType===1&&t)||null}var hn=function(t,e){return t(e)},gn=function(t,e){return t(e)},_n=X,mn=Ae,He={useState:be,useId:Rt,useReducer:Qe,useEffect:ue,useLayoutEffect:ye,useInsertionEffect:Tt,useTransition:It,useDeferredValue:$t,useSyncExternalStore:Nt,startTransition:Et,useRef:Ie,useImperativeHandle:Ot,useMemo:J,useCallback:Te,useContext:Ze,useDebugValue:Lt,version:"18.3.1",Children:Xr,render:At,hydrate:an,unmountComponentAtNode:Ft,createPortal:sn,createElement:j,createContext:Ct,createFactory:cn,cloneElement:fn,createRef:zr,Fragment:X,isValidElement:Ae,isElement:mn,isFragment:un,isMemo:dn,findDOMNode:pn,Component:q,PureComponent:qe,memo:Zr,forwardRef:fe,flushSync:gn,unstable_batchedUpdates:hn,StrictMode:_n,Suspense:Oe,SuspenseList:ge,lazy:nn,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:ln};const Oo=Object.freeze(Object.defineProperty({__proto__:null,Children:Xr,Component:q,Fragment:X,PureComponent:qe,StrictMode:_n,Suspense:Oe,SuspenseList:ge,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:ln,cloneElement:fn,createContext:Ct,createElement:j,createFactory:cn,createPortal:sn,createRef:zr,default:He,findDOMNode:pn,flushSync:gn,forwardRef:fe,hydrate:an,isElement:mn,isFragment:un,isMemo:dn,isValidElement:Ae,lazy:nn,memo:Zr,render:At,startTransition:Et,unmountComponentAtNode:Ft,unstable_batchedUpdates:hn,useCallback:Te,useContext:Ze,useDebugValue:Lt,useDeferredValue:$t,useEffect:ue,useErrorBoundary:es,useId:Rt,useImperativeHandle:Ot,useInsertionEffect:Tt,useLayoutEffect:ye,useMemo:J,useReducer:Qe,useRef:Ie,useState:be,useSyncExternalStore:Nt,useTransition:It,version:_s},Symbol.toStringTag,{value:"Module"}));function Lo(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function Ro(t){if(Object.prototype.hasOwnProperty.call(t,"__esModule"))return t;var e=t.default;if(typeof e=="function"){var r=function n(){return this instanceof n?Reflect.construct(e,arguments,this.constructor):e.apply(this,arguments)};r.prototype=e.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(t).forEach(function(n){var s=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(r,n,s.get?s:{enumerable:!0,get:function(){return t[n]}})}),r}const ms=(t,e,r,n)=>{const s=[r,{code:e,...n||{}}];if(t?.services?.logger?.forward)return t.services.logger.forward(s,"warn","react-i18next::",!0);ce(s[0])&&(s[0]=`react-i18next:: ${s[0]}`),t?.services?.logger?.warn?t.services.logger.warn(...s):console?.warn&&console.warn(...s)},xr={},dt=(t,e,r,n)=>{ce(r)&&xr[r]||(ce(r)&&(xr[r]=new Date),ms(t,e,r,n))},bn=(t,e)=>()=>{if(t.isInitialized)e();else{const r=()=>{setTimeout(()=>{t.off("initialized",r)},0),e()};t.on("initialized",r)}},ft=(t,e,r)=>{t.loadNamespaces(e,bn(t,r))},wr=(t,e,r,n)=>{if(ce(r)&&(r=[r]),t.options.preload&&t.options.preload.indexOf(e)>-1)return ft(t,r,n);r.forEach(s=>{t.options.ns.indexOf(s)<0&&t.options.ns.push(s)}),t.loadLanguages(e,bn(t,n))},bs=(t,e,r={})=>!e.languages||!e.languages.length?(dt(e,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:e.languages}),!0):e.hasLoadedNamespace(t,{lng:r.lng,precheck:(n,s)=>{if(r.bindI18n?.indexOf("languageChanging")>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!s(n.isLanguageChangingTo,t))return!1}}),ce=t=>typeof t=="string",ys=t=>typeof t=="object"&&t!==null,vs=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,xs={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},ws=t=>xs[t],Ss=t=>t.replace(vs,ws);let pt={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:Ss};const ks=(t={})=>{pt={...pt,...t}},Cs=()=>pt;let yn;const Os=t=>{yn=t},Ls=()=>yn,Po={type:"3rdParty",init(t){ks(t.options.react),Os(t)}},Rs=Ct();class Ps{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(r=>{this.usedNamespaces[r]||(this.usedNamespaces[r]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const Ns=(t,e)=>{const r=Ie();return ue(()=>{r.current=t},[t,e]),r.current},vn=(t,e,r,n)=>t.getFixedT(e,r,n),Es=(t,e,r,n)=>Te(vn(t,e,r,n),[t,e,r,n]),No=(t,e={})=>{const{i18n:r}=e,{i18n:n,defaultNS:s}=Ze(Rs)||{},o=r||n||Ls();if(o&&!o.reportNamespaces&&(o.reportNamespaces=new Ps),!o){dt(o,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const w=(k,$)=>ce($)?$:ys($)&&ce($.defaultValue)?$.defaultValue:Array.isArray(k)?k[k.length-1]:k,L=[w,{},!1];return L.t=w,L.i18n={},L.ready=!1,L}o.options.react?.wait&&dt(o,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const i={...Cs(),...o.options.react,...e},{useSuspense:a,keyPrefix:l}=i;let c=t||s||o.options?.defaultNS;c=ce(c)?[c]:c||["translation"],o.reportNamespaces.addUsedNamespaces?.(c);const d=(o.isInitialized||o.initializedStoreOnce)&&c.every(w=>bs(w,o,i)),u=Es(o,e.lng||null,i.nsMode==="fallback"?c:c[0],l),p=()=>u,f=()=>vn(o,e.lng||null,i.nsMode==="fallback"?c:c[0],l),[h,b]=be(p);let m=c.join();e.lng&&(m=`${e.lng}${m}`);const v=Ns(m),y=Ie(!0);ue(()=>{const{bindI18n:w,bindI18nStore:L}=i;y.current=!0,!d&&!a&&(e.lng?wr(o,e.lng,c,()=>{y.current&&b(f)}):ft(o,c,()=>{y.current&&b(f)})),d&&v&&v!==m&&y.current&&b(f);const k=()=>{y.current&&b(f)};return w&&o?.on(w,k),L&&o?.store.on(L,k),()=>{y.current=!1,o&&w?.split(" ").forEach($=>o.off($,k)),L&&o&&L.split(" ").forEach($=>o.store.off($,k))}},[o,m]),ue(()=>{y.current&&d&&b(p)},[o,l,d]);const P=[h,o,d];if(P.t=h,P.i18n=o,P.ready=d,d||!d&&!a)return P;throw new Promise(w=>{e.lng?wr(o,e.lng,c,()=>w()):ft(o,c,()=>w())})},Eo="JP",$o="JP",Io="ja",To=function t(e){function r(s,o,i){var a,l={};if(Array.isArray(s))return s.concat(o);for(a in s)l[i?a.toLowerCase():a]=s[a];for(a in o){var c=i?a.toLowerCase():a,d=o[a];l[c]=c in l&&typeof d=="object"?r(l[c],d,c=="headers"):d}return l}function n(s,o,i,a,l){var c=typeof s!="string"?(o=s).url:s,d={config:o},u=r(e,o),p={};a=a||u.data,(u.transformRequest||[]).map(function(f){a=f(a,u.headers)||a}),u.auth&&(p.authorization=u.auth),a&&typeof a=="object"&&typeof a.append!="function"&&typeof a.text!="function"&&(a=JSON.stringify(a),p["content-type"]="application/json");try{p[u.xsrfHeaderName]=decodeURIComponent(document.cookie.match(RegExp("(^|; )"+u.xsrfCookieName+"=([^;]*)"))[2])}catch{}return u.baseURL&&(c=c.replace(/^(?!.*\/\/)\/?/,u.baseURL+"/")),u.params&&(c+=(~c.indexOf("?")?"&":"?")+(u.paramsSerializer?u.paramsSerializer(u.params):new URLSearchParams(u.params))),(u.fetch||fetch)(c,{method:(i||u.method||"get").toUpperCase(),body:a,headers:r(u.headers,p,!0),credentials:u.withCredentials?"include":l}).then(function(f){for(var h in f)typeof f[h]!="function"&&(d[h]=f[h]);return u.responseType=="stream"?(d.data=f.body,d):f[u.responseType||"text"]().then(function(b){d.data=b,d.data=JSON.parse(b)}).catch(Object).then(function(){return(u.validateStatus?u.validateStatus(f.status):f.ok)?d:Promise.reject(d)})})}return e=e||{},n.request=n,n.get=function(s,o){return n(s,o,"get")},n.delete=function(s,o){return n(s,o,"delete")},n.head=function(s,o){return n(s,o,"head")},n.options=function(s,o){return n(s,o,"options")},n.post=function(s,o,i){return n(s,i,"post",o)},n.put=function(s,o,i){return n(s,i,"put",o)},n.patch=function(s,o,i){return n(s,i,"patch",o)},n.all=Promise.all.bind(Promise),n.spread=function(s){return s.apply.bind(s,s)},n.CancelToken=typeof AbortController=="function"?AbortController:Object,n.defaults=e,n.create=t,n}(),ht={env:window.__psp_process_env__||{}};window.__psp_process_env__=void 0;const Ao=new Proxy({},{get(t,e){if(e in ht.env)return ht.env[e];throw new Error(`The key "${String(e)}" does not exist in the client environment.`)}});function jo(t){ht.env=t}var $s=0;function ee(t,e,r,n,s,o){e||(e={});var i,a,l=e;if("ref"in l)for(a in l={},e)a=="ref"?i=e[a]:l[a]=e[a];var c={type:t,props:l,key:r,ref:i,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:--$s,__i:-1,__u:0,__source:s,__self:o};if(typeof t=="function"&&(i=t.defaultProps))for(a in i)l[a]===void 0&&(l[a]=i[a]);return S.vnode&&S.vnode(c),c}const Is={}.hasOwnProperty;function Ke(...t){const e=[];return t.forEach(r=>{if(r){if(typeof r=="string"||typeof r=="number")e.push(r.toString());else if(Array.isArray(r)){const n=Ke(...r);n&&e.push(n)}else if(typeof r=="object"){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]"))return void e.push(r.toString());Object.keys(r).forEach(n=>{Is.call(r,n)&&r[n]&&e.push(n)})}}}),Array.from(new Set(e)).join(" ")}const Mt="-",Ts=t=>{const e=js(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=t;return{getClassGroupId:i=>{const a=i.split(Mt);return a[0]===""&&a.length!==1&&a.shift(),xn(a,e)||As(i)},getConflictingClassGroupIds:(i,a)=>{const l=r[i]||[];return a&&n[i]?[...l,...n[i]]:l}}},xn=(t,e)=>{if(t.length===0)return e.classGroupId;const r=t[0],n=e.nextPart.get(r),s=n?xn(t.slice(1),n):void 0;if(s)return s;if(e.validators.length===0)return;const o=t.join(Mt);return e.validators.find(({validator:i})=>i(o))?.classGroupId},Sr=/^\[(.+)\]$/,As=t=>{if(Sr.test(t)){const e=Sr.exec(t)[1],r=e?.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},js=t=>{const{theme:e,classGroups:r}=t,n={nextPart:new Map,validators:[]};for(const s in r)gt(r[s],n,s,e);return n},gt=(t,e,r,n)=>{t.forEach(s=>{if(typeof s=="string"){const o=s===""?e:kr(e,s);o.classGroupId=r;return}if(typeof s=="function"){if(Fs(s)){gt(s(n),e,r,n);return}e.validators.push({validator:s,classGroupId:r});return}Object.entries(s).forEach(([o,i])=>{gt(i,kr(e,o),r,n)})})},kr=(t,e)=>{let r=t;return e.split(Mt).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},Fs=t=>t.isThemeGetter,Ms=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,r=new Map,n=new Map;const s=(o,i)=>{r.set(o,i),e++,e>t&&(e=0,n=r,r=new Map)};return{get(o){let i=r.get(o);if(i!==void 0)return i;if((i=n.get(o))!==void 0)return s(o,i),i},set(o,i){r.has(o)?r.set(o,i):s(o,i)}}},_t="!",mt=":",Ds=mt.length,Vs=t=>{const{prefix:e,experimentalParseClassName:r}=t;let n=s=>{const o=[];let i=0,a=0,l=0,c;for(let h=0;h<s.length;h++){let b=s[h];if(i===0&&a===0){if(b===mt){o.push(s.slice(l,h)),l=h+Ds;continue}if(b==="/"){c=h;continue}}b==="["?i++:b==="]"?i--:b==="("?a++:b===")"&&a--}const d=o.length===0?s:s.substring(l),u=zs(d),p=u!==d,f=c&&c>l?c-l:void 0;return{modifiers:o,hasImportantModifier:p,baseClassName:u,maybePostfixModifierPosition:f}};if(e){const s=e+mt,o=n;n=i=>i.startsWith(s)?o(i.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(r){const s=n;n=o=>r({className:o,parseClassName:s})}return n},zs=t=>t.endsWith(_t)?t.substring(0,t.length-1):t.startsWith(_t)?t.substring(1):t,Us=t=>{const e=Object.fromEntries(t.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const s=[];let o=[];return n.forEach(i=>{i[0]==="["||e[i]?(s.push(...o.sort(),i),o=[]):o.push(i)}),s.push(...o.sort()),s}},Hs=t=>({cache:Ms(t.cacheSize),parseClassName:Vs(t),sortModifiers:Us(t),...Ts(t)}),Ks=/\s+/,Bs=(t,e)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:s,sortModifiers:o}=e,i=[],a=t.trim().split(Ks);let l="";for(let c=a.length-1;c>=0;c-=1){const d=a[c],{isExternal:u,modifiers:p,hasImportantModifier:f,baseClassName:h,maybePostfixModifierPosition:b}=r(d);if(u){l=d+(l.length>0?" "+l:l);continue}let m=!!b,v=n(m?h.substring(0,b):h);if(!v){if(!m){l=d+(l.length>0?" "+l:l);continue}if(v=n(h),!v){l=d+(l.length>0?" "+l:l);continue}m=!1}const y=o(p).join(":"),P=f?y+_t:y,w=P+v;if(i.includes(w))continue;i.push(w);const L=s(v,m);for(let k=0;k<L.length;++k){const $=L[k];i.push(P+$)}l=d+(l.length>0?" "+l:l)}return l};function Ws(){let t=0,e,r,n="";for(;t<arguments.length;)(e=arguments[t++])&&(r=wn(e))&&(n&&(n+=" "),n+=r);return n}const wn=t=>{if(typeof t=="string")return t;let e,r="";for(let n=0;n<t.length;n++)t[n]&&(e=wn(t[n]))&&(r&&(r+=" "),r+=e);return r};function Gs(t,...e){let r,n,s,o=i;function i(l){const c=e.reduce((d,u)=>u(d),t());return r=Hs(c),n=r.cache.get,s=r.cache.set,o=a,a(l)}function a(l){const c=n(l);if(c)return c;const d=Bs(l,r);return s(l,d),d}return function(){return o(Ws.apply(null,arguments))}}const M=t=>{const e=r=>r[t]||[];return e.isThemeGetter=!0,e},Sn=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,kn=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Js=/^\d+\/\d+$/,qs=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ys=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Qs=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Zs=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Xs=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,he=t=>Js.test(t),O=t=>!!t&&!Number.isNaN(Number(t)),ie=t=>!!t&&Number.isInteger(Number(t)),Cr=t=>t.endsWith("%")&&O(t.slice(0,-1)),oe=t=>qs.test(t),eo=()=>!0,to=t=>Ys.test(t)&&!Qs.test(t),Dt=()=>!1,ro=t=>Zs.test(t),no=t=>Xs.test(t),so=t=>!g(t)&&!_(t),oo=t=>ve(t,Ln,Dt),g=t=>Sn.test(t),ae=t=>ve(t,Rn,to),nt=t=>ve(t,mo,O),io=t=>ve(t,Cn,Dt),ao=t=>ve(t,On,no),lo=t=>ve(t,Dt,ro),_=t=>kn.test(t),ze=t=>xe(t,Rn),co=t=>xe(t,bo),uo=t=>xe(t,Cn),fo=t=>xe(t,Ln),po=t=>xe(t,On),ho=t=>xe(t,yo,!0),ve=(t,e,r)=>{const n=Sn.exec(t);return n?n[1]?e(n[1]):r(n[2]):!1},xe=(t,e,r=!1)=>{const n=kn.exec(t);return n?n[1]?e(n[1]):r:!1},Cn=t=>t==="position",go=new Set(["image","url"]),On=t=>go.has(t),_o=new Set(["length","size","percentage"]),Ln=t=>_o.has(t),Rn=t=>t==="length",mo=t=>t==="number",bo=t=>t==="family-name",yo=t=>t==="shadow",vo=()=>{const t=M("color"),e=M("font"),r=M("text"),n=M("font-weight"),s=M("tracking"),o=M("leading"),i=M("breakpoint"),a=M("container"),l=M("spacing"),c=M("radius"),d=M("shadow"),u=M("inset-shadow"),p=M("drop-shadow"),f=M("blur"),h=M("perspective"),b=M("aspect"),m=M("ease"),v=M("animate"),y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],P=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],w=()=>["auto","hidden","clip","visible","scroll"],L=()=>["auto","contain","none"],k=()=>[he,"px","full","auto",_,g,l],$=()=>[ie,"none","subgrid",_,g],Y=()=>["auto",{span:["full",ie,_,g]},_,g],A=()=>[ie,"auto",_,g],D=()=>["auto","min","max","fr",_,g],U=()=>[_,g,l],G=()=>["start","end","center","between","around","evenly","stretch","baseline"],V=()=>["start","end","center","stretch"],C=()=>[_,g,l],B=()=>["px",...C()],T=()=>["px","auto",...C()],z=()=>[he,"auto","px","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",_,g,l],R=()=>[t,_,g],ne=()=>[Cr,ae],F=()=>["","none","full",c,_,g],N=()=>["",O,ze,ae],W=()=>["solid","dashed","dotted","double"],se=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],we=()=>["","none",f,_,g],zt=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",_,g],je=()=>["none",O,_,g],Fe=()=>["none",O,_,g],Xe=()=>[O,_,g],Me=()=>[he,"full","px",_,g,l];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[oe],breakpoint:[oe],color:[eo],container:[oe],"drop-shadow":[oe],ease:["in","out","in-out"],font:[so],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[oe],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[oe],shadow:[oe],spacing:[O],text:[oe],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",he,g,_,b]}],container:["container"],columns:[{columns:[O,g,_,a]}],"break-after":[{"break-after":y()}],"break-before":[{"break-before":y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...P(),g,_]}],overflow:[{overflow:w()}],"overflow-x":[{"overflow-x":w()}],"overflow-y":[{"overflow-y":w()}],overscroll:[{overscroll:L()}],"overscroll-x":[{"overscroll-x":L()}],"overscroll-y":[{"overscroll-y":L()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:k()}],"inset-x":[{"inset-x":k()}],"inset-y":[{"inset-y":k()}],start:[{start:k()}],end:[{end:k()}],top:[{top:k()}],right:[{right:k()}],bottom:[{bottom:k()}],left:[{left:k()}],visibility:["visible","invisible","collapse"],z:[{z:[ie,"auto",_,g]}],basis:[{basis:[he,"full","auto",_,g,a,l]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[O,he,"auto","initial","none",g]}],grow:[{grow:["",O,_,g]}],shrink:[{shrink:["",O,_,g]}],order:[{order:[ie,"first","last","none",_,g]}],"grid-cols":[{"grid-cols":$()}],"col-start-end":[{col:Y()}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":$()}],"row-start-end":[{row:Y()}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":D()}],"auto-rows":[{"auto-rows":D()}],gap:[{gap:U()}],"gap-x":[{"gap-x":U()}],"gap-y":[{"gap-y":U()}],"justify-content":[{justify:[...G(),"normal"]}],"justify-items":[{"justify-items":[...V(),"normal"]}],"justify-self":[{"justify-self":["auto",...V()]}],"align-content":[{content:["normal",...G()]}],"align-items":[{items:[...V(),"baseline"]}],"align-self":[{self:["auto",...V(),"baseline"]}],"place-content":[{"place-content":G()}],"place-items":[{"place-items":[...V(),"baseline"]}],"place-self":[{"place-self":["auto",...V()]}],p:[{p:B()}],px:[{px:B()}],py:[{py:B()}],ps:[{ps:B()}],pe:[{pe:B()}],pt:[{pt:B()}],pr:[{pr:B()}],pb:[{pb:B()}],pl:[{pl:B()}],m:[{m:T()}],mx:[{mx:T()}],my:[{my:T()}],ms:[{ms:T()}],me:[{me:T()}],mt:[{mt:T()}],mr:[{mr:T()}],mb:[{mb:T()}],ml:[{ml:T()}],"space-x":[{"space-x":C()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":C()}],"space-y-reverse":["space-y-reverse"],size:[{size:z()}],w:[{w:[a,"screen",...z()]}],"min-w":[{"min-w":[a,"screen","none",...z()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[i]},...z()]}],h:[{h:["screen",...z()]}],"min-h":[{"min-h":["screen","none",...z()]}],"max-h":[{"max-h":["screen",...z()]}],"font-size":[{text:["base",r,ze,ae]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,_,nt]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Cr,g]}],"font-family":[{font:[co,g,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,_,g]}],"line-clamp":[{"line-clamp":[O,"none",_,nt]}],leading:[{leading:[_,g,o,l]}],"list-image":[{"list-image":["none",_,g]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",_,g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:R()}],"text-color":[{text:R()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...W(),"wavy"]}],"text-decoration-thickness":[{decoration:[O,"from-font","auto",_,ae]}],"text-decoration-color":[{decoration:R()}],"underline-offset":[{"underline-offset":[O,"auto",_,g]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:["px",...C()]}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",_,g]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",_,g]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...P(),uo,io]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",fo,oo]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},ie,_,g],radial:["",_,g],conic:[ie,_,g]},po,ao]}],"bg-color":[{bg:R()}],"gradient-from-pos":[{from:ne()}],"gradient-via-pos":[{via:ne()}],"gradient-to-pos":[{to:ne()}],"gradient-from":[{from:R()}],"gradient-via":[{via:R()}],"gradient-to":[{to:R()}],rounded:[{rounded:F()}],"rounded-s":[{"rounded-s":F()}],"rounded-e":[{"rounded-e":F()}],"rounded-t":[{"rounded-t":F()}],"rounded-r":[{"rounded-r":F()}],"rounded-b":[{"rounded-b":F()}],"rounded-l":[{"rounded-l":F()}],"rounded-ss":[{"rounded-ss":F()}],"rounded-se":[{"rounded-se":F()}],"rounded-ee":[{"rounded-ee":F()}],"rounded-es":[{"rounded-es":F()}],"rounded-tl":[{"rounded-tl":F()}],"rounded-tr":[{"rounded-tr":F()}],"rounded-br":[{"rounded-br":F()}],"rounded-bl":[{"rounded-bl":F()}],"border-w":[{border:N()}],"border-w-x":[{"border-x":N()}],"border-w-y":[{"border-y":N()}],"border-w-s":[{"border-s":N()}],"border-w-e":[{"border-e":N()}],"border-w-t":[{"border-t":N()}],"border-w-r":[{"border-r":N()}],"border-w-b":[{"border-b":N()}],"border-w-l":[{"border-l":N()}],"divide-x":[{"divide-x":N()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":N()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...W(),"hidden","none"]}],"divide-style":[{divide:[...W(),"hidden","none"]}],"border-color":[{border:R()}],"border-color-x":[{"border-x":R()}],"border-color-y":[{"border-y":R()}],"border-color-s":[{"border-s":R()}],"border-color-e":[{"border-e":R()}],"border-color-t":[{"border-t":R()}],"border-color-r":[{"border-r":R()}],"border-color-b":[{"border-b":R()}],"border-color-l":[{"border-l":R()}],"divide-color":[{divide:R()}],"outline-style":[{outline:[...W(),"none","hidden"]}],"outline-offset":[{"outline-offset":[O,_,g]}],"outline-w":[{outline:["",O,ze,ae]}],"outline-color":[{outline:[t]}],shadow:[{shadow:["","none",d,ho,lo]}],"shadow-color":[{shadow:R()}],"inset-shadow":[{"inset-shadow":["none",_,g,u]}],"inset-shadow-color":[{"inset-shadow":R()}],"ring-w":[{ring:N()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:R()}],"ring-offset-w":[{"ring-offset":[O,ae]}],"ring-offset-color":[{"ring-offset":R()}],"inset-ring-w":[{"inset-ring":N()}],"inset-ring-color":[{"inset-ring":R()}],opacity:[{opacity:[O,_,g]}],"mix-blend":[{"mix-blend":[...se(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":se()}],filter:[{filter:["","none",_,g]}],blur:[{blur:we()}],brightness:[{brightness:[O,_,g]}],contrast:[{contrast:[O,_,g]}],"drop-shadow":[{"drop-shadow":["","none",p,_,g]}],grayscale:[{grayscale:["",O,_,g]}],"hue-rotate":[{"hue-rotate":[O,_,g]}],invert:[{invert:["",O,_,g]}],saturate:[{saturate:[O,_,g]}],sepia:[{sepia:["",O,_,g]}],"backdrop-filter":[{"backdrop-filter":["","none",_,g]}],"backdrop-blur":[{"backdrop-blur":we()}],"backdrop-brightness":[{"backdrop-brightness":[O,_,g]}],"backdrop-contrast":[{"backdrop-contrast":[O,_,g]}],"backdrop-grayscale":[{"backdrop-grayscale":["",O,_,g]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[O,_,g]}],"backdrop-invert":[{"backdrop-invert":["",O,_,g]}],"backdrop-opacity":[{"backdrop-opacity":[O,_,g]}],"backdrop-saturate":[{"backdrop-saturate":[O,_,g]}],"backdrop-sepia":[{"backdrop-sepia":["",O,_,g]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":C()}],"border-spacing-x":[{"border-spacing-x":C()}],"border-spacing-y":[{"border-spacing-y":C()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",_,g]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[O,"initial",_,g]}],ease:[{ease:["linear","initial",m,_,g]}],delay:[{delay:[O,_,g]}],animate:[{animate:["none",v,_,g]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,_,g]}],"perspective-origin":[{"perspective-origin":zt()}],rotate:[{rotate:je()}],"rotate-x":[{"rotate-x":je()}],"rotate-y":[{"rotate-y":je()}],"rotate-z":[{"rotate-z":je()}],scale:[{scale:Fe()}],"scale-x":[{"scale-x":Fe()}],"scale-y":[{"scale-y":Fe()}],"scale-z":[{"scale-z":Fe()}],"scale-3d":["scale-3d"],skew:[{skew:Xe()}],"skew-x":[{"skew-x":Xe()}],"skew-y":[{"skew-y":Xe()}],transform:[{transform:[_,g,"","none","gpu","cpu"]}],"transform-origin":[{origin:zt()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Me()}],"translate-x":[{"translate-x":Me()}],"translate-y":[{"translate-y":Me()}],"translate-z":[{"translate-z":Me()}],"translate-none":["translate-none"],accent:[{accent:R()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:R()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",_,g]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",_,g]}],fill:[{fill:["none",...R()]}],"stroke-w":[{stroke:[O,ze,ae,nt]}],stroke:[{stroke:["none",...R()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}},st=Gs(vo),Vt=fe(({IconSvg:t,width:e,height:r,viewBox:n,style:s,containerStyle:o,className:i="",containerClassName:a=""},l)=>ee("div",{ref:l,...a&&{className:a},...o&&{style:o},children:ee(t,{...e&&{width:e},...r&&{height:r},...n&&{viewBox:n},...i&&{className:i},...s&&{style:s}})}));var Or,Lr;function bt(){return bt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},bt.apply(null,arguments)}var xo=function(t){return j("svg",bt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"currentColor"},t),Or||(Or=j("path",{fill:"inherit",fillRule:"evenodd",d:"M5.315 5.315a1.08 1.08 0 0 1 1.523 0l11.847 11.847a1.077 1.077 0 0 1-1.523 1.523L5.315 6.838a1.077 1.077 0 0 1 0-1.523",clipRule:"evenodd"})),Lr||(Lr=j("path",{fill:"inherit",fillRule:"evenodd",d:"M18.685 5.315c.42.421.42 1.103 0 1.523L6.838 18.685a1.077 1.077 0 0 1-1.523-1.523L17.162 5.315c.42-.42 1.102-.42 1.523 0",clipRule:"evenodd"})))};const Fo=fe((t,e)=>ee(Vt,{ref:e,IconSvg:xo,...t}));var Rr,Pr,Nr;function yt(){return yt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},yt.apply(null,arguments)}var wo=function(t){return j("svg",yt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"currentColor"},t),Rr||(Rr=j("path",{fill:"inherit",fillRule:"evenodd",d:"M12 20a8 8 0 1 1 0-16 8 8 0 0 1 0 16M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10S17.523 2 12 2 2 6.477 2 12",clipRule:"evenodd"})),Pr||(Pr=j("path",{fill:"inherit",fillRule:"evenodd",d:"M12 7a1 1 0 0 1 1 1v4a1 1 0 1 1-2 0V8a1 1 0 0 1 1-1",clipRule:"evenodd"})),Nr||(Nr=j("circle",{cx:12,cy:16,r:1,fill:"inherit"})))};const Mo=fe((t,e)=>ee(Vt,{ref:e,IconSvg:wo,...t}));var Er;function vt(){return vt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},vt.apply(null,arguments)}var So=function(t){return j("svg",vt({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"currentColor"},t),Er||(Er=j("path",{fill:"inherit",fillRule:"evenodd",d:"M20 11.955a7.96 7.96 0 0 0-1.326-4.413c-.292-.44-.27-1.034.106-1.403.414-.406 1.09-.382 1.421.093A9.95 9.95 0 0 1 22 11.955c0 4.931-3.569 9.028-8.264 9.85-.574.1-1.07-.366-1.07-.949 0-.524.406-.955.92-1.058A8 8 0 0 0 20 11.955M5.894 18.643c.409-.402.39-1.062.018-1.498a8.001 8.001 0 0 1 5.09-13.128c.548-.068.998-.51.998-1.062s-.449-1.005-.999-.95A10 10 0 0 0 2 11.954c0 2.566.966 4.906 2.555 6.677.354.394.96.381 1.339.01Z",clipRule:"evenodd"})))};const ko=fe((t,e)=>ee(Vt,{ref:e,IconSvg:So,...t})),Do=fe(({type:t="default",size:e="default",disabled:r=!1,loading:n=!1,block:s=!1,htmlType:o="button",href:i,target:a="_self",icon:l,children:c,className:d="",style:u,onClick:p,...f},h)=>{const b=J(()=>t==="link"||t==="text",[t]),m=J(()=>!c&&!!l,[c,l]),v=J(()=>!!c&&!!l,[c,l]),y=Te($=>{p?.($)},[p]),P=i?"a":"button",w=J(()=>({"scale-[0.67]":e==="small","scale-[0.75]":e==="middle"||e==="default","scale-100":e==="large"||e==="xLarge"}),[e]),L=J(()=>({"scale-[0.67]":e==="small"&&m,"scale-100":(e==="middle"||e==="default")&&m,"scale-[1.8]":e==="large"&&m,"scale-[2]":e==="xLarge"&&m}),[m,e]),k=J(()=>({"scale-[0.67]":e==="small"&&v,"scale-[0.75]":(e==="middle"||e==="default")&&v,"scale-100":(e==="large"||e==="xLarge")&&v}),[v,e]);return ee(P,{ref:h,...f,className:st(Ke("flex items-center justify-center gap-x-0.5",{"border-transparent":!0,"bg-brand-tertiary-base text-font-overlay dark:bg-neutral-0 dark:text-font-primary":t==="primary","bg-error-default text-white":t==="danger","bg-transparent text-font-primary dark:text-font-overlay":t==="text","bg-brand-primary-base text-font-primary":t==="highlight","border-2 border-solid border-brand-tertiary-base bg-transparent text-font-primary dark:border-font-overlay dark:text-font-overlay":t==="stroke","bg-transparent text-link-default dark:text-brand-secondary-secondary":t==="link","bg-brand-tertiary-container text-font-primary dark:bg-font-secondary dark:text-font-overlay":t==="secondary"||t==="default","hover:bg-font-secondary dark:hover:bg-neutral-3":t==="primary","hover:bg-error-disabled":t==="danger","hover:bg-brand-primary-container":t==="highlight","hover:text-brand-secondary-secondary dark:hover:text-brand-secondary-container":t==="link","dark:hover:border-neutral-3 dark:hover:bg-neutral-3":t==="stroke","hover:bg-surface-tertiary dark:hover:bg-neutral-6":t==="text"||t==="stroke"||t==="secondary"||t==="default","active:bg-font-secondary dark:active:bg-neutral-5":t==="primary","active:bg-error-disabled":t==="danger","active:bg-surface-tertiary":t==="text","active:bg-brand-primary-secondary":t==="highlight","active:text-brand-secondary-base ":t==="link","active:bg-font-disabled dark:active:bg-neutral-5":t==="secondary"||t==="default","!text-font-disabled dark:!text-font-secondary":r,"bg-neutral-3 dark:!bg-neutral-8":(t==="default"||t==="secondary"||t==="danger"||t==="highlight"||t==="primary")&&r,"!border-font-disabled dark:!border-font-secondary":t==="stroke"&&r,"opacity-100":!n,"opacity-40":n},{"w-fit rounded-full px-sm":!b&&!m,"h-6 min-w-[4.25rem] gap-x-0":e==="small"&&!b&&!m,"h-10 min-w-20":!(e!=="middle"&&e!=="default"||b||m),"h-16 min-w-[6.25rem]":e==="large"&&!b&&!m,"h-20 min-w-[7.5rem] px-xl":e==="xLarge"&&!b&&!m,"size-fit":b,"gap-x-0 rounded-xs px-xs py-xxs":e==="small"&&b,"gap-x-0 rounded-md p-xs":(e==="middle"||e==="default")&&b,"rounded px-sm py-xs":e==="large"&&b,"rounded px-xl py-xxs":e==="xLarge"&&b,"rounded-full":m,"p-xs":(e==="middle"||e==="default")&&m,"p-5":e==="large"&&m,"p-6":e==="xLarge"&&m},{"text-center text-xs font-semibold":!0,"!text-sm":e==="middle"||e==="default","!text-base":e==="large","!text-xl":e==="xLarge"},{"!w-full":s},{"cursor-pointer":!r&&!n,"cursor-not-allowed":r,"cursor-wait":n}),d),disabled:r,type:o,...!!i&&{href:i,target:a},...!r&&p&&{onClick:y},...u&&{style:u},children:[n&&ee("div",{className:"animate-spin","data-testid":"g123-btn-loading-spin",children:ee(ko,{className:st(Ke(w,L,k,l&&He.isValidElement(l)?l.props.className:""))})}),l&&He.isValidElement(l)&&!n&&He.cloneElement(l,{className:st(Ke(L,k,l.props.className))}),c&&ee("span",{className:"truncate",children:c})]})});function Vo(t){return{render:function(e){At(e,t)},unmount:function(){Ft(t)}}}const $r={en:{translation:()=>H(()=>import("./psp-81b3dea9-BQ756E0T.js"),[])},ja:{translation:()=>H(()=>import("./psp-03cd3bbf-C5M548y0.js"),[])},"zh-TW":{translation:()=>H(()=>import("./psp-768fee49-pltAIi18.js"),[])},"zh-CN":{translation:()=>H(()=>import("./psp-e832736d-D6zRb7sy.js"),[])},ko:{translation:()=>H(()=>import("./psp-1a56043d-hpSjsVcP.js"),[])},es:{translation:()=>H(()=>import("./psp-2a520b46-CdMe8gh9.js"),[])},it:{translation:()=>H(()=>import("./psp-7a9ad24a-CchzxQth.js"),[])},fr:{translation:()=>H(()=>import("./psp-b969a660-2OPMZ33t.js"),[])},nl:{translation:()=>H(()=>import("./psp-ce0583d2-DjUEREOB.js"),[])},pt:{translation:()=>H(()=>import("./psp-0b58101c-I1m7e-AQ.js"),[])},de:{translation:()=>H(()=>import("./psp-fc500ecb-DHiCQ4kz.js"),[])},th:{translation:()=>H(()=>import("./psp-6f1a139a-BZffqHa7.js"),[])},id:{translation:()=>H(()=>import("./psp-c47ef961-D6RtVjt-.js"),[])},vi:{translation:()=>H(()=>import("./psp-3af34f6a-I8Ftyuz1.js"),[])},tl:{translation:()=>H(()=>import("./psp-e1e98e85-BqjDgilA.js"),[])}};async function zo(t,e){const r={[t]:$r[t],[e]:$r[e]},n={},s=[];for(const o of Object.keys(r)){const i=r[o];if(i)for(const a of Object.keys(i)){const l=i[a],c=l().then(d=>{n[o]||(n[o]={}),n[o][a]=d}).catch(d=>{console.error(d)});s.push(c)}}return await Promise.all(s),n}export{Ie as A,fe as B,_n as C,Io as D,Vt as E,st as F,ye as G,q as H,Rs as I,Do as J,Ct as K,Fo as L,Zr as M,Ot as N,Xr as O,Mo as P,ko as Q,He as R,Rt as S,J as T,$r as U,Ro as V,Oo as W,Lt as X,j as _,Vo as a,No as b,Ao as c,be as d,To as e,Po as f,Lo as g,$o as h,K as i,Eo as j,Ls as k,zo as l,Cs as m,ce as n,Ae as o,ms as p,Te as q,Ke as r,jo as s,ys as t,ee as u,fn as v,dt as w,X as x,ue as y,Ze as z};
