@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,::backdrop,:after,:before{--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-scroll-snap-strictness:proximity;--tw-space-y-reverse:0;--tw-border-style:solid;--tw-leading:initial;--tw-font-weight:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-duration:initial;--tw-ease:initial}}}@layer base{*,::backdrop,:after,:before{border:0 solid;box-sizing:border-box;margin:0;padding:0}::file-selector-button{border:0 solid;box-sizing:border-box;margin:0;padding:0}:host,html{-webkit-text-size-adjust:100%;font-feature-settings:var(--default-font-feature-settings,normal);-webkit-tap-highlight-color:transparent;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-variation-settings:var(--default-font-variation-settings,normal);line-height:1.5;tab-size:4}body{line-height:inherit}hr{border-top-width:1px;color:inherit;height:0}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-feature-settings:var(--default-mono-font-feature-settings,normal);font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-size:1em;font-variation-settings:var(--default-mono-font-variation-settings,normal)}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{border-collapse:collapse;border-color:inherit;text-indent:0}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}menu,ol,ul{list-style:none}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{height:auto;max-width:100%}button,input,optgroup,select,textarea{font-feature-settings:inherit;background-color:#0000;border-radius:0;color:inherit;font:inherit;font-variation-settings:inherit;letter-spacing:inherit;opacity:1}::file-selector-button{font-feature-settings:inherit;background-color:#0000;border-radius:0;color:inherit;font:inherit;font-variation-settings:inherit;letter-spacing:inherit;opacity:1}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{color:currentColor;opacity:1}@supports (color:color-mix(in lab,red,red)){::placeholder{color:color-mix(in oklab,currentColor 50%,transparent)}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit,::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){appearance:button}::file-selector-button{appearance:button}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}*,::backdrop,:after,:before{border-color:var(--color-gray-200,currentColor)}::file-selector-button{border-color:var(--color-gray-200,currentColor)}}@layer theme{:host,:root{--color-red-600:oklch(57.7% .245 27.325);--color-yellow-50:oklch(98.7% .026 102.212);--color-gray-200:oklch(92.8% .006 264.531);--color-gray-400:oklch(70.7% .022 261.325);--color-gray-600:oklch(44.6% .03 256.802);--color-zinc-400:oklch(70.5% .015 286.067);--color-zinc-600:oklch(44.2% .017 285.786);--color-neutral-800:oklch(26.9% 0 0);--color-black:#000;--color-white:#fff;--spacing:.25rem;--font-weight-light:300;--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--font-weight-extrabold:800;--radius-xs:.125rem;--radius-sm:.25rem;--radius-md:.375rem;--radius-lg:.5rem;--radius-xl:.75rem;--radius-3xl:1.5rem;--ease-out:cubic-bezier(0,0,.2,1);--animate-spin:spin 1s linear infinite;--animate-pulse:pulse 1.4s infinite ease-in-out both;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-font-feature-settings:var(--font-sans--font-feature-settings);--default-font-variation-settings:var(--font-sans--font-variation-settings);--default-mono-font-family:var(--font-mono);--default-mono-font-feature-settings:var(--font-mono--font-feature-settings);--default-mono-font-variation-settings:var(--font-mono--font-variation-settings);--font-sans:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;--text-xxs:.625rem;--text-xs:.75rem;--text-sm:.875rem;--text-base:1rem;--text-lg:1.125rem;--text-xl:1.25rem;--text-2xl:1.5rem;--spacing-xxs:.25rem;--spacing-xs:.5rem;--spacing-sm:.75rem;--spacing-lg:1.5rem;--spacing-xl:2rem;--color-primary:#136c72;--color-danger:#ff385c;--color-brand-primary-base:#e3ff34;--color-brand-primary-secondary:#d2ef1c;--color-brand-primary-container:#f1ff99;--color-brand-secondary-base:#136c72;--color-brand-secondary-secondary:#89b5b8;--color-brand-secondary-container:#d9ede2;--color-brand-secondary-bg:#eef6ef;--color-brand-tertiary-base:#262626;--color-brand-tertiary-container:#f0f0f0;--color-font-primary:#262626;--color-font-secondary:#595959;--color-font-disabled:#d9d9d9;--color-font-overlay:#fff;--color-link-default:#136c72;--color-link-disabled:#89b5b8;--color-error-default:#f6375a;--color-error-disabled:#faaebb;--color-info-default:#fad511;--color-success-default:#61c630;--color-surface-primary:#fff;--color-surface-tertiary:#f5f5f5;--color-line-divider:#f0f0f0;--color-neutral-0:#fff;--color-neutral-3:#f0f0f0;--color-neutral-5:#bfbfbf;--color-neutral-6:#8c8c8c;--color-neutral-7:#595959;--color-neutral-8:#434343;--color-neutral-9:#262626;--animate-fade-in-bottom:fade-in-bottom .4s ease-out forwards;--animate-fade-out-bottom:fade-out-bottom .4s ease-out forwards;--animate-slide-in-bottom:slide-in-bottom .4s cubic-bezier(.2,.8,.2,1);--transition-property-height:height;--color-character-primary:#222;--color-character-secondary:#666;--color-brand-green:#136c72;--color-brand-gray-100:#eeeef0}}@layer components;@layer utilities{.psp-scope .pointer-events-auto{pointer-events:auto}.psp-scope .invisible{visibility:hidden}.psp-scope .visible{visibility:visible}.psp-scope .sr-only{clip:rect(0,0,0,0);border-width:0;height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}.psp-scope .absolute{position:absolute}.psp-scope .fixed{position:fixed}.psp-scope .relative{position:relative}.psp-scope .static{position:static}.psp-scope .inset-0{inset:calc(var(--spacing)*0)}.psp-scope .inset-\[3px\]{inset:3px}.psp-scope .inset-x-0{inset-inline:calc(var(--spacing)*0)}.psp-scope .inset-y-0{inset-block:calc(var(--spacing)*0)}.psp-scope .-top-4{top:calc(var(--spacing)*-4)}.psp-scope .top-0{top:calc(var(--spacing)*0)}.psp-scope .top-0\.5{top:calc(var(--spacing)*.5)}.psp-scope .top-1\/2{top:50%}.psp-scope .top-2{top:calc(var(--spacing)*2)}.psp-scope .top-4{top:calc(var(--spacing)*4)}.psp-scope .top-\[-5px\]{top:-5px}.psp-scope .top-\[3px\]{top:3px}.psp-scope .top-\[82px\]\!{top:82px!important}.psp-scope .-right-0\.5{right:calc(var(--spacing)*-.5)}.psp-scope .right-0{right:calc(var(--spacing)*0)}.psp-scope .right-0\.5{right:calc(var(--spacing)*.5)}.psp-scope .right-1{right:calc(var(--spacing)*1)}.psp-scope .right-2\.5{right:calc(var(--spacing)*2.5)}.psp-scope .right-4{right:calc(var(--spacing)*4)}.psp-scope .right-6{right:calc(var(--spacing)*6)}.psp-scope .right-\[3px\]{right:3px}.psp-scope .-bottom-3{bottom:calc(var(--spacing)*-3)}.psp-scope .bottom-0{bottom:calc(var(--spacing)*0)}.psp-scope .bottom-2{bottom:calc(var(--spacing)*2)}.psp-scope .bottom-5{bottom:calc(var(--spacing)*5)}.psp-scope .-left-1{left:calc(var(--spacing)*-1)}.psp-scope .left-0{left:calc(var(--spacing)*0)}.psp-scope .left-0\.5{left:calc(var(--spacing)*.5)}.psp-scope .left-1\/2{left:50%}.psp-scope .left-6{left:calc(var(--spacing)*6)}.psp-scope .left-\[3px\]{left:3px}.psp-scope .-z-10{z-index:-10}.psp-scope .z-10{z-index:10}.psp-scope .z-20{z-index:20}.psp-scope .z-40{z-index:40}.psp-scope .z-50{z-index:50}.psp-scope .container{width:100%}@media (min-width:40rem){.psp-scope .container{max-width:40rem}}@media (min-width:48rem){.psp-scope .container{max-width:48rem}}@media (min-width:64rem){.psp-scope .container{max-width:64rem}}@media (min-width:80rem){.psp-scope .container{max-width:80rem}}@media (min-width:96rem){.psp-scope .container{max-width:96rem}}.psp-scope .-m-px{margin:-1px}.psp-scope .m-0{margin:calc(var(--spacing)*0)}.psp-scope .m-auto{margin:auto}.psp-scope .mx-0{margin-inline:calc(var(--spacing)*0)}.psp-scope .mx-1{margin-inline:calc(var(--spacing)*1)}.psp-scope .mx-2{margin-inline:calc(var(--spacing)*2)}.psp-scope .mx-3{margin-inline:calc(var(--spacing)*3)}.psp-scope .mx-6{margin-inline:calc(var(--spacing)*6)}.psp-scope .mx-auto{margin-inline:auto}.psp-scope .my-0{margin-block:calc(var(--spacing)*0)}.psp-scope .my-3{margin-block:calc(var(--spacing)*3)}.psp-scope .my-4{margin-block:calc(var(--spacing)*4)}.psp-scope .my-5{margin-block:calc(var(--spacing)*5)}.psp-scope .my-auto{margin-block:auto}.psp-scope .mt-1{margin-top:calc(var(--spacing)*1)}.psp-scope .mt-3{margin-top:calc(var(--spacing)*3)}.psp-scope .mt-4{margin-top:calc(var(--spacing)*4)}.psp-scope .mt-5{margin-top:calc(var(--spacing)*5)}.psp-scope .mt-8{margin-top:calc(var(--spacing)*8)}.psp-scope .mt-30{margin-top:calc(var(--spacing)*30)}.psp-scope .mr-1\.5{margin-right:calc(var(--spacing)*1.5)}.psp-scope .mr-2{margin-right:calc(var(--spacing)*2)}.psp-scope .mr-4{margin-right:calc(var(--spacing)*4)}.psp-scope .mb-1\.5{margin-bottom:calc(var(--spacing)*1.5)}.psp-scope .mb-2{margin-bottom:calc(var(--spacing)*2)}.psp-scope .mb-3{margin-bottom:calc(var(--spacing)*3)}.psp-scope .mb-4{margin-bottom:calc(var(--spacing)*4)}.psp-scope .mb-5{margin-bottom:calc(var(--spacing)*5)}.psp-scope .mb-6{margin-bottom:calc(var(--spacing)*6)}.psp-scope .-ml-2{margin-left:calc(var(--spacing)*-2)}.psp-scope .ml-0\.5{margin-left:calc(var(--spacing)*.5)}.psp-scope .ml-4{margin-left:calc(var(--spacing)*4)}.psp-scope .ml-auto{margin-left:auto}.psp-scope .box-border{box-sizing:border-box}.psp-scope .box-content{box-sizing:content-box}.psp-scope .hidden-scrollbar{-ms-overflow-style:none;scrollbar-width:none}.psp-scope .hidden-scrollbar::-webkit-scrollbar{display:none}.psp-scope .block{display:block}.psp-scope .contents{display:contents}.psp-scope .flex{display:flex}.psp-scope .grid{display:grid}.psp-scope .hidden{display:none}.psp-scope .inline{display:inline}.psp-scope .inline-block{display:inline-block}.psp-scope .inline-flex{display:inline-flex}.psp-scope .table{display:table}.psp-scope .size-3{height:calc(var(--spacing)*3);width:calc(var(--spacing)*3)}.psp-scope .size-4{height:calc(var(--spacing)*4);width:calc(var(--spacing)*4)}.psp-scope .size-5{height:calc(var(--spacing)*5);width:calc(var(--spacing)*5)}.psp-scope .size-6{height:calc(var(--spacing)*6);width:calc(var(--spacing)*6)}.psp-scope .size-8{height:calc(var(--spacing)*8);width:calc(var(--spacing)*8)}.psp-scope .size-10{height:calc(var(--spacing)*10);width:calc(var(--spacing)*10)}.psp-scope .size-15{height:calc(var(--spacing)*15);width:calc(var(--spacing)*15)}.psp-scope .size-24{height:calc(var(--spacing)*24);width:calc(var(--spacing)*24)}.psp-scope .size-\[0\.4375rem\]{height:.4375rem;width:.4375rem}.psp-scope .size-\[1\.125rem\]{height:1.125rem;width:1.125rem}.psp-scope .size-\[90px\]{height:90px;width:90px}.psp-scope .size-fit{height:fit-content;width:fit-content}.psp-scope .size-full{height:100%;width:100%}.psp-scope .size-px{height:1px;width:1px}.psp-scope .h-0{height:calc(var(--spacing)*0)}.psp-scope .h-1{height:calc(var(--spacing)*1)}.psp-scope .h-3{height:calc(var(--spacing)*3)}.psp-scope .h-4{height:calc(var(--spacing)*4)}.psp-scope .h-5{height:calc(var(--spacing)*5)}.psp-scope .h-6{height:calc(var(--spacing)*6)}.psp-scope .h-8{height:calc(var(--spacing)*8)}.psp-scope .h-9{height:calc(var(--spacing)*9)}.psp-scope .h-10{height:calc(var(--spacing)*10)}.psp-scope .h-11{height:calc(var(--spacing)*11)}.psp-scope .h-12{height:calc(var(--spacing)*12)}.psp-scope .h-14{height:calc(var(--spacing)*14)}.psp-scope .h-15{height:calc(var(--spacing)*15)}.psp-scope .h-16{height:calc(var(--spacing)*16)}.psp-scope .h-20{height:calc(var(--spacing)*20)}.psp-scope .h-56{height:calc(var(--spacing)*56)}.psp-scope .h-\[38rem\]{height:38rem}.psp-scope .h-fit{height:fit-content}.psp-scope .h-full{height:100%}.psp-scope .h-screen{height:100vh}.psp-scope .max-h-16{max-height:calc(var(--spacing)*16)}.psp-scope .min-h-10{min-height:calc(var(--spacing)*10)}.psp-scope .min-h-fit{min-height:fit-content}.psp-scope .min-h-screen{min-height:100vh}.psp-scope .\!w-full{width:100%!important}.psp-scope .w-1\/2{width:50%}.psp-scope .w-4{width:calc(var(--spacing)*4)}.psp-scope .w-5{width:calc(var(--spacing)*5)}.psp-scope .w-6{width:calc(var(--spacing)*6)}.psp-scope .w-7{width:calc(var(--spacing)*7)}.psp-scope .w-8{width:calc(var(--spacing)*8)}.psp-scope .w-9{width:calc(var(--spacing)*9)}.psp-scope .w-11{width:calc(var(--spacing)*11)}.psp-scope .w-12{width:calc(var(--spacing)*12)}.psp-scope .w-14{width:calc(var(--spacing)*14)}.psp-scope .w-16{width:calc(var(--spacing)*16)}.psp-scope .w-24{width:calc(var(--spacing)*24)}.psp-scope .w-96{width:calc(var(--spacing)*96)}.psp-scope .w-180{width:calc(var(--spacing)*180)}.psp-scope .w-fit{width:fit-content}.psp-scope .w-full{width:100%}.psp-scope .w-screen{width:100vw}.psp-scope .max-w-\[4rem\]{max-width:4rem}.psp-scope .max-w-\[768px\]{max-width:768px}.psp-scope .max-w-\[calc\(100\%-3rem\)\]{max-width:calc(100% - 3rem)}.psp-scope .max-w-full{max-width:100%}.psp-scope .max-w-max{max-width:max-content}.psp-scope .min-w-0{min-width:calc(var(--spacing)*0)}.psp-scope .min-w-20{min-width:calc(var(--spacing)*20)}.psp-scope .min-w-64{min-width:calc(var(--spacing)*64)}.psp-scope .min-w-\[4\.25rem\]{min-width:4.25rem}.psp-scope .min-w-\[6\.25rem\]{min-width:6.25rem}.psp-scope .min-w-\[7\.5rem\]{min-width:7.5rem}.psp-scope .min-w-\[375px\]{min-width:375px}.psp-scope .min-w-fit{min-width:fit-content}.psp-scope .flex-1{flex:1}.psp-scope .flex-none{flex:none}.psp-scope .shrink-0{flex-shrink:0}.psp-scope .flex-grow,.psp-scope .grow{flex-grow:1}.psp-scope .grow-0{flex-grow:0}.psp-scope .-translate-x-1\/2{--tw-translate-x:-50%;translate:var(--tw-translate-x)var(--tw-translate-y)}.psp-scope .-translate-y-1\/2{--tw-translate-y:-50%;translate:var(--tw-translate-x)var(--tw-translate-y)}.psp-scope .translate-y-0{--tw-translate-y:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.psp-scope .translate-y-full{--tw-translate-y:100%;translate:var(--tw-translate-x)var(--tw-translate-y)}.psp-scope .scale-0{--tw-scale-x:0%;--tw-scale-y:0%;--tw-scale-z:0%;scale:var(--tw-scale-x)var(--tw-scale-y)}.psp-scope .scale-50{--tw-scale-x:50%;--tw-scale-y:50%;--tw-scale-z:50%;scale:var(--tw-scale-x)var(--tw-scale-y)}.psp-scope .scale-75{--tw-scale-x:75%;--tw-scale-y:75%;--tw-scale-z:75%;scale:var(--tw-scale-x)var(--tw-scale-y)}.psp-scope .scale-100{--tw-scale-x:100%;--tw-scale-y:100%;--tw-scale-z:100%;scale:var(--tw-scale-x)var(--tw-scale-y)}.psp-scope .scale-\[0\.4\]{scale:.4}.psp-scope .scale-\[0\.5\]{scale:.5}.psp-scope .scale-\[0\.8\]{scale:.8}.psp-scope .scale-\[0\.55\]{scale:.55}.psp-scope .scale-\[0\.65\]{scale:.65}.psp-scope .scale-\[0\.67\]{scale:.67}.psp-scope .scale-\[0\.75\]{scale:.75}.psp-scope .scale-\[0\.85\]{scale:.85}.psp-scope .scale-\[1\.8\]{scale:1.8}.psp-scope .scale-\[2\.5\]{scale:2.5}.psp-scope .scale-\[2\]{scale:2}.psp-scope .transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.psp-scope .animate-fade-in-bottom{animation:var(--animate-fade-in-bottom)}.psp-scope .animate-fade-out-bottom{animation:var(--animate-fade-out-bottom)}.psp-scope .animate-pulse{animation:var(--animate-pulse)}.psp-scope .animate-slide-in-bottom{animation:var(--animate-slide-in-bottom)}.psp-scope .animate-spin{animation:var(--animate-spin)}.psp-scope .cursor-default{cursor:default}.psp-scope .cursor-help{cursor:help}.psp-scope .cursor-not-allowed{cursor:not-allowed}.psp-scope .cursor-pointer{cursor:pointer}.psp-scope .cursor-wait{cursor:wait}.psp-scope .resize-y{resize:vertical}.psp-scope .snap-x{scroll-snap-type:x var(--tw-scroll-snap-strictness)}.psp-scope .snap-mandatory{--tw-scroll-snap-strictness:mandatory}.psp-scope .snap-center{scroll-snap-align:center}.psp-scope .grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.psp-scope .grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.psp-scope .grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.psp-scope .flex-col{flex-direction:column}.psp-scope .flex-row{flex-direction:row}.psp-scope .flex-nowrap{flex-wrap:nowrap}.psp-scope .flex-wrap{flex-wrap:wrap}.psp-scope .items-center{align-items:center}.psp-scope .items-start{align-items:flex-start}.psp-scope .justify-between{justify-content:space-between}.psp-scope .justify-center{justify-content:center}.psp-scope .justify-end{justify-content:flex-end}.psp-scope .justify-start{justify-content:flex-start}.psp-scope .justify-items-center{justify-items:center}.psp-scope .gap-1{gap:calc(var(--spacing)*1)}.psp-scope .gap-1\.5{gap:calc(var(--spacing)*1.5)}.psp-scope .gap-2{gap:calc(var(--spacing)*2)}.psp-scope .gap-3{gap:calc(var(--spacing)*3)}.psp-scope .gap-4{gap:calc(var(--spacing)*4)}.psp-scope .gap-6{gap:calc(var(--spacing)*6)}:where(.psp-scope .space-y-4>:not(:last-child)){--tw-space-y-reverse:0;margin-block-end:calc(var(--spacing)*4*(1 - var(--tw-space-y-reverse)));margin-block-start:calc(var(--spacing)*4*var(--tw-space-y-reverse))}.psp-scope .gap-x-0{column-gap:calc(var(--spacing)*0)}.psp-scope .gap-x-0\.5{column-gap:calc(var(--spacing)*.5)}.psp-scope .gap-x-2{column-gap:calc(var(--spacing)*2)}.psp-scope .gap-x-10{column-gap:calc(var(--spacing)*10)}.psp-scope .gap-y-2{row-gap:calc(var(--spacing)*2)}.psp-scope .gap-y-4{row-gap:calc(var(--spacing)*4)}.psp-scope .gap-y-6{row-gap:calc(var(--spacing)*6)}.psp-scope .self-stretch{align-self:stretch}.psp-scope .truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.psp-scope .overflow-auto{overflow:auto}.psp-scope .overflow-hidden{overflow:hidden}.psp-scope .overflow-scroll{overflow:scroll}.psp-scope .overflow-x-auto{overflow-x:auto}.psp-scope .overflow-x-hidden{overflow-x:hidden}.psp-scope .overflow-y-auto{overflow-y:auto}.psp-scope .overflow-y-scroll{overflow-y:scroll}.psp-scope .scroll-smooth{scroll-behavior:smooth}.psp-scope .rounded{border-radius:.25rem}.psp-scope .rounded-3xl{border-radius:var(--radius-3xl)}.psp-scope .rounded-\[20px\]{border-radius:20px}.psp-scope .rounded-full{border-radius:3.40282e+38px}.psp-scope .rounded-lg{border-radius:var(--radius-lg)}.psp-scope .rounded-md{border-radius:var(--radius-md)}.psp-scope .rounded-sm{border-radius:var(--radius-sm)}.psp-scope .rounded-xl{border-radius:var(--radius-xl)}.psp-scope .rounded-xs{border-radius:var(--radius-xs)}.psp-scope .rounded-t-xl{border-top-left-radius:var(--radius-xl);border-top-right-radius:var(--radius-xl)}.psp-scope .rounded-b-none{border-bottom-left-radius:0;border-bottom-right-radius:0}.psp-scope .border{border-style:var(--tw-border-style);border-width:1px}.psp-scope .border-0{border-style:var(--tw-border-style);border-width:0}.psp-scope .border-2{border-style:var(--tw-border-style);border-width:2px}.psp-scope .border-4{border-style:var(--tw-border-style);border-width:4px}.psp-scope .border-8{border-style:var(--tw-border-style);border-width:8px}.psp-scope .border-\[1\.5px\]{border-style:var(--tw-border-style);border-width:1.5px}.psp-scope .border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.psp-scope .border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.psp-scope .border-l{border-left-style:var(--tw-border-style);border-left-width:1px}.psp-scope .border-dashed{--tw-border-style:dashed;border-style:dashed}.psp-scope .border-none{--tw-border-style:none;border-style:none}.psp-scope .border-solid{--tw-border-style:solid;border-style:solid}.psp-scope .\!border-font-disabled{border-color:var(--color-font-disabled)!important}.psp-scope .border-black\/10{border-color:#0000001a}@supports (color:color-mix(in lab,red,red)){.psp-scope .border-black\/10{border-color:color-mix(in oklab,var(--color-black)10%,transparent)}}.psp-scope .border-brand-gray-100{border-color:var(--color-brand-gray-100)}.psp-scope .border-brand-green{border-color:var(--color-brand-green)}.psp-scope .border-brand-tertiary-base{border-color:var(--color-brand-tertiary-base)}.psp-scope .border-font-disabled{border-color:var(--color-font-disabled)}.psp-scope .border-gray-200{border-color:var(--color-gray-200)}.psp-scope .border-line-divider{border-color:var(--color-line-divider)}.psp-scope .border-neutral-5{border-color:var(--color-neutral-5)}.psp-scope .border-primary{border-color:var(--color-primary)}.psp-scope .border-surface-primary{border-color:var(--color-surface-primary)}.psp-scope .border-transparent{border-color:#0000}.psp-scope .border-t-\[\#336A70\]{border-top-color:#336a70}.psp-scope .border-b-surface-tertiary{border-bottom-color:var(--color-surface-tertiary)}.psp-scope .\!bg-surface-primary{background-color:var(--color-surface-primary)!important}.psp-scope .bg-\[\#DFFB30\]{background-color:#dffb30}.psp-scope .bg-\[\#ECF6F1\]{background-color:#ecf6f1}.psp-scope .bg-black{background-color:var(--color-black)}.psp-scope .bg-black\/30{background-color:#0000004d}@supports (color:color-mix(in lab,red,red)){.psp-scope .bg-black\/30{background-color:color-mix(in oklab,var(--color-black)30%,transparent)}}.psp-scope .bg-black\/40{background-color:#0006}@supports (color:color-mix(in lab,red,red)){.psp-scope .bg-black\/40{background-color:color-mix(in oklab,var(--color-black)40%,transparent)}}.psp-scope .bg-brand-primary-base{background-color:var(--color-brand-primary-base)}.psp-scope .bg-brand-secondary-base{background-color:var(--color-brand-secondary-base)}.psp-scope .bg-brand-secondary-bg{background-color:var(--color-brand-secondary-bg)}.psp-scope .bg-brand-tertiary-base{background-color:var(--color-brand-tertiary-base)}.psp-scope .bg-brand-tertiary-container{background-color:var(--color-brand-tertiary-container)}.psp-scope .bg-error-default{background-color:var(--color-error-default)}.psp-scope .bg-font-disabled{background-color:var(--color-font-disabled)}.psp-scope .bg-font-secondary{background-color:var(--color-font-secondary)}.psp-scope .bg-info-default{background-color:var(--color-info-default)}.psp-scope .bg-line-divider{background-color:var(--color-line-divider)}.psp-scope .bg-neutral-3{background-color:var(--color-neutral-3)}.psp-scope .bg-neutral-5\/30{background-color:#bfbfbf4d}@supports (color:color-mix(in lab,red,red)){.psp-scope .bg-neutral-5\/30{background-color:color-mix(in oklab,var(--color-neutral-5)30%,transparent)}}.psp-scope .bg-neutral-9\/88\!{background-color:#262626e0!important}@supports (color:color-mix(in lab,red,red)){.psp-scope .bg-neutral-9\/88\!{background-color:color-mix(in oklab,var(--color-neutral-9)88%,transparent)!important}}.psp-scope .bg-primary{background-color:var(--color-primary)}.psp-scope .bg-success-default{background-color:var(--color-success-default)}.psp-scope .bg-surface-primary{background-color:var(--color-surface-primary)}.psp-scope .bg-surface-primary\/50{background-color:#ffffff80}@supports (color:color-mix(in lab,red,red)){.psp-scope .bg-surface-primary\/50{background-color:color-mix(in oklab,var(--color-surface-primary)50%,transparent)}}.psp-scope .bg-surface-tertiary{background-color:var(--color-surface-tertiary)}.psp-scope .bg-transparent{background-color:#0000}.psp-scope .bg-white{background-color:var(--color-white)}.psp-scope .bg-contain{background-size:contain}.psp-scope .bg-center{background-position:50%}.psp-scope .bg-no-repeat{background-repeat:no-repeat}.psp-scope .bg-origin-content{background-origin:content-box}.psp-scope .fill-black{fill:var(--color-black)}.psp-scope .fill-current{fill:currentColor}.psp-scope .fill-primary{fill:var(--color-primary)}.psp-scope .stroke-font-secondary{stroke:var(--color-font-secondary)}.psp-scope .object-cover{object-fit:cover}.psp-scope .\!p-0{padding:calc(var(--spacing)*0)!important}.psp-scope .p-0{padding:calc(var(--spacing)*0)}.psp-scope .p-0\!{padding:calc(var(--spacing)*0)!important}.psp-scope .p-3{padding:calc(var(--spacing)*3)}.psp-scope .p-5{padding:calc(var(--spacing)*5)}.psp-scope .p-6{padding:calc(var(--spacing)*6)}.psp-scope .p-9{padding:calc(var(--spacing)*9)}.psp-scope .p-xs{padding:var(--spacing-xs)}.psp-scope .\!px-1\.5{padding-inline:calc(var(--spacing)*1.5)!important}.psp-scope .px-0{padding-inline:calc(var(--spacing)*0)}.psp-scope .px-0\!{padding-inline:calc(var(--spacing)*0)!important}.psp-scope .px-1{padding-inline:calc(var(--spacing)*1)}.psp-scope .px-2{padding-inline:calc(var(--spacing)*2)}.psp-scope .px-3{padding-inline:calc(var(--spacing)*3)}.psp-scope .px-4{padding-inline:calc(var(--spacing)*4)}.psp-scope .px-5{padding-inline:calc(var(--spacing)*5)}.psp-scope .px-6{padding-inline:calc(var(--spacing)*6)}.psp-scope .px-8{padding-inline:calc(var(--spacing)*8)}.psp-scope .px-10{padding-inline:calc(var(--spacing)*10)}.psp-scope .px-12{padding-inline:calc(var(--spacing)*12)}.psp-scope .px-\[3px\]{padding-inline:3px}.psp-scope .px-lg{padding-inline:var(--spacing-lg)}.psp-scope .px-sm{padding-inline:var(--spacing-sm)}.psp-scope .px-xl{padding-inline:var(--spacing-xl)}.psp-scope .px-xs{padding-inline:var(--spacing-xs)}.psp-scope .\!py-2\.5{padding-block:calc(var(--spacing)*2.5)!important}.psp-scope .py-0{padding-block:calc(var(--spacing)*0)}.psp-scope .py-0\!{padding-block:calc(var(--spacing)*0)!important}.psp-scope .py-0\.5{padding-block:calc(var(--spacing)*.5)}.psp-scope .py-1{padding-block:calc(var(--spacing)*1)}.psp-scope .py-2{padding-block:calc(var(--spacing)*2)}.psp-scope .py-3{padding-block:calc(var(--spacing)*3)}.psp-scope .py-3\!{padding-block:calc(var(--spacing)*3)!important}.psp-scope .py-3\.5{padding-block:calc(var(--spacing)*3.5)}.psp-scope .py-4{padding-block:calc(var(--spacing)*4)}.psp-scope .py-5{padding-block:calc(var(--spacing)*5)}.psp-scope .py-6{padding-block:calc(var(--spacing)*6)}.psp-scope .py-px{padding-block:1px}.psp-scope .py-xs{padding-block:var(--spacing-xs)}.psp-scope .py-xxs{padding-block:var(--spacing-xxs)}.psp-scope .pt-0{padding-top:calc(var(--spacing)*0)}.psp-scope .pt-2{padding-top:calc(var(--spacing)*2)}.psp-scope .pt-3{padding-top:calc(var(--spacing)*3)}.psp-scope .pt-4{padding-top:calc(var(--spacing)*4)}.psp-scope .pt-10{padding-top:calc(var(--spacing)*10)}.psp-scope .pr-0\!{padding-right:calc(var(--spacing)*0)!important}.psp-scope .pr-2{padding-right:calc(var(--spacing)*2)}.psp-scope .pr-2\.5{padding-right:calc(var(--spacing)*2.5)}.psp-scope .pr-5{padding-right:calc(var(--spacing)*5)}.psp-scope .pb-3{padding-bottom:calc(var(--spacing)*3)}.psp-scope .pb-4{padding-bottom:calc(var(--spacing)*4)}.psp-scope .pb-5{padding-bottom:calc(var(--spacing)*5)}.psp-scope .pb-6{padding-bottom:calc(var(--spacing)*6)}.psp-scope .pb-12{padding-bottom:calc(var(--spacing)*12)}.psp-scope .pb-lg{padding-bottom:var(--spacing-lg)}.psp-scope .pl-2{padding-left:calc(var(--spacing)*2)}.psp-scope .pl-5{padding-left:calc(var(--spacing)*5)}.psp-scope .pl-12{padding-left:calc(var(--spacing)*12)}.psp-scope .text-center{text-align:center}.psp-scope .text-left{text-align:left}.psp-scope .font-\[\'Hiragino_Sans\'\]{font-family:Hiragino Sans}.psp-scope .\!text-base{font-size:var(--text-base)!important}.psp-scope .\!text-sm{font-size:var(--text-sm)!important}.psp-scope .\!text-xl{font-size:var(--text-xl)!important}.psp-scope .text-2xl{font-size:var(--text-2xl)}.psp-scope .text-base{font-size:var(--text-base)}.psp-scope .text-lg{font-size:var(--text-lg)}.psp-scope .text-sm{font-size:var(--text-sm)}.psp-scope .text-xs{font-size:var(--text-xs)}.psp-scope .text-xxs{font-size:var(--text-xxs)}.psp-scope .leading-5{--tw-leading:calc(var(--spacing)*5);line-height:calc(var(--spacing)*5)}.psp-scope .leading-6{--tw-leading:calc(var(--spacing)*6);line-height:calc(var(--spacing)*6)}.psp-scope .font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.psp-scope .font-extrabold{--tw-font-weight:var(--font-weight-extrabold);font-weight:var(--font-weight-extrabold)}.psp-scope .font-light{--tw-font-weight:var(--font-weight-light);font-weight:var(--font-weight-light)}.psp-scope .font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.psp-scope .font-normal{--tw-font-weight:var(--font-weight-normal);font-weight:var(--font-weight-normal)}.psp-scope .font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.psp-scope .break-normal{overflow-wrap:normal;word-break:normal}.psp-scope .break-words{overflow-wrap:break-word}.psp-scope .text-ellipsis{text-overflow:ellipsis}.psp-scope .whitespace-nowrap{white-space:nowrap}.psp-scope .whitespace-pre-line{white-space:pre-line}.psp-scope .whitespace-pre-wrap{white-space:pre-wrap}.psp-scope .\!text-font-disabled{color:var(--color-font-disabled)!important}.psp-scope .\!text-font-primary{color:var(--color-font-primary)!important}.psp-scope .text-\[\#3B87F7\]{color:#3b87f7}.psp-scope .text-\[\#666\]{color:#666}.psp-scope .text-\[\#999\]{color:#999}.psp-scope .text-black{color:var(--color-black)}.psp-scope .text-black\/60{color:#0009}@supports (color:color-mix(in lab,red,red)){.psp-scope .text-black\/60{color:color-mix(in oklab,var(--color-black)60%,transparent)}}.psp-scope .text-brand-tertiary-base{color:var(--color-brand-tertiary-base)}.psp-scope .text-character-primary{color:var(--color-character-primary)}.psp-scope .text-character-secondary{color:var(--color-character-secondary)}.psp-scope .text-danger{color:var(--color-danger)}.psp-scope .text-error-default{color:var(--color-error-default)}.psp-scope .text-font-disabled{color:var(--color-font-disabled)}.psp-scope .text-font-overlay{color:var(--color-font-overlay)}.psp-scope .text-font-overlay\!{color:var(--color-font-overlay)!important}.psp-scope .text-font-primary{color:var(--color-font-primary)}.psp-scope .text-font-secondary{color:var(--color-font-secondary)}.psp-scope .text-gray-400{color:var(--color-gray-400)}.psp-scope .text-gray-600{color:var(--color-gray-600)}.psp-scope .text-info-default{color:var(--color-info-default)}.psp-scope .text-inherit{color:inherit}.psp-scope .text-link-default{color:var(--color-link-default)}.psp-scope .text-link-disabled{color:var(--color-link-disabled)}.psp-scope .text-neutral-0\!{color:var(--color-neutral-0)!important}.psp-scope .text-neutral-5{color:var(--color-neutral-5)}.psp-scope .text-neutral-6{color:var(--color-neutral-6)}.psp-scope .text-neutral-800{color:var(--color-neutral-800)}.psp-scope .text-red-600{color:var(--color-red-600)}.psp-scope .text-success-default{color:var(--color-success-default)}.psp-scope .text-surface-primary{color:var(--color-surface-primary)}.psp-scope .text-transparent{color:#0000}.psp-scope .text-white{color:var(--color-white)}.psp-scope .text-zinc-400{color:var(--color-zinc-400)}.psp-scope .text-zinc-600{color:var(--color-zinc-600)}.psp-scope .uppercase{text-transform:uppercase}.psp-scope .not-italic{font-style:normal}.psp-scope .underline{text-decoration-line:underline}.psp-scope .opacity-0{opacity:0}.psp-scope .opacity-30{opacity:.3}.psp-scope .opacity-40{opacity:.4}.psp-scope .opacity-50{opacity:.5}.psp-scope .opacity-100{opacity:1}.psp-scope .shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a)}.psp-scope .shadow-lg,.psp-scope .shadow-md{box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.psp-scope .shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a)}.psp-scope .outline,.psp-scope .outline-1{outline-style:var(--tw-outline-style);outline-width:1px}.psp-scope .outline-offset-\[-1px\]{outline-offset:-1px}.psp-scope .outline-black\/20{outline-color:#0003}@supports (color:color-mix(in lab,red,red)){.psp-scope .outline-black\/20{outline-color:color-mix(in oklab,var(--color-black)20%,transparent)}}.psp-scope .blur{--tw-blur:blur(8px)}.psp-scope .blur,.psp-scope .filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.psp-scope .filter-none{filter:none}.psp-scope .transition{transition-duration:var(--tw-duration,var(--default-transition-duration));transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))}.psp-scope .transition-all{transition-duration:var(--tw-duration,var(--default-transition-duration));transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))}.psp-scope .transition-colors{transition-duration:var(--tw-duration,var(--default-transition-duration));transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))}.psp-scope .transition-height{transition-duration:var(--tw-duration,var(--default-transition-duration));transition-property:var(--transition-property-height);transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))}.psp-scope .transition-opacity{transition-duration:var(--tw-duration,var(--default-transition-duration));transition-property:opacity;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))}.psp-scope .duration-300{--tw-duration:.3s;transition-duration:.3s}.psp-scope .ease-out{--tw-ease:var(--ease-out);transition-timing-function:var(--ease-out)}.psp-scope .select-none{-webkit-user-select:none;user-select:none}.psp-scope .first\:ml-0:first-child{margin-left:calc(var(--spacing)*0)}.psp-scope .last\:mr-0:last-child{margin-right:calc(var(--spacing)*0)}.psp-scope .last\:border-b-0:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0}@media (hover:hover){.psp-scope .hover\:border-\[\#336A70\]:hover{border-color:#336a70}.psp-scope .hover\:bg-brand-primary-container:hover{background-color:var(--color-brand-primary-container)}.psp-scope .hover\:bg-error-disabled:hover{background-color:var(--color-error-disabled)}.psp-scope .hover\:bg-font-secondary:hover{background-color:var(--color-font-secondary)}.psp-scope .hover\:bg-neutral-3:hover{background-color:var(--color-neutral-3)}.psp-scope .hover\:bg-surface-tertiary:hover{background-color:var(--color-surface-tertiary)}.psp-scope .hover\:bg-yellow-50:hover{background-color:var(--color-yellow-50)}.psp-scope .hover\:bg-zinc-600:hover{background-color:var(--color-zinc-600)}.psp-scope .hover\:text-brand-secondary-secondary:hover{color:var(--color-brand-secondary-secondary)}}.psp-scope .focus\:outline-hidden:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.psp-scope .focus\:outline-hidden:focus{outline:2px solid #0000;outline-offset:2px}}.psp-scope .active\:bg-brand-primary-secondary:active{background-color:var(--color-brand-primary-secondary)}.psp-scope .active\:bg-error-disabled:active{background-color:var(--color-error-disabled)}.psp-scope .active\:bg-font-disabled:active{background-color:var(--color-font-disabled)}.psp-scope .active\:bg-font-secondary:active{background-color:var(--color-font-secondary)}.psp-scope .active\:bg-surface-tertiary:active{background-color:var(--color-surface-tertiary)}.psp-scope .active\:bg-zinc-600:active{background-color:var(--color-zinc-600)}.psp-scope .active\:text-brand-secondary-base:active{color:var(--color-brand-secondary-base)}@media (min-width:40rem){.psp-scope .sm\:mb-8{margin-bottom:calc(var(--spacing)*8)}.psp-scope .sm\:w-5\/6{width:83.3333%}.psp-scope .sm\:w-7\/12{width:58.3333%}.psp-scope .sm\:max-w-\[20rem\]{max-width:20rem}.psp-scope .sm\:max-w-\[28rem\]{max-width:28rem}.psp-scope .sm\:min-w-\[640px\]{min-width:640px}.psp-scope .sm\:grid-cols-5{grid-template-columns:repeat(5,minmax(0,1fr))}.psp-scope .sm\:gap-4{gap:calc(var(--spacing)*4)}.psp-scope .sm\:gap-8{gap:calc(var(--spacing)*8)}.psp-scope .sm\:gap-x-1{column-gap:calc(var(--spacing)*1)}.psp-scope .sm\:px-0{padding-inline:calc(var(--spacing)*0)}.psp-scope .sm\:text-sm{font-size:var(--text-sm)}}.psp-scope .dark\:\!border-font-secondary:where([data-mode=g123-dark],[data-mode=g123-dark] *){border-color:var(--color-font-secondary)!important}.psp-scope .dark\:border-font-overlay:where([data-mode=g123-dark],[data-mode=g123-dark] *){border-color:var(--color-font-overlay)}.psp-scope .dark\:\!bg-neutral-7:where([data-mode=g123-dark],[data-mode=g123-dark] *){background-color:var(--color-neutral-7)!important}.psp-scope .dark\:\!bg-neutral-8:where([data-mode=g123-dark],[data-mode=g123-dark] *){background-color:var(--color-neutral-8)!important}.psp-scope .dark\:bg-font-secondary:where([data-mode=g123-dark],[data-mode=g123-dark] *){background-color:var(--color-font-secondary)}.psp-scope .dark\:bg-neutral-0:where([data-mode=g123-dark],[data-mode=g123-dark] *){background-color:var(--color-neutral-0)}.psp-scope .dark\:bg-neutral-7:where([data-mode=g123-dark],[data-mode=g123-dark] *){background-color:var(--color-neutral-7)}.psp-scope .dark\:\!text-font-overlay:where([data-mode=g123-dark],[data-mode=g123-dark] *){color:var(--color-font-overlay)!important}.psp-scope .dark\:\!text-font-secondary:where([data-mode=g123-dark],[data-mode=g123-dark] *){color:var(--color-font-secondary)!important}.psp-scope .dark\:text-brand-secondary-secondary:where([data-mode=g123-dark],[data-mode=g123-dark] *){color:var(--color-brand-secondary-secondary)}.psp-scope .dark\:text-font-overlay:where([data-mode=g123-dark],[data-mode=g123-dark] *){color:var(--color-font-overlay)}.psp-scope .dark\:text-font-primary:where([data-mode=g123-dark],[data-mode=g123-dark] *){color:var(--color-font-primary)}@media (hover:hover){.psp-scope .dark\:hover\:border-neutral-3:where([data-mode=g123-dark],[data-mode=g123-dark] *):hover{border-color:var(--color-neutral-3)}.psp-scope .dark\:hover\:bg-neutral-3:where([data-mode=g123-dark],[data-mode=g123-dark] *):hover{background-color:var(--color-neutral-3)}.psp-scope .dark\:hover\:bg-neutral-6:where([data-mode=g123-dark],[data-mode=g123-dark] *):hover{background-color:var(--color-neutral-6)}.psp-scope .dark\:hover\:text-brand-secondary-container:where([data-mode=g123-dark],[data-mode=g123-dark] *):hover{color:var(--color-brand-secondary-container)}}.psp-scope .dark\:active\:bg-neutral-5:where([data-mode=g123-dark],[data-mode=g123-dark] *):active{background-color:var(--color-neutral-5)}}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-scroll-snap-strictness{syntax:"*";inherits:false;initial-value:proximity}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-leading{syntax:"*";inherits:false}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@keyframes spin{to{transform:rotate(1turn)}}@keyframes pulse{0%,80%,to{transform:scale(0)}40%{transform:scale(1)}}@keyframes fade-in-bottom{0%{-webkit-backdrop-filter:blur();opacity:0;transform:translateY(10px)}to{-webkit-backdrop-filter:blur(8px);opacity:1;transform:translateY(0)}}@keyframes fade-out-bottom{0%{-webkit-backdrop-filter:blur(8px);opacity:1;transform:translateY(0)}to{-webkit-backdrop-filter:blur();opacity:0;transform:translateY(10px)}}@keyframes slide-in-bottom{0%{opacity:0;transform:translateY(100%)}to{opacity:1;transform:translateY(0)}}
