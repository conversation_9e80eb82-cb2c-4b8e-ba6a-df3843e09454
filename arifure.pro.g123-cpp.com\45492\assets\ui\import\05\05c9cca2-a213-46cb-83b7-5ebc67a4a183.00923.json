[1, ["45zjs7SMpD1anQIYPcwrur@f9941", "f0SyhgotZPqaGvGXhOBIwY@f9941", "91dhWE6PJAWYALu9zldVSV@f9941", "09mfDbq2VJq5XKsx742YJp@f9941"], ["node", "_spriteFrame", "root", "data", "_parent"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_parent", "_lpos", "_children", "_lscale"], 1, 9, 4, 1, 5, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_lineHeight", "_overflow", "_isBold", "_fontSize", "_horizontalAlign", "_enableWrapText", "_enableOutline", "_outlineWidth", "_cacheMode", "node", "__prefab", "_color", "_outlineColor"], -8, 1, 4, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Widget", ["_alignFlags", "_bottom", "_originalWidth", "_originalHeight", "node", "__prefab"], -1, 1, 4], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab"], 1, 1, 4], ["7563fP+WuxL1p5rM8Q2NGlY", ["node", "__prefab", "spfs"], 3, 1, 4, 3], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4]], [[7, 0, 2], [4, 0, 1, 2, 3, 4, 5, 5], [2, 0, 1, 2, 3, 1], [0, 0, 1, 4, 2, 3, 5, 7, 3], [2, 0, 1, 2, 1], [8, 0, 1, 2, 3, 3], [0, 0, 1, 6, 2, 3, 3], [0, 0, 1, 4, 2, 3, 5, 3], [3, 2, 3, 4, 1], [10, 0, 1, 2, 2], [6, 0, 2], [0, 0, 1, 4, 6, 2, 3, 5, 3], [3, 0, 1, 2, 3, 4, 3], [9, 0, 1, 2, 1], [4, 0, 1, 3, 2, 4, 5, 5], [1, 0, 6, 1, 2, 3, 7, 4, 11, 12, 13, 8], [1, 0, 6, 1, 5, 2, 3, 4, 11, 12, 13, 8], [1, 0, 1, 5, 2, 3, 7, 4, 8, 9, 11, 12, 13, 10], [1, 0, 1, 5, 2, 3, 4, 10, 8, 9, 11, 12, 14, 10], [5, 0, 1, 4, 5, 3], [5, 0, 2, 3, 4, 5, 4]], [[10, "gvg_ymj_tips_panel"], [6, "gvg_ymj_tips_panel", 33554432, [-4], [[4, -2, [0, "98uxGpqZhKArK2Ac6EtMoa"], [5, 640, 1280]], [20, 45, 720, 134, -3, [0, "2bkx+/WHxITKU6QCZLhzFu"]]], [14, "74gXne1LNBxbeD40dL+ibu", null, null, [], -1, 0]], [6, "img_panel_tips", 33554432, [-8, -9, -10, -11, -12, -13], [[2, -5, [0, "95M5VMWnJPX4xhL4Al0Wsc"], [5, 305, 80], [0, 1, 0.5]], [12, 1, 0, -6, [0, "52pfq53rJNI66OerKiVTKz"], 4], [5, 3, 0.9, -7, [0, "e1dUXYc2ZHtp5Aka5ASWby"]]], [1, "37YL07zdlN1IfYIl/11p6w", null, null, null, 1, 0]], [7, "img_sdsl", 33554432, 2, [[4, -14, [0, "72e/DMwS1F3bOyy5MPTqh0"], [5, 60, 49]], [8, -15, [0, "b9vxS7a/5IXbHHOjZqST6Y"], 0], [5, 3, 0.9, -16, [0, "d5hGVz81xLManYt+7mrAwH"]], [13, -17, [0, "b9ONVL90NC25uBnMhK++Z1"], [1, 2]]], [1, "b2my+nDEZCo49X8Pv0OHEp", null, null, null, 1, 0], [1, -264.101, -3.596, 0]], [11, "item_tips", 33554432, 1, [2], [[2, -18, [0, "f5O6DmqIdHmrW7Z4dls8kE"], [5, 305, 80], [0, 1, 0.5]], [19, 4, 400, -19, [0, "58hgnh4IROn6vJtfan8llF"]]], [1, "91kA6Vc4hPubVMjEXZt/KC", null, null, null, 1, 0], [1, 317.53, -200, 0]], [7, "icon_shou", 33554432, 2, [[4, -20, [0, "52OXD+ZAlPnpXQvMpvLHQ+"], [5, 33, 34]], [8, -21, [0, "c6JHUl2gxOCJgXMmqjpUod"], 3], [5, 3, 0.9, -22, [0, "14XS2MtwxIcrH1Yxo3rUE+"]]], [1, "c1cb+k1fNLs77jWiTbV1bg", null, null, null, 1, 0], [1, -294.448, 26.258, 0]], [3, "txt_qw", 33554432, 2, [[2, -23, [0, "a1fw5Cvk5L+7L3qtM9f1QB"], [5, 126, 58.92], [0, 0, 0.5]], [17, "", 36, 36, 42, 2, false, true, true, 3, -24, [0, "71q+p+0YRE1oZiweofq7Rl"], [4, 4290117376]], [9, "sdsl_75", -25, [0, "6bmvYjUQNHJ7+5BrA6b4Rx"]]], [1, "1cTvDC5jBETrCoJA3AMfOH", null, null, null, 1, 0], [1, -63.717, 14.711, 0], [1, 0.5, 0.5, 1]], [3, "txt_sdsl", 33554432, 2, [[4, -26, [0, "b64wblDe9NMZrwh2+KVTGt"], [5, 176, 69.9]], [18, "", 35, 34, 36, 2, true, 1, true, 3, -27, [0, "ccSJhSnFZGqaElKl5ZELI2"], [4, 4278453288]], [9, "help_name_84", -28, [0, "8cciUpTu9AvKLRBNPHDmKS"]]], [1, "28GjPkHB1AP7TISLrrK9ra", null, null, null, 1, 0], [1, -265.149, -27.075, 0], [1, 0.5, 0.5, 1]], [3, "txt_pd", 33554432, 2, [[2, -29, [0, "b5+OQQcM5C6rhH/Kp9sF9o"], [5, 303.139844, 52.92], [0, 0, 0.5]], [15, "", 0, 40, 42, 2, false, true, -30, [0, "9cy56Ii8xJDbtPNyincGLi"], [4, 4280098330]]], [1, "03hkru+2JIBa/XcTaF7Efb", null, null, null, 1, 0], [1, -221.931, 12.87, 0], [1, 0.5, 0.5, 1]], [3, "txt_dw", 33554432, 2, [[2, -31, [0, "2dRjBNIgxJhaq5+mW9/rkX"], [5, 441.679688, 52.92], [0, 0, 0.5]], [16, "", 0, 37, 36, 42, 2, true, -32, [0, "afdrDc2SFBToWdFFCKaNeo"], [4, 4278584374]]], [1, "3d6FBC+lRFA6hyT9JVVzMU", null, null, null, 1, 0], [1, -230.725, -13.794, 0], [1, 0.5, 0.5, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 5, 0, -3, 8, 0, -4, 9, 0, -5, 6, 0, -6, 7, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 3, 1, 2, 4, 4, 32], [0, 0, 0, 0, 0], [1, -1, -2, 1, 1], [0, 0, 1, 2, 3]]