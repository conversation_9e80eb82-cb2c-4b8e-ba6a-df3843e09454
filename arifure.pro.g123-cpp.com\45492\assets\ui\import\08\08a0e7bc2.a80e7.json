[1, ["c5SF+6ttVHZqzYYwTBRTWe@f9941", "64lSvS+G5B8byIQUc3dbfv@f9941", "b0npEaocVKQZe7wW0GMNiV", "a7ZHpooe1A07RKUYPDmCm2", "dcwUZqxgJDXLyPwA3lVD1P@f9941", "bc30maPN1PQ4Brvq4j3aKK@f9941", "4bvUePSW1My72lVB0hkqFh", "ddN2KCl3hGbpw2Tr1dWw2R@f9941", "f5e5MFGkpJZ7ZpwE/9InyR@f9941", "7dweVJXLdPTqzrjzZYfuin@f9941", "dbf3gIROhNPYlO5+IxfcSA", "4cnL3vqgxJ3Yg452+iRCt9", "b7MFJ8MjNBwqr3fNq1j5dJ@f9941", "c69/1J7Y9CU4Njtd5e0g5w", "e1nudC4xJKxZkrt1OL9jlp@f9941", "c8e8OJkJ9HrpeUy+besO+S", "1cSSstP5VGZZImZIJjL/nf", "1c8mQDh1RK2aLL2xGfAoyw", "feICwAxUFPJYcnH3vp7Q9k", "08MWRSi95IL70mKuknMmEp", "35Ae/Kd6tGuapXHCd/eFGk@f9941", "46BO9SJOhMj67kcwL3MXoo", "52nQdoCIBLmZBf7iVS09Kb@f9941", "7fBcOZowtH46o/b1J35pRi"], ["node", "targetInfo", "_spriteFrame", "spriteFrame", "root", "asset", "_target", "_skeletonData", "_content", "_parent", "tmpNode", "data", "value"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos", "_lscale"], -2, 4, 9, 1, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_originalHeight", "_originalWidth", "_bottom", "_top", "_left", "_right", "_verticalCenter", "node", "__prefab"], -5, 1, 4], ["cc.Sprite", ["_isTrimmedMode", "_sizeMode", "_type", "_enabled", "node", "__prefab", "_spriteFrame"], -1, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isBold", "_lineHeight", "_overflow", "_horizontalAlign", "_enableOutline", "_outlineWidth", "_verticalAlign", "_cacheMode", "node", "__prefab", "_color", "_outlineColor"], -8, 1, 4, 5, 5], ["cc.Layout", ["_layoutType", "_spacingX", "_resizeMode", "_paddingLeft", "_paddingRight", "_spacingY", "_constraintNum", "_paddingTop", "_paddingBottom", "_enabled", "node", "__prefab"], -7, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_target", "_normalColor"], 1, 1, 4, 1, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["StatusData", ["status", "fileId", "fontsize", "spriteFrame", "gradient_material", "i18nLabelStr", "position", "rotation", "scale", "size", "color"], -3, 5, 5, 5, 5, 5], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedComponents"], 2, 1, 9, 9], ["sp.Skeleton", ["defaultSkin", "_premultipliedAlpha", "_preCacheMode", "defaultAnimation", "loop", "node", "__prefab", "_skeletonData"], -2, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "nestedPrefabInstanceRoots", "root", "instance", "targetOverrides", "asset"], 1, 1, 4, 9, 6], ["011c8MZ++JCbqPChKjiX2MO", ["_statusIndex", "currStatusName", "statusNameArray", "node", "__prefab", "statusNodes", "statusData"], 0, 1, 4, 2, 9], ["StatusData", ["status", "fileId", "label_font", "gradient_material", "rotation", "scale", "size", "spriteFrame"], -1, 5, 5, 5, 6], ["<PERSON><PERSON>", ["bounceDuration", "brake", "horizontal", "node", "__prefab", "_content"], 0, 1, 4, 1], ["ad253TrMIpHFZgL3Bp6KTbe", ["node", "__prefab", "tmpNode", "pageChangeEvent", "renderEvent", "selectedEvent"], 3, 1, 4, 1, 4, 4, 4], ["cc.ClickEvent", [], 3], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.TargetInfo", ["localID"], 2], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 6], ["cc.LabelOutline", ["node", "__prefab"], 3, 1, 4], ["cc.TargetOverrideInfo", ["propertyPath", "source", "sourceInfo", "target", "targetInfo"], 2, 1, 4, 1, 4], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4], ["7563fP+WuxL1p5rM8Q2NGlY", ["node", "__prefab", "spfs"], 3, 1, 4, 3], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4]], [[11, 0, 2], [13, 0, 1, 2, 3, 4, 5, 5], [23, 0, 2], [6, 0, 1, 2, 1], [26, 0, 1, 2, 2], [25, 0, 1, 2, 3], [28, 0, 1, 2, 2], [0, 0, 1, 7, 8, 6, 5, 9, 3], [6, 0, 1, 2, 3, 1], [17, 0, 1, 2, 3, 4, 5, 6, 7, 5], [0, 0, 1, 7, 6, 5, 3], [0, 0, 1, 7, 6, 5, 9, 3], [0, 3, 4, 7, 5, 3], [0, 0, 1, 7, 8, 6, 5, 3], [14, 0, 1, 2, 3, 4, 5, 4], [8, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 6, 7, 8, 9, 10, 6], [0, 0, 1, 7, 6, 5, 9, 10, 3], [5, 0, 1, 2, 3, 5, 4, 3], [16, 0, 1, 2, 3, 4, 5, 6, 4], [3, 0, 1, 2, 4, 5, 3, 11, 12, 13, 7], [27, 0, 1, 2, 3], [1, 0, 2, 1, 8, 9, 4], [1, 0, 3, 8, 9, 3], [2, 2, 0, 4, 5, 6, 3], [7, 0, 1, 2, 3, 5, 4, 6, 7, 8, 9, 10, 7], [20, 1], [30, 0, 1, 1], [32, 0, 1, 2, 2], [9, 0, 1, 2, 5, 6, 7, 4], [4, 10, 11, 1], [2, 1, 0, 4, 5, 6, 3], [5, 0, 1, 2, 3, 3], [3, 0, 1, 2, 4, 5, 3, 10, 7, 8, 11, 12, 14, 10], [1, 0, 5, 6, 4, 3, 2, 1, 8, 9, 8], [1, 0, 4, 3, 2, 1, 8, 9, 6], [2, 1, 4, 5, 6, 2], [2, 1, 0, 4, 5, 3], [2, 4, 5, 6, 1], [18, 0, 1, 2, 3, 4, 5, 4], [21, 0, 1, 1], [22, 0, 1, 2, 1], [31, 0, 1, 2, 3, 4, 2], [10, 0, 2], [0, 0, 1, 8, 6, 5, 3], [0, 0, 1, 8, 6, 5, 9, 3], [0, 0, 2, 1, 7, 8, 6, 5, 9, 4], [0, 0, 2, 1, 7, 6, 5, 9, 4], [1, 0, 3, 2, 1, 8, 9, 5], [2, 0, 4, 5, 2], [19, 0, 1, 2, 3, 4, 5, 1], [34, 0, 1, 1], [0, 0, 2, 1, 8, 6, 5, 4], [0, 0, 2], [1, 0, 4, 3, 1, 8, 9, 5], [1, 0, 1, 8, 9, 3], [1, 0, 7, 8, 9, 3], [1, 0, 5, 6, 2, 1, 8, 9, 6], [1, 0, 8, 9, 2], [12, 0, 1, 2, 3, 4, 5, 4], [15, 0, 1, 2, 3, 4, 5, 3], [4, 2, 0, 1, 10, 11, 4], [4, 2, 0, 3, 1, 10, 11, 5], [4, 2, 0, 3, 4, 7, 8, 1, 5, 6, 10, 11, 10], [4, 0, 3, 4, 1, 5, 6, 10, 11, 7], [4, 0, 3, 1, 10, 11, 4], [4, 9, 2, 0, 1, 10, 11, 5], [2, 3, 2, 1, 4, 5, 6, 4], [2, 0, 4, 5, 6, 2], [5, 0, 1, 2, 3, 4, 3], [3, 0, 6, 9, 1, 2, 4, 5, 3, 7, 8, 11, 12, 13, 11], [3, 0, 6, 1, 2, 3, 11, 12, 13, 6], [3, 0, 6, 9, 1, 2, 4, 5, 3, 11, 12, 13, 9], [3, 0, 6, 1, 2, 4, 3, 11, 12, 13, 7], [3, 0, 1, 2, 4, 5, 3, 7, 8, 11, 12, 14, 9], [3, 0, 1, 2, 4, 3, 7, 8, 11, 12, 8], [8, 0, 1, 3, 2, 2], [24, 0, 1, 1], [29, 0, 1, 2, 2], [33, 0, 1, 2, 1], [9, 0, 3, 1, 2, 4, 5, 6, 7, 6]], [[[[43, "huoban<PERSON><PERSON><PERSON>_bb"], [44, "huoban<PERSON><PERSON><PERSON>_bb", 33554432, [-16, -17], [[3, -14, [0, "51klx2JyFK/afrBkKIm1ov"], [5, 640, 1280]], [22, 45, 100, 100, -15, [0, "22VELJBH9IGLxcujFYmBfZ"]]], [59, "05WBu3u/5Hm4OXhkQF1mrH", null, [], -13, 0, [-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12]]], [52, "<PERSON><PERSON><PERSON>", false, 33554432, [-20, -21, -22, -23, -24, -25, -26], [[3, -18, [0, "c77L1jXLREBbPZJ50zeXRt"], [5, 640, 1280]], [34, 45, 3.3861802251067274e-15, -3.3861802251067274e-15, 2.531308496145357e-14, -2.531308496145357e-14, 100, 100, -19, [0, "b6gOcb1JBEFqbefQ9x4Iqy"]]], [1, "180Q5RziRJ2rGWUVR2EzbP", null, null, null, 1, 0]], [7, "tab", 33554432, 2, [-30, -31, -32, -33, -34], [[3, -27, [0, "ffjkHdayVBla+MvKaOYKi8"], [5, 606, 49]], [61, 1, 1, 4, -28, [0, "51AtSfa3lCqILYSzchM6bI"]], [23, 4, 176, -29, [0, "fcY7c66ydGSrkzc53eZkq5"]]], [1, "b5Jb8/uXFPJp5dkf9kbl1m", null, null, null, 1, 0], [1, 0, -439.5, 0]], [44, "shen<PERSON>", 33554432, [-37, -38, -39, -40, -41, -42], [[3, -35, [0, "1c03iwSiNEzoeJV9d22Bs+"], [5, 640, 1280]], [34, 45, 3.3861802251067274e-15, -3.3861802251067274e-15, 2.531308496145357e-14, -2.531308496145357e-14, 100, 100, -36, [0, "20UIzEZTxEdJdWMrOWDIp+"]]], [1, "c4Iw/gdVxH1r0fA8VkexJn", null, null, null, 1, 0]], [45, "list", 33554432, [-46, -47, -48, -49, -50], [[3, -43, [0, "8brX3m3Z1FVpwlsqxcakG1"], [5, 601, 159]], [36, 0, -44, [0, "42qOVs09hJQ43/8AoGxiM3"], 38], [23, 20, 561, -45, [0, "e6L72BfQRMKbiPnQYuaep6"]]], [1, "277XxkjBVOzKVDPNoVVdd5", null, null, null, 1, 0], [1, 0, -79.5, 0]], [13, "ui", 33554432, 1, [-54, 2, 4, -55], [[3, -51, [0, "d0QRCyCMNNiJHzmh3rQyLt"], [5, 640, 1280]], [37, 2, false, -52, [0, "1f9etdG4dKtI4PNx2YutNt"]], [22, 45, 640, 1280, -53, [0, "063RIjEwxAeqB5R9i1z3BK"]]], [1, "68BLIdY/ZJfov8JZvSPjHw", null, null, null, 1, 0]], [7, "bg_di1", 33554432, 2, [-59, -60, -61, -62], [[3, -56, [0, "65ntYWXptJ95xjA0PYVwdd"], [5, 601, 159]], [36, 0, -57, [0, "a7Qvhv9Z5OD4dctm87m8T1"], 15], [23, 20, 246.15800000000002, -58, [0, "8bMZKyBcFMqph3L5XqDGOo"]]], [1, "c6TFluCRRB8queRLJUmdKi", null, null, null, 1, 0], [1, 0, -314.342, 0]], [7, "tab_top", 33554432, 2, [-66, -67, -68], [[8, -63, [0, "0d6rE6kWVCAKglvDYxNkGI"], [5, 377, 52], [0, 0, 0.5]], [62, 1, 1, 18, 4, -64, [0, "77O6dcRG9A7baWhPXBA5ML"]], [54, 9, 86.5, 1154.562, 49, -65, [0, "57XOefCY5Jtr86BrEC/szo"]]], [1, "cbbIKo7fFE6bCGVD4+jCRj", null, null, null, 1, 0], [1, -320, 527.5, 0]], [7, "btn_tab1", 33554432, 8, [-75, -76], [[3, -69, [0, "14EE7NNYdFkbPxKj8ZT4ur"], [5, 117, 52]], [18, 3, 0.9, -71, [0, "11xcYVemhLIK3iRtSGKeVI"], [4, 4292269782], -70], [19, 0, "0", ["0", "1"], -74, [0, "48OyFa/GBACpFnji6mipX2"], [-72, -73], [[9, "1", "55XFUFXopNCIyTQEEliFd3", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 3], [25, "1", "4e3X27zCNDMriAaziNitxx", 22, null, "uibeibao_2", null, [1, 0, 1.5, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 98.013671875, 40.32], [1, 26, 30, 29]], [9, "0", "55XFUFXopNCIyTQEEliFd3", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 4], [25, "0", "4e3X27zCNDMriAaziNitxx", 22, null, "uibeibao_2", null, [1, 0, 1.5, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 98.013671875, 40.32], [1, 26, 30, 29]]]]], [1, "7cvILv155KTqgO3F73Fa8p", null, null, null, 1, 0], [1, 76.5, 0, 0]], [7, "btn_tab2", 33554432, 8, [-83, -84], [[3, -77, [0, "fehF/LV/dOtqlh9gkt3rwJ"], [5, 117, 52]], [18, 3, 0.9, -79, [0, "29X+yHX61DEoPeaKq/5F1H"], [4, 4292269782], -78], [19, 1, "1", ["0", "1"], -82, [0, "10hNdg4+NN5L2ycsNuuADG"], [-80, -81], [[9, "1", "55XFUFXopNCIyTQEEliFd3", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 6], [25, "1", "4e3X27zCNDMriAaziNitxx", 22, null, "uibeibao_2", null, [1, 0, 1.5, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 98.013671875, 40.32], [1, 26, 30, 29]], [9, "0", "55XFUFXopNCIyTQEEliFd3", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 7], [25, "0", "4e3X27zCNDMriAaziNitxx", 22, null, "uibeibao_2", null, [1, 0, 1.5, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 98.013671875, 40.32], [1, 26, 30, 29]]]]], [1, "cfGT0/5cBAcr+5i7782qiV", null, null, null, 1, 0], [1, 197.5, 0, 0]], [7, "btn_tab3", 33554432, 8, [-91, -92], [[3, -85, [0, "8b5wu7MGtHE4YnMZyvlSGa"], [5, 117, 52]], [18, 3, 0.9, -87, [0, "1f3PbVgn9OrYSU3XWt0H1A"], [4, 4292269782], -86], [19, 1, "1", ["0", "1"], -90, [0, "f0YEKSiclLXJfJZwkgdV+J"], [-88, -89], [[9, "1", "55XFUFXopNCIyTQEEliFd3", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 9], [25, "1", "4e3X27zCNDMriAaziNitxx", 22, null, "uibeibao_2", null, [1, 0, 1.5, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 98.013671875, 40.32], [1, 26, 30, 29]], [9, "0", "55XFUFXopNCIyTQEEliFd3", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 10], [25, "0", "4e3X27zCNDMriAaziNitxx", 22, null, "uibeibao_2", null, [1, 0, 1.5, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 98.013671875, 40.32], [1, 26, 30, 29]]]]], [1, "a2dWC88kdBGZfGSRPlOx66", null, null, null, 1, 0], [1, 318.5, 0, 0]], [7, "btn_tab", 33554432, 3, [-99, -100], [[3, -93, [0, "58MkPg4aVODY2W82Wv5rpQ"], [5, 118, 52]], [18, 3, 0.9, -95, [0, "9fqbGw7BNJNKHLHin+Du5c"], [4, 4292269782], -94], [19, 1, "1", ["0", "1"], -98, [0, "7bH22y+xBG85Obs5OtYZ1N"], [-96, -97], [[9, "0", "dbKLNlknBNWp6EpnGLgLB7", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 17], [16, "0", "70P12Ch3FIvoTc9e4OkG1A", 22, null, null, [1, 0, 2, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 32], [1, 26, 30, 29]], [9, "1", "dbKLNlknBNWp6EpnGLgLB7", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 18], [16, "1", "70P12Ch3FIvoTc9e4OkG1A", 22, null, null, [1, 0, 2, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 32], [1, 26, 30, 29]]]]], [1, "1dYmKNqydIiYFjaOnrFLKd", null, null, null, 1, 0], [1, -244, -12.083, 0]], [7, "btn_tab-001", 33554432, 3, [-107, -108], [[3, -101, [0, "5cyNcUnM9EeZw108lAvTNA"], [5, 118, 52]], [18, 3, 0.9, -103, [0, "edBPatjEJLiL/Y2uGuvWJQ"], [4, 4292269782], -102], [19, 1, "1", ["0", "1"], -106, [0, "67B9RSFIpBWa2bvVe2Xqwj"], [-104, -105], [[9, "0", "dbKLNlknBNWp6EpnGLgLB7", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 20], [16, "0", "70P12Ch3FIvoTc9e4OkG1A", 22, null, null, [1, 0, 2, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 32], [1, 26, 30, 29]], [9, "1", "dbKLNlknBNWp6EpnGLgLB7", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 21], [16, "1", "70P12Ch3FIvoTc9e4OkG1A", 22, null, null, [1, 0, 2, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 32], [1, 26, 30, 29]]]]], [1, "ab5g4TvFpEx4AZ+/E0EJE/", null, null, null, 1, 0], [1, -122, -12.083, 0]], [7, "btn_tab-002", 33554432, 3, [-115, -116], [[3, -109, [0, "deLhLMLilHrIY7ywbmlQpj"], [5, 118, 52]], [18, 3, 0.9, -111, [0, "5bysa5b1NIKKb24ATGJH4z"], [4, 4292269782], -110], [19, 1, "1", ["0", "1"], -114, [0, "11duJjbNRBHaZGV9ttp7p9"], [-112, -113], [[9, "0", "dbKLNlknBNWp6EpnGLgLB7", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 23], [16, "0", "70P12Ch3FIvoTc9e4OkG1A", 22, null, null, [1, 0, 2, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 32], [1, 26, 30, 29]], [9, "1", "dbKLNlknBNWp6EpnGLgLB7", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 24], [16, "1", "70P12Ch3FIvoTc9e4OkG1A", 22, null, null, [1, 0, 2, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 32], [1, 26, 30, 29]]]]], [1, "05/5OZHiVDfqtOiWnQgipm", null, null, null, 1, 0], [1, 0, -12.083, 0]], [7, "btn_tab-003", 33554432, 3, [-123, -124], [[3, -117, [0, "592koBwxtJILA35v0y1Xeg"], [5, 118, 52]], [18, 3, 0.9, -119, [0, "13RuiFy4FOAaX4uU+aO98y"], [4, 4292269782], -118], [19, 1, "1", ["0", "1"], -122, [0, "c4bscZ0BVKYq19Q96FDi92"], [-120, -121], [[9, "0", "dbKLNlknBNWp6EpnGLgLB7", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 26], [16, "0", "70P12Ch3FIvoTc9e4OkG1A", 22, null, null, [1, 0, 2, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 32], [1, 26, 30, 29]], [9, "1", "dbKLNlknBNWp6EpnGLgLB7", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 27], [16, "1", "70P12Ch3FIvoTc9e4OkG1A", 22, null, null, [1, 0, 2, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 32], [1, 26, 30, 29]]]]], [1, "15VjRFi5pJ2InpTJ6f4WfQ", null, null, null, 1, 0], [1, 122, -12.083, 0]], [7, "btn_tab-004", 33554432, 3, [-131, -132], [[3, -125, [0, "8bJBqQibRHzqRM+P2r/rB5"], [5, 118, 52]], [18, 3, 0.9, -127, [0, "dfWS4XuJBL1YAz2yS84Fl0"], [4, 4292269782], -126], [19, 0, "0", ["0", "1"], -130, [0, "48hU/uU/BIX7T/ChneKaEq"], [-128, -129], [[9, "0", "dbKLNlknBNWp6EpnGLgLB7", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 29], [16, "0", "70P12Ch3FIvoTc9e4OkG1A", 22, null, null, [1, 0, 2, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 32], [1, 26, 30, 29]], [9, "1", "dbKLNlknBNWp6EpnGLgLB7", null, null, [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 118, 52], 30], [16, "1", "70P12Ch3FIvoTc9e4OkG1A", 22, null, null, [1, 0, 2, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 110, 32], [1, 26, 30, 29]]]]], [1, "0a6OwIJoZLvrN0EziLzV0q", null, null, null, 1, 0], [1, 244, -12.083, 0]], [53, "New Node"], [7, "ScrollView", 33554432, 2, [-139], [[8, -133, [0, "d1cR09WVpIsYlMSoIroy8c"], [5, 640, 720], [0, 0.5, 1]], [39, 0.23, 0.75, false, -135, [0, "79oM3UEtxCSpa8xjcR+aTq"], -134], [35, 45, 144.637, 415.363, 240, 250, -136, [0, "671ZTvo+xP1qwBVKApDuFL"]], [50, -138, [0, "43KmODbndAyoDl9YMA8FhT"], -137, [26], [26], [26]]], [1, "64dcfRr4JNNbJkqsMQu1gs", null, null, null, 1, 0], [1, 0, 495.363, 0]], [13, "view", 33554432, 18, [-144], [[8, -140, [0, "07eFk7Js5LX4AR7MdUGuXZ"], [5, 640, 720], [0, 0.5, 1]], [40, -141, [0, "0cD2A62tVKZokRMuQqRb9g"]], [41, -142, [0, "5eyhV9XMxIvaikKMA7Bf/2"], [4, 16777215]], [22, 45, 240, 250, -143, [0, "35XNl4g3VAmZLLrQsd+/wA"]]], [1, "97cLWIeTBFSZAmDsNAKYG4", null, null, null, 1, 0]], [13, "content", 33554432, 19, [-148], [[8, -145, [0, "a0L46updxEO69zrAv7Hcf/"], [5, 640, 140], [0, 0.5, 1]], [63, 1, 3, 20, 20, 10, 20, 12.5, 14, 5, -146, [0, "acwnoAUcxB6YswN3ITqzhN"]], [48, 45, 580, 220, 400, -147, [0, "a31uiNQ2hFVoG3qtP/sTZX"]]], [1, "bdmGjQDRlK3KwqrB6lPTqP", null, null, null, 1, 0]], [7, "ScrollView-top", 33554432, 4, [-154], [[3, -149, [0, "93ep8USfhJML6oc+Hnx1Px"], [5, 640, 130]], [67, false, 1, 0, -150, [0, "84VVUNKTtM3o+xlzW8vtzt"], 33], [39, 0.23, 0.75, false, -152, [0, "48+YTAF6JCyZNw4DID7W8C"], -151], [35, 45, 65.54700000000003, 1084.453, 640, 130, -153, [0, "25FaH1JwNMfZY4YtHf9LNo"]]], [1, "92v29ohTVClKOpTKx+iHUS", null, null, null, 1, 0], [1, 0, 509.453, 0]], [13, "view", 33554432, 21, [-159], [[3, -155, [0, "8fx0XwedlAU67EuOAoJmAY"], [5, 640, 130]], [40, -156, [0, "e7mk533pVCEYtIkUjoy7QW"]], [41, -157, [0, "f5wSGcSupBw4dvARYLoCq4"], [4, 16777215]], [22, 45, 640, 130, -158, [0, "0eAv1PUb5BSK+bnkc2y/XO"]]], [1, "eeNr/1mtdLhqf3cjMFxDn0", null, null, null, 1, 0]], [7, "ScrollView", 33554432, 4, [-166], [[8, -160, [0, "92Vl9Zx31Pz6TKpzSmvUM4"], [5, 601, 770], [0, 0.5, 1]], [39, 0.23, 0.75, false, -162, [0, "d6Q1O4OhdPcqUYQKk1hdBe"], -161], [34, 45, 19.5, 19.5, 220.13200000000006, 289.868, 240, 250, -163, [0, "096Xv05lpEuqdck0RDnU/7"]], [50, -165, [0, "88q3joZ25Ew7fLb4Zm/CSk"], -164, [26], [26], [26]]], [1, "3f9956uJ1MUL9dpnu/w23I", null, null, null, 1, 0], [1, 0, 419.868, 0]], [13, "view", 33554432, 23, [-171], [[8, -167, [0, "36S6PVqMFFqLKCfl4S7kqz"], [5, 601, 770], [0, 0.5, 1]], [40, -168, [0, "ebnfdDyQBKLaFYdAIB5naC"]], [41, -169, [0, "835dir0hVAuou+SV8qwTiX"], [4, 16777215]], [22, 45, 240, 250, -170, [0, "3ewCtZYKROmaaETHwUVD+u"]]], [1, "744Yuk/PBJbpCr3P+HF1C4", null, null, null, 1, 0]], [13, "content", 33554432, 24, [5], [[8, -172, [0, "07aiBAmsdNmJju79vfe+dO"], [5, 601, 720], [0, 0.5, 1]], [64, 2, 20, 20, 12.5, 16, 5, -173, [0, "0b0oj4kWRG+ZI37WOlcYkt"]], [48, 45, 50, 220, 400, -174, [0, "09KNeeYFhJAIlXi0cZjCWo"]]], [1, "c8kTwTBUVM6Y6FBhE1mXON", null, null, null, 1, 0]], [2, ["0dpBBsahVNwKBm3pB6bLA5"]], [2, ["74gXne1LNBxbeD40dL+ibu"]], [2, ["cd8ZiUa7RE4YhEMZ+TmPzP"]], [7, "content", 33554432, 22, [-177], [[8, -175, [0, "baq4/rqDhFnJ6gOTRXOvZy"], [5, 640, 130], [0, 0.5, 1]], [65, 1, 18, 20, -176, [0, "5dt/5Om2BFAJ1lGWB4RmR+"]]], [1, "c8RxUxv89FUZ2PpvFN2CwP", null, null, null, 1, 0], [1, 0, 65.976, 0]], [10, "bg", 33554432, 1, [[3, -178, [0, "bfW9+28EJM+I23IJogELqJ"], [5, 640, 1280]], [31, 2, false, -179, [0, "0aV0qsjlZKA4E9Zo9Dyt8u"], 0], [55, 17, 1280, -180, [0, "66nPhg6L9Op7f4jzUyhp5I"]]], [1, "b71Qg88ctJQr0ob8G1HqJK", null, null, null, 1, 0]], [10, "btn_tab_1", 33554432, 9, [[3, -181, [0, "45MwIan2tIoITbAfIrRJvU"], [5, 118, 52]], [31, 2, false, -182, [0, "1ajW+G621Gyp7DuLvxDWJB"], 2]], [1, "0c5LjXhYtHFJRCQZ/Ls9gM", null, null, null, 1, 0]], [11, "txt_tab_1", 33554432, 9, [[3, -183, [0, "5azLdpgPBMppWadWJmWEws"], [5, 98.013671875, 40.32]], [20, "", 23, 22, 32, 2, true, -184, [0, "3flYmx5pJBrrdiU6rFiCrv"], [4, 4280098330]]], [1, "53T4rcEcFN25OqvhK2sh/J", null, null, null, 1, 0], [1, 0, 1.5, 0]], [10, "btn_tab_1", 33554432, 10, [[3, -185, [0, "de71VbKGtEBakvtjfnOv5n"], [5, 118, 52]], [31, 2, false, -186, [0, "edKk+f0u9IRpY/cgRik2nN"], 5]], [1, "6f2B/58JFIhbd0rRwRRcm3", null, null, null, 1, 0]], [11, "txt_tab_1", 33554432, 10, [[3, -187, [0, "31I0NhESFPaprkRCp/t92Q"], [5, 98.013671875, 40.32]], [20, "", 23, 22, 32, 2, true, -188, [0, "5ayoMuMSVHo6DKiVgW4aA2"], [4, 4280098330]]], [1, "a9itbbFMVPVIu4WFN4Qbmx", null, null, null, 1, 0], [1, 0, 1.5, 0]], [10, "btn_tab_1", 33554432, 11, [[3, -189, [0, "2cs4trNH9AcLadUcovE0Sp"], [5, 118, 52]], [31, 2, false, -190, [0, "78P+Kl2H5HBaSIePponBrP"], 8]], [1, "c5/Bic2+VOkKtXIbaMP6vF", null, null, null, 1, 0]], [11, "txt_tab_1", 33554432, 11, [[3, -191, [0, "5fOxUdrI9GcoNlex4WKhLc"], [5, 98.013671875, 40.32]], [20, "", 23, 22, 32, 2, true, -192, [0, "f67vMeL4BEAIGcyFxbRERo"], [4, 4280098330]]], [1, "47y6vL+hxN7Zr2pugchXeG", null, null, null, 1, 0], [1, 0, 1.5, 0]], [12, 0, {}, 2, [14, "cd8ZiUa7RE4YhEMZ+TmPzP", null, null, -197, [76, "0dCJKfI2NCc4jvu9Gz18Pe", 1, [[77, [2, ["cd8ZiUa7RE4YhEMZ+TmPzP"]], [[56, 2, 77.167, -196, [0, "dacwEgQoZNOKq+7vHiMCZ0"]]]]], [[5, "ty_zw", ["_name"], 28], [4, ["_lpos"], 28, [1, 0, 77.167, 0]], [4, ["_lrot"], 28, [3, 0, 0, 0, 1]], [4, ["_euler"], 28, [1, 0, 0, 0]], [5, "暂无任何道具", ["_string"], -193], [5, true, ["_active"], 28], [5, true, ["_isBold"], -194], [4, ["_color"], -195, [4, 4294967295]]]], 11]], [12, 0, {}, 20, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -202, [15, "fdlnh9o/RISoaUbx+/DLFP", 1, [[5, "ty_item1", ["_name"], -198], [4, ["_lpos"], -199, [1, -245, -65, 0]], [4, ["_lrot"], -200, [3, 0, 0, 0, 1]], [4, ["_euler"], -201, [1, 0, 0, 0]], [21, "2000", ["_string"], [2, ["eeoXHNF6JHCZD795RQ9Goj"]]]]], 12]], [2, ["79BcNar8tDmZucBHLjoa8/"]], [2, ["79BcNar8tDmZucBHLjoa8/"]], [2, ["03XfDFaqNOdp6SCwdhIQpD"]], [10, "btn_tab_1", 33554432, 12, [[3, -203, [0, "5emy1tfGxHCb2kn9XpPivW"], [5, 118, 52]], [24, 1, false, -204, [0, "f5unJHhbJDnKLi3h2AfvHO"], 16]], [1, "dbKLNlknBNWp6EpnGLgLB7", null, null, null, 1, 0]], [11, "txt_tab_1", 33554432, 12, [[3, -205, [0, "d3WpPmsj5O+r6DNtOgnd/G"], [5, 110, 32]], [20, "", 23, 22, 24, 2, true, -206, [0, "f5OiaeTfRMwbE9nyG062No"], [4, 4280098330]]], [1, "70P12Ch3FIvoTc9e4OkG1A", null, null, null, 1, 0], [1, 0, 2, 0]], [10, "btn_tab_1", 33554432, 13, [[3, -207, [0, "a8+gp/+N5CrqmbFIl1PaV7"], [5, 118, 52]], [24, 1, false, -208, [0, "c0TPuDPDJNxbDtXWDBlbMk"], 19]], [1, "e6YlqXS9VI4bpju0DJ6j12", null, null, null, 1, 0]], [11, "txt_tab_1", 33554432, 13, [[3, -209, [0, "afBJKxSQhGta9g5U7xALfM"], [5, 110, 32]], [20, "", 23, 22, 24, 2, true, -210, [0, "51HsBkpSVLBIU/9XHfwPgk"], [4, 4280098330]]], [1, "9aYI4MmhZHBou5hRrbxQNV", null, null, null, 1, 0], [1, 0, 2, 0]], [10, "btn_tab_1", 33554432, 14, [[3, -211, [0, "094uRdrsFMoZnQzCbHXgpi"], [5, 118, 52]], [24, 1, false, -212, [0, "3bON5dR0dDi4/514q+uVTY"], 22]], [1, "95gfiaZXpKNLM+sAdaGcdx", null, null, null, 1, 0]], [11, "txt_tab_1", 33554432, 14, [[3, -213, [0, "0dHMmSh81PGIVnOoxdeq3i"], [5, 110, 32]], [20, "", 23, 22, 24, 2, true, -214, [0, "56TG3lS6lAbqtfht6l0qDO"], [4, 4280098330]]], [1, "858PU9425ImINE4XeXImAx", null, null, null, 1, 0], [1, 0, 2, 0]], [10, "btn_tab_1", 33554432, 15, [[3, -215, [0, "05jxQGrkFGdL2ipKvhmvog"], [5, 118, 52]], [24, 1, false, -216, [0, "0ff++4u9ZPUL6kcg/ZFw+7"], 25]], [1, "feFsUibAxMtJWBoFW+H7Bo", null, null, null, 1, 0]], [11, "txt_tab_1", 33554432, 15, [[3, -217, [0, "8cOze/jBVCyJUWHqOJWyqd"], [5, 110, 32]], [20, "", 23, 22, 24, 2, true, -218, [0, "34vgGtkgZOyZJ4zuJ/t/mc"], [4, 4280098330]]], [1, "45FGDOJXhAdr4ga/SWoPOc", null, null, null, 1, 0], [1, 0, 2, 0]], [10, "btn_tab_1", 33554432, 16, [[3, -219, [0, "a4liD9mbtBUaX8QvfPAOsA"], [5, 118, 52]], [24, 1, false, -220, [0, "bciOEmDPREKbeNpc3wtMXo"], 28]], [1, "45DfvigQNHaJwHzv43ZjqX", null, null, null, 1, 0]], [11, "txt_tab_1", 33554432, 16, [[3, -221, [0, "5fS9rjc4hK2qq2FZqD/Xbp"], [5, 110, 32]], [20, "", 23, 22, 24, 2, true, -222, [0, "cchtf8IJ5JqJrWcIXWG7NH"], [4, 4280098330]]], [1, "61Zy/H1s5N2LZSeakO1OcH", null, null, null, 1, 0], [1, 0, 2, 0]], [11, "img_huoban_tab", 33554432, 2, [[3, -223, [0, "03Sfq1RqVO1LNUGXATFl6k"], [5, 640, 8]], [38, -224, [0, "40/RIEvcpDyp26fodYmxbG"], 31], [23, 4, 144.00099999999998, -225, [0, "56ICeSjR1KybcyyUdmFPen"]]], [1, "ed5ufBYs1K37TfNzA7Nk+p", null, null, null, 1, 0], [1, 0, -491.999, 0]], [12, 0, {}, 29, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -234, [15, "10VoY96ZhN3oLmpGX7stQA", 1, [[5, "ty_item1", ["_name"], -226], [4, ["_lpos"], -227, [1, -245.5, -66.85000000000014, 0]], [4, ["_lrot"], -228, [3, 0, 0, 0, 1]], [4, ["_euler"], -229, [1, 0, 0, 0]], [5, "2000", ["_string"], -230], [21, true, ["_active"], [2, ["d6i3dsjTBAsYIwkgrHXPK3"]]], [5, 0, ["_horizontalAlign"], -231], [6, ["_lpos"], [2, ["8ev1z6IH5OgYw4mQ5jIqFq"]], [1, 50, -37, 0]], [6, ["_contentSize"], [2, ["2a+NEz1hJH37Gy8QtP+C4Q"]], [5, 112.78125, 94.2]], [5, 24, ["_actualFontSize"], -232], [5, 0, ["_overflow"], -233]]], 32]], [2, ["79BcNar8tDmZucBHLjoa8/"]], [2, ["eeoXHNF6JHCZD795RQ9Goj"]], [2, ["79BcNar8tDmZucBHLjoa8/"]], [17, "txt_des", 33554432, 5, [[8, -235, [0, "a70tcUIlNNoZWJMimdibuY"], [5, 250, 69], [0, 0, 1]], [70, "攻击", 0, 0, 48, 48, 50, 3, true, true, 3, -236, [0, "31TR0jhaxEGJy6nemVuoi4"], [4, 4290051584]], [27, -237, [0, "efu41RD51Pd4rkh1ktcmCb"]]], [1, "211/aGdqlIir3oz0XWsXKw", null, null, null, 1, 0], [1, -150, -0.091, 0], [1, 0.5, 0.5, 1]], [2, ["79BcNar8tDmZucBHLjoa8/"]], [2, ["03XfDFaqNOdp6SCwdhIQpD"]], [11, "img_huoban_tab", 33554432, 4, [[3, -238, [0, "2fTpNEcwNHZo91n0FjPgBf"], [5, 640, 8]], [38, -239, [0, "332bJmkElBE4i1d7rsSrRm"], 39], [23, 4, 254.57100000000003, -240, [0, "ddC8a0nPhG1bz7WzHhYHbF"]]], [1, "1eCFNEBZhGHJZHH7VQFr7j", null, null, null, 1, 0], [1, 0, -381.429, 0]], [2, ["03XfDFaqNOdp6SCwdhIQpD"]], [2, ["03XfDFaqNOdp6SCwdhIQpD"]], [2, ["74gXne1LNBxbeD40dL+ibu"]], [12, 0, {}, 6, [60, "74gXne1LNBxbeD40dL+ibu", null, -265, [15, "b8HJNcp9FAx7cwGF1RTUKQ", 1, [[4, ["_lpos"], -241, [1, -320, 1, 0]], [4, ["_contentSize"], -242, [5, 193, 50]], [5, "top", ["_name"], 27], [4, ["_lpos"], 27, [1, 0, 640, 0]], [4, ["_lrot"], 27, [3, 0, 0, 0, 1]], [4, ["_euler"], 27, [1, 0, 0, 0]], [5, true, ["_active"], 27], [5, false, ["_active"], -243], [4, ["_lpos"], -244, [1, -254, -69.118, 0]], [6, ["_contentSize"], [2, ["f13smBUjVM5aTGvryOe2LQ", "47Cs6IYElOBK5jyf6MFKqL"]], [5, 112, 123]], [5, true, ["_active"], -245], [4, ["_anchorPoint"], -246, [0, 0, 0.5]], [5, false, ["_active"], -247], [5, false, ["_active"], -248], [4, ["_lpos"], -249, [1, -310, -52, 0]], [6, ["_contentSize"], [2, ["71zD9bLs9Mq7lonERFJ2Qn"]], [5, 260, 35]], [5, 0, ["_statusIndex"], -250], [5, "0", ["currStatusName"], -251], [6, ["_contentSize"], [2, ["96C4JU9MpA/ZlFPdWApYj2"]], [5, 244, 34]], [5, true, ["_active"], -252], [4, ["_lpos"], -253, [1, -202, 0, 0]], [21, "", ["_string"], [2, ["cdnlTQpTJDFLkfdNI3v4Tq"]]], [6, ["_contentSize"], [2, ["95K4clJ89K5LDhtN9gpjeB"]], [5, 0, 65.52]], [6, ["_contentSize"], [2, ["2d88r7bGZIr5k0w2UWhor4"]], [5, 38, 38]], [21, 1, ["_sizeMode"], [2, ["0cMIwapedPPI3oBJxFll+S"]]], [4, ["_lpos"], -254, [1, -248.04999999999998, 0, 0]], [6, ["_lpos"], [2, ["70Q6eMd81IGqk+LIGq/Yzk"]], [1, 317.178, -25.652, 0]], [4, ["_lscale"], -255, [1, 1, 1, 1]], [4, ["_lpos"], -256, [1, -66, 0, 0]], [5, 0, ["_right"], -257], [5, 9, ["_top"], -258], [4, ["_lscale"], -259, [1, 1, 1, 1]], [4, ["_lpos"], -260, [1, -204, 0, 0]], [5, 149, ["_right"], -261], [5, 9, ["_top"], -262], [6, ["_lpos"], [2, ["10k98IkY5DuoNXsricp7jv", "79BcNar8tDmZucBHLjoa8/"]], [1, -342, 0, 0]], [5, 305.45, ["_right"], -263], [5, 10.6, ["_top"], -264]]], [[42, ["statusNodes", "0"], 17, [2, ["b2QoCvpR9PQ7/byygakvuO"]], 17, [2, ["749LXfhDlNKpFGD9kWraJg"]]], [42, ["statusNodes", "1"], 17, [2, ["b2QoCvpR9PQ7/byygakvuO"]], 17, [2, ["9dyECLwZ5AGL1Hkq5Woil1"]]], [42, ["statusNodes", "2"], 17, [2, ["b2QoCvpR9PQ7/byygakvuO"]], 17, [2, ["f8cpP/4edI1rgT8n8IFJLE"]]]], 1]], [10, "bg", 33554432, 2, [[3, -266, [0, "38m1OFDkRKv5BU9x5x/2Ok"], [5, 640, 1280]], [37, 2, false, -267, [0, "3fnkReFAxMmruImPms5zgb"]]], [1, "9bUMpzGTpA0ZwWxbB0RdY3", null, null, null, 1, 0]], [2, ["bc2uWjqHtNapRxXJ0PmLNM"]], [7, "item", 33554432, 7, [-269], [[3, -268, [0, "07r5i4Ak9Ni5Oujqs75zEn"], [5, 110, 110]]], [1, "88GnyFQZpEcYfB2d/GKQIp", null, null, null, 1, 0], [1, -218, 2, 0]], [12, 0, {}, 67, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -270, [15, "66M95K2alPhqeq8zULOlQ1", 1, [[5, "ty_item1", ["_name"], 40], [4, ["_lpos"], 40, [1, 0, 0, 0]], [4, ["_lrot"], 40, [3, 0, 0, 0, 1]], [4, ["_euler"], 40, [1, 0, 0, 0]]]], 13]], [11, "txt_name", 33554432, 7, [[8, -271, [0, "33fPSH00RD/KiA2IF1OiJl"], [5, 130, 50.4], [0, 0, 0.5]], [71, "緹奧魔法書", 0, 26, 26, true, -272, [0, "7cx2CpbnJLzrzdgrMnRMHm"], [4, 4280249295]]], [1, "75jraciaVIa4+LR87EDTPS", null, null, null, 1, 0], [1, -150, 38, 0]], [11, "txt_des", 33554432, 7, [[8, -273, [0, "75lH80AWdH4ZS+luMAlR1H"], [5, 250, 56.49999999999999], [0, 0, 1]], [72, "可以用来从图书馆中学习魔法知识", 0, 0, 22, 22, 25, 3, true, -274, [0, "9emf7Nc0tMk7zlUsI7YBHi"], [4, 4278190080]]], [1, "5fPBosmcVNJYV7cdsLO1Vb", null, null, null, 1, 0], [1, -150, 14, 0]], [12, 0, {}, 7, [14, "03XfDFaqNOdp6SCwdhIQpD", null, null, -275, [15, "6dONJAaGlGgrQNtC7bMFf6", 1, [[5, "btn_ty_75", ["_name"], 41], [4, ["_lpos"], 41, [1, 200, 0, 0]], [4, ["_lrot"], 41, [3, 0, 0, 0, 1]], [4, ["_euler"], 41, [1, 0, 0, 0]], [6, ["_lpos"], [2, ["5eZAD6dg1PkIthJGeTXCdA"]], [1, 0, 0, 0]], [6, ["_lpos"], [2, ["5cvq8qCo5I7pXjdLcFwDsb"]], [1, 0, 2, 0]]]], 14]], [10, "bg", 33554432, 4, [[3, -276, [0, "d9GQGvvkNPwKP6HpLsndf3"], [5, 640, 1280]], [37, 2, false, -277, [0, "990GMO6o9BW5cEDAGXmMsi"]]], [1, "82GK0Lxd1CY7RmEVsSAUq4", null, null, null, 1, 0]], [7, "item", 33554432, 5, [-279], [[3, -278, [0, "daay1x2Q5BU7iTipp+Q+Qs"], [5, 110, 110]]], [1, "4fu4fFy7tMQr2IMXFnZK1V", null, null, null, 1, 0], [1, -218, 2, 0]], [12, 0, {}, 73, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -280, [15, "fdtjLGZT5ARY0MPSJuQ65q", 1, [[5, "ty_item1", ["_name"], 56], [4, ["_lpos"], 56, [1, 0, 0, 0]], [4, ["_lrot"], 56, [3, 0, 0, 0, 1]], [4, ["_euler"], 56, [1, 0, 0, 0]]]], 34]], [17, "txt_name", 33554432, 5, [[8, -281, [0, "28Ea88kpFGCq1sXvkxvLvw"], [5, 104, 100.8], [0, 0, 0.5]], [73, "名字", 0, 52, 52, 80, true, -282, [0, "78yBZHU3FMb6dhhCloWE29"], [4, 4283515135]]], [1, "18p+IZKWlDEJhW3Z1A60gf", null, null, null, 1, 0], [1, -150, 29.884, 0], [1, 0.5, 0.5, 1]], [12, 0, {}, 5, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -287, [15, "984mqsNGFJOYi/Kq1XoZ3c", 1, [[5, "list_xh1", ["_name"], 58], [4, ["_lpos"], 58, [1, 196, 30.584, 0]], [4, ["_lrot"], 58, [3, 0, 0, 0, 1]], [4, ["_euler"], 58, [1, 0, 0, 0]], [78, ["_spriteFrame"], -283, 36], [6, ["_contentSize"], [2, ["6eLK82LLdNRKcY/pydBpFc"]], [5, 28, 28]], [5, 2, ["_sizeMode"], -284], [6, ["_lpos"], [2, ["ab6OaQyhFLSaBnxtHZuhoA"]], [1, 17, 0, 0]], [6, ["_contentSize"], [2, ["47Cs6IYElOBK5jyf6MFKqL"]], [5, 92.615234375, 30]], [6, ["_lpos"], [2, ["5dxakxnn1IH5IOSWRbNW25"]], [1, -32.3076171875, 0, 0]], [5, "x2100", ["_string"], 26], [6, ["_contentSize"], [2, ["feEue8VsVDB4gRggznsuay"]], [5, 117.23046875, 61.44]], [5, 40, ["_fontSize"], 26], [5, 40, ["_actualFontSize"], 26], [5, 44, ["_lineHeight"], 26], [5, true, ["_affectedByScale"], -285], [5, 6, ["_spacingX"], -286], [4, ["_color"], 26, [4, 4281742902]], [5, true, ["_enableOutline"], 26]]], 35]], [12, 0, {}, 5, [14, "03XfDFaqNOdp6SCwdhIQpD", null, null, -288, [15, "f1uEimIGJJyJfyR3qz+atR", 1, [[5, "btn_ty_75", ["_name"], 59], [4, ["_lpos"], 59, [1, 196, -14, 0]], [4, ["_lrot"], 59, [3, 0, 0, 0, 1]], [4, ["_euler"], 59, [1, 0, 0, 0]], [6, ["_lpos"], [2, ["5eZAD6dg1PkIthJGeTXCdA"]], [1, 0, 0, 0]], [6, ["_lpos"], [2, ["5cvq8qCo5I7pXjdLcFwDsb"]], [1, 0, 2, 0]], [21, "", ["_string"], [2, ["bc648ctydDD5l0O0o5vV7i"]]]]], 37]], [12, 0, {}, 4, [14, "03XfDFaqNOdp6SCwdhIQpD", null, null, -289, [15, "c3DX/nGAtGQZhhgmmInvhE", 1, [[4, ["_lpos"], 61, [1, -137.25, -445.083, 0]], [5, "btn_ty1", ["_name"], 61], [4, ["_lrot"], 61, [3, 0, 0, 0, 1]], [4, ["_euler"], 61, [1, 0, 0, 0]], [6, ["_contentSize"], [2, ["89Kmzp7M5JRJxKSraI3F2C"]], [5, 190, 64]], [21, 1, ["_sizeMode"], [2, ["43Z+zpMHNH95/fghPESQqh"]]], [6, ["_contentSize"], [2, ["1657nTijNEh7Bns5UYA0sV"]], [5, 19, 74]]]], 40]], [12, 0, {}, 4, [14, "03XfDFaqNOdp6SCwdhIQpD", null, null, -290, [15, "8cmGi7QF1DEZ9Q4cMZsQML", 1, [[4, ["_lpos"], 62, [1, 135.907, -445.083, 0]], [5, "btn_ty2", ["_name"], 62], [4, ["_lrot"], 62, [3, 0, 0, 0, 1]], [4, ["_euler"], 62, [1, 0, 0, 0]], [6, ["_contentSize"], [2, ["89Kmzp7M5JRJxKSraI3F2C"]], [5, 190, 64]], [21, 1, ["_sizeMode"], [2, ["43Z+zpMHNH95/fghPESQqh"]]], [6, ["_contentSize"], [2, ["1657nTijNEh7Bns5UYA0sV"]], [5, 192, 74]]]], 41]], [12, 0, {}, 6, [14, "74gXne1LNBxbeD40dL+ibu", null, null, -294, [15, "50CFKZEJ1LorrLvWVO/kI5", 1, [[4, ["_lpos"], 63, [1, 0, -1.0860000000000127, 0]], [5, "downer", ["_name"], 63], [4, ["_lrot"], 63, [3, 0, 0, 0, 1]], [4, ["_euler"], 63, [1, 0, 0, 0]], [5, 1.0860000000000127, ["_top"], -291], [5, -1.0860000000000127, ["_bottom"], -292], [5, 0, ["_horizontalCenter"], -293], [6, ["_anchorPoint"], [2, ["98uxGpqZhKArK2Ac6EtMoa"]], [0, 0.5, 0.5]], [6, ["_lpos"], [2, ["34tMfY/05JDZEmxyODLq8p"]], [1, 0, -597.5, 0]], [21, false, ["_active"], [2, ["d9qPuY0LRFqY3LXFdevKVd"]]]]], 42]], [2, ["2bkx+/WHxITKU6QCZLhzFu"]], [2, ["fb5ROS0d9OPp1XIe+p7u/S"]], [2, ["43MxB5wuhC/pg5QU6tDl5o"]], [2, ["f13smBUjVM5aTGvryOe2LQ", "79BcNar8tDmZucBHLjoa8/"]], [2, ["96qdgGS7hBMLKOQYeKUHLS"]], [2, ["62ebj2/O1EcLWYj0eeYKBo"]], [2, ["64LlQPXztDToD5U2fxgqJG"]], [2, ["bb+gzw+61LHY0+g7OuxqPd"]], [2, ["0auZUEG5hBO4WZpQ5ji5vT", "79BcNar8tDmZucBHLjoa8/"]], [2, ["0auZUEG5hBO4WZpQ5ji5vT", "19n/K2C05CNY1bdUhWOfAY"]], [2, ["c7OUAvDZlGoaEwRvzGTwGL", "79BcNar8tDmZucBHLjoa8/"]], [2, ["c7OUAvDZlGoaEwRvzGTwGL", "60AusxFx5IdKYWfLRUe+HB"]], [2, ["10k98IkY5DuoNXsricp7jv", "60AusxFx5IdKYWfLRUe+HB"]], [2, ["90X4zuDxlM2ap0ok+kkml6"]], [2, ["46wgLU6vlHt54vDxwmo6H/"]]], 0, [0, -1, 80, 0, -2, 79, 0, -3, 78, 0, -4, 77, 0, -5, 76, 0, -6, 74, 0, -7, 53, 0, -8, 71, 0, -9, 68, 0, -10, 38, 0, -11, 37, 0, -12, 64, 0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 30, 0, -2, 6, 0, 0, 2, 0, 0, 2, 0, -1, 65, 0, -2, 8, 0, -3, 37, 0, -4, 18, 0, -5, 7, 0, -6, 3, 0, -7, 52, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, -4, 15, 0, -5, 16, 0, 0, 4, 0, 0, 4, 0, -1, 72, 0, -2, 21, 0, -3, 23, 0, -4, 60, 0, -5, 78, 0, -6, 79, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 73, 0, -2, 75, 0, -3, 57, 0, -4, 76, 0, -5, 77, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 64, 0, -4, 80, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 67, 0, -2, 69, 0, -3, 70, 0, -4, 71, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 9, 0, -2, 10, 0, -3, 11, 0, 0, 9, 0, 6, 9, 0, 0, 9, 0, -1, 31, 0, -2, 32, 0, 0, 9, 0, -1, 31, 0, -2, 32, 0, 0, 10, 0, 6, 10, 0, 0, 10, 0, -1, 33, 0, -2, 34, 0, 0, 10, 0, -1, 33, 0, -2, 34, 0, 0, 11, 0, 6, 11, 0, 0, 11, 0, -1, 35, 0, -2, 36, 0, 0, 11, 0, -1, 35, 0, -2, 36, 0, 0, 12, 0, 6, 12, 0, 0, 12, 0, -1, 42, 0, -2, 43, 0, 0, 12, 0, -1, 42, 0, -2, 43, 0, 0, 13, 0, 6, 13, 0, 0, 13, 0, -1, 44, 0, -2, 45, 0, 0, 13, 0, -1, 44, 0, -2, 45, 0, 0, 14, 0, 6, 14, 0, 0, 14, 0, -1, 46, 0, -2, 47, 0, 0, 14, 0, -1, 46, 0, -2, 47, 0, 0, 15, 0, 6, 15, 0, 0, 15, 0, -1, 48, 0, -2, 49, 0, 0, 15, 0, -1, 48, 0, -2, 49, 0, 0, 16, 0, 6, 16, 0, 0, 16, 0, -1, 50, 0, -2, 51, 0, 0, 16, 0, -1, 50, 0, -2, 51, 0, 0, 18, 0, 8, 20, 0, 0, 18, 0, 0, 18, 0, 10, 38, 0, 0, 18, 0, -1, 19, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, -1, 20, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, -1, 38, 0, 0, 21, 0, 0, 21, 0, 8, 29, 0, 0, 21, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, -1, 29, 0, 0, 23, 0, 8, 25, 0, 0, 23, 0, 0, 23, 0, 10, 53, 0, 0, 23, 0, -1, 24, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, -1, 25, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 0, 29, 0, 0, 29, 0, -1, 53, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 1, 66, 0, 1, 66, 0, 1, 66, 0, 0, 37, 0, 4, 37, 0, 1, 39, 0, 1, 39, 0, 1, 39, 0, 1, 39, 0, 4, 38, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 52, 0, 1, 54, 0, 1, 54, 0, 1, 54, 0, 1, 54, 0, 1, 55, 0, 1, 55, 0, 1, 55, 0, 1, 55, 0, 4, 53, 0, 0, 57, 0, 0, 57, 0, 0, 57, 0, 0, 60, 0, 0, 60, 0, 0, 60, 0, 1, 82, 0, 1, 83, 0, 1, 84, 0, 1, 84, 0, 1, 82, 0, 1, 83, 0, 1, 85, 0, 1, 86, 0, 1, 86, 0, 1, 87, 0, 1, 87, 0, 1, 88, 0, 1, 85, 0, 1, 88, 0, 1, 89, 0, 1, 89, 0, 1, 90, 0, 1, 90, 0, 1, 91, 0, 1, 91, 0, 1, 92, 0, 1, 92, 0, 1, 93, 0, 1, 93, 0, 4, 64, 0, 0, 65, 0, 0, 65, 0, 0, 67, 0, -1, 68, 0, 4, 68, 0, 0, 69, 0, 0, 69, 0, 0, 70, 0, 0, 70, 0, 4, 71, 0, 0, 72, 0, 0, 72, 0, 0, 73, 0, -1, 74, 0, 4, 74, 0, 0, 75, 0, 0, 75, 0, 1, 94, 0, 1, 94, 0, 1, 95, 0, 1, 95, 0, 4, 76, 0, 4, 77, 0, 4, 78, 0, 4, 79, 0, 1, 81, 0, 1, 81, 0, 1, 81, 0, 4, 80, 0, 11, 1, 2, 9, 6, 4, 9, 6, 5, 9, 25, 294], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 5, 2, 3, 3, 2, 3, 3, 2, 3, 3, 5, 5, 5, 5, 2, 2, 3, 3, 2, 3, 3, 2, 3, 3, 2, 3, 3, 2, 3, 3, 2, 5, 2, 5, 5, 12, 5, 2, 2, 5, 5, 5], [9, 10, 1, 0, 1, 0, 0, 1, 0, 0, 1, 11, 2, 2, 3, 4, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 1, 0, 5, 2, 12, 2, 13, 14, 3, 4, 5, 6, 6, 15]], [[[43, "downer"], [45, "downer", 33554432, [-4, -5, -6, -7], [[8, -2, [0, "98uxGpqZhKArK2Ac6EtMoa"], [5, 640, 1280], [0, 0.5, 1]], [57, 21, 288, 288, 64, 134, -3, [0, "2bkx+/WHxITKU6QCZLhzFu"]]], [1, "74gXne1LNBxbeD40dL+ibu", null, null, null, -1, 0], [1, 0, 640, 0]], [7, "img_downer_di1", 33554432, 1, [-12, -13, -14, -15, -16], [[3, -8, [0, "06NU6aJMJHJr/EfvYLFHwN"], [5, 640, 122]], [38, -9, [0, "41N/qoikpH8Y4u7J/eJh7P"], 10], [58, 20, -10, [0, "0cVniz8Q1AgajKtYCOdIdt"]], [66, false, 1, 1, 8, -11, [0, "91kJ7fcHhHq5nAoKFlHU1t"]]], [1, "34tMfY/05JDZEmxyODLq8p", null, null, null, 1, 0], [1, 0, -1219, 0]], [7, "btn_dowmer_zj", 33554432, 2, [-21], [[3, -17, [0, "864PQWhmtE4JgPIRoK5U7N"], [5, 120, 117]], [30, -18, [0, "68PxgIk2FPKYiF3Ckwwfk4"]], [69, 3, 0.9, -20, [0, "5bpLcBe6BOyYrZ5N+l7noP"], -19]], [1, "ef9rHj02ZM+KxzDb3LFIFb", null, null, null, 1, 0], [1, -128, 0, 0]], [7, "btn_dowmer_ks", 33554432, 2, [-25, -26], [[3, -22, [0, "ebUND5PS5FWpHWMgXnFnHf"], [5, 120, 117]], [30, -23, [0, "5ad5IYl61IgIlhkGllRT7R"]], [32, 3, 0.9, -24, [0, "4afDP9lCJOWqELmieJpKsq"]]], [1, "daSqtbc7hJtY1Gw+JSKs0q", null, null, null, 1, 0], [1, 128, 0, 0]], [13, "btn_main", 33554432, 2, [-29, -30], [[3, -27, [0, "82KAORwedFCICJqmRAFp2p"], [5, 118, 119]], [32, 3, 0.9, -28, [0, "1boQ27qzRCFqgGJl/zeCp+"]]], [1, "418+kETflKAppg+qtr9zKF", null, null, null, 1, 0]], [17, "txt_btn_zd1", 33554432, 5, [[3, -31, [0, "4djbBngztCrp7elK2lSiZv"], [5, 220, 71.52]], [74, "", 49, 48, 52, 2, true, true, 3, -32, [0, "4bNRWVM/FEjIcB9TmHBqAl"], [4, 4278453288]], [28, "jiayuan_5", -33, [0, "485f7aw8BP1JI/H/jr1YAJ"]], [27, -34, [0, "9d92m0B+FMG4gX4cxmzQ34"]]], [1, "43AI1i9TVJlpoQfWt+AB6Z", null, null, null, 1, 0], [1, 0, -27.506, 0], [1, 0.5, 0.5, 1]], [7, "btn_dowmer_hb", 33554432, 2, [-38], [[3, -35, [0, "17fJQLegRNxJQvc5MVyLjl"], [5, 120, 117]], [30, -36, [0, "dbmb0XLm5DIKtC3pmhtcZn"]], [32, 3, 0.9, -37, [0, "a0bVGUUlZGVLj0X1HMj59o"]]], [1, "50pI0OtHBJmIUSKNhY0zUR", null, null, null, 1, 0], [1, -258, 0, 0]], [13, "btn_downer_tab1", 33554432, 7, [-41, -42], [[3, -39, [0, "eah5tw5VxD0YQYjyQsQGsW"], [5, 140, 117]], [68, false, -40, [0, "4eEfa1QphBr4qEFlsiJT1z"], 2]], [1, "42FyW0f71BXYc0AO+VCR88", null, null, null, 1, 0]], [17, "txt_btn_hb1", 33554432, 8, [[3, -43, [0, "3bvfuVeBNIM513mwUKGq1v"], [5, 208.2, 68]], [33, "", 49, 48, 52, 2, true, 1, true, 3, -44, [0, "75u2I0JY5OuZRykvFJorGj"], [4, 4278453288]], [28, "mainui_1", -45, [0, "c13kNZHNVMOpFFcjzi1EG8"]], [27, -46, [0, "e7uf5x2TZPQr7CRHdvUd6L"]]], [1, "72xjcAmDRDSaVKthGpnpmo", null, null, null, 1, 0], [1, 6, -26.626000000000005, 0], [1, 0.5, 0.5, 1]], [13, "btn_downer_tab1", 33554432, 3, [-49, -50], [[3, -47, [0, "8cu5hZ5jtMV7+tWBRbitOJ"], [5, 140, 117]], [24, 1, false, -48, [0, "c7MaqT8XtBn5Qb4C74aoF0"], 4]], [1, "363sGIRP5LAJzvTx/Ew97P", null, null, null, 1, 0]], [17, "txt_btn_zj1", 33554432, 10, [[3, -51, [0, "38TRImS5FHZYJnDn7F8KzP"], [5, 208.2, 71.52]], [33, "", 49, 48, 52, 2, true, 1, true, 3, -52, [0, "39/VgH9epFw5vnu7RvbVQm"], [4, 4278453288]], [28, "zhuangbei_1", -53, [0, "03dYcpEzNE3LULaBzvD/+J"]], [27, -54, [0, "4dhVdN7+dJo5JoW5e6UdO9"]]], [1, "a2XbQ5F45DMbuwbi75jqED", null, null, null, 1, 0], [1, 6, -27.506, 0], [1, 0.5, 0.5, 1]], [13, "btn_downer_tab1", 33554432, 4, [-57, -58], [[3, -55, [0, "4cRpInFRNJWq7lk0D9V6LK"], [5, 140, 117]], [49, false, -56, [0, "07rKMOqZ9KQ41F5GDM+HPt"]]], [1, "8eBs7AJ+dLx45GbY9El8qX", null, null, null, 1, 0]], [17, "txt_btn_ks1", 33554432, 12, [[3, -59, [0, "dadKpR0KJFfpJyXFTUmuwh"], [5, 208.2, 71.52]], [33, "", 49, 48, 52, 2, true, 1, true, 3, -60, [0, "c3a/juFNJGhbWOqpL/cbFZ"], [4, 4278453288]], [28, "mainui_4", -61, [0, "3abvlbc71Do4bpQ49YddxG"]], [27, -62, [0, "34UTKHrt5MW70CG/zyu5F0"]]], [1, "60PXKIkwJBkqUZV9p1Jf4+", null, null, null, 1, 0], [1, -6, -27.506, 0], [1, 0.5, 0.5, 1]], [46, "icon_hongdian1", false, 33554432, 4, [-66], [[3, -63, [0, "0buQyUybJHfafHZSWqncmc"], [5, 33, 35]], [36, 0, -64, [0, "d3uoHZm6RKX41+oEx71nQH"], 6], [79, -65, [0, "d7BDFhW0xF4a2wuz/wslVK"], [7, 8]]], [1, "43SOUVF99Go54ftYPB2ezP", null, null, null, 1, 0], [1, 50, 50, 0]], [7, "btn_dowmer_fb", 33554432, 2, [-70], [[3, -67, [0, "4d74DVFFlFrpigywFd4ngc"], [5, 120, 117]], [30, -68, [0, "f6wol/wZpBzoB2YkiaZmLT"]], [32, 3, 0.9, -69, [0, "80K8nUNIJF8Ze3lRo1cSYw"]]], [1, "73iFvHOaVA46faxUJIjLzM", null, null, null, 1, 0], [1, 258, 0, 0]], [13, "btn_downer_tab1", 33554432, 15, [-73, -74], [[3, -71, [0, "4dXxx0nzRC6Ip9KiZkShdW"], [5, 140, 117]], [49, false, -72, [0, "cbtbGxoT5EvYXQBNfTz31e"]]], [1, "98DUi4ja1AAYw9KyRSVlpS", null, null, null, 1, 0]], [17, "txt_btn_fb1", 33554432, 16, [[3, -75, [0, "9fGkeFQE9D660EEj/1+4ij"], [5, 208.2, 68]], [33, "", 49, 48, 52, 2, true, 1, true, 3, -76, [0, "35NYyaHQVNfrYu0H9iDc/s"], [4, 4278453288]], [28, "mainui_5", -77, [0, "f1w9lGaYlMVJhUUs6YtYHa"]], [27, -78, [0, "c7JT5h1c1HD5LOSHqLUZFh"]]], [1, "62PdmVtOtPVaRLnjpAANa3", null, null, null, 1, 0], [1, -6, -26.626000000000005, 0], [1, 0.5, 0.5, 1]], [46, "mask_click_bg", false, 33554432, 1, [-82], [[3, -79, [0, "fbxtzCIBRL2qpwlHzdDePR"], [5, 640, 1280]], [22, 45, 100, 100, -80, [0, "ebWZbX8ntGzrMXkYOzdYbs"]], [51, -81, [0, "3bGnGP4fFJyrIQ6GeWXaJk"]]], [1, "98kwvcguRHd6nzAhEXOEIq", null, null, null, 1, 0], [1, 0, -640, 0]], [47, "ani_glow", false, 33554432, 1, [[8, -83, [0, "850j8hLzpCEpVEUE0wz2cQ"], [5, 642.0001220703125, 1309.8448486328125], [0, 0.6431247997581411, 0.07634491986159873]], [80, "default", "animation", false, 0, false, -84, [0, "fcv+2Bs9hJFoMZslAxheEv"], 11], [23, 4, -44.30600000000004, -85, [0, "66rsuHZn5DXIA49v4uGahr"]]], [1, "86H6/MZ9VNAol2ZzYd17oi", null, null, null, 1, 0], [1, 125.208, -1224.306, 0]], [47, "close_all_btn", false, 33554432, 1, [[3, -86, [0, "20y+MTD7tLaZ8dMVh3VHa0"], [5, 640, 1280]], [22, 45, 640, 1280, -87, [0, "9cabJzdK9F17N9XKBvTpm1"]], [51, -88, [0, "adEU+0IJ9GTrR9AY7tOOlL"]]], [1, "17rcvlVdZNUK3jppKu6Tbo", null, null, null, 1, 0], [1, 0, -640, 0]], [11, "img_downer1", 33554432, 5, [[8, -89, [0, "6eIvfk2u9AYrQ2UrOPUlx3"], [5, 132, 132], [0, 0.5, 0.4583333333333333]], [29, "default", false, 0, -90, [0, "66oMlCLJ5CxJ5LtukB7G6O"], 0]], [1, "1dCT1UiNlE86a+MhnxXvBk", null, null, null, 1, 0], [1, -1, 4, 0]], [10, "img_downer_hb1", 33554432, 8, [[8, -91, [0, "4fql2pcU9GhoImTd90E4f4"], [5, 143.6680908203125, 130.78756713867188], [0, 0.48723414921375885, 0.45433670808631693]], [29, "default", false, 0, -92, [0, "b0a4qDEKNGNrBkurKdGpDX"], 1]], [1, "20PrTRu8pJfa81Y1L1c6YF", null, null, null, 1, 0]], [10, "img_downer_zj1", 33554432, 10, [[8, -93, [0, "04vSGW4QpKJKk+Gw25ihRW"], [5, 153, 148.68362426757812], [0, 0.5, 0.4405327104626067]], [29, "default", false, 0, -94, [0, "51GWPtCrVKWbeWWfww/4JN"], 3]], [1, "5c3vGvtrNLWKUI3+278aOu", null, null, null, 1, 0]], [10, "img_downer_ks1", 33554432, 12, [[8, -95, [0, "5dj8LWbrNLHK/524AZUueg"], [5, 150.3892059326172, 139.59274291992188], [0, 0.5345410625323473, 0.42572439292331404]], [29, "default", false, 0, -96, [0, "d7VKCZocdH6IdiJliYn4tX"], 5]], [1, "e2AYR0kFlNWL9mqWqzi0dK", null, null, null, 1, 0]], [17, "txt_hd_sl1", 33554432, 14, [[3, -97, [0, "3afe6yck5LNKk0hsvv/wXE"], [5, 41.59375, 51.36]], [75, "99", 32, 32, 36, true, true, 3, -98, [0, "a33sIn9gZL/IWK+Q6qQl3f"]]], [1, "2db2iz1FBDsKJTtUk1XBxc", null, null, null, 1, 0], [1, 0, 2, 0], [1, 0.5, 0.5, 1]], [10, "img_downer_fb1", 33554432, 16, [[8, -99, [0, "e5YTsX6ehIbpULGn1w/g0s"], [5, 140, 135.59701538085938], [0, 0.5, 0.4361969118051837]], [29, "default", false, 0, -100, [0, "24bGjkQj5GZ7/7Swujg7JW"], 9]], [1, "dd6kI5zQpHdYxaiJOx/AZN", null, null, null, 1, 0]], [11, "click_bg", 33554432, 18, [[3, -101, [0, "a4agosOcpCNLS4T3BbIC4o"], [5, 640, 870]], [35, 45, 269.212, 140.788, 640, 870, -102, [0, "afUcyXpwRC4ZI86aUbg4zW"]]], [1, "0apY9j+f5Efr7+9iziYtCt", null, null, null, 1, 0], [1, 0, -64.21199999999999, 0]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 19, 0, -3, 20, 0, -4, 18, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, -2, 7, 0, -3, 3, 0, -4, 4, 0, -5, 15, 0, 0, 3, 0, 0, 3, 0, 6, 3, 0, 0, 3, 0, -1, 10, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 12, 0, -2, 14, 0, 0, 5, 0, 0, 5, 0, -1, 21, 0, -2, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, 0, 8, 0, -1, 22, 0, -2, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, -1, 23, 0, -2, 11, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, -1, 24, 0, -2, 13, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, -1, 25, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, -1, 16, 0, 0, 16, 0, 0, 16, 0, -1, 26, 0, -2, 17, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, -1, 27, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 11, 1, 102], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, 7, 2, 7, 2, 7, 2, -1, -2, 7, 2, 7], [16, 17, 7, 18, 7, 19, 8, 8, 20, 21, 22, 23]]]]