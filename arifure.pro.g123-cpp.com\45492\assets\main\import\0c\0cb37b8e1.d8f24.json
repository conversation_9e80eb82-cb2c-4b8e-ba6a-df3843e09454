[1, ["2f/iTwe7BCIL3XlsN4UxGF@f9941", "2f/iTwe7BCIL3XlsN4UxGF@6c48a", "61Haos/flMWJILVDOLjlgW@6c48a", "c6iudGYnhEk4LB2kqw6cFt@6c48a", "d5j4AhkolNnqsIP/ovqElw@6c48a", "e5RkyYIENDIppjKmhP+Vjj@6c48a", "ebbk0fcxRCCJp/i2gFY3jp@6c48a", "d5j4AhkolNnqsIP/ovqElw@f9941", "e5RkyYIENDIppjKmhP+Vjj@f9941", "61Haos/flMWJILVDOLjlgW@f9941", "c6iudGYnhEk4LB2kqw6cFt@f9941", "1dezwRWiVLIIdsyCyckem9@f9941", "ebbk0fcxRCCJp/i2gFY3jp@f9941", "86gnoxNAVGHKoMShFcHdq7@f9941", "08peoyqBhHzL9an9EmP+/A@f9941", "3fnJCq3hZPRqAusq6KiVus@f9941"], ["node", "_spriteFrame", "_textureSource", "_cameraComponent", "scene", "_parent"], [["cc.Node", ["_name", "_layer", "_id", "_active", "_components", "_parent", "_lpos", "_children", "_lscale"], -1, 9, 1, 5, 2, 5], "cc.SpriteFrame", ["cc.Widget", ["_alignFlags", "_originalWidth", "_bottom", "_left", "_top", "_right", "_originalHeight", "node"], -4, 1], ["cc.UITransform", ["node", "_contentSize", "_anchorPoint"], 3, 1, 5, 5], ["cc.Mask", ["_inverted", "node"], 2, 1], ["cc.Sprite", ["node", "_spriteFrame"], 3, 1, 6], ["cc.SceneAsset", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_lpos"], 2, 1, 2, 5], ["cc.<PERSON>", ["node", "_cameraComponent"], 3, 1, 1], ["cc.Graphics", ["node", "_fillColor"], 3, 1, 5], ["0e053LUNEVPzLKpVGpCvvm5", ["node"], 3, 1], ["cc.Animation", ["node"], 3, 1], ["7563fP+WuxL1p5rM8Q2NGlY", ["node", "spfs"], 3, 1, 3], ["cc.Label", ["_string", "_horizontalAlign", "_verticalAlign", "_actualFontSize", "_fontSize", "_lineHeight", "_overflow", "_isBold", "_enableOutline", "_outlineWidth", "node"], -7, 1], ["cc.Layout", ["node"], 3, 1], ["cc.Scene", ["_name", "_children", "_prefab", "_globals"], 2, 2, 4, 4], ["cc.PrefabInfo", ["root", "asset", "fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots"], -3], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyColorHDR", "_groundAlbedoHDR"], 3, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", [], 3], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", [], 3], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3], ["cc.Camera", ["_projection", "_priority", "_orthoHeight", "_near", "_clearFlags", "_visibility", "node", "_color"], -3, 1, 5]], [[3, 0, 1, 1], [5, 0, 1, 1], [0, 0, 1, 5, 4, 3], [3, 0, 1, 2, 1], [2, 0, 1, 6, 7, 4], [0, 0, 1, 5, 4, 6, 3], [0, 0, 1, 5, 4, 6, 8, 3], [9, 0, 1, 1], [0, 0, 1, 5, 7, 4, 3], [4, 1, 1], [0, 0, 3, 1, 5, 4, 6, 4], [3, 0, 1], [2, 0, 3, 5, 4, 2, 1, 7, 7], [13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], [6, 0, 2], [0, 0, 1, 2, 7, 4, 6, 4], [0, 0, 5, 4, 2], [7, 0, 1, 2, 3, 2], [8, 0, 1, 1], [2, 0, 4, 2, 7, 4], [2, 0, 3, 5, 1, 7, 5], [2, 0, 3, 2, 1, 7, 5], [4, 0, 1, 2], [5, 0, 1], [10, 0, 1], [11, 0, 1], [12, 0, 1, 1], [14, 0, 1], [15, 0, 1, 2, 3, 2], [16, 0, 1, 2, 3, 4, 5, 7], [17, 0, 1, 2, 3, 4, 5, 6, 7, 1], [18, 0, 1, 1], [19, 0, 1, 1], [20, 1], [21, 1], [22, 1], [23, 1], [24, 1], [25, 1], [26, 0, 1, 2, 3, 4, 5, 6, 7, 7]], [[[{"name": "logo", "rect": {"x": 0, "y": 0, "width": 803, "height": 501}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 803, "height": 501}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-401.5, -250.5, 0, 401.5, -250.5, 0, -401.5, 250.5, 0, 401.5, 250.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 501, 803, 501, 0, 0, 803, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -401.5, "y": -250.5, "z": 0}, "maxPos": {"x": 401.5, "y": 250.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [1], 0, [0], [2], [1]], [[{"name": "main_bg_3", "rect": {"x": 0, "y": 0, "width": 1040, "height": 640}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1040, "height": 640}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-520, -320, 0, 520, -320, 0, -520, 320, 0, 520, 320, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 640, 1040, 640, 0, 0, 1040, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -520, "y": -320, "z": 0}, "maxPos": {"x": 520, "y": 320, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [1], 0, [0], [2], [2]], [[{"name": "main_bg_4", "rect": {"x": 0, "y": 0, "width": 1040, "height": 640}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1040, "height": 640}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-520, -320, 0, 520, -320, 0, -520, 320, 0, 520, 320, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 640, 1040, 640, 0, 0, 1040, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -520, "y": -320, "z": 0}, "maxPos": {"x": 520, "y": 320, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [1], 0, [0], [2], [3]], [[{"name": "main_bg_1", "rect": {"x": 0, "y": 0, "width": 1040, "height": 640}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1040, "height": 640}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-520, -320, 0, 520, -320, 0, -520, 320, 0, 520, 320, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 640, 1040, 640, 0, 0, 1040, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -520, "y": -320, "z": 0}, "maxPos": {"x": 520, "y": 320, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [1], 0, [0], [2], [4]], [[{"name": "main_bg_2", "rect": {"x": 0, "y": 0, "width": 1040, "height": 640}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1040, "height": 640}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-520, -320, 0, 520, -320, 0, -520, 320, 0, 520, 320, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 640, 1040, 640, 0, 0, 1040, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -520, "y": -320, "z": 0}, "maxPos": {"x": 520, "y": 320, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [1], 0, [0], [2], [5]], [[{"name": "logo_ko", "rect": {"x": 0, "y": 0, "width": 803, "height": 514}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 803, "height": 514}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-401.5, -257, 0, 401.5, -257, 0, -401.5, 257, 0, 401.5, 257, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 514, 803, 514, 0, 0, 803, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -401.5, "y": -257, "z": 0}, "maxPos": {"x": 401.5, "y": 257, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [1], 0, [0], [2], [6]], [[[14, "Main"], [15, "<PERSON><PERSON>", 33554432, "beI88Z2HpFELqR4T5EMHpg", [-5, -6, -7, -8, -9, -10, -11, -12, -13], [[0, -1, [5, 640, 1280]], [18, -3, -2], [19, 45, 5.684341886080802e-14, 5.684341886080802e-14, -4]], [1, 320, 640, 0]], [8, "mainbg", 33554432, 1, [-18, -19, -20, -21, -22, -23, -24], [[0, -14, [5, 640, 1280]], [22, true, -15], [7, -16, [4, 16777215]], [4, 45, 100, 100, -17]]], [8, "main_bg", 33554432, 2, [-27, -28, -29, -30], [[0, -25, [5, 2680, 3000]], [23, -26]]], [2, "viewRoot", 33554432, 1, [[0, -31, [5, 640, 1280]], [4, 45, 640, 100, -32], [9, -33], [7, -34, [4, 16777215]]]], [8, "<PERSON><PERSON><PERSON>", 33554432, 1, [-38], [[11, -35], [24, -36], [25, -37]]], [2, "uiTopRoot", 33554432, 1, [[0, -39, [5, 640, 1280]], [4, 45, 640, 100, -40], [9, -41], [7, -42, [4, 16777215]]]], [2, "uiTouchRoot", 33554432, 1, [[0, -43, [5, 640, 1280]], [4, 45, 640, 100, -44], [9, -45], [7, -46, [4, 16777215]]]], [10, "logo", false, 33554432, 2, [[3, -47, [5, 803, 501], [0, 1, 0.5]], [1, -48, 5], [20, 8, -851.1970000000001, 688.197, 803, -49], [26, -50, [6, 7, 8]]], [1, -368.1970000000001, 0, 0]], [10, "shuiyin1", false, 33554432, 2, [[3, -51, [5, 721, 42], [0, 1, 0.5]], [1, -52, 4], [21, 12, -750, 10, 721, -53]], [1, -349, -609, 0]], [5, "s<PERSON><PERSON>n", 33554432, 2, [[3, -54, [5, 720, 200], [0, 1, 0]], [13, "©白米良・オーバーラップ／ありふれた製作委員会", 2, 2, 31, 30, 32, 2, true, true, 4, -55], [12, 36, -780, 700, 1220, 18, 720, -56]], [1, -380, -622, 0]], [5, "shuiyin2", 33554432, 2, [[3, -57, [5, 740, 200], [0, 0, 0]], [13, "©佐藤大輔・佐藤ショウジ/KADOKAWA/H.O.T.D.製作委員会", 0, 2, 31, 30, 32, 2, true, true, 4, -58], [12, 36, -780, -770.47, 1220, 0.6349999999999909, 720, -59]], [1, 350.47, -639.365, 0]], [2, "zxrw_tips", 33554432, 1, [[0, -60, [5, 640, 1280]], [27, -61]]], [2, "guide_lay", 33554432, 1, [[0, -62, [5, 640, 1280]], [4, 45, 100, 100, -63]]], [6, "main_bg_1", 33554432, 3, [[0, -64, [5, 1040, 640]], [1, -65, 0]], [1, -780, 480, 0], [1, 1.5, 1.5, 1]], [6, "main_bg_2", 33554432, 3, [[0, -66, [5, 1040, 640]], [1, -67, 1]], [1, 780, 480, 0], [1, 1.5, 1.5, 1]], [6, "main_bg_3", 33554432, 3, [[0, -68, [5, 1040, 640]], [1, -69, 2]], [1, -780, -480, 0], [1, 1.5, 1.5, 1]], [6, "main_bg_4", 33554432, 3, [[0, -70, [5, 1040, 640]], [1, -71, 3]], [1, 780, -480, 0], [1, 1.5, 1.5, 1]], [5, "logo_ld_wy", 33554432, 2, [[0, -72, [5, 613, 418]], [1, -73, 9]], [1, 871.301, 0, 0]], [5, "logo_ld_wy2", 33554432, 2, [[0, -74, [5, 552, 344]], [1, -75, 10]], [1, -932.172, 0, 0]], [28, "Main", [1], [29, null, null, "f1d033ca-1d96-490e-8e4b-affd8db5bbec", null, null, null], [30, [31, [2, 0, 0, 0, 0.520833125], [2, 0, 0, 0, 0]], [32, [4, 4283190348], [0, 512, 512]], [33], [34], [35], [36], [37], [38]]], [17, "Camera", 1, [-76], [1, 0, 0, 1000]], [39, 0, 1, 640, 0, 6, 1108344832, 21, [4, 4278190080]], [2, "serverTimeRendr", 33554432, 5, [[11, -77]]], [16, "uiLoading", 1, [[3, -78, [5, 45.84197235107422, 85.71095275878906], [0, 0.4607383934258219, 0.011215430496997322]]]]], 0, [0, 0, 1, 0, 3, 22, 0, 0, 1, 0, 0, 1, 0, -1, 21, 0, -2, 4, 0, -3, 5, 0, -4, 12, 0, -5, 24, 0, -6, 13, 0, -7, 6, 0, -8, 7, 0, -9, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 9, 0, -3, 8, 0, -4, 18, 0, -5, 19, 0, -6, 10, 0, -7, 11, 0, 0, 3, 0, 0, 3, 0, -1, 14, 0, -2, 15, 0, -3, 16, 0, -4, 17, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 23, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, -1, 22, 0, 0, 23, 0, 0, 24, 0, 4, 20, 1, 5, 20, 78], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, -1, -2, -3, 1, 1], [7, 8, 9, 10, 11, 0, 0, 12, 13, 14, 15]]]]