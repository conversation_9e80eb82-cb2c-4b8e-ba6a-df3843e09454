import{d as r,c as u,u as e,r as P,f as I,g as h}from"./game-fd4578d2-CJjC28Jv.js";import{c as T}from"./game-6f09ab72-Bi-UxEEh.js";import{a as C,P as v}from"./game-8cc43aba-CiMuGUxk.js";import{T as y,a as R}from"./game-4959c158-C6dCbD6U.js";import{u as _}from"./game-33507154-FZO9iRpN.js";import"./game-1db2353a-OPVs455c.js";import"./game-d8b296a6-D6-XlEtG.js";import"./app-rLJ3tKt9.js";const b=({onDismiss:t})=>{const{t:s}=_(),o=C(g=>g.pwa.isPwaInstallPromptReady),[n,m]=r(-R),[p,a]=r(!0),[i,d]=r(!1),c=()=>{a(!1),t?.()},w=async()=>{o&&window?.pwaInstallPrompt?(window.pwaInstallPrompt.prompt(),await window.pwaInstallPrompt.userChoice):I.error(s("common.actions.add_to_desktop.pwa_unsupported"),{variant:"dark"})},f=()=>{a(!1),d(!1),w()};return u(()=>{setTimeout(()=>{m(0)},1e3)},[]),e("div",{className:P("absolute z-10 w-full","pointer-events-none select-none overflow-visible",{hidden:i,flex:!i}),children:e(y,{isTriggerShown:p,triggerBottomOffset:n,onTriggerClose:c,onTriggerConfirm:f})})},l="pwaInstallPrompt";function A(t){if(window?.pwaInstallPrompt){const{handleDismiss:s}=t||{};let o=document.getElementById(l);o&&document.body.removeChild(o),o=document.createElement("div"),o.id=l,o.className="g123App",document.body.appendChild(o),T(o).render(e(v,{store:h,children:e(b,{onDismiss:s})}))}}export{A as show};
