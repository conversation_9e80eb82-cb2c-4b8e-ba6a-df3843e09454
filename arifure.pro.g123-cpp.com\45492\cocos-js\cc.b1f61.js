System.register(["./_virtual_cc-1b85bdd4.js"],(function(e){"use strict";return{setters:[function(a){e({Acceleration:a.ef,AffineTransform:a.az,AlphaKey:a.c6,AmbientInfo:a.dn,AnimCurve:a.a5,Animation:a.a3,AnimationClip:a.a1,AnimationComponent:a.a3,AnimationManager:a.a9,AnimationState:a.a2,Asset:a.dM,AssetManager:a.e0,AsyncDelegate:a.bH,AudioClip:a.ad,AudioPCMDataView:a.ae,AudioSource:a.ac,AudioSourceComponent:a.ac,BASELINE_RATIO:a.x,BITMASK_TAG:a.cy,BaseNode:a.db,BaseRenderData:a.t,BitMask:a.ba,BitmapFont:a.B,BlockInputEvents:a.fm,BloomStage:a.cZ,BoxCollider2D:a.eI,BufferAsset:a.dN,BuiltinResMgr:a.e2,Button:a.f7,CCBoolean:a.bx,CCClass:a.bs,CCFloat:a.bw,CCInteger:a.bv,CCObject:a.bu,CCString:a.by,CacheMode:a.q,CachedArray:a.b9,CallbacksInvoker:a.cC,Camera:a.dA,Canvas:a.C,CircleCollider2D:a.eJ,Collider2D:a.eH,Color:a.aE,ColorKey:a.c5,CompactValueTypeArray:a.bz,Component:a.dg,Contact2DType:a.eB,DEFAULT_OCTREE_DEPTH:a.du,DEFAULT_WORLD_MAX_POS:a.dt,DEFAULT_WORLD_MIN_POS:a.ds,DebugMode:a.bU,DebugView:a.d5,DeferredPipeline:a.cV,DeferredPipelineBuilder:a.da,Details:a.dK,Director:a.dF,DistanceJoint2D:a.eM,DynamicAtlasManager:a.X,ECollider2DType:a.ex,EJoint2DType:a.ey,ENUM_TAG:a.cx,EPSILON:a.aJ,EPhysics2DDrawFlags:a.eC,ERaycast2DType:a.eA,ERigidBody2DType:a.ew,EasingMethod:a.cB,EditBox:a.f8,EditorExtendable:a.bE,EffectAsset:a.dV,Enum:a.bb,Event:a.e6,EventAcceleration:a.e7,EventGamepad:a.eb,EventHMD:a.ed,EventHandheld:a.ee,EventHandle:a.ec,EventHandler:a.df,EventInfo:a.a6,EventKeyboard:a.e8,EventMouse:a.e9,EventTarget:a.bF,EventTouch:a.ea,Eventify:a.bG,ExtrapolationMode:a.c0,FixedJoint2D:a.eR,FogInfo:a.dq,Font:a.F,ForwardFlow:a.cT,ForwardPipeline:a.cR,ForwardPipelineBuilder:a.d9,ForwardStage:a.cU,GCObject:a.cu,Game:a.dH,GbufferStage:a.cX,Gradient:a.c7,Graphics:a.G,HALF_PI:a.aH,HingeJoint2D:a.eT,HorizontalTextAlignment:a.H,HtmlTextParser:a.w,ImageAsset:a.dS,Input:a.ej,InstanceMaterialType:a.I,InstancedBuffer:a.d1,Intersection2D:a.eo,JavaScript:a.dZ,Joint2D:a.eL,JsonAsset:a.dR,KeyCode:a.eg,LRUCache:a.A,Label:a.r,LabelAtlas:a.L,LabelOutline:a.m,LabelShadow:a.o,Layers:a.dd,Layout:a.f9,LightProbeInfo:a.dy,LightingStage:a.cY,MATH_FLOAT_ARRAY:a.b5,MIDDLE_RATIO:a.y,MainFlow:a.cW,Mask:a.h,Mat3:a.aw,Mat4:a.ax,Material:a.dW,MathBase:a.b6,MeshBuffer:a.M,MeshRenderData:a.v,MissingScript:a.dD,MobilityMode:a.dl,ModelRenderer:a.dB,MotionStreak:a.eq,MotionStreakAssemblerManager:a.er,MouseJoint2D:a.eO,Node:a.db,NodeActivator:a.dh,NodeEventType:a.dm,NodePool:a.e4,NodeSpace:a.dj,ObjectCurve:a.c4,OctreeInfo:a.dv,Overflow:a.O,PHYSICS_2D_PTM_RATIO:a.eD,PageView:a.fi,PageViewIndicator:a.fj,ParticleAsset:a.et,ParticleSystem2D:a.ep,ParticleSystem2DAssembler:a.es,Physics2DManifoldType:a.eF,Physics2DUtils:a.eu,PhysicsGroup:a.ez,PhysicsSystem2D:a.eE,PipelineEventProcessor:a.d3,PipelineEventType:a.d4,PipelineSceneData:a.cQ,PipelineStateManager:a.d2,PolygonCollider2D:a.eK,Pool:a.b7,PostProcessStage:a.c_,PostSettingsInfo:a.dx,Prefab:a.di,PrefabLink:a.dE,Profiler:a.eU,ProgressBar:a.fa,Quat:a.au,QuatCurve:a.c2,QuatInterpolationMode:a.c3,RatioSampler:a.a4,RealCurve:a.b_,RealInterpolationMode:a.b$,Rect:a.aC,RecyclePool:a.b8,ReflectionProbeFlow:a.d6,ReflectionProbeStage:a.d7,RelativeJoint2D:a.eP,RenderData:a.u,RenderFlow:a.cO,RenderPipeline:a.cN,RenderRoot2D:a.R,RenderStage:a.cP,RenderTexture:a.dX,Renderer:a.dC,RenderingSubMesh:a.dO,ResolutionPolicy:a.fq,RichText:a.i,RigidBody2D:a.eG,Root:a.ah,SafeArea:a.fk,Scene:a.dc,SceneAsset:a.dP,SceneGlobals:a.dz,Scheduler:a.bZ,Script:a.dY,ScrollBar:a.fb,ScrollView:a.fc,Settings:a.c8,ShadowFlow:a.c$,ShadowStage:a.d0,ShadowsInfo:a.dr,Size:a.aA,SkinInfo:a.dw,SkyboxInfo:a.dp,Slider:a.fd,SliderJoint2D:a.eQ,Sorting:a.$,SortingLayers:a.Z,SpringJoint2D:a.eN,Sprite:a.j,SpriteAtlas:a.a,SpriteFrame:a.b,SpriteRenderer:a.f,StencilManager:a.S,SubContextView:a.fn,System:a.ca,SystemEvent:a.el,SystemEventType:a.e5,TTFFont:a.T,TWO_PI:a.aI,TangentWeightMode:a.c1,TextAsset:a.dQ,Texture2D:a.dT,TextureCube:a.dU,TiledLayer:a.e_,TiledMap:a.eX,TiledMapAsset:a.eY,TiledObjectGroup:a.e$,TiledTile:a.f0,TiledUserNodeData:a.eZ,Toggle:a.fe,ToggleContainer:a.ff,Touch:a.eh,TransformBit:a.dk,Tween:a.f5,TweenAction:a.f6,TweenSystem:a.f2,TypeScript:a.d_,UIComponent:a.U,UICoordinateTracker:a.fl,UIMeshRenderer:a.k,UIOpacity:a.p,UIRenderer:a.d,UIStaticBatch:a.n,UITransform:a.e,VERSION:a.al,ValueType:a.bd,Vec2:a.ao,Vec3:a.aq,Vec4:a.as,VerticalTextAlignment:a.V,VideoClip:a.fs,VideoPlayer:a.ft,View:a.fp,ViewGroup:a.fg,WebGL2Device:a.fu,WebGLDevice:a.en,WheelJoint2D:a.eS,Widget:a.fh,WorldNode3DToLocalNodeUI:a.bo,WorldNode3DToWorldNodeUI:a.bp,__checkObsoleteInNamespace__:a.bn,__checkObsolete__:a.bm,_decorator:a.br,_resetDebugSetting:a.cE,absMax:a.b1,absMaxComponent:a.b0,animation:a.a0,applyMixins:a.cD,approx:a.aL,assert:a.bM,assertID:a.bQ,assertIsNonNullable:a.cI,assertIsTrue:a.cJ,assertsArrayIndex:a.cK,assetManager:a.d$,bezier:a.cd,bezierByTime:a.ce,binarySearch:a.cF,binarySearchBy:a.cH,binarySearchEpsilon:a.cG,bits:a.an,builtinResMgr:a.e3,ccenum:a.bc,cclegacy:a.am,clamp:a.aM,clamp01:a.aN,color:a.aF,computeRatioByType:a.a8,convertUtils:a.bq,createDefaultPipeline:a.cS,debug:a.bI,deprecateModuleExportedName:a.bl,deserialize:a.dJ,deserializeTag:a.bB,director:a.dG,disallowAnimation:a.co,displayName:a.cj,displayOrder:a.ck,dynamicAtlasManager:a.Y,easing:a.cb,editable:a.cg,editorExtrasTag:a.bA,enumerableProps:a.b2,equals:a.aK,error:a.bK,errorID:a.bO,find:a.de,flattenCodeArray:a.cL,floatToHalf:a.b3,formerlySerializedAs:a.cq,fragmentText:a.W,game:a.dI,garbageCollectionManager:a.ct,geometry:a.ak,getBaselineOffset:a.z,getEnglishWordPartAtFirst:a.P,getEnglishWordPartAtLast:a.Q,getError:a.bT,getPathFromRoot:a.aa,getPhaseID:a.d8,getSerializationMetadata:a.bD,getWorldTransformUntilRoot:a.ab,gfx:a.af,graphicsAssembler:a.g,halfToFloat:a.b4,input:a.ei,instantiate:a.dL,inverseLerp:a.a$,isCCClassOrFastDefined:a.bt,isCCObject:a.cz,isDisplayStats:a.bR,isEnglishWordPartAtFirst:a.K,isEnglishWordPartAtLast:a.N,isUnicodeCJK:a.D,isUnicodeSpace:a.E,isValid:a.cA,js:a.be,jsbUtils:a.cf,labelAssembler:a.l,lerp:a.aO,log:a.bJ,logID:a.bN,macro:a.bX,markAsWarning:a.bk,mat4:a.ay,math:a.ai,memop:a.aj,misc:a.bf,murmurhash2_32_gc:a.cc,native:a.em,nextPow2:a.aY,override:a.cp,path:a.bg,pingPong:a.a_,pipeline:a.cM,preTransforms:a.aG,profiler:a.eV,pseudoRandom:a.aV,pseudoRandomRange:a.aW,pseudoRandomRangeInt:a.aX,quat:a.av,random:a.aR,randomRange:a.aT,randomRangeInt:a.aU,range:a.cl,rangeStep:a.cm,rect:a.aD,removeProperty:a.bj,renderer:a.ag,repeat:a.aZ,replaceProperty:a.bi,resources:a.e1,safeMeasureText:a.J,sampleAnimationCurve:a.a7,screen:a.bV,selector:a.ev,serializable:a.cr,serializeTag:a.bC,setDefaultLogTimes:a.bh,setDisplayStats:a.bS,setPropertyEnumType:a.cv,setPropertyEnumTypeOnAttrs:a.cw,setRandGenerator:a.aS,settings:a.c9,shift:a.cs,size:a.aB,slide:a.cn,sp:a.eW,spriteAssembler:a.s,sys:a.bW,systemEvent:a.ek,tiledLayerAssembler:a.f1,toDegree:a.aQ,toRadian:a.aP,tooltip:a.ch,tween:a.f3,tweenUtil:a.f4,v2:a.ap,v3:a.ar,v4:a.at,view:a.fr,visible:a.ci,visibleRect:a.bY,warn:a.bL,warnID:a.bP,widgetManager:a.fo})}],execute:function(){}}}));
