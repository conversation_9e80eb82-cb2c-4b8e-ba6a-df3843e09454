@layer base{*,::backdrop,::file-selector-button,:after,:before{border:0 solid;box-sizing:border-box;margin:0;padding:0}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:--theme(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:--theme(--default-font-feature-settings,normal);font-variation-settings:--theme(--default-font-variation-settings,normal);-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-tap-highlight-color:transparent}hr{border-top-width:1px;color:inherit;height:0}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:--theme(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:--theme(--default-mono-font-feature-settings,normal);font-size:1em;font-variation-settings:--theme(--default-mono-font-variation-settings,normal)}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{border-collapse:collapse;border-color:inherit;text-indent:0}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}menu,ol,ul{list-style:none}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{height:auto;max-width:100%}::file-selector-button,button,input,optgroup,select,textarea{background-color:transparent;border-radius:0;color:inherit;font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;opacity:1}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::-moz-placeholder{opacity:1}::placeholder{opacity:1}@supports (not (-webkit-appearance:-apple-pay-button)) or (contain-intrinsic-size:1px){::-moz-placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-meridiem-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-year-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}::file-selector-button,button,input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}}@layer base{*,::backdrop,::file-selector-button,:after,:before{border-color:var(--color-gray-200,currentColor)}}body,html{font-size:16px;-webkit-font-smoothing:antialiased;height:100%;inset:0;overflow:hidden;position:fixed;width:100%}body,html,iframe{touch-action:none}body{background-color:#000}.skip-link{background:#000;color:#fff;left:0;padding:8px;position:absolute;top:-40px;z-index:100}.skip-link:focus{top:0}#splash{align-items:center;background-color:#136c72;display:flex;height:100%;justify-content:center;inset:0;position:absolute;width:100%;z-index:-100}#splash img,#splash svg{height:auto;width:200px}#splash-progress-container{align-items:flex-end;display:none;flex-direction:row;height:180px;left:auto;position:absolute;top:auto;width:360px}#splash-progress{background-color:#ffffff40;border-radius:4px;height:8px;overflow:hidden;width:100%}#splash-progress span{background-color:#e3ff34;background-size:100vw;border-radius:3px;box-shadow:inset 0 1px #ffffff80;display:block;height:100%;position:relative;transition:width 1s ease-in;width:0}@media screen and (max-width:576px){#splash img,#splash svg{width:130px}#splash-progress-container{height:126px;width:280px}}#iframe-game{border:0;display:block;height:100%;opacity:0;width:100%}@media screen and (orientation:landscape){#header{width:100%}}.seo-description-header-1{border-width:0;height:1px;margin:-1px;overflow:hidden;overflow-wrap:normal!important;padding:0;position:absolute!important;width:1px;word-break:normal!important;clip:"rect(1px, 1px, 1px, 1px)";clip-path:"inset(50%)"}.hidden{display:none!important}#open-with-default-browser-top{background-color:#000c;display:none;inset:0;position:fixed;z-index:10000}#open-with-default-browser-top .imgs{float:right}#open-with-default-browser-top .imgs .main{margin-top:21px;width:218px}#open-with-default-browser-top .imgs .close{position:absolute;right:190px;top:10px;width:41px}#open-with-default-browser-bottom{background-color:#000c;display:none;inset:0;position:fixed;z-index:10000}#open-with-default-browser-bottom .imgs{bottom:0;position:absolute;right:0}#open-with-default-browser-bottom .imgs .main{width:218px}#open-with-default-browser-bottom .imgs .close{position:absolute;right:190px;top:-15px;width:41px}.CustomerSupportBox{bottom:4rem;height:650px;position:fixed;right:1rem;width:375px;z-index:var(--z-index-gbutton)}@media only screen and (max-width:576px){.CustomerSupportBox{height:100%;left:0;opacity:.95;position:absolute;top:0;width:100%}}.close{border:0 solid #fff;border-radius:50%;box-sizing:border-box;cursor:pointer;height:43px;position:absolute;right:0;top:0;width:43px}.close:before{transform:rotate(45deg)}.close:after,.close:before{background-color:#333a41;content:"";height:2px;left:50%;margin-left:-9px;margin-top:-1px;position:absolute;top:50%;width:18px}.close:after{transform:rotate(-45deg)}
