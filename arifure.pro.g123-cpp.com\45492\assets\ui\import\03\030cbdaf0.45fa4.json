[1, ["adTZ8rYgREdpmmUtZlpw1Z@f9941", "f28HUhyqRHHKbqjFyhgzH9@f9941", "10I8iCpWVA062UoMj6NsQ4", "28bfwm51dOHoLVvMxcWZRh@f9941", "d2YRdRZ5NMT5O9QwcUGHSe", "09Yng9V0ZOC6g+bFLu3bRL", "9a7ojTp0dCYo0YJERsgFoY@f9941", "8esgF/4NJKA4U67TKFSc9s@f9941", "65+2zCPkhHIrRZYwYrZ0cZ@f9941", "eeftyx1l1Ak5StnB/jP3xQ@f9941", "63Zn/NUG1K1Za2w0l5SFxp@f9941", "a7ZHpooe1A07RKUYPDmCm2", "98mS0mBYFBNI8JaJt7A8/o@f9941", "fc5KYzrxRGZZFwHFncwS3y@f9941", "f28HUhyqRHHKbqjFyhgzH9@6c48a"], ["node", "_spriteFrame", "_parent", "target", "source", "targetInfo", "root", "asset", "_defaultClip", "_content", "data", "_textureSource"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_lpos", "_children", "_lscale", "_lrot", "_euler"], -2, 4, 9, 1, 5, 2, 5, 5, 5], ["cc.Widget", ["_alignFlags", "_top", "_originalWidth", "_originalHeight", "_left", "_bottom", "_right", "node", "__prefab"], -4, 1, 4], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_affectedByScale", "_enabled", "_spacingY", "_paddingTop", "node", "__prefab"], -4, 1, 4], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_lineHeight", "_isBold", "_fontSize", "_overflow", "_enableOutline", "_outlineWidth", "_enableWrapText", "node", "__prefab", "_outlineColor", "_color"], -6, 1, 4, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.TargetOverrideInfo", ["propertyPath", "source", "sourceInfo", "target", "targetInfo"], 2, 1, 4, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "mountedComponents", "propertyOverrides", "mountedChil<PERSON>n", "removedComponents"], 2, 1, 9, 9, 9, 9], ["cc.Animation", ["playOnLoad", "node", "__prefab", "_clips", "_defaultClip"], 2, 1, 4, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.TargetInfo", ["localID"], 2], ["cc.MountedChildrenInfo", ["targetInfo", "nodes"], 3, 4, 2], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["cc.UIOpacity", ["node", "__prefab"], 3, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab"], 1, 1, 4], ["<PERSON><PERSON>", ["bounceDuration", "brake", "horizontal", "node", "__prefab", "_content"], 0, 1, 4, 1], ["ad253TrMIpHFZgL3Bp6KTbe", ["_batch", "node", "__prefab", "tmpNode", "pageChangeEvent", "renderEvent", "selectedEvent"], 2, 1, 4, 1, 4, 4, 4], ["cc.ClickEvent", [], 3], ["7563fP+WuxL1p5rM8Q2NGlY", ["node", "__prefab", "spfs"], 3, 1, 4, 3]], [[12, 0, 2], [15, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [16, 0, 2], [20, 0, 1, 2, 2], [3, 2, 3, 4, 1], [0, 0, 1, 9, 6, 5, 8, 3], [21, 0, 1, 2, 2], [0, 0, 1, 7, 9, 6, 5, 3], [0, 0, 1, 7, 6, 5, 8, 3], [5, 0, 1, 2, 3, 1], [6, 1, 2, 3, 4, 1], [0, 0, 1, 7, 9, 6, 5, 8, 3], [0, 0, 1, 7, 6, 5, 8, 10, 3], [3, 0, 2, 3, 4, 2], [26, 0, 1, 2, 2], [30, 1], [31, 0, 1, 2, 1], [0, 3, 4, 7, 5, 3], [11, 0, 1, 2, 3, 4, 5, 6, 3], [1, 0, 7, 8, 2], [1, 0, 1, 7, 8, 3], [14, 0, 1, 2, 3, 4, 5, 4], [18, 0, 1, 1], [19, 0, 1, 2, 3], [3, 1, 0, 2, 3, 4, 3], [3, 1, 0, 2, 3, 3], [2, 7, 8, 1], [27, 0, 1, 2, 3, 3], [10, 0, 2], [0, 0, 1, 9, 6, 5, 3], [0, 0, 2, 1, 9, 6, 5, 8, 4], [0, 0, 1, 7, 6, 5, 10, 3], [0, 0, 2, 1, 7, 9, 6, 5, 8, 4], [0, 0, 2, 1, 7, 6, 5, 8, 4], [0, 0, 2, 1, 7, 6, 5, 8, 11, 12, 4], [0, 0, 1, 7, 6, 5, 3], [1, 0, 2, 3, 7, 8, 4], [1, 0, 4, 6, 1, 5, 2, 3, 7, 8, 8], [1, 0, 5, 7, 8, 3], [1, 0, 4, 1, 7, 8, 4], [13, 0, 1, 2, 3, 4, 5, 3], [6, 0, 1, 2, 3, 4, 2], [7, 0, 1, 4, 2, 3, 5, 2], [7, 0, 1, 2, 3, 2], [17, 0, 1, 1], [8, 1, 2, 3, 4, 1], [8, 0, 1, 2, 3, 4, 2], [22, 0, 1, 1], [3, 2, 3, 1], [23, 0, 1, 1], [2, 0, 1, 2, 3, 7, 8, 5], [2, 4, 0, 1, 7, 8, 4], [2, 0, 1, 2, 7, 8, 4], [2, 0, 1, 5, 3, 7, 8, 5], [2, 0, 1, 6, 7, 8, 4], [24, 0, 1, 1], [25, 0, 1, 2, 1], [4, 0, 1, 4, 2, 3, 9, 10, 12, 6], [4, 0, 1, 4, 2, 5, 3, 9, 10, 7], [4, 0, 1, 2, 3, 9, 10, 5], [4, 0, 1, 2, 8, 3, 6, 7, 9, 10, 11, 8], [4, 0, 1, 2, 5, 3, 6, 7, 9, 10, 11, 8], [28, 0, 1, 2, 3, 4, 5, 4], [29, 0, 1, 2, 3, 4, 5, 6, 2]], [[[[29, "dixiayiji_ksbx"], [30, "dixiayiji_ksbx", 33554432, [-16], [[2, -14, [0, "d8Zq2f+xhIhZRIP80AJ4OH"], [5, 640, 1280]], [20, 18, -15, [0, "81782Oba9IF45TfXeI7cgx"]]], [41, "c46/YsCPVOJYA4mWEpNYRx", null, -13, 0, [[11, -4, [3, ["1ewSJMpRxDzZgm7T8hYvdX"]], -3, [3, ["1ewSJMpRxDzZgm7T8hYvdX"]]], [11, -6, [3, ["72nsteiR9AHbJm455pmF/k"]], -5, [3, ["72nsteiR9AHbJm455pmF/k"]]], [11, -8, [3, ["d8iUwHt0hETKFJE6WBmUXw"]], -7, [3, ["d8iUwHt0hETKFJE6WBmUXw"]]], [11, -10, [3, ["35RZRk3exEx7bsRtb5VNYa"]], -9, [3, ["35RZRk3exEx7bsRtb5VNYa"]]], [42, ["tmpNode"], -12, [3, ["7fhwtMeZ9FrYijU6klI9f1"]], -11, [3, ["c0hi6si6dDurQFojJYc2dM"]]]], [-1, -2]]], [18, 0, {}, 1, [22, "79BcNar8tDmZucBHLjoa8/", null, null, -23, [43, "0aw7ITDzFKaKQ+kKH3oBV1", 1, [[45, [3, ["1fsEC25OBJgoi9GEKoIKfz"]], [-22]]], [[23, [3, ["b0Z2p+e89KwbvfAK7hx5EH"]], [-21]]], [[24, "ty_tips_1", ["_name"], -17], [4, ["_lpos"], -18, [1, 0, 0, 0]], [4, ["_lrot"], -19, [3, 0, 0, 0, 1]], [4, ["_euler"], -20, [1, 0, 0, 0]], [7, ["_contentSize"], [3, ["a8yqhYeWNJcLI3gVLCtZZ1"]], [5, 598, 654]], [7, ["_lpos"], [3, ["b0Z2p+e89KwbvfAK7hx5EH"]], [1, 0, 285, 0]], [7, ["_contentSize"], [3, ["aaJpjQcuxBA6G/l2SbxiF5"]], [5, 557, 547]], [7, ["_lpos"], [3, ["29Ph8qd5NBALHzar7VE2v1"]], [1, 0, -357, 0]], [7, ["_contentSize"], [3, ["88MR1y1E9HsLmj95IcTT4P"]], [5, 523, 738]]], [[3, ["bb225yrLhDQ5VOo/G7os5O"]], [3, ["d73FunEIpCgJNJHRSfGcLx"]], [3, ["8bMXvYDfJClLJsujq0y2A9"]], [3, ["8eN7Rq5yBLQaK+feJo9EF4"]]]], 0]], [31, "levItem", false, 33554432, [-28, -29, -30], [[2, -24, [0, "167UBnvbFNg4qigp9E1Bo6"], [5, 492, 82]], [46, -25, [0, "d8iUwHt0hETKFJE6WBmUXw"], [20], 21], [48, -26, [0, "dcrTK9mixPVLnAg/BExcsj"]], [5, -27, [0, "35RZRk3exEx7bsRtb5VNYa"], 22]], [1, "82Er2J9sRMrJDNm1ElfjuH", null, null, null, 1, 0], [1, 0, -83, 0]], [6, "bg_tips_1", 33554432, [-34, -35, -36], [[2, -31, [0, "a8yqhYeWNJcLI3gVLCtZZ1"], [5, 598, 654]], [25, 1, 0, -32, [0, "6ar4qEo+1BvqNyFi7XMf3l"], 6], [50, -33, [0, "08Pgego05LQpoLd8O0Y5ah"]]], [1, "adU4hcIF1KBIngaH7EmOZ6", null, null, null, 1, 0], [1, 0, 42, 0]], [8, "ani_in", 33554432, 2, [-42], [[2, -37, [0, "d9OrvkIAVDmbEjJx3Cr0Xo"], [5, 452, 535]], [27, -38, [0, "08NLvQ4JpHR42gPYCLSfbv"]], [47, true, -39, [0, "a6rFwUB3dLSJ2bJ71B4og3"], [1], 2], [49, -40, [0, "a26BcbLtpAboaZx3mV/JuP"]], [20, 2, -41, [0, "188ss4RxFKpI0DWLudxjoO"]]], [1, "24tCD+YXhKCLwFSDt4YRnG", null, null, null, 1, 0]], [6, "click_close", 33554432, [-45, -46, -47], [[2, -43, [0, "e4D+Prbk5MErMgRCU2caSo"], [5, 270, 24]], [51, 1, 1, 2, true, -44, [0, "8ezZ89Ni9LR7x96Xo6WnJk"]]], [1, "29Ph8qd5NBALHzar7VE2v1", null, null, null, 1, 0], [1, 0, -357, 0]], [6, "view", 33554432, [-52], [[10, -48, [0, "6etqE17zVJnoSNbVERjoR0"], [5, 504, 491.9], [0, 0.5, 1]], [56, -49, [0, "dcqrFnN2xIDYL0yyf9rlqt"]], [57, -50, [0, "feP3a44bFF9JJwCZplI4wZ"], [4, 16777215]], [37, 45, 240, 250, -51, [0, "99jEawbARCuZp3KvYnQW98"]]], [1, "51OZhARMlFoqXk1YoItjqG", null, null, null, 1, 0], [1, 0, 245.95, 0]], [6, "item", 33554432, [-55, 3], [[10, -53, [0, "5aSj0mXpBHhbPyhCw54eSa"], [5, 492, 42], [0, 0.5, 1]], [52, false, 1, 2, -54, [0, "f14LWSSRBOFqDInWEZJy0p"]]], [1, "c0hi6si6dDurQFojJYc2dM", null, null, null, 1, 0], [1, 0, -6, 0]], [12, "img_yiji_di5", 33554432, 8, [-58, -59, -60], [[2, -56, [0, "dd/VAEgEVMjpUS/PLMb4FP"], [5, 503, 42]], [5, -57, [0, "1afjdAVAxCW6pJqMUbs9dc"], 9]], [1, "6eshp9FqBMs6Q5X2/J9z6/", null, null, null, 1, 0], [1, 0, -14.386, 0]], [12, "layout_xx", 33554432, 3, [-63, -64, -65], [[2, -61, [0, "c69M0FXUxHmICqH6K2q4t2"], [5, 143, 62.8]], [53, 1, 1, 4, -62, [0, "06UDOZFy9KWr59hNOXOqbA"]]], [1, "24Jf7UEgVDra2klFX6ZiP/", null, null, null, 1, 0], [1, -33.867999999999995, 0, 0]], [6, "bg_tips_8", 33554432, [-68], [[2, -66, [0, "d5RUqMQXlK3JEEAIO9dIGo"], [5, 504, 494]], [26, 1, 0, -67, [0, "e9X5ZXqRRNAapJvBzGknzA"]]], [1, "e9mhhEjlVKjY+uqaSfAh2y", null, null, null, 1, 0], [1, 0, 22.6, 0]], [12, "neirong", 33554432, 4, [11], [[2, -69, [0, "aaJpjQcuxBA6G/l2SbxiF5"], [5, 557, 547]], [27, -70, [0, "6708FesS9JAovuydwwVcvT"]], [38, 45, 20.5, 20.5, 86, 21, 482, 420, -71, [0, "5eZdaeA3xHQI4CPyYgk/kQ"]]], [1, "1fsEC25OBJgoi9GEKoIKfz", null, null, null, 1, 0], [1, 0, -32.5, 0]], [8, "<PERSON>_suofang", 33554432, 5, [4, 6], [[2, -72, [0, "88MR1y1E9HsLmj95IcTT4P"], [5, 523, 738]], [54, 1, 2, 60, true, -73, [0, "bbY45haOlM5qLSY7/7nS7G"]]], [1, "10U9/vnbBONoruSblOhHzL", null, null, null, 1, 0]], [32, "txt_gb", 33554432, 6, [[2, -74, [0, "4fWK/e/RpHoIUitppwo9Rm"], [5, 308, 75.6]], [58, "點擊空白處關閉", 44, 44, 60, true, -75, [0, "b3J8hPlwRGIZBp39Y3kOHz"], [4, 3221225471]], [39, 2, -125.39999999999998, -76, [0, "16bUIVxypFRrhyDk8X1FnZ"]], [15, "djkb", -77, [0, "4eBrrFUMxBB5WWmciQILOX"]]], [1, "78n3BOogNNKaIOv5y9wQoP", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [19, "txt_title1", 33554432, 4, [[[2, -78, [0, "8a1ZxWWyxPyIyY//yWxbVN"], [5, 700, 100]], [59, "", 53, 52, 70, 2, true, -79, [0, "daLVJEkn9IhpizEzbnEOJb"]], [21, 1, 17, -80, [0, "70/3vA5OpN8LaNyd5FUwzR"]], -81], 4, 4, 4, 1], [1, "b0Z2p+e89KwbvfAK7hx5EH", null, null, null, 1, 0], [1, 0, 285, 0], [1, 0.5, 0.5, 1]], [33, "img_xian_2", false, 33554432, 4, [-85], [[2, -82, [0, "0baviFw+VGYZTyKzUPGLoM"], [5, 450, 3]], [26, 1, 0, -83, [0, "46ptCyrGlH9oRpEAXLnZIn"]], [21, 1, 89, -84, [0, "dasWxlv1RNrrhMkFumI+ux"]]], [1, "ecdLmaKdFMOqhExD1woACw", null, null, null, 1, 0], [1, 0, 66, 0]], [34, "btn_close", false, 33554432, 16, [[2, -86, [0, "69dLSBEyBP/6SdzwdLZ0KL"], [5, 59, 61]], [5, -87, [0, "f252Zzgq9C/6SR8iAEnw25"], 5], [40, 9, 445, -85, -88, [0, "f22AcbXlNFxINSS0W0MnO0"]], [28, 3, 0.9, -89, [0, "e0ES++bdRON7wqagLbPS/g"]]], [1, "9aMeua5xxHo7Wg7Ik8OTd0", null, null, null, 1, 0], [1, 243.5, 59.5, 0]], [6, "listView", 33554432, [7], [[2, -90, [0, "1ewSJMpRxDzZgm7T8hYvdX"], [5, 504, 491.9]], [63, 0.23, 0.75, false, -92, [0, "d4SRwcywxBcoxnrHi2cRcX"], -91], [64, false, -93, [0, "7fhwtMeZ9FrYijU6klI9f1"], 8, [16], [16], [16]]], [1, "ed6pyai4tDv756si+vG9yU", null, null, null, 1, 0], [1, 0, 0.95, 0]], [8, "content", 33554432, 7, [8], [[10, -94, [0, "72nsteiR9AHbJm455pmF/k"], [5, 504, 48], [0, 0.5, 1]], [55, 1, 2, 6, -95, [0, "ddWEi+yElFILBWy+fOri5N"]]], [1, "80KWzvFw5AopSC3bHAAGmZ", null, null, null, 1, 0]], [3, ["03XfDFaqNOdp6SCwdhIQpD"]], [8, "bg_tips_9", 33554432, 11, [18], [[2, -96, [0, "90S0s9v3JD4o2pb7kbP24g"], [5, 504, 494]], [25, 1, 0, -97, [0, "4a/3YtBm5AOJHj1u+X8Dph"], 23]], [1, "b9kGcgIcZNa4aoDUfflJrr", null, null, null, 1, 0]], [35, "btn_arrow_13", false, 33554432, 9, [[2, -98, [0, "6ah0MCtTlLRqfu9M7UlEhE"], [5, 41, 43]], [5, -99, [0, "c3r4EYO05NSIkSN8syO612"], 8], [28, 3, 0.9, -100, [0, "c84YB8XMdF3r5ZwTA6GJ6g"]]], [1, "06Qd6DXi5CmYLJyTbSK9zr", null, null, null, 1, 0], [1, 214.107, 0, 0], [3, 0, 0, 1, 6.123233995736766e-17], [1, 0, 0, 180]], [9, "img_yj_xx1", 33554432, 10, [[2, -101, [0, "987HwNCT9Jh7R601PTAqAx"], [5, 45, 45]], [14, 0, -102, [0, "7exiV6cuxMjpImMXg/Wpvg"], 10], [17, -103, [0, "94y0m08btMFKqKvZ/GiJr8"], [11, 12]]], [1, "06UGke2nBM25IXOXWuMtiB", null, null, null, 1, 0], [1, -49, 0, 0]], [36, "img_yj_xx2", 33554432, 10, [[2, -104, [0, "18RWwUQkpIDodzXc9xsolm"], [5, 45, 45]], [14, 0, -105, [0, "9fa7HaEopJYLwi0ksn/hfp"], 13], [17, -106, [0, "3cYdKTxOZMy5lveM18xtf1"], [14, 15]]], [1, "d19ZNVkulCi7yqFg/3EXIH", null, null, null, 1, 0]], [9, "img_yj_xx3", 33554432, 10, [[2, -107, [0, "fa6W3zzS5Hk535AOArtV+E"], [5, 45, 45]], [14, 0, -108, [0, "4c7RyD4UpE85RdvV0fXeXg"], 16], [17, -109, [0, "466tuPxC9PTp+tvC5KUWTz"], [17, 18]]], [1, "9ffvR+2hRN27NuVgh7Xrvw", null, null, null, 1, 0], [1, 49, 0, 0]], [18, 0, {}, 3, [22, "03XfDFaqNOdp6SCwdhIQpD", null, null, -111, [44, "49EEL+vERIPY5VDitY5oJB", 1, [[23, [3, ["5cvq8qCo5I7pXjdLcFwDsb"]], [-110]]], [[24, "btn_ty_75", ["_name"], 20], [4, ["_lpos"], 20, [1, 174.14, 0, 0]], [4, ["_lrot"], 20, [3, 0, 0, 0, 1]], [4, ["_euler"], 20, [1, 0, 0, 0]], [4, ["_lscale"], 20, [1, 0.75, 0.75, 1]]]], 19]], [3, ["79BcNar8tDmZucBHLjoa8/"]], [13, "img_jt1", 33554432, 6, [[2, -112, [0, "eee/9eN+NJzKI628HWK7x1"], [5, 56, 31]], [5, -113, [0, "68BOlH9JlMJJNVsCv0L5TE"], 3]], [1, "5cO5jcQ/dJF502OHYmrDg3", null, null, null, 1, 0], [1, -107, 0, 0], [1, -1, 1, 1]], [9, "img_jt2", 33554432, 6, [[2, -114, [0, "881UsWnGJCfapWV3Ch/EkM"], [5, 56, 31]], [5, -115, [0, "3bWlSrUlRPELoUCq41DPmO"], 4]], [1, "a1v7P3vClBB7uq9CDdFhA7", null, null, null, 1, 0], [1, 107, 0, 0]], [9, "icon_fk", 33554432, 9, [[2, -116, [0, "0aPHHrn+FH8IzGY60qrCbw"], [5, 24, 24]], [5, -117, [0, "1eMLkiA19Obq7VKuGKXIFA"], 7]], [1, "630bpSiIlM3III5c1SJ4ZN", null, null, null, 1, 0], [1, -226.699, 2.915, 0]], [13, "txt_level", 33554432, 9, [[10, -118, [0, "9bXDggdvJPqpBQtWu8Kd5B"], [5, 253.45703125, 52.92], [0, 0, 0.5]], [60, "遗迹1层 26/27", 40, 42, true, -119, [0, "c0A54wRr5H9IKUO6C+g/oh"]]], [1, "91xVJL0b9CfKoZUWFrV53A", null, null, null, 1, 0], [1, -205.906, 2.915, 0], [1, 0.5, 0.5, 1]], [13, "txt_gq", 33554432, 3, [[2, -120, [0, "8aGf7+sV9EE4CfiZx8T/J7"], [5, 108.24609375, 58.92]], [61, "第8关", 40, 42, false, true, true, 3, -121, [0, "80fNW+u89OZbqdfyPAO2nA"], [4, 4280229916]]], [1, "05uA/DI1JELYSGE2lTnjya", null, null, null, 1, 0], [1, -198.08, 0, 0], [1, 0.5, 0.5, 1]], [19, "txt_tab_1", 33554432, 26, [[[2, -122, [0, "77Mt/A7RxGrJ0Q7zFDQxkY"], [5, 300, 70.56]], [62, "", 41, 56, 2, true, true, 3, -123, [0, "bc648ctydDD5l0O0o5vV7i"], [4, 4282867204]], -124], 4, 4, 1], [1, "5cvq8qCo5I7pXjdLcFwDsb", null, null, null, 1, 0], [1, 0, 2, 0], [1, 0.5, 0.5, 1]], [15, "dxyj_32", 15, [0, "162ncCVjROnYzEkqMG8U+X"]], [15, "QW", 33, [0, "71EAqYfzpBtY1UoCX0Tqix"]]], 0, [0, -1, 2, 0, -2, 26, 0, 3, 2, 0, 4, 2, 0, 3, 2, 0, 4, 2, 0, 3, 2, 0, 4, 2, 0, 3, 2, 0, 4, 2, 0, 3, 2, 0, 4, 2, 0, 6, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 5, 27, 0, 5, 27, 0, 5, 27, 0, 5, 27, 0, -1, 34, 0, -1, 11, 0, 6, 2, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 32, 0, -2, 10, 0, -3, 26, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 15, 0, -2, 16, 0, -3, 12, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 13, 0, 0, 6, 0, 0, 6, 0, -1, 28, 0, -2, 14, 0, -3, 29, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 19, 0, 0, 8, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 9, 0, -1, 30, 0, -2, 31, 0, -3, 22, 0, 0, 10, 0, 0, 10, 0, -1, 23, 0, -2, 24, 0, -3, 25, 0, 0, 11, 0, 0, 11, 0, -1, 21, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, -4, 34, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, -1, 17, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 9, 19, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, -1, 35, 0, 6, 26, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, -3, 35, 0, 10, 1, 3, 2, 8, 4, 2, 13, 6, 2, 13, 7, 2, 18, 8, 2, 19, 11, 2, 12, 18, 2, 21, 124], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [7, -1, 8, 1, 1, 1, 1, 1, 1, 1, 1, -1, -2, 1, -1, -2, 1, -1, -2, 7, -1, 8, 1, 1], [5, 2, 2, 3, 3, 6, 7, 8, 9, 10, 0, 0, 1, 0, 0, 1, 0, 0, 1, 11, 4, 4, 12, 13]], [[{"name": "bg_hb_tj_xx1", "rect": {"x": 0, "y": 0, "width": 75, "height": 75}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 75, "height": 75}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-37.5, -37.5, 0, 37.5, -37.5, 0, -37.5, 37.5, 0, 37.5, 37.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 75, 75, 75, 0, 0, 75, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -37.5, "y": -37.5, "z": 0}, "maxPos": {"x": 37.5, "y": 37.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [9], 0, [0], [11], [14]]]]