const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/g123_base-0qVdWh3D.css"])))=>i.map(i=>d[i]);
import{s as I,_ as J}from"./psp-cd6a1b8d-CSw1A9l7.js";import{g as V,c as T,R as Z,u,r as Q,a as ee,C as te,d as L,A as ne,b as oe,T as ae,q as C,y as b,s as W,e as se,l as re,D as N,i as ie,f as le,h as pe,j as ce}from"./psp-f1fd60fd-CDYNsoiC.js";import{D as de,s as ue,C as fe,P as me,o as Pe,w as F,a as ye,r as ge,g as he,b as U,i as we,c as Se,d as _e,L as Ee,e as k,f as Re}from"./psp-80684902-PJUjq1z_.js";import{S as v}from"./psp-3c7ed04b-BuqB0BnI.js";var M={exports:{}},H;function Ce(){return H||(H=1,function(n){(function(t){var e=!1,o,p;function f(){if(typeof o<"u")return o;var i=document.documentElement,r=document.createElement("div");return r.setAttribute("style","width:99px;height:99px;position:absolute;top:-9999px;overflow:scroll;"),i.appendChild(r),o=r.offsetWidth-r.clientWidth,i.removeChild(r),o}function g(){return document.documentElement.scrollHeight>window.innerHeight}function P(i){if(!(typeof document>"u"||e)){var r=document.documentElement;p=window.pageYOffset,g()?r.style.width="calc(100% - "+f()+"px)":r.style.width="100%",r.style.position="fixed",r.style.top=-p+"px",r.style.overflow="hidden",e=!0}}function _(){if(!(typeof document>"u"||!e)){var i=document.documentElement;i.style.width="",i.style.position="",i.style.top="",i.style.overflow="",window.scroll(0,p),e=!1}}function h(){if(e){_();return}P()}var E={on:P,off:_,toggle:h};n.exports=E})()}(M)),M.exports}var Ae=Ce();const $=V(Ae);async function Ie(n){let t=null,e="";const o=await fetch(`${T.SHD_G123_AUXIN_ENDPOINT}/external/v1/refund_campaign/game/${n}/status`,{method:"get",headers:{"Content-Type":"application/json",Authorization:`${window.option?.code}`}}),p=await o.json();return console.log("psp refund_campaign status result: ",p),o.status===200&&(t=p.info),t?.pay_window&&(e=(await(await fetch(`${T.SHD_G123_AUXIN_ENDPOINT}/external/v1/refund_campaign/game/${n}/order_token`,{method:"post",headers:{"Content-Type":"application/json",Authorization:`${window.option?.code}`}})).json())?.info?.token||""),{status:t,token:e}}async function Ne(n){try{let t=null;return I.getItem(v.REFUND_CAMPAIGN_TARGET_USER)==="true"?(t=await Ie(n),(!t.status||!t.status.target_user)&&I.removeItem(v.REFUND_CAMPAIGN_TARGET_USER)):I.removeItem(v.REFUND_CAMPAIGN_TARGET_USER),t}catch(t){return I.removeItem(v.REFUND_CAMPAIGN_TARGET_USER),window.captureGlobalException?.(t),null}}function ve(){window.top?.postMessage({type:"auxin_start_psp"},"*")}function De(){window.top?.postMessage({type:"auxin_cancel_psp"},"*")}function Te(n,t,e){window.top?.postMessage({type:"auxin_finish_psp",data:{is_app_first:n,amount:t,currency:e}},"*")}J(()=>Promise.resolve({}),__vite__mapDeps([0]));const Oe=Z.forwardRef(({isShown:n,children:t},e)=>u("div",{ref:e,className:Q("absolute inset-0","z-50","flex items-center","pointer-events-auto","size-full","m-0 p-0",n?"block":"hidden"),role:"presentation",onClick:o=>{o.preventDefault(),o.stopPropagation()},children:t})),xe=()=>u("div",{className:"absolute size-full",role:"presentation",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"}});function Le(n,t){const e=new URL(n);return t.lang&&e.searchParams.set("lang",t.lang),e.searchParams.set("seq",`${t.seq}`),e.searchParams.set("t",`${Math.floor(Date.now()/6e4)}`),e.href}const be=n=>{Re.success(n,{variant:"dark"})},l={pspAppChannelClient:null,lastOrder:null},D={PAYMENT_EXIT:"payment_exit",ERROR_APP_CLIENT_UNINITIALIZED:"error_app_client_uninitialized",ERROR_APP_INIT_ERROR:"error_app_init_error"},Ue=n=>{const{authCode:t,appCode:e,lang:o,pspOrigin:p,topOrigin:f,userId:g,onPaymentCompleted:P}=n,[_,h]=L(!0),[E,i]=L(!1),[r,A]=L(0),w=ne(null),{t:z}=oe(),B=ae(()=>Le(p,{lang:o,seq:r}),[p,o,r]),G=C(()=>{$.on(),i(!0)},[]),R=C(a=>{a.startsWith("ERROR")&&console.error("[PSP][SDK] EXIT_CODE",new Error(a)),$.off(),i(!1),h(!0),A(c=>c+1),a.startsWith("ERROR")?console.error("[PSP][SDK] EXIT_CODE",new Error(a)):console.info("[PSP][SDK] EXIT_CODE",a)},[]),j=C(async a=>{let c=null;try{c=await Ne(e)}catch(s){console.error(s)}l.pspAppChannelClient?(l.pspAppChannelClient.stub.init({...a,_meta:{authCode:t,userId:g,appCode:e,gameUrl:window.location.href,referer:document.referrer||"",country:k().country,region:k().region,lang:k().lang,campaignRefundStatus:c?.status||null,campaignToken:c?.token||""}}).then(()=>{ve()}).catch(s=>{console.error(D.ERROR_APP_INIT_ERROR,s),R(D.ERROR_APP_INIT_ERROR)}),G()):(console.error("pspAppChannelClient is not initialized"),R(D.ERROR_APP_CLIENT_UNINITIALIZED))},[t,e,G,g,R]),O=C(a=>{a.data&&a.data.type==="PspCommand"&&a.data.action==="EnterPayment"&&a.data.orderNo&&a.data.token&&j({orderNo:a.data.orderNo,token:a.data.token})},[j]);b(()=>{window?.top?.postMessage({event:"PaymentSdkMonitor",payload:{type:"sdk",status:"loaded",time:Date.now()}})},[]),b(()=>(console.info("[PSP][SDK] Add handleEnterPayment listener"),window.addEventListener("message",O,!1),()=>{console.info("[PSP][SDK] Remove handleEnterPayment listener"),window.removeEventListener("message",O,!1)}),[O]);const K=C(async a=>{const{amount:c,currency:s,orderNo:S,isAppFirst:m,order:d}=a;console.info("PaymentSucceed",{event:"PaymentSucceed",amount:c,currency:s,orderID:S,isAppFirst:m,order:d}),l.lastOrder=d,d?.error||be(z("payment.complete")),window.postMessage({event:"PaymentSucceed",amount:c,orderID:S,isAppFirst:m,order:d},f),Te(m,c,s)},[z,f]);return b(()=>{const a={handlePspIframeLoaded:()=>{if(console.info("[PSP][SDK] handlePspIframeLoaded()"),!w.current?.contentWindow){const s=new Error("[PSP][SDK] pspTarget is not initialized");throw console.error(s,w),s}l.lastOrder=null,l.pspAppChannelClient=new Se({target:w.current.contentWindow,channelId:_e,timeout:5e3}),h(!1)},handlePspIframeBeforeUnload:()=>{console.info("[PSP][SDK] handlePspIframeBeforeUnload()"),l.pspAppChannelClient=null},handlePaymentStart:()=>{console.warn("[PSP][SDK]handlePaymentStart is deprecated")},handlePaymentExit:()=>{R(D.PAYMENT_EXIT),P&&P(l.lastOrder?.orderNo),l.lastOrder?.orderNo||De()},handlepPaymentSucceed:K,stripePayDetect:async s=>null,stripePayStart:s=>{},applePayDetect:async()=>{const s=we();setTimeout(()=>{l.pspAppChannelClient?.stub.applePayDetectResult({isAvailable:s})})},applePayBeginSession:(s,S,m)=>{const d=ge();d||console.error("[ApplePay][applePayBeginSession] ApplePay is not supported"),he(s,m,{version:d,onpaymentauthorized:async y=>{console.log("[ApplePay][applePayModule] onpaymentauthorized"),l.pspAppChannelClient?.stub.applePayOnEvent({type:"applepay:onpaymentauthorized",event:JSON.parse(U(y))})},oncancel:async y=>{console.log("[ApplePay][applePayModule] oncancel",y,JSON.parse(U(y))),l.pspAppChannelClient?.stub.applePayOnEvent({type:"applepay:oncancel",event:JSON.parse(U(y))})}}).begin()},handlePaymentRedirect:s=>{const{type:S,url:m,orderNo:d,title:y,extra:Y}=s;if(console.log("[PSP][SDK] handlePaymentRedirect",s),S==="topredirect"){window.location.href=m;return}if(S==="toppopupget"){const x=Pe(m,y);F(x,()=>{setTimeout(()=>{l.pspAppChannelClient?.stub.refreshOrder(d)},1e3)});return}if(S==="toppopuppost"){const x=ye(m,y,{extra:Y});F(x,()=>{setTimeout(()=>{l.pspAppChannelClient?.stub.refreshOrder(d)},1e3)});return}},switchConnectType:()=>!0,ping:()=>"pong"};console.info("[PSP][SDK] Create New SdkChannelServerHandler");const c=new fe({channelId:me,handler:a});return c.start(),()=>{console.info("[PSP][SDK] Destroy SdkChannelServerHandler"),c.stop()}},[R,P,K]),u(Oe,{isShown:E,children:[u(xe,{}),u(Ee,{color:"#fff",hidden:!_}),u("div",{className:"absolute inset-0 size-full overflow-auto",children:u("iframe",{ref:w,allow:"payment",className:"flex size-full",name:"pspIframe",role:"presentation",src:B,title:"g123-psp"})})]})};function ke(n,t){ue({country:t.country,region:t.region,lang:t.lang});let e=document.getElementById(n);e||(e=document.createElement("div"),e.id=n,e.style.position="absolute",e.style.top="0",e.style.left="0",e.style.width="100%",e.style.height="100%",e.style.zIndex="9999",e.style.pointerEvents="none",document.body.appendChild(e)),e.classList.contains("psp-scope")||e.classList.add("psp-scope");const o=ee(e);return o.render(u(te,{children:[u(Ue,{...t}),u(de,{containerClassName:"top-[82px]!"})]})),()=>{e&&o.unmount()}}const Me="pspframe";window?.top?.postMessage({event:"PaymentSdkMonitor",payload:{type:"sdk",status:"loading",time:Date.now()}});const X={"h5.g123.jp":"https://psp.g123.jp","h5.semi.g123.jp":"https://psp.semi.g123.jp","h5.stg.g123.jp":"https://psp.stg.g123.jp","h5.local.g123.jp":"https://psp.local.g123.jp"}[window.location.hostname];let q=!1;async function ze(n,t){if(window!==window.top)return;const{authCode:e,appCode:o,userId:p,lang:f,country:g,region:P,onpaymentcompleted:_,envs:h}=n;if(h)W(h);else{const A=await se.get(`${X}/config`);if(A.data)W(A.data);else{const w=new Error(`[PSP] Failed to get config from ${X}/config`);throw console.error(w),w}}if(o||console.error("appCode does not exist"),p||console.error("userId does not exist"),!T.SHD_G123_PSP_URL)throw new Error("PSP_URL does not exist");const E=window.location.origin,i=new URL(T.SHD_G123_PSP_URL).origin,r=await re(f||N,"ja");await ie.use(le).init({resources:r,lng:f||N,fallbackLng:N,debug:!1,interpolation:{escapeValue:!1}}),ke(Me,{authCode:e,appCode:o,country:g||ce,region:P||pe,lang:f||N,pspOrigin:i,topOrigin:E,userId:p,onPaymentCompleted:_}),q=!0}async function Fe(n){console.info("[PSP] initG123Psp",n),!q&&await ze(n)}export{Fe as initG123Psp};
