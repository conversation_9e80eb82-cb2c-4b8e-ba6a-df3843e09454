const n={production:"production",staging:"staging",local:"local"},f={"h5.g123.jp":n.production,"h5.semi.g123.jp":n.production,"h5.stg.g123.jp":n.staging,"h5.local.g123.jp":n.local,"psp.g123.jp":n.production,"psp.semi.g123.jp":n.production,"psp.stg.g123.jp":n.staging,"psp.local.g123.jp":n.local},E=f[window.location.hostname]||"production",y={[n.production]:"https://platform-sc.g123.jp",[n.staging]:"https://platform-sc.stg.g123.jp",[n.local]:"https://platform-sc.local.g123.jp"};window.__dynamic_base_psp__=`${y[E]}/psp`;const S="modulepreload",j=function(e){return window.__dynamic_base_psp__+"/"+e},m={},I=function(r,i,k){let u=Promise.resolve();if(i&&i.length>0){let a=function(o){return Promise.all(o.map(l=>Promise.resolve(l).then(p=>({status:"fulfilled",value:p}),p=>({status:"rejected",reason:p}))))};document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),d=s?.nonce||s?.getAttribute("nonce");u=a(i.map(o=>{if(o=j(o),o in m)return;m[o]=!0;const l=o.endsWith(".css"),p=l?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${o}"]${p}`))return;const c=document.createElement("link");if(c.rel=l?"stylesheet":S,l||(c.as="script"),c.crossOrigin="",c.href=o,d&&c.setAttribute("nonce",d),document.head.appendChild(c),l)return new Promise((_,h)=>{c.addEventListener("load",_),c.addEventListener("error",()=>h(new Error(`Unable to preload CSS for ${o}`)))})}))}function g(a){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=a,window.dispatchEvent(s),!s.defaultPrevented)throw a}return u.then(a=>{for(const s of a||[])s.status==="rejected"&&g(s.reason);return r().catch(g)})};let t;const C={aiHelpUnread:"ai_help_unread",ACCOUNT_RECOVERY_REDIRECTED_USER_ID:"psp:account_recovery_redirected_user_id"},v=()=>{let e={};return{get length(){return Object.keys(e).length},setItem:(r,i)=>{e[r]=String(i)},getItem:r=>e[r],key:r=>Object.keys(e)[r],removeItem:r=>{delete e[r]},clear:()=>(e={},e)}};function w(){if(t)return t;const e="g123-storage-check-key";try{return t=window.localStorage,t.setItem(e,"1"),t.removeItem(e),t}catch{}try{return t=window.sessionStorage,t.setItem(e,"1"),t.removeItem(e),t}catch{}return t=v(),t}const O=w();export{C as S,I as _,w as g,O as s};
