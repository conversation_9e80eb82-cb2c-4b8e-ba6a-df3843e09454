[1, ["f9kC5wpG9KOIbA1ITrk4eC@6c48a"], 0, [["sp.SkeletonData", ["_name", "_native", "_atlasText", "textureNames", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "ani_mh_p1_1", ".bin", "\nani_mh_p1_1.png\nsize: 1207,1207\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n1mh1_11_1\n  rotate: false\n  xy: 2, 609\n  size: 644, 596\n  orig: 644, 596\n  offset: 0, 0\n  index: -1\n1mh1_17\n  rotate: true\n  xy: 719, 4\n  size: 19, 33\n  orig: 20, 33\n  offset: 0, 0\n  index: -1\n1mh1_18\n  rotate: true\n  xy: 778, 233\n  size: 155, 191\n  orig: 155, 207\n  offset: 0, 0\n  index: -1\n1mh1_19\n  rotate: true\n  xy: 644, 184\n  size: 423, 132\n  orig: 425, 134\n  offset: 1, 1\n  index: -1\n1mh1_20\n  rotate: true\n  xy: 892, 692\n  size: 31, 121\n  orig: 33, 123\n  offset: 1, 1\n  index: -1\n1mh1_21\n  rotate: false\n  xy: 778, 186\n  size: 87, 45\n  orig: 89, 47\n  offset: 1, 1\n  index: -1\n1mh1_22\n  rotate: false\n  xy: 955, 51\n  size: 182, 152\n  orig: 184, 154\n  offset: 1, 1\n  index: -1\n1mh1_23\n  rotate: false\n  xy: 648, 612\n  size: 25, 10\n  orig: 27, 12\n  offset: 1, 1\n  index: -1\n1mh1_24\n  rotate: false\n  xy: 905, 10\n  size: 28, 13\n  orig: 30, 15\n  offset: 1, 1\n  index: -1\n1mh1_25\n  rotate: true\n  xy: 1034, 264\n  size: 32, 55\n  orig: 32, 55\n  offset: 0, 0\n  index: -1\n1mh1_26\n  rotate: false\n  xy: 867, 191\n  size: 25, 40\n  orig: 26, 41\n  offset: 1, 1\n  index: -1\n1mh1_27\n  rotate: false\n  xy: 942, 409\n  size: 24, 62\n  orig: 24, 62\n  offset: 0, 0\n  index: -1\n1mh1_28\n  rotate: true\n  xy: 1069, 20\n  size: 29, 66\n  orig: 30, 67\n  offset: 0, 1\n  index: -1\n1mh1_29\n  rotate: false\n  xy: 754, 4\n  size: 26, 19\n  orig: 28, 21\n  offset: 1, 1\n  index: -1\n1mh1_30\n  rotate: false\n  xy: 782, 5\n  size: 28, 18\n  orig: 30, 20\n  offset: 1, 1\n  index: -1\n1mh1_31\n  rotate: true\n  xy: 912, 725\n  size: 146, 149\n  orig: 148, 151\n  offset: 1, 1\n  index: -1\n1mh1_32\n  rotate: true\n  xy: 648, 852\n  size: 353, 207\n  orig: 355, 209\n  offset: 1, 1\n  index: -1\n1mh1_33\n  rotate: true\n  xy: 853, 391\n  size: 22, 30\n  orig: 27, 30\n  offset: 4, 0\n  index: -1\n1mh1_34\n  rotate: true\n  xy: 814, 391\n  size: 29, 37\n  orig: 29, 38\n  offset: 0, 0\n  index: -1\n1mh1_35\n  rotate: false\n  xy: 812, 7\n  size: 30, 16\n  orig: 32, 18\n  offset: 1, 1\n  index: -1\n1mh1_36\n  rotate: false\n  xy: 844, 8\n  size: 29, 15\n  orig: 32, 17\n  offset: 2, 1\n  index: -1\n1mh1_37\n  rotate: false\n  xy: 875, 9\n  size: 28, 14\n  orig: 30, 16\n  offset: 1, 1\n  index: -1\n1mh1_38\n  rotate: false\n  xy: 857, 858\n  size: 27, 13\n  orig: 29, 15\n  offset: 1, 1\n  index: -1\n1mh1_39\n  rotate: false\n  xy: 971, 298\n  size: 132, 173\n  orig: 132, 174\n  offset: 0, 0\n  index: -1\n1mh1_40\n  rotate: false\n  xy: 648, 624\n  size: 242, 226\n  orig: 244, 228\n  offset: 1, 1\n  index: -1\n1mh1_41\n  rotate: true\n  xy: 1139, 98\n  size: 105, 50\n  orig: 107, 52\n  offset: 1, 1\n  index: -1\n1mh1_42\n  rotate: false\n  xy: 697, 615\n  size: 26, 7\n  orig: 28, 9\n  offset: 1, 1\n  index: -1\n1mh1_43\n  rotate: false\n  xy: 725, 615\n  size: 23, 7\n  orig: 25, 9\n  offset: 1, 1\n  index: -1\n1mh1_44\n  rotate: true\n  xy: 1091, 267\n  size: 29, 59\n  orig: 29, 59\n  offset: 0, 0\n  index: -1\n1mh1_45\n  rotate: true\n  xy: 971, 257\n  size: 39, 61\n  orig: 39, 61\n  offset: 0, 0\n  index: -1\n1mh1_46\n  rotate: true\n  xy: 1140, 236\n  size: 29, 58\n  orig: 29, 58\n  offset: 0, 0\n  index: -1\n1mh1_47\n  rotate: true\n  xy: 912, 205\n  size: 26, 226\n  orig: 26, 226\n  offset: 0, 0\n  index: -1\n1mh1_48\n  rotate: false\n  xy: 778, 390\n  size: 34, 232\n  orig: 34, 232\n  offset: 0, 0\n  index: -1\n1mh1_49\n  rotate: false\n  xy: 644, 2\n  size: 26, 21\n  orig: 28, 23\n  offset: 1, 1\n  index: -1\n1mh1_50\n  rotate: false\n  xy: 885, 391\n  size: 27, 22\n  orig: 29, 24\n  offset: 1, 1\n  index: -1\n1mh1_51\n  rotate: false\n  xy: 1063, 766\n  size: 89, 106\n  orig: 91, 108\n  offset: 1, 1\n  index: -1\n1mh1_52\n  rotate: true\n  xy: 644, 25\n  size: 157, 266\n  orig: 159, 269\n  offset: 1, 2\n  index: -1\n1mh1_53\n  rotate: false\n  xy: 1014, 995\n  size: 126, 210\n  orig: 128, 212\n  offset: 1, 1\n  index: -1\n1mh1_54\n  rotate: false\n  xy: 905, 2\n  size: 24, 6\n  orig: 26, 8\n  offset: 1, 1\n  index: -1\n1mh1_55\n  rotate: false\n  xy: 931, 2\n  size: 22, 6\n  orig: 24, 8\n  offset: 1, 1\n  index: -1\n1mh1_56\n  rotate: true\n  xy: 870, 415\n  size: 29, 66\n  orig: 30, 66\n  offset: 0, 0\n  index: -1\n1mh1_57\n  rotate: false\n  xy: 912, 142\n  size: 41, 61\n  orig: 41, 61\n  offset: 0, 0\n  index: -1\n1mh1_58\n  rotate: true\n  xy: 979, 565\n  size: 125, 129\n  orig: 125, 129\n  offset: 0, 0\n  index: -1\n1mh1_59\n  rotate: false\n  xy: 814, 473\n  size: 163, 149\n  orig: 165, 151\n  offset: 1, 1\n  index: -1\n1mh1_60\n  rotate: false\n  xy: 675, 613\n  size: 20, 9\n  orig: 22, 11\n  offset: 1, 1\n  index: -1\n1mh1_61\n  rotate: false\n  xy: 886, 859\n  size: 24, 12\n  orig: 26, 14\n  offset: 1, 1\n  index: -1\n1mh1_62\n  rotate: true\n  xy: 955, 6\n  size: 43, 112\n  orig: 43, 112\n  offset: 0, 0\n  index: -1\n1mh1_63\n  rotate: false\n  xy: 912, 26\n  size: 28, 114\n  orig: 30, 114\n  offset: 0, 0\n  index: -1\n1mh1_64\n  rotate: true\n  xy: 814, 422\n  size: 49, 54\n  orig: 52, 56\n  offset: 0, 2\n  index: -1\n1mh1_65\n  rotate: false\n  xy: 695, 3\n  size: 22, 20\n  orig: 24, 22\n  offset: 1, 1\n  index: -1\n1mh1_66\n  rotate: false\n  xy: 672, 2\n  size: 21, 21\n  orig: 23, 23\n  offset: 1, 1\n  index: -1\n1mh1_67\n  rotate: true\n  xy: 857, 873\n  size: 94, 104\n  orig: 96, 106\n  offset: 1, 1\n  index: -1\n1mh1_67_1\n  rotate: false\n  xy: 870, 446\n  size: 70, 25\n  orig: 70, 26\n  offset: 0, 0\n  index: -1\n1mh1_68\n  rotate: false\n  xy: 857, 969\n  size: 155, 236\n  orig: 157, 238\n  offset: 1, 1\n  index: -1\n1mh1_69\n  rotate: true\n  xy: 963, 874\n  size: 93, 176\n  orig: 95, 178\n  offset: 1, 1\n  index: -1\n1mh1_70\n  rotate: false\n  xy: 1139, 9\n  size: 45, 87\n  orig: 45, 87\n  offset: 0, 0\n  index: -1\n1mh1_71_1\n  rotate: false\n  xy: 2, 2\n  size: 640, 605\n  orig: 640, 605\n  offset: 0, 0\n  index: -1\n", ["ani_mh_p1_1.png"], [0]], -1], 0, 0, [0], [-1], [0]]