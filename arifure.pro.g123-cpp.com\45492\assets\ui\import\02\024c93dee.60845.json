[1, ["5cGsBPQ2NEca4VvTAL9+K2@f9941", "86z+YWZthMvbHunssb8bFs", "68Zui2hoNLLrkC/3PE/4Hu", "f28HUhyqRHHKbqjFyhgzH9@f9941", "898ZpveSdOZKKZBnJBygGl@f9941", "a7OOJp3bJGKqaNk+w2fVMO@f9941", "03uWwLDb1Gka0HRrYM9dQS@f9941", "26fvYFFp5KWaj6b74y4iKN", "7amYLf+K5ARquKrCZxj5lq@f9941", "34lVp7YO5EjrerSocNZaVQ@f9941", "b63Af44kpIlpf6S1VK5k6d@f9941", "0fcGqJ1ntNo6BF0rwVX3zL@f9941", "c7ZGef3kJDErRdr60/LRF8@f9941", "85Uf4JWNJIRI2eJs7cg91J@f9941", "deg0NqyAJHm4INZn+RfFXs@f9941", "77/gMgtn1CGrJbpigWYZXR@f9941", "125QFcOKpAHaHiMQYvLOhF@f9941", "b5dXI3TRBMu64yHcnfzKgb", "basp3e20pGPaHtwrdohz63@f9941", "fcfATM00NP6aMUfMeuFPxG", "2aA+UgWvZNMJfFX8nZLwKN", "99YfKnFh5Ce5M6OyvXZUVU@f9941", "bdkfjlfehECqjWGRYYVaZ5", "3dUw3pcH1LNr1VK93FjCwT@f9941", "1dXz688uxHqr/8QRkI+zWx@f9941", "eblhp7aW5MJI4TF/s+eB7y@f9941", "e23UBWjNJG6rQoaMTyh2dq@f9941", "61OGxbc5hCB4rExGB3pAd2@f9941", "a45kCPt29KzI+kKG5bmaIF@f9941", "17n/zcnA9FgbQhKupSgBvN@f9941", "dfW5KoxkdP+LLYVAyIkmzx@f9941", "47GnI38wlFp7zEma5x5GoM@f9941", "a0GSiOGl9Fm6UteLNmLnAY@f9941", "b7nq8KqrJFlKvo4+3oITK6@f9941", "7bfmMYO3ND/bAmHPa3tjam@f9941", "50JWsGYylG5qv23MyXYOy6@f9941", "6dsNWzSMNCp5V27fQzD/Uu", "3adiIv/LZGtarqxPnJCvG2@f9941", "e2hKzOM6BJf5G7oaQ8ug90@f9941", "68uW0A6WJHVYVtuX6S8Ifr@f9941", "202qRtvchAuqqnMw8TnnSp@f9941", "b5h+OVRzJH2bmdaHS4BuA0@f9941", "38AotOaohNV5VlATHsRYEf@f9941", "22HfCjIwNLk6uJkyNjlcO5@f9941", "81fm+CrndFPpR0VS8/amkT@f9941", "2bbw674DZO9riMNGUvm/bn@f9941", "5bVZZMfe5MZZfv2kwmpQ4g", "45T4kjfTRDFbFG35w1+hpg", "aaOfrsTMpIir5t/nSCVbnd", "d9M/UfWWZA4Y5oqmn/u9E8", "645ab/d9dPLr1b2CpfjBxU", "bfZMvqg3dEzpL5udZaspTY", "b7jaebNkdJhr7OIIsyqLhp", "1cfmGaZLNOU4KywUlgmu4Y", "aauq6hFxdGCpDZK4Ltiycg", "06jLRW8bROcJkbRTRXeKaf@f9941", "0aLPtvgK9CWLJmcFXQ7aGU@f9941", "4dqR6BqLRNsrOFDEkByeOR@f9941", "5e1kyQQ7ZFubH27ahvf22a@f9941", "d4nrO8HqNOHIuUwS+AHbhz", "6c0LTIDr1OVpYlsdz3l9O8@6c48a", "3adiIv/LZGtarqxPnJCvG2@6c48a", "4dqR6BqLRNsrOFDEkByeOR@6c48a", "5e1kyQQ7ZFubH27ahvf22a@6c48a", "06hC90wJdF6qsEYUH5FwIC", "86c+pTxmZH45kQhOU8j7Sa", "e4Ji9igsVEPLpSt8E/LQzS@f9941", "aecbLyPuVIvKsn/gpwx8rP@f9941", "b5U/axcONNg4FpLKkrahth@f9941", "58hbb6wfZJBpk4N97nFxTF@f9941", "65sgYp4OhPo5Qw8Tf8Om3V@f9941", "ddXs7gZAlIcqCHlW2Jc8V0@f9941", "d8Q1N4aO1FfYKPDS9oD3U7@f9941", "83nqoCjyFI04ycJSndW0hB@f9941", "a3iB9y2xxO/7M90LI2UaZE@f9941", "d4ojzu1yNLA63kQQQ0WcZI@6c48a", "bcVSPQSWxCI668bVuySWiY@f9941", "56fVOV4NZD87m/V2RFKLq5@f9941", "d5Q7lgXwlL0rgvq0W2RR3l@f9941", "ef+infVk5BHpUYJkKl0CIL@f9941", "b5MWm+PwVM+7Zo7EIxUXBL@f9941", "39HbthGCZOBIV4xH944ofg@f9941", "77w4ZvKfpG7ZuSL0O+YNra@f9941", "2aPiXZupBMeaA/NDYFP6K0@f9941", "dbjzTYQipC+KSsb5S6gxEj@f9941", "09yc3jpgpAn5bXFHdOszHd@f9941", "d3mMeL+JtC0YdQmwOzOPB6", "86/EbnI2JGaZ1cFfOXB0Ku", "56aJuSedFBTKHOfbTr4Qco@f9941", "7eGSAg/2pP4ZbjXz1PJeR4@f9941", "61gVyO+7dNVZBSH9PuIH0x@f9941", "54lOPobJdG44YARN5VNTFs@f9941", "2ebco+62FPyqtT2cfLX8Wg", "c466Wmz/dC46HUL0fzx+Xd@6c48a", "1dasU887VLML1NJSISJCCc@6c48a", "7cJ9stK+NCo5KDt5xPwTKq@f9941", "d39v7w849PxLpN0idr8ROJ", "04Uj8mUrNIf6YweU2iWKkY", "b6O8vq+K9KopykK/o3nRU6@f9941", "91Byi89XJIGa4/9osUoTQf@f9941", "4fmPhUtOFH/oMdL9U+Ngf3@f9941"], ["node", "_spriteFrame", "targetInfo", "_parent", "material", "_defaultClip", "root", "_skeletonData", "data", "asset", "_textureSource", "value", "_font", "spine", "_target", "fuhuoAni_r", "fuhuoAni_l", "rolePos15", "rolePos14", "rolePos13", "rolePos12", "rolePos11", "rolePosClicker1", "rolePos05", "rolePos04", "rolePos03", "rolePos02", "rolePos01", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "round", "skillTipsParent", "roleTmp", "clickerTmp", "dropHPTmp", "buffPZTmp", "dropAwardTmp", "shenshuTmp", "skillTipsTmp", "hougongLay", "rollLay", "animalLay", "msHdAni", "hpMs", "msSpine", "buffLayout", "nuqibar", "hudun<PERSON>", "hpbar", "hpchangebar", "rightside", "leftside", "skill_hz_r", "skill_dz_r", "skill_hz_l", "skill_dz_l"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_parent", "_components", "_lpos", "_children", "_lscale", "_euler", "_lrot"], -2, 4, 1, 9, 5, 2, 5, 5, 5], ["cc.Label", ["_actualFontSize", "_isBold", "_string", "_enableOutline", "_lineHeight", "_fontSize", "_outlineWidth", "_isItalic", "_enableWrapText", "_horizontalAlign", "_enableShadow", "_shadowBlur", "_overflow", "_isSystemFontUsed", "_spacingX", "node", "__prefab", "_outlineColor", "_color", "_shadowColor", "_shadowOffset", "_font"], -12, 1, 4, 5, 5, 5, 5, 6], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "_fillRange", "_fillType", "node", "__prefab", "_spriteFrame", "_color", "_fillCenter"], -2, 1, 4, 6, 5, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_affectedByScale", "_enabled", "_verticalDirection", "_spacingY", "_constraint", "_constraintNum", "_isAlign", "node", "__prefab"], -7, 1, 4], ["cc.Widget", ["_alignFlags", "_top", "_originalHeight", "_bottom", "_left", "_originalWidth", "node", "__prefab"], -3, 1, 4], ["sp.Skeleton", ["_premultipliedAlpha", "_preCacheMode", "defaultSkin", "loop", "defaultAnimation", "node", "__prefab", "_skeletonData", "_sockets"], -2, 1, 4, 6, 9], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_children"], 1, 1, 12, 4, 5, 9], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], "cc.SpriteFrame", ["cc.Animation", ["node", "__prefab", "_clips", "_defaultClip"], 3, 1, 4, 3, 6], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_target"], 1, 1, 4, 1], ["sp.Skeleton.SpineSocket", ["path", "target"], 2, 1], ["cc.Mask", ["_enabled", "_alphaThreshold", "node", "__prefab"], 1, 1, 4], ["cc.Graphics", ["_enabled", "node", "__prefab", "_fillColor"], 2, 1, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab"], 2, 12, 9, 4], ["cc.Node", ["_name", "_layer", "_parent", "_children", "_components", "_prefab", "_lpos"], 1, 1, 2, 12, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["7563fP+WuxL1p5rM8Q2NGlY", ["node", "__prefab", "spfs"], 3, 1, 4, 3], ["45669NsoCFJ3ILy3OuG02Pm", ["node", "__prefab", "skillTipsParent", "round", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rolePos01", "rolePos02", "rolePos03", "rolePos04", "rolePos05", "rolePosClicker1", "rolePos11", "rolePos12", "rolePos13", "rolePos14", "rolePos15", "fuhuoAni_l", "fuhuoAni_r", "roleTmp", "clickerTmp", "dropHPTmp", "buffPZTmp", "dropAwardTmp", "shenshuTmp", "skillTipsTmp"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 6, 6, 6, 6, 6, 6], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 6], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["cc.TargetInfo", ["localID"], 2], ["sp.SkeletonData", ["_name", "_native", "_atlasText", "textureNames", "textures"], -1, 3], ["cc.UIOpacity", ["node", "__prefab"], 3, 1, 4], ["80801awInNCm55tEhtITSKB", ["mix1", "mix2", "mix3", "mix4", "node", "__prefab", "color", "material"], -1, 1, 4, 12, 6], ["b4933dhvuND6JINGenvFkWq", ["node", "__prefab", "spine", "hpchangebar", "hpbar", "hudun<PERSON>", "nuqibar", "buffLayout", "msSpine", "hpMs", "msHdAni", "animalLay", "rollLay", "hougongLay"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.ProgressBar", ["_totalLength", "_progress", "node", "__prefab", "_barSprite"], 1, 1, 4, 1], ["bd0e9iUztpPnKPObQqAhDrn", ["node", "__prefab", "skill_dz_l", "skill_hz_l", "skill_dz_r", "skill_hz_r", "leftside", "rightside"], 3, 1, 4, 1, 1, 1, 1, 1, 1], ["011c8MZ++JCbqPChKjiX2MO", ["_statusIndex", "currStatusName", "statusNameArray", "node", "__prefab", "statusNodes", "statusData"], 0, 1, 4, 2, 9], ["StatusData", ["status", "fileId", "fontsize", "spriteFrame", "gradient_material", "position", "rotation", "scale", "anchor", "size", "color"], -2, 5, 5, 5, 5, 5, 5], ["d1b19xU/lNKRpzir/NwyNaE", ["node", "__prefab", "spine"], 3, 1, 4, 1]], [[17, 0, 2], [19, 0, 1, 2, 3, 4, 5, 5], [7, 0, 1, 2, 1], [2, 5, 6, 7, 1], [29, 0, 2], [7, 0, 1, 2, 3, 1], [0, 0, 6, 7, 5, 8, 2], [7, 0, 1, 1], [0, 0, 6, 7, 5, 8, 10, 2], [0, 0, 1, 6, 7, 5, 8, 3], [21, 0, 1, 2, 1], [25, 0, 1, 2, 2], [31, 0, 1, 1], [32, 0, 1, 2, 3, 4, 5, 6, 7, 5], [37, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 6], [0, 0, 2, 6, 7, 5, 8, 3], [9, 0, 1, 2, 3, 1], [24, 0, 1, 2, 3], [7, 0, 1, 3, 1], [0, 0, 2, 6, 9, 7, 5, 3], [3, 0, 1, 2, 3, 10, 11, 5], [0, 0, 1, 6, 7, 5, 8, 10, 3], [1, 2, 0, 5, 4, 7, 1, 3, 6, 15, 16, 17, 9], [26, 0, 1, 2, 2], [5, 0, 1, 5, 6, 3], [1, 2, 0, 5, 4, 1, 3, 6, 10, 11, 15, 16, 18, 17, 19, 20, 10], [0, 0, 2, 6, 7, 5, 8, 10, 11, 3], [28, 0, 1, 2, 3], [3, 10, 11, 1], [11, 0, 1, 2], [14, 0, 2], [0, 0, 1, 6, 9, 7, 5, 8, 3], [0, 0, 1, 6, 7, 5, 3], [0, 0, 2, 6, 7, 5, 3], [0, 0, 6, 7, 5, 2], [0, 0, 6, 9, 7, 5, 8, 2], [2, 5, 6, 1], [2, 1, 0, 5, 6, 7, 3], [0, 0, 9, 7, 5, 2], [0, 0, 2, 6, 9, 7, 5, 8, 3], [4, 0, 6, 7, 2], [1, 2, 0, 5, 4, 1, 3, 6, 10, 11, 15, 16, 17, 19, 20, 10], [5, 2, 0, 1, 5, 6, 7, 4], [0, 0, 2, 6, 7, 5, 10, 11, 3], [0, 0, 2, 6, 7, 5, 10, 3], [0, 0, 6, 5, 8, 11, 2], [6, 0, 1, 2, 3, 4, 5, 3], [16, 0, 1, 2, 3, 4, 5, 6, 3], [2, 1, 0, 5, 6, 3], [1, 0, 5, 4, 1, 3, 6, 15, 16, 17, 7], [5, 0, 1, 5, 6, 8, 3], [34, 0, 1, 2, 3, 4, 3], [0, 0, 2, 1, 6, 9, 7, 5, 8, 4], [0, 3, 4, 6, 5, 3], [0, 0, 6, 9, 7, 5, 8, 10, 2], [4, 0, 5, 2, 6, 7, 4], [4, 0, 4, 1, 6, 7, 4], [20, 0, 1, 2, 3, 4, 5, 4], [2, 0, 5, 6, 7, 2], [2, 0, 2, 5, 6, 7, 3], [10, 0, 1, 2, 3, 3], [23, 0, 1, 2, 2], [11, 1], [30, 0, 1, 2, 3, 4, 5], [12, 2, 3, 1], [13, 1, 2, 3, 1], [36, 0, 1, 2, 3, 4, 5, 6, 4], [0, 0, 1, 9, 7, 5, 3], [0, 0, 2, 1, 6, 7, 5, 8, 4], [0, 0, 2, 1, 6, 7, 5, 4], [0, 0, 9, 7, 5, 8, 11, 2], [4, 0, 1, 6, 7, 3], [5, 0, 5, 6, 2], [3, 0, 1, 10, 11, 3], [0, 0, 1, 6, 9, 7, 5, 3], [0, 0, 1, 6, 9, 7, 5, 8, 10, 3], [0, 0, 6, 9, 7, 5, 2], [0, 0, 1, 6, 7, 5, 10, 3], [0, 0, 2, 6, 9, 7, 5, 8, 11, 3], [0, 0, 6, 9, 7, 5, 8, 11, 2], [0, 0, 6, 7, 5, 8, 10, 11, 2], [0, 0, 2, 1, 6, 9, 7, 5, 4], [0, 0, 9, 7, 5, 8, 10, 11, 2], [15, 0, 1, 2, 3, 2], [4, 0, 4, 3, 6, 7, 4], [2, 0, 5, 6, 2], [2, 1, 0, 5, 6, 8, 7, 3], [1, 2, 9, 0, 5, 4, 1, 3, 6, 15, 16, 9], [1, 2, 0, 5, 4, 1, 3, 6, 15, 16, 8], [1, 2, 0, 5, 4, 8, 13, 14, 1, 3, 15, 16, 18, 17, 21, 10], [1, 2, 0, 5, 4, 7, 1, 3, 6, 15, 16, 18, 9], [1, 2, 0, 7, 1, 3, 6, 15, 16, 18, 7], [1, 2, 0, 5, 7, 1, 3, 6, 15, 16, 8], [27, 0, 1, 2, 2], [0, 0, 6, 5, 2], [0, 0, 6, 5, 11, 2], [0, 0, 2, 1, 6, 7, 5, 8, 10, 4], [0, 0, 6, 7, 5, 10, 2], [0, 0, 6, 5, 8, 2], [0, 0, 1, 6, 7, 5, 8, 12, 11, 3], [6, 0, 2, 6, 3, 4, 2], [6, 0, 2, 3, 4, 5, 2], [6, 0, 2, 6, 3, 4, 5, 2], [4, 0, 2, 6, 7, 3], [4, 0, 1, 3, 2, 6, 7, 5], [4, 0, 1, 3, 6, 7, 4], [18, 0, 1, 2, 3, 4, 5, 4], [2, 5, 6, 8, 1], [2, 0, 2, 5, 6, 3], [2, 1, 3, 5, 6, 7, 3], [2, 1, 4, 3, 5, 6, 8, 9, 7, 4], [10, 0, 1, 2, 3, 4, 3], [9, 0, 1, 2, 1], [9, 0, 1, 1], [22, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 1], [1, 2, 9, 0, 5, 4, 1, 15, 16, 7], [1, 2, 0, 4, 1, 3, 6, 15, 16, 17, 7], [1, 2, 0, 5, 4, 1, 15, 16, 18, 6], [1, 2, 0, 5, 4, 8, 1, 3, 6, 15, 16, 9], [1, 2, 0, 12, 1, 3, 6, 15, 16, 18, 17, 7], [1, 2, 0, 5, 4, 8, 1, 3, 15, 16, 8], [1, 2, 0, 4, 12, 1, 3, 6, 15, 16, 17, 8], [5, 0, 1, 3, 5, 6, 4], [5, 2, 0, 1, 5, 6, 8, 7, 4], [5, 0, 1, 3, 5, 6, 8, 4], [5, 2, 4, 0, 1, 5, 6, 7, 5], [3, 4, 0, 1, 5, 3, 9, 10, 11, 7], [3, 0, 1, 2, 6, 5, 7, 8, 10, 11, 8], [3, 0, 1, 2, 7, 8, 10, 11, 6], [3, 0, 1, 2, 10, 11, 4], [3, 0, 1, 6, 3, 10, 11, 5], [3, 0, 1, 3, 10, 11, 4], [3, 4, 0, 1, 2, 3, 10, 11, 6], [33, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 1], [12, 0, 1, 2, 3, 3], [13, 0, 1, 2, 3, 2], [35, 0, 1, 2, 3, 4, 5, 6, 7, 1], [38, 0, 1, 2, 1]], [[[[30, "zhandou_test1"], [38, "zhandou_test1", [-8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21], [[2, -6, [0, "a22I16mP9H1Jk2QDBzWrju"], [5, 640, 1280]], [55, 45, 640, 1280, -7, [0, "3aBQ6lZwZOhbkKeuoLRw70"]]], [106, "c46/YsCPVOJYA4mWEpNYRx", null, null, -5, 0, [-1, -2, -3, -4]]], [38, "Roles", [-23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36], [[7, -22, [0, "77aCgtL55HOJbuauAfq5jz"]]], [1, "35gB/MWzJI44/FOPboDywT", null, null, null, 1, 0]], [67, "img_zrgh1_r", 33554432, [-40, -41, -42, -43, -44], [[2, -37, [0, "6dlloT959AZYQ3z3oAXpWb"], [5, 102, 100]], [3, -38, [0, "47kDHJm05KIL2629aut+Uc"], 27], [10, -39, [0, "11G+zfL45CR68uGmbonmp2"], [28, 29, 30, 31]]], [1, "84zcRu70xLZ6iGH//MMWG1", null, null, null, 1, 0]], [52, "bg_di_rw2", false, 33554432, 1, [-48, -49, -50, -51, -52], [[2, -45, [0, "6f4EBRTxFElYIpBD9HPx/Z"], [5, 270, 70]], [85, 0, -46, [0, "fa4SPYA3FLUKxMS653ZL42"]], [56, 33, 349.637, 98.27499999999998, -47, [0, "571ZihqQREX4U/HW5XBjwL"]]], [1, "64mhViL3BIuJ32fI2zua8G", null, null, null, 1, 0], [1, 185, 506.725, 0]], [52, "bg_di_rw1", false, 33554432, 1, [-56, -57, -58, -59, -60], [[2, -53, [0, "7bYi0/HAdDvb/0yKjbREoi"], [5, 270, 72]], [107, -54, [0, "c2qMvCXflLXrk3xKVhJb9W"], [4, 4278190080]], [71, 9, 97.27499999999998, -55, [0, "48MO9FFBZAWJN6qMt0oNve"]]], [1, "a8xpA/9lhJkqsLBboOK+rT", null, null, null, 1, 0], [1, -185, 506.725, 0]], [67, "img_zrgh1_l", 33554432, [-64, -65, -66, -67, -68], [[2, -61, [0, "02OskderJDA5WKeMCUBc7e"], [5, 102, 100]], [3, -62, [0, "d9QVU93HpNUrXzpS1Ia9U7"], 76], [10, -63, [0, "02xbj3QY1O+7o/X8X1eJ0B"], [77, 78, 79, 80]]], [1, "b1wAzqBFVPzb9sUVfp2VaN", null, null, null, 1, 0]], [52, "img_zr_gh_r", false, 33554432, 1, [-74, 3], [[2, -69, [0, "92TReJTqVL/IT85yfXCjHG"], [5, 100, 98]], [36, -70, [0, "1b5keKjh5PEquy1qWIGPfO"]], [56, 9, 528, 184.49400000000014, -71, [0, "c2G32ytqVBPqGRgDe5uZ9u"]], [111, 3, 0.9, -73, [0, "65C7l3RuVOUqhapnhKzMsw"], -72]], [1, "echd+m9jtBYp/tM5mu3WGP", null, null, null, 1, 0], [1, 258, 406.50599999999986, 0]], [74, "bg1", 33554432, 1, [-79, -80, -81], [[2, -75, [0, "f3GxvMk1hNv509rM7erov1"], [5, 640, 1280]], [108, 0, false, -76, [0, "3fRenNvZdBpKB0Uk2rwmcq"]], [103, 21, 1280, -77, [0, "baMjPdnwxNfLAfJhEG0Rbz"]], [112, -78, [0, "30AYB3trZBR6gtW8GYpOmv"], [48, 49]]], [1, "75+fRLDOtOlq/Yk0mvW0av", null, null, null, 1, 0]], [31, "img_tuiguan_top", 33554432, 1, [-85, -86, -87], [[2, -82, [0, "acZqUUpDNE/6CjGdOSMhvJ"], [5, 640, 62]], [3, -83, [0, "efgsXtWKNBo4aXP1IQqq5D"], 47], [40, 1, -84, [0, "c987o1XAJMmZlDBVOa0UTq"]]], [1, "39iTag4ApJ2a6RKbnyFOF4", null, null, null, 1, 0], [1, 0, 609, 0]], [52, "img_zr_gh_l", false, 33554432, 1, [-92, 6], [[2, -88, [0, "c2arhv5O5Gr6wAXcsXqNqe"], [5, 100, 98]], [36, -89, [0, "44M5ovYLFGS6OSybFRNJGb"]], [56, 9, 12, 184.49400000000003, -90, [0, "03dwyPa2JCLJbYNU40ohcI"]], [60, 3, 0.9, -91, [0, "87qBh30HVH57nB9B6fUkwS"]]], [1, "3aW+aTWvtBb5ftp7cYxPhc", null, null, null, 1, 0], [1, -258, 406.506, 0]], [31, "btn_tg_tg1", 33554432, 1, [-97], [[2, -93, [0, "fbU+52aN9FfJrHZaWOr833"], [5, 66, 69]], [58, 0, -94, [0, "a4x96qji5BpIiH/fJMMN+l"], 92], [60, 3, 0.9, -95, [0, "9dKQBVvUBJg6hkEWlsuMom"]], [84, 12, 38, 26, -96, [0, "e5lYUFfdxGkLTg78Sy5uL8"]]], [1, "cefGQIAyVK7qgw4HgzcxCo", null, null, null, 1, 0], [1, -249, -579.5, 0]], [31, "btn_tg_js1", 33554432, 1, [-102], [[2, -98, [0, "7741Gq7AFMs4DjILISkayH"], [5, 66, 69]], [58, 0, -99, [0, "21u2FCLDlEO6VhvBCVNldd"], 93], [60, 3, 0.9, -100, [0, "b1WiKGvUhLfYMU4GMi6tPM"]], [84, 12, 126, 26, -101, [0, "4d0p3DZ91Nsr9+320sXsfB"]]], [1, "e9xbuZ5glCqIWWvaUNRp/e", null, null, null, 1, 0], [1, -161, -579.5, 0]], [75, "bg_jdt_2", 33554432, 4, [-105, -106], [[2, -103, [0, "fcG0iCxLtPebBUkLmDpyqF"], [5, 201, 18]], [37, 1, 0, -104, [0, "b3XbK3P3hN5oUvaJfD0bQD"], 34]], [1, "00icMU4XRG75pRNWFQWh1f", null, null, null, 1, 0], [1, -33.5, -2.2970000000000255, 0], [1, 1, 1.3, 1]], [75, "bg_jdt_1", 33554432, 5, [-109, -110], [[2, -107, [0, "7dmiwuEJtFuLVRxz8WX06j"], [5, 201, 18]], [86, 1, 0, -108, [0, "3b6yPtRupE46dm0NMHq8bt"], [4, 4293980415], 42]], [1, "bd6bdZK2xDnoTMJ0i1o1Lm", null, null, null, 1, 0], [1, 33.5, -2.2970000000000255, 0], [1, 1, 1.3, 1]], [76, "FightNode", 1, [2], [[114, -127, [0, "71kjFcG49C44XzzyLlp5hB"], -126, -125, -124, -123, -122, -121, -120, -119, -118, -117, -116, -115, -114, -113, -112, -111, 83, 84, 85, 86, 87, 88, 89], [2, -128, [0, "ac6HUBmstHj4hhpoQylU29"], [5, 640, 1280]], [55, 45, 100, 100, -129, [0, "6aVWbR2LxGn7FMyDuVGLbQ"]]], [1, "efc8K4pSBEtKLCL3m+vkuD", null, null, null, 1, 0]], [31, "bg_tx_title", 33554432, 1, [-133], [[2, -130, [0, "f1TFfDYE9DCZJRy0nzfcEG"], [5, 329, 28]], [3, -131, [0, "73iDItiedJwL2889Xc+dp7"], 90], [71, 1, 200.29700000000003, -132, [0, "0bAQBfm5dII7wIkocIam4F"]]], [1, "47+OgErzhLPqXGf3kVlI+H", null, null, null, 1, 0], [1, 0, 425.703, 0]], [32, "bg_tx_2", 33554432, 1, [[2, -134, [0, "0bDE8xp/dKp5lVmvN+gXkx"], [5, 640, 1280]], [59, 0, false, -135, [0, "07SPSWYEFPK7LnslbZKJd4"], 0], [40, 17, -136, [0, "35us26bFpP+5/W5KLa8odZ"]]], [1, "99YjRVrg9PJITIdenqwosm", null, null, null, 1, 0]], [9, "img_zrgh_r1", 33554432, 3, [[2, -137, [0, "91gxcig3NPuqpwmukwBcZJ"], [5, 20, 20]], [3, -138, [0, "ba9GacA39AdIQL9nAeKUhh"], 2], [10, -139, [0, "a4xagFACNIlbYuo+8XuvWB"], [3, 4, 5, 6]]], [1, "56pUFqxj5C1rcmUplR9Vq+", null, null, null, 1, 0], [1, -40, 2.11, 0]], [9, "img_zrgh_r2", 33554432, 3, [[2, -140, [0, "1fDjriEbZOUaOaEIsRd3W0"], [5, 20, 20]], [3, -141, [0, "a9cGSRWKRLiZwl5xuVdl0I"], 7], [10, -142, [0, "430qQCES9HXIXhcDofj2Bw"], [8, 9, 10, 11]]], [1, "acYHPuJ9FBXbhOpJuthEl5", null, null, null, 1, 0], [1, -28.94, -25.021, 0]], [9, "img_zrgh_r3", 33554432, 3, [[2, -143, [0, "d5UPUwjTxNfZbv/AmXJZbE"], [5, 20, 20]], [3, -144, [0, "a5X8saPQ1DPYcKfaiMUfNl"], 12], [10, -145, [0, "2c9eim+sZBVLUfcO4XLudH"], [13, 14, 15, 16]]], [1, "14tKCTKDRDu6uMe5Npyegw", null, null, null, 1, 0], [1, -0.904, -39.189, 0]], [9, "img_zrgh_r4", 33554432, 3, [[2, -146, [0, "089xAuRc5EE6OGd0CZwt3O"], [5, 20, 20]], [3, -147, [0, "66dMBFcNRLjodnsSTgeusX"], 17], [10, -148, [0, "f7U7/6BQhAHL+RDD8hAKuM"], [18, 19, 20, 21]]], [1, "61j8QaZKxDjb9cO7sPC1uG", null, null, null, 1, 0], [1, 27.131, -25.021, 0]], [9, "img_zrgh_r5", 33554432, 3, [[2, -149, [0, "2emqHzJ7RCJrO7KQ9maZ6E"], [5, 20, 20]], [3, -150, [0, "60887l+49JmqVQ8pW2fU1d"], 22], [10, -151, [0, "85Y/zPWi5BRbdlVY2FKoNt"], [23, 24, 25, 26]]], [1, "51LNJ9uGtOvYmHT+lIH6ci", null, null, null, 1, 0], [1, 40, 2.11, 0]], [68, "img_tuiguan_vs", false, 33554432, 1, [[2, -152, [0, "b6qgBDIAtGO6bl6faJpAdG"], [5, 125, 154]], [3, -153, [0, "109jU1WNJPQJQgaDIxRV5V"], 38], [71, 1, 72.53299999999996, -154, [0, "5eyhT3m6BNho9kZLtvCGrO"]]], [1, "4bCMg5VQxDc5P14TRkbcl1", null, null, null, 1, 0], [1, 3, 490.4670000000001, 0]], [21, "txt_gk1", 33554432, 9, [[2, -155, [0, "a2l2YJzr1FnqnuCFxZzSUd"], [5, 336, 70.56]], [115, "精英關卡模式", 0, 56, 56, 56, true, -156, [0, "4cfsLTcVFN17oC30w4xvED"]], [56, 9, 236, 7.359999999999999, -157, [0, "70nEcYj/NCqrawEGu+kUZX"]]], [1, "cf6l9LT0dBzrzym6t3sUpu", null, null, null, 1, 0], [1, 0, 6, 0], [1, 0.5, 0.5, 1]], [9, "img_zrgh_l1", 33554432, 6, [[2, -158, [0, "f5TXrnwh1PaIk3oezV0JKb"], [5, 20, 20]], [3, -159, [0, "e6OX4Mb7BN5JQmHf3XmDj6"], 51], [10, -160, [0, "6dhobf84NOpJjMo7j4fYie"], [52, 53, 54, 55]]], [1, "542QIhn2xHOK4Bt5afbziw", null, null, null, 1, 0], [1, -40, 2.11, 0]], [9, "img_zrgh_l2", 33554432, 6, [[2, -161, [0, "a6ZYdiNC5O5qoUNVKf0LDl"], [5, 20, 20]], [3, -162, [0, "ddcWF+8f1Hd7EYOK8nk7vA"], 56], [10, -163, [0, "53SK6wMp9M1q9iGRQMUxJz"], [57, 58, 59, 60]]], [1, "74ShZowVVBHKY6xIwhMnP4", null, null, null, 1, 0], [1, -28.94, -25.021, 0]], [9, "img_zrgh_l3", 33554432, 6, [[2, -164, [0, "c0f/reAIJAbpXyOHAgOBIh"], [5, 20, 20]], [3, -165, [0, "f6AokkIMpGaq55t259AlxK"], 61], [10, -166, [0, "8a/qTsXhdJM5TMR2sIszt5"], [62, 63, 64, 65]]], [1, "4fGN5bxF5LTY7YdMs4gUGz", null, null, null, 1, 0], [1, -0.904, -39.189, 0]], [9, "img_zrgh_l4", 33554432, 6, [[2, -167, [0, "76Bj6F5VpAh7Eh5B4KZcNm"], [5, 20, 20]], [3, -168, [0, "2bOXllqitLkJHYf9bdRbhc"], 66], [10, -169, [0, "6durIuD7FLIIQZ0ypYa4d0"], [67, 68, 69, 70]]], [1, "80SjC0T1BPYaztHeSsTbn4", null, null, null, 1, 0], [1, 27.131, -25.021, 0]], [9, "img_zrgh_l5", 33554432, 6, [[2, -170, [0, "8e4lmSD+pMUavJnl7N2Faf"], [5, 20, 20]], [3, -171, [0, "60RSVXzSVPIrjFdTa2UoEh"], 71], [10, -172, [0, "874f78jOdGHbsth+nD/3So"], [72, 73, 74, 75]]], [1, "d2zJ+nEdRC+oXaL+A3x0cv", null, null, null, 1, 0], [1, 40, 2.11, 0]], [68, "ani_35601_fuhuo_l", false, 33554432, 2, [[5, -173, [0, "32u51Wz0NDAJ4hYljA94mP"], [5, 782.2781372070312, 347.7327880859375], [0, 0.5, 0.5955023683339378]], [42, "default", false, 0, -174, [0, "409vxJKn1MZqyHUEPziy0v"], 81]], [1, "42dbrO4T9GIJBtNv8k+CHA", null, null, null, 1, 0], [1, -103.435, -37.99, 0]], [68, "ani_35601_fuhuo_r", false, 33554432, 2, [[5, -175, [0, "96bTLHCYhIw7kH60SgwGdS"], [5, 782.2781372070312, 347.7327880859375], [0, 0.5, 0.5955023683339378]], [42, "default", false, 0, -176, [0, "bfoDPSlBZO455sNJo3aKdy"], 82]], [1, "57HvlagGhJKrsonPraQHAZ", null, null, null, 1, 0], [1, 109.941, -37.364, 0]], [32, "skillTipsParent", 33554432, 1, [[2, -177, [0, "524FamejpApZRYAHDKyGAu"], [5, 640, 1280]], [55, 45, 100, 100, -178, [0, "035WguTtxCdoD9LvNkeT5E"]]], [1, "f4euzgvBFCcoHOTE0OkRNg", null, null, null, 1, 0]], [77, "txt_rw_ms1", 33554432, 16, [[2, -179, [0, "c5Xv1/f05FmYHgyKMeHg73"], [5, 6, 94.2]], [116, "", 40, 70, true, true, 3, -180, [0, "03qq69LjZBDI+I01tXmH8x"], [4, 4280098330]]], [1, "6c7uIord9Ds40lmJrhKWHR", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [32, "img_zrgh_zw_r", 33554432, 7, [[2, -181, [0, "0c3sDjAHBGkrgyLxyJDTg9"], [5, 100, 98]], [3, -182, [0, "53CAOr06hEJJ1bZ9KuR2/o"], 1]], [1, "a6plm/wrZNsqLVYHlS7egv", null, null, null, 1, 0]], [21, "txt_rw_ms2", 33554432, 4, [[5, -183, [0, "f4FelWOLhJL6HQRJ/XmKqy"], [5, 6, 81.6], [0, 1, 0.5]], [87, "", 0, 36, 36, 60, true, true, 3, -184, [0, "45ZDUsL6NI+44tv0rHdVuI"]]], [1, "7f9mBZxJxEoI02H5vreOoB", null, null, null, 1, 0], [1, 37.664999999999964, 27.624000000000024, 0], [1, 0.5, 0.5, 1]], [21, "bar_hp2_1", 33554432, 13, [[5, -185, [0, "60v19dIHxArYyJ/KLc4nbM"], [5, 170, 13], [0, 0, 0.5]], [37, 3, 0, -186, [0, "23stamU/BGJ4YVOr5NfPVg"], 32]], [1, "4eHq0TA2dCu5E2C/9W+beQ", null, null, null, 1, 0], [1, 81.96017405451812, 0, 0], [1, -1, 1, 1]], [21, "bar_hp2_2", 33554432, 13, [[5, -187, [0, "08VIKK5OdGrLw1jyETtca9"], [5, 170, 13], [0, 0, 0.5]], [37, 3, 0, -188, [0, "bec+9oZQtMlrdFdUh6HUMB"], 33]], [1, "f3l+9cVy1BDbSI316As6p/", null, null, null, 1, 0], [1, 81.96017405451812, 0, 0], [1, -1, 1, 1]], [21, "txt_jd_1", 33554432, 4, [[5, -189, [0, "54pLJOXi1M36deltWUFaa/"], [5, 6, 61.44], [0, 1, 0.5]], [88, "", 36, 36, 44, true, true, 3, -190, [0, "631EYLSBxDcJ/vxZpMbPQN"]]], [1, "93mz1WvL9F2JqrejW5zgbY", null, null, null, 1, 0], [1, 37.664999999999964, -1.8269999999999982, 0], [1, 0.5, 0.5, 1]], [53, 0, {}, 4, [57, "79BcNar8tDmZucBHLjoa8/", null, null, -198, [61, "157xTsV11Fn6g7mrQpH6l5", 1, [[17, "ty_txk2", ["_name"], [4, ["79BcNar8tDmZucBHLjoa8/"]]], [11, ["_lpos"], [4, ["79BcNar8tDmZucBHLjoa8/"]], [1, 88.5, 4.583, 0]], [11, ["_lrot"], [4, ["79BcNar8tDmZucBHLjoa8/"]], [3, 0, 0, 0, 1]], [11, ["_euler"], [4, ["79BcNar8tDmZucBHLjoa8/"]], [1, 0, 0, 0]], [11, ["_lscale"], [4, ["79BcNar8tDmZucBHLjoa8/"]], [1, 0.9, 0.9, 1]], [17, true, ["_active"], [4, ["42Rk0WEWpD9J2OZPPe3K6X"]]], [23, ["_lscale"], -191, [1, 0.6, 0.6, 1]], [23, ["_lscale"], -192, [1, 0.8, 0.8, 1]], [17, true, ["_active"], [4, ["79BcNar8tDmZucBHLjoa8/"]]], [11, ["_contentSize"], [4, ["6cP4a1qqlAqKZFbaaU0XP2"]], [5, 105, 105]], [11, ["_contentSize"], [4, ["83Iz4xnIJBp4B4NrxAK6Pg"]], [5, 150, 150]], [11, ["_contentSize"], [4, ["47Cs6IYElOBK5jyf6MFKqL"]], [5, 112, 123]], [93, ["_spriteFrame"], [4, ["cdSAz8tiZPwYFWQVGKww65"]], 36], [17, null, ["_spriteFrame"], [4, ["34goCFzhxCNaeCDZ72IZKB"]]], [23, ["_lscale"], -193, [1, 0.6, 0.6, 1]], [27, true, ["_active"], -194], [27, "icon_txk2", ["_name"], -195], [17, "txt_wj_dj2", ["_name"], [4, ["8ev1z6IH5OgYw4mQ5jIqFq"]]], [27, "touxiang2", ["_name"], -196], [27, "icon_tx_di2", ["_name"], -197]]], 35]], [4, ["42NwrH+3dFtrJTfzGLD8tn"]], [53, 0, {}, 4, [57, "c46/YsCPVOJYA4mWEpNYRx", null, null, -203, [61, "d0rf8ag+hF7Zhv7ZPGTx4h", 1, [[17, "ty_zl1", ["_name"], [4, ["c46/YsCPVOJYA4mWEpNYRx"]]], [11, ["_lpos"], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [1, -31, -34.387, 0]], [11, ["_lrot"], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [3, 0, 0, 0, 1]], [23, ["_euler"], -199, [1, 0, 0, 0]], [23, ["_lscale"], -200, [1, -0.8, 0.8, 1]], [17, "", ["_string"], [4, ["dbf558ErpD24fb8rDfwYgY"]]], [23, ["_lscale"], -201, [1, -0.5, 0.5, 1]], [27, "txt_hp1", ["_name"], -202]]], 37]], [21, "txt_gk2", 33554432, 5, [[5, -204, [0, "9aX4EMrGNGd4er6RtB0BV9"], [5, 6, 81.6], [0, 0, 0.5]], [87, "", 2, 36, 36, 60, true, true, 3, -205, [0, "32P4ZbXKdBmbHzF//8R+xT"]]], [1, "27pUogs15LZ55t47iKZZ67", null, null, null, 1, 0], [1, -37.66499999999999, 27.624000000000024, 0], [1, 0.5, 0.5, 1]], [53, 0, {}, 5, [57, "c46/YsCPVOJYA4mWEpNYRx", null, null, -208, [61, "51gcBf6C9CMb89LsdMdqBB", 1, [[17, "ty_zl1", ["_name"], [4, ["c46/YsCPVOJYA4mWEpNYRx"]]], [11, ["_lpos"], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [1, 31, -34.387, 0]], [11, ["_lrot"], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [3, 0, 0, 0, 1]], [23, ["_euler"], -206, [1, 0, 0, 0]], [17, "txt_hp1", ["_name"], [4, ["749LXfhDlNKpFGD9kWraJg"]]], [23, ["_lscale"], -207, [1, 0.8, 0.8, 1]]]], 39]], [9, "bar_hp1_1", 33554432, 14, [[5, -209, [0, "6akumuGkNGQqBMtq1WwKa7"], [5, 170, 14], [0, 0, 0.5]], [37, 3, 0, -210, [0, "23k62gHBRHMJmlz1OA8JX6"], 40]], [1, "e6XcRmh0NHXIcbnw+zQFgM", null, null, null, 1, 0], [1, -81.685, 0, 0]], [9, "bar_hp1_2", 33554432, 14, [[5, -211, [0, "95ytIOaV5CcLUcmM7VpSL7"], [5, 170, 14], [0, 0, 0.5]], [37, 3, 0, -212, [0, "61FCFhZXBIn7+lVBUAgXco"], 41]], [1, "38jhf+1ERHAoprMoPmZ0D2", null, null, null, 1, 0], [1, -81.685, 0, 0]], [21, "txt_jd_1", 33554432, 5, [[5, -213, [0, "bb8sxmZLZLkoGpjW+djucr"], [5, 6, 61.44], [0, 0, 0.5]], [88, "", 36, 36, 44, true, true, 3, -214, [0, "cbekT4Gz9NK62YCB2KhVkB"]]], [1, "643Y4aqNJAK6CPjNRtmC2f", null, null, null, 1, 0], [1, -37.66499999999999, -1.8269999999999982, 0], [1, 0.5, 0.5, 1]], [53, 0, {}, 5, [57, "79BcNar8tDmZucBHLjoa8/", null, null, -221, [61, "793boLVA9L47M5qfGEtn8i", 1, [[17, "ty_txk1", ["_name"], [4, ["79BcNar8tDmZucBHLjoa8/"]]], [11, ["_lpos"], [4, ["79BcNar8tDmZucBHLjoa8/"]], [1, -88.5, 4.583, 0]], [11, ["_lrot"], [4, ["79BcNar8tDmZucBHLjoa8/"]], [3, 0, 0, 0, 1]], [11, ["_euler"], [4, ["79BcNar8tDmZucBHLjoa8/"]], [1, 0, 0, 0]], [11, ["_lscale"], [4, ["79BcNar8tDmZucBHLjoa8/"]], [1, 0.9, 0.9, 1]], [17, true, ["_active"], [4, ["42Rk0WEWpD9J2OZPPe3K6X"]]], [23, ["_lscale"], -215, [1, 0.6, 0.6, 1]], [23, ["_lscale"], -216, [1, 0.8, 0.8, 1]], [17, true, ["_active"], [4, ["79BcNar8tDmZucBHLjoa8/"]]], [11, ["_contentSize"], [4, ["6cP4a1qqlAqKZFbaaU0XP2"]], [5, 105, 105]], [11, ["_contentSize"], [4, ["83Iz4xnIJBp4B4NrxAK6Pg"]], [5, 150, 150]], [11, ["_contentSize"], [4, ["47Cs6IYElOBK5jyf6MFKqL"]], [5, 112, 123]], [93, ["_spriteFrame"], [4, ["cdSAz8tiZPwYFWQVGKww65"]], 44], [17, null, ["_spriteFrame"], [4, ["34goCFzhxCNaeCDZ72IZKB"]]], [23, ["_lscale"], -217, [1, 0.6, 0.6, 1]], [27, true, ["_active"], -218], [17, "txt_wj_dj1", ["_name"], [4, ["8ev1z6IH5OgYw4mQ5jIqFq"]]], [27, "touxiang1", ["_name"], -219], [27, "icon_tx_di1", ["_name"], -220]]], 43]], [9, "img_tuiguan_top_jingong", 33554432, 9, [[2, -222, [0, "4cVlpgZBpPwZRo/kBwV4AM"], [5, 82, 105]], [59, 2, false, -223, [0, "5fIVVjeu9JboGQm8KVeLzj"], 45]], [1, "f6vG0lIghCHoLk0b7BBXFY", null, null, null, 1, 0], [1, -270, -15.576, 0]], [9, "img_tuiguan_top_fangshou", 33554432, 9, [[2, -224, [0, "3fHnJIPXFH6qury1lz+LMt"], [5, 82, 105]], [3, -225, [0, "50tZU0hQlBQoaYlfQa8OjO"], 46]], [1, "faftScAhFEiYNz8jCUiExF", null, null, null, 1, 0], [1, 270, -15.576, 0]], [69, "bg_static", false, 33554432, 8, [[2, -226, [0, "50DDXBcGZHgrf64Wi2Nii1"], [5, 640, 1400]], [36, -227, [0, "77k1jkZXVPiIaMTFHWWdRV"]]], [1, "1fIm14d+1ExJ4n4AgonX8J", null, null, null, 1, 0]], [77, "bg_spine", 33554432, 8, [[18, -228, [0, "4b9sPx+nNNx551pxxGqSOj"], [0, 0.5, 0.4998212370396854]], [122, false, 0, false, -229, [0, "0bNYFQo6xMkZh3LXmOWJoV"]]], [1, "aepntXdwxHz5efp9PpXl+Y", null, null, null, 1, 0], [1, 1, 1.02, 1]], [69, "bg_ss_spine", false, 33554432, 8, [[7, -230, [0, "98VCSGfEpO0JE9GVNo0ZO3"]], [72, false, -231, [0, "16OMr4QHRIY5WNlqjzFSCT"]]], [1, "fe4er3RMJMAJUxVIC6zZ9L", null, null, null, 1, 0]], [32, "img_zrgh_zw_l", 33554432, 10, [[2, -232, [0, "eakCoXr+lBY5KhRnvj+XbM"], [5, 100, 98]], [3, -233, [0, "cf9KC0ZgRKMrCRYEprHTdm"], 50]], [1, "68RULtIJBLA7ckdmu4ItE4", null, null, null, 1, 0]], [69, "bg_ss_spine", false, 33554432, 1, [[7, -234, [0, "78+NLLmxdGR6IpqVY8TGnG"]], [72, false, -235, [0, "51Z4yC+5lKvLWsp3TPuywD"]]], [1, "204rJwdz5IoaCaGqqyc6BJ", null, null, null, 1, 0]], [6, "<PERSON><PERSON><PERSON>", 2, [[7, -236, [0, "b0i00IG/hK6ZxLUvFAhuyM"]]], [1, "7ayhTPv+BLpLWloyGNMxbg", null, null, null, 1, 0], [1, -137.177, 269.613, 0]], [6, "Clicker1", 2, [[7, -237, [0, "549hzumg9HPqTbpc92ccJZ"]]], [1, "4dRe+npNxGPrLeWzHEgG6Y", null, null, null, 1, 0], [1, 137.177, 269.613, 0]], [6, "L3", 2, [[7, -238, [0, "7dmpllMvdKf4PLCe+9o4B3"]]], [1, "f9FEmZfxlAv4PBUfeTdJ85", null, null, null, 1, 0], [1, -229.051, 84.917, 0]], [6, "R3", 2, [[7, -239, [0, "6dqa81D8pKqKYE0PktiXWQ"]]], [1, "9egBhEOIpOurXASVxXCILs", null, null, null, 1, 0], [1, 236.387, 91.026, 0]], [6, "L1", 2, [[7, -240, [0, "d7Bzy1hsROp7PkP0FrmpKU"]]], [1, "b1lt0+/P9HBrs3iIOtJcaZ", null, null, null, 1, 0], [1, -103.435, -37.99, 0]], [6, "R1", 2, [[7, -241, [0, "43AXxtumxNcoxpUAvovfoJ"]]], [1, "cbmuHxtlBIK60/lyHCC19b", null, null, null, 1, 0], [1, 109.941, -37.364, 0]], [6, "L4", 2, [[7, -242, [0, "39kDpOwXZAQ741HDQUh7li"]]], [1, "7f9CvmbFtJrrtuqGH1rR2m", null, null, null, 1, 0], [1, -230.488, -117.122, 0]], [6, "R4", 2, [[7, -243, [0, "39337zXKlOVrDs6uqT06HR"]]], [1, "dbQ36y0oBOT5OvBVYJvo3u", null, null, null, 1, 0], [1, 242.222, -121.013, 0]], [6, "L2", 2, [[7, -244, [0, "dbAXlLbCVLfLERUIl04JNn"]]], [1, "a9P5UptsxEIpTOdRr/2Oas", null, null, null, 1, 0], [1, -101.138, -207.997, 0]], [6, "R2", 2, [[7, -245, [0, "1bXHYHXpJCNZoaryW3ySZ1"]]], [1, "e5aHxgkNpHlLZNneWDFZ7K", null, null, null, 1, 0], [1, 111.887, -208.552, 0]], [6, "L5", 2, [[7, -246, [0, "4bDesgOtFJNII+3mq3vjef"]]], [1, "0aEI8OCqRK+75SX+9JNgBM", null, null, null, 1, 0], [1, -228.542, -305.817, 0]], [6, "R5", 2, [[7, -247, [0, "7aTeeXZglDZLAvxk5M3T1U"]]], [1, "7ayM4V/dZIEYSFfmWLlyUR", null, null, null, 1, 0], [1, 242.222, -301.926, 0]], [9, "btn_tg_js2", 33554432, 11, [[2, -248, [0, "9eZP0cGjpBpqIijqkDC+wr"], [5, 28, 31]], [3, -249, [0, "41e+KvDMVMIZRPpyjez3NE"], 91]], [1, "83j1oZo45LEbsj5fLHzAxz", null, null, null, 1, 0], [1, 2, 2, 0]], [21, "txt_js", 33554432, 12, [[2, -250, [0, "c2Mpg0EIJG1YGEbt9/QUVr"], [5, 88.06640625, 100.8]], [117, "X3", 72, 72, 80, true, -251, [0, "6deVnuljtFXpXf1X7bHoPd"], [4, 4291493631]]], [1, "c1gIYtDH5CyYA9KYpiN9J4", null, null, null, 1, 0], [1, 0, 2, 0], [1, 0.5, 0.5, 1]], [4, ["42Rk0WEWpD9J2OZPPe3K6X"]], [4, ["6bVIarBmlP8bobDR7pZWzE"]], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [4, ["749LXfhDlNKpFGD9kWraJg"]], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [4, ["42Rk0WEWpD9J2OZPPe3K6X"]], [4, ["6bVIarBmlP8bobDR7pZWzE"]], [4, ["42NwrH+3dFtrJTfzGLD8tn"]]], 0, [0, -1, 47, 0, -2, 43, 0, -3, 41, 0, -4, 39, 0, 6, 1, 0, 0, 1, 0, 0, 1, 0, -1, 17, 0, -2, 7, 0, -3, 4, 0, -4, 23, 0, -5, 5, 0, -6, 9, 0, -7, 8, 0, -8, 10, 0, -9, 54, 0, -10, 15, 0, -11, 11, 0, -12, 12, 0, -13, 16, 0, -14, 32, 0, 0, 2, 0, -1, 55, 0, -2, 56, 0, -3, 57, 0, -4, 58, 0, -5, 30, 0, -6, 59, 0, -7, 31, 0, -8, 60, 0, -9, 61, 0, -10, 62, 0, -11, 63, 0, -12, 64, 0, -13, 65, 0, -14, 66, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 18, 0, -2, 19, 0, -3, 20, 0, -4, 21, 0, -5, 22, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 35, 0, -2, 13, 0, -3, 38, 0, -4, 39, 0, -5, 41, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 42, 0, -2, 43, 0, -3, 14, 0, -4, 46, 0, -5, 47, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 25, 0, -2, 26, 0, -3, 27, 0, -4, 28, 0, -5, 29, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 14, 7, 0, 0, 7, 0, -1, 34, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 50, 0, -2, 51, 0, -3, 52, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 24, 0, -2, 48, 0, -3, 49, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, -1, 53, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, -1, 67, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, -1, 68, 0, 0, 13, 0, 0, 13, 0, -1, 36, 0, -2, 37, 0, 0, 14, 0, 0, 14, 0, -1, 44, 0, -2, 45, 0, 15, 31, 0, 16, 30, 0, 17, 66, 0, 18, 62, 0, 19, 58, 0, 20, 64, 0, 21, 60, 0, 22, 56, 0, 23, 65, 0, 24, 61, 0, 25, 57, 0, 26, 63, 0, 27, 59, 0, 28, 55, 0, 29, 33, 0, 30, 32, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, -1, 33, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 2, 69, 0, 2, 70, 0, 2, 40, 0, 2, 40, 0, 2, 40, 0, 2, 70, 0, 2, 69, 0, 6, 39, 0, 2, 71, 0, 2, 71, 0, 2, 72, 0, 2, 72, 0, 6, 41, 0, 0, 42, 0, 0, 42, 0, 2, 73, 0, 2, 73, 0, 6, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 2, 74, 0, 2, 75, 0, 2, 76, 0, 2, 76, 0, 2, 75, 0, 2, 74, 0, 6, 47, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 67, 0, 0, 68, 0, 0, 68, 0, 8, 1, 2, 3, 15, 3, 3, 7, 6, 3, 10, 251], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, -1, -2, -3, -4, 1, -1, -2, -3, -4, 1, -1, -2, -3, -4, 1, -1, -2, -3, -4, 1, -1, -2, -3, -4, 1, -1, -2, -3, -4, 1, 1, 1, 9, 11, 9, 1, 9, 1, 1, 1, 9, 11, 1, 1, 1, -1, -2, 1, 1, -1, -2, -3, -4, 1, -1, -2, -3, -4, 1, -1, -2, -3, -4, 1, -1, -2, -3, -4, 1, -1, -2, -3, -4, 1, -1, -2, -3, -4, 7, 7, 31, 32, 33, 34, 35, 36, 37, 1, 1, 1, 1], [37, 12, 0, 0, 4, 5, 6, 0, 0, 4, 5, 6, 0, 0, 4, 5, 6, 0, 0, 4, 5, 6, 0, 0, 4, 5, 6, 8, 8, 13, 14, 15, 38, 39, 16, 17, 18, 19, 40, 19, 41, 42, 16, 17, 18, 43, 44, 45, 46, 47, 12, 0, 0, 4, 5, 6, 0, 0, 4, 5, 6, 0, 0, 4, 5, 6, 0, 0, 4, 5, 6, 0, 0, 4, 5, 6, 8, 8, 13, 14, 15, 20, 20, 48, 49, 50, 51, 52, 53, 54, 55, 56, 21, 21]], [[[30, "<PERSON><PERSON><PERSON>"], [38, "<PERSON><PERSON><PERSON>", [-3, -4, -5, -6], [[7, -2, [0, "78EWmzcapLu5yyh5xOr0DW"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [78, "bg_left", false, 1, [-8, -9, -10, -11, -12, -13, -14], [[7, -7, [0, "c2bn4au11LzYpz5iBIle9C"]]], [1, "bdc7UsoMRPBKDMNZyG/aqN", null, null, null, 1, 0], [1, -123, -16, 0], [1, 0, 0, 2.504478065487657e-06]], [78, "bg_right", false, 1, [-16, -17, -18, -19, -20, -21, -22], [[7, -15, [0, "1dTRJifUhOaJqoRyxj5Sn9"]]], [1, "2aYzb6sRhF+LsjgJeqVg+9", null, null, null, 1, 0], [1, 134.5, -16, 0], [1, 0, 0, 2.504478065487657e-06]], [70, "star", [-25, -26, -27, -28, -29], [[2, -23, [0, "9diIDSNe9IbKYK+TfAv5AJ"], [5, 0, 100]], [73, 1, 1, -24, [0, "b6VMPslhBGabWRA2IiZSdA"]]], [1, "b45vc/nFpMWKQBmFBpBczR", null, null, null, 1, 0], [1, 60.0000114440918, -249.5, 0], [1, 0, 0, 2.504478065487657e-06]], [38, "txt_name", [-31, -32, -33, -34, -35], [[7, -30, [0, "9fDuTJ60xDXK4tTIN2HQSw"]]], [1, "e4qsJCbfJACpVXzwFbhjuA", null, null, null, 1, 0]], [79, "star", 2, [-38, -39, -40, -41, -42], [[2, -36, [0, "cbCKMVMKVONLCi3VhrEAom"], [5, 0, 100]], [73, 1, 1, -37, [0, "3cv4ESlOlN2J+0qiJKREIw"]]], [1, "40ZwznBuNFJKgQn3Xu4Jwr", null, null, null, 1, 0], [1, -54.357, 26.355, 0], [1, 0, 0, 2.504478065487657e-06]], [79, "star", 3, [-45, -46, -47, -48, -49], [[2, -43, [0, "a9kSKQWHpCO6mgqSyn9mdk"], [5, 0, 100]], [73, 1, 1, -44, [0, "9084bN0nVGopKv3xVvvgMN"]]], [1, "5chSYOmixADJrpJa6sOgD8", null, null, null, 1, 0], [1, 60, 28.874, 0], [1, 0, 0, 2.504478065487657e-06]], [76, "hanzhao", 1, [4, 5], [[18, -50, [0, "ea4RHgvzhNSINTO8i7IqFQ"], [0, 0.5, 0.5181327078477317]], [50, false, 0, -51, [0, "43/j2fEaxJO5Ae3gxqYLOI"], [[29, "root/wyui_1", 4], [29, "root/wyui_2", 5]]]], [1, "03h3UlbUVPgKSH5F2XkhrA", null, null, null, 1, 0]], [33, "icon_xx_1", false, 4, [[2, -52, [0, "d2l+thdrBGJKepojy2F0uA"], [5, 75, 75]], [3, -53, [0, "809VhRQMVP9bfkdgzIxFFR"], 0]], [1, "15IbvOrlBFrpaCS0IfDpzu", null, null, null, 1, 0]], [15, "icon_xx_2", false, 4, [[2, -54, [0, "44JnM5z/xNlaf3P4YyQIre"], [5, 75, 75]], [3, -55, [0, "a6zZKby+9GjaKaQpRkuYLO"], 1]], [1, "fd2Nk8nK5FtLAPCkYW81Hh", null, null, null, 1, 0], [1, -112.5, 0, 0]], [15, "icon_xx_3", false, 4, [[2, -56, [0, "512AVj15RHO4oBKbf0BrfF"], [5, 75, 75]], [3, -57, [0, "16iVMBTvFFYLq96ku84Pg7"], 2]], [1, "71a2NlGVZHnYoQxzKMahyE", null, null, null, 1, 0], [1, -37.5, 0, 0]], [15, "icon_xx_4", false, 4, [[2, -58, [0, "8afwb9QotI1Lnc6Y3Ug9rb"], [5, 75, 75]], [3, -59, [0, "44GWea6XdBpKoJF8lDIVzs"], 3]], [1, "f53WQQRiJE4bAyti9pHXEb", null, null, null, 1, 0], [1, 37.5, 0, 0]], [15, "icon_xx_5", false, 4, [[2, -60, [0, "97i4iKwNtL05XUMmMnJpy+"], [5, 75, 75]], [3, -61, [0, "22/u2DfI1EwIEUQKD294Us"], 4]], [1, "b2gq1kN2ZLZaxoFsc6Vx7S", null, null, null, 1, 0], [1, 112.5, 0, 0]], [43, "name", false, 5, [[2, -62, [0, "baRoVfF8JGe5Wul87drLNO"], [5, 128, 83.6]], [25, "单挑", 60, 60, 60, true, true, 4, true, 35, -63, [0, "cdRF+/oZZBNrTB1gi4ecDT"], [4, 4291546356], [4, 4279046773], [4, 3506441471], [0, 0, 0]]], [1, "12FfeKxCRMrLP4uL98ARgb", null, null, null, 1, 0], [1, 1.55, 1.8999999999999997, 1], [1, 0, 0, 2.5044780654876575e-06]], [43, "name1", false, 5, [[2, -64, [0, "c1o3UO59ZAFZnYN3MCb/rC"], [5, 128, 83.6]], [25, "单挑", 60, 60, 60, true, true, 4, true, 35, -65, [0, "e73KfsStFCIbrD/kI+WPNk"], [4, 4294960639], [4, 4288364664], [4, 3690954958], [0, 0, 0]]], [1, "7fFKqdrDRBco3WV8LlFNTa", null, null, null, 1, 0], [1, 1.55, 1.8999999999999997, 1], [1, 0, 0, 2.5044780654876575e-06]], [43, "name2", false, 5, [[2, -66, [0, "62h/BDlJFPapKaDcFHSAj7"], [5, 128, 83.6]], [25, "单挑", 60, 60, 60, true, true, 4, true, 35, -67, [0, "c6fjXOsBhLJpvbnOWZnCj9"], [4, 4294960639], [4, 4288500672], [4, 4292735487], [0, 0, 0]]], [1, "7fohGXLDZKNKdMhLEkEIBL", null, null, null, 1, 0], [1, 1.55, 1.8999999999999997, 1], [1, 0, 0, 2.5044780654876575e-06]], [43, "name3", false, 5, [[2, -68, [0, "f0kadW5S1M9p7cYZfHj96c"], [5, 128, 83.6]], [41, "单挑", 60, 60, 60, true, true, 4, true, 35, -69, [0, "f3uHLAl+pAcrwy+ZwG7Q+S"], [4, 4290342985], [4, 4294949268], [0, 0, 0]]], [1, "b3gjTIowlHXIgI+pE5AB01", null, null, null, 1, 0], [1, 1.55, 1.8999999999999997, 1], [1, 0, 0, 2.5044780654876575e-06]], [43, "name4", false, 5, [[2, -70, [0, "1epj0RCEpBM7Owz4w+uWrX"], [5, 128, 83.6]], [41, "单挑", 60, 60, 60, true, true, 4, true, 35, -71, [0, "09XFOG9rdDpZa0sEIEcHcR"], [4, 4287937493], [4, 3864378367], [0, 0, 0]]], [1, "5e+0qMNM9D+LicjxnF//d/", null, null, null, 1, 0], [1, 1.55, 1.8999999999999997, 1], [1, 0, 0, 2.5044780654876575e-06]], [34, "img_left", 2, [[2, -72, [0, "23gT2Pk1ZCAKKV45m1xbuq"], [5, 394, 410]], [3, -73, [0, "62AtbgihdILqaZnZCKeB6c"], 5]], [1, "fbMqZ/+KBAqalXxmDeZfGi", null, null, null, 1, 0]], [15, "icon_xx_1", false, 6, [[2, -74, [0, "69AfFA+VBMGa8KcUzapA6M"], [5, 75, 75]], [3, -75, [0, "75JcNV+y9AOI9e7pmTenC2"], 6]], [1, "6caa1upTpCApAPECawQB1g", null, null, null, 1, 0], [1, -150, 0, 0]], [15, "icon_xx_2", false, 6, [[2, -76, [0, "c74B3/AmRNEqK3x6Fy5z50"], [5, 75, 75]], [3, -77, [0, "8aXbaeEDZKDbVZycqlrGKb"], 7]], [1, "66XeKQnXxCAoUp+Ud79XA4", null, null, null, 1, 0], [1, -112.5, 0, 0]], [15, "icon_xx_3", false, 6, [[2, -78, [0, "0f1RcPISFEVKnSqHO4tvyX"], [5, 75, 75]], [3, -79, [0, "c59UQ8y5dFupAy69xDDtt9"], 8]], [1, "9fOfUMMPdI969B5ozyD88s", null, null, null, 1, 0], [1, -37.5, 0, 0]], [15, "icon_xx_4", false, 6, [[2, -80, [0, "03c5+XPktBFa3MZwQiARJA"], [5, 75, 75]], [3, -81, [0, "b3sza136BIQb7tUMVVN3cB"], 9]], [1, "9b+jf+sw9BEpjt8dOMRG89", null, null, null, 1, 0], [1, 37.5, 0, 0]], [15, "icon_xx_5", false, 6, [[2, -82, [0, "98ZODnnPFGG5yarVHP9ppU"], [5, 75, 75]], [3, -83, [0, "422M+EQ/NODoIxJAfDwKLj"], 10]], [1, "07P0beklpIGY8aDaJPWJFl", null, null, null, 1, 0], [1, 112.5, 0, 0]], [26, "name4", false, 2, [[2, -84, [0, "e9y6gs1uFCB7a9g+mNNLqr"], [5, 128, 83.6]], [41, "单挑", 60, 60, 60, true, true, 4, true, 35, -85, [0, "cbu3YRwHRPF5KQzPl9jq0p"], [4, 4287937493], [4, 3864378367], [0, 0, 0]]], [1, "71HrpFeeNEkpbAEqW/u+Hx", null, null, null, 1, 0], [1, -34.591, -95.537, 0], [1, 1.55, 1.9, 1], [1, 0, 0, 2.504478065487657e-06]], [26, "name3", false, 2, [[2, -86, [0, "4blcQZKJ9KG5gAykENQB5M"], [5, 128, 83.6]], [41, "单挑", 60, 60, 60, true, true, 4, true, 35, -87, [0, "61QvCbrgBPUq4ZNpSfhjBI"], [4, 4290342985], [4, 4294949268], [0, 0, 0]]], [1, "aeasitxpxI6YIY1WZho6kU", null, null, null, 1, 0], [1, -34.591, -95.537, 0], [1, 1.55, 1.9, 1], [1, 0, 0, 2.504478065487657e-06]], [26, "name2", false, 2, [[2, -88, [0, "fcLq2wQVBPoKFJk6si6QRj"], [5, 128, 83.6]], [25, "单挑", 60, 60, 60, true, true, 4, true, 35, -89, [0, "ddmEeVGvpDppK1+SSnMcgn"], [4, 4294960639], [4, 4288500672], [4, 4292735487], [0, 0, 0]]], [1, "14PMJaZ+lKVK5heLaPTmGr", null, null, null, 1, 0], [1, -34.591, -95.537, 0], [1, 1.55, 1.9, 1], [1, 0, 0, 2.504478065487657e-06]], [26, "name1", false, 2, [[2, -90, [0, "04rO/6/1BLiLPD7PVrMCUt"], [5, 128, 83.6]], [25, "单挑", 60, 60, 60, true, true, 4, true, 35, -91, [0, "b7AetRfeBBJKI0h5EKlhZc"], [4, 4294960639], [4, 4288364664], [4, 3690954958], [0, 0, 0]]], [1, "aeSyYpPopHMq1RPtjyB/qm", null, null, null, 1, 0], [1, -34.591, -95.537, 0], [1, 1.55, 1.9, 1], [1, 0, 0, 2.504478065487657e-06]], [80, "name", 2, [[2, -92, [0, "ddaa7Pc3JI/KcQSkwix/d6"], [5, 128, 83.6]], [25, "单挑", 60, 60, 60, true, true, 4, true, 35, -93, [0, "a67KzvdO5DRZWiGtY7S3Na"], [4, 4291546356], [4, 4279046773], [4, 3506441471], [0, 0, 0]]], [1, "2833LeTOJMcr6nyCiO76za", null, null, null, 1, 0], [1, -34.591, -95.537, 0], [1, 1.55, 1.9, 1], [1, 0, 0, 2.504478065487657e-06]], [34, "img_right", 3, [[2, -94, [0, "17aKUsdqRJNKSnPZcyItIs"], [5, 371, 410]], [3, -95, [0, "08uY33KXRKgLad25pXyC+K"], 11]], [1, "95ErfHezxB7JH8sL4i1ReA", null, null, null, 1, 0]], [15, "icon_xx_1", false, 7, [[2, -96, [0, "85BoS1MT1DIKqjzQJS/ul0"], [5, 75, 75]], [3, -97, [0, "54Im7VJo5N/5SvhgpMY5iQ"], 12]], [1, "e9NLBDV15JY485uGe3dDWw", null, null, null, 1, 0], [1, -150, 0, 0]], [15, "icon_xx_2", false, 7, [[2, -98, [0, "fdP182XWBJKa4oUeHEfR5g"], [5, 75, 75]], [3, -99, [0, "650Z23MzNNEa1eIcyzkdvx"], 13]], [1, "8bz8ZnhINP0YrZznsQwn5v", null, null, null, 1, 0], [1, -112.5, 0, 0]], [15, "icon_xx_3", false, 7, [[2, -100, [0, "7fnNr6Ot1PmLLvbISvs8CL"], [5, 75, 75]], [3, -101, [0, "94WajSaHFP8KF5uztyrWcY"], 14]], [1, "39laMq+yVGlKm7g54MbN3A", null, null, null, 1, 0], [1, -37.5, 0, 0]], [15, "icon_xx_4", false, 7, [[2, -102, [0, "85P5HjfPhLFIkiEsA4+o6x"], [5, 75, 75]], [3, -103, [0, "f8acEYjytNbZkRzRnf7Odb"], 15]], [1, "18XX3v8fdKypjoSl8t39u3", null, null, null, 1, 0], [1, 37.5, 0, 0]], [15, "icon_xx_5", false, 7, [[2, -104, [0, "51JOHlbE9EeYDBQa3IREkc"], [5, 75, 75]], [3, -105, [0, "91095mL3BAfrW2Y9sxhrnV"], 16]], [1, "52+ECR+J1BW65pqEkuOLlE", null, null, null, 1, 0], [1, 112.5, 0, 0]], [26, "name4", false, 3, [[2, -106, [0, "60eBH8CpVKXbL0bLppzMDu"], [5, 128, 83.6]], [41, "单挑", 60, 60, 60, true, true, 4, true, 35, -107, [0, "55jra7YQFNOogiKTWUL/Hr"], [4, 4287937493], [4, 3864378367], [0, 0, 0]]], [1, "6ej6sYgB1L5LokxrYIZ2e4", null, null, null, 1, 0], [1, 34.591, -95.537, 0], [1, 1.55, 1.9, 1], [1, 0, 0, 2.504478065487657e-06]], [26, "name3", false, 3, [[2, -108, [0, "7cNd9pWC5JLrsgThAuQie8"], [5, 128, 83.6]], [41, "单挑", 60, 60, 60, true, true, 4, true, 35, -109, [0, "b3SklRewlA24NoczzSRK+9"], [4, 4290342985], [4, 4294949268], [0, 0, 0]]], [1, "adZf+dUgBE27W9vsPA9c36", null, null, null, 1, 0], [1, 34.591, -95.537, 0], [1, 1.55, 1.9, 1], [1, 0, 0, 2.504478065487657e-06]], [26, "name2", false, 3, [[2, -110, [0, "25g5Ntya5Ava4qaNiz8u4O"], [5, 128, 83.6]], [25, "单挑", 60, 60, 60, true, true, 4, true, 35, -111, [0, "4eARoN/BpLRJxYFcTz0j7B"], [4, 4294960639], [4, 4288500672], [4, 4292735487], [0, 0, 0]]], [1, "f3GlQdGfhI/KGT7UCcB9WF", null, null, null, 1, 0], [1, 34.591, -95.537, 0], [1, 1.55, 1.9, 1], [1, 0, 0, 2.504478065487657e-06]], [26, "name1", false, 3, [[2, -112, [0, "d33lpp4bBNHK/g3tuHm/NT"], [5, 128, 83.6]], [25, "单挑", 60, 60, 60, true, true, 4, true, 35, -113, [0, "15KG62v/pFEb758Z+a4bgI"], [4, 4294960639], [4, 4288364664], [4, 3690954958], [0, 0, 0]]], [1, "1dVFb9f4NL4p7eusLYr5Zo", null, null, null, 1, 0], [1, 34.591, -95.537, 0], [1, 1.55, 1.9, 1], [1, 0, 0, 2.504478065487657e-06]], [80, "name", 3, [[2, -114, [0, "7bUORgZ6VIr5O5L3xSWY8z"], [5, 128, 83.6]], [25, "单挑", 60, 60, 60, true, true, 4, true, 35, -115, [0, "9b4FWLKPVOerTPjRUYlqVH"], [4, 4291546356], [4, 4279046773], [4, 3506441471], [0, 0, 0]]], [1, "17xXWubHhMh69INof4k4D/", null, null, null, 1, 0], [1, 34.591, -95.537, 0], [1, 1.55, 1.9, 1], [1, 0, 0, 2.504478065487657e-06]], [33, "vs_spine", false, 1, [[2, -116, [0, "33CbU8N59OBZeuONXcbP2V"], [5, 642, 640]], [123, "default", false, 0, -117, [0, "98+Ei/NTpMI7kDlK+/m4i7"], [[29, "root/left", 2], [29, "root/right", 3]], 17]], [1, "da2ZraMp9HyqqjmqqT4XAb", null, null, null, 1, 0]]], 0, [0, 6, 1, 0, 0, 1, 0, -1, 8, 0, -2, 2, 0, -3, 3, 0, -4, 41, 0, 0, 2, 0, -1, 19, 0, -2, 6, 0, -3, 25, 0, -4, 26, 0, -5, 27, 0, -6, 28, 0, -7, 29, 0, 0, 3, 0, -1, 30, 0, -2, 7, 0, -3, 36, 0, -4, 37, 0, -5, 38, 0, -6, 39, 0, -7, 40, 0, 0, 4, 0, 0, 4, 0, -1, 9, 0, -2, 10, 0, -3, 11, 0, -4, 12, 0, -5, 13, 0, 0, 5, 0, -1, 14, 0, -2, 15, 0, -3, 16, 0, -4, 17, 0, -5, 18, 0, 0, 6, 0, 0, 6, 0, -1, 20, 0, -2, 21, 0, -3, 22, 0, -4, 23, 0, -5, 24, 0, 0, 7, 0, 0, 7, 0, -1, 31, 0, -2, 32, 0, -3, 33, 0, -4, 34, 0, -5, 35, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 8, 1, 4, 3, 8, 5, 3, 8, 117], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7], [3, 3, 3, 3, 3, 57, 3, 3, 3, 3, 3, 58, 3, 3, 3, 3, 3, 59]], [[[63, "ani_35601_fuhuo", ".bin", "\nani_35601_fuhuo.png\nsize: 695,695\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n1/lz_00\n  rotate: false\n  xy: 647, 380\n  size: 30, 69\n  orig: 48, 90\n  offset: 9, 10\n  index: -1\n1/lz_01\n  rotate: false\n  xy: 542, 542\n  size: 29, 75\n  orig: 48, 90\n  offset: 9, 5\n  index: -1\n1/lz_02\n  rotate: false\n  xy: 573, 543\n  size: 28, 74\n  orig: 48, 90\n  offset: 10, 7\n  index: -1\n1/lz_03\n  rotate: false\n  xy: 574, 620\n  size: 28, 73\n  orig: 48, 90\n  offset: 10, 8\n  index: -1\n1/lz_04\n  rotate: true\n  xy: 614, 46\n  size: 27, 73\n  orig: 48, 90\n  offset: 10, 8\n  index: -1\n1/lz_05\n  rotate: true\n  xy: 614, 18\n  size: 26, 72\n  orig: 48, 90\n  offset: 11, 9\n  index: -1\n1/lz_06\n  rotate: false\n  xy: 182, 124\n  size: 26, 71\n  orig: 48, 90\n  offset: 11, 10\n  index: -1\n1/lz_07\n  rotate: false\n  xy: 182, 51\n  size: 26, 71\n  orig: 48, 90\n  offset: 11, 10\n  index: -1\n1/lz_08\n  rotate: false\n  xy: 210, 14\n  size: 24, 70\n  orig: 48, 90\n  offset: 12, 11\n  index: -1\n1/lz_09\n  rotate: false\n  xy: 236, 15\n  size: 23, 69\n  orig: 48, 90\n  offset: 12, 11\n  index: -1\n1/lz_10\n  rotate: true\n  xy: 113, 36\n  size: 22, 56\n  orig: 48, 90\n  offset: 12, 14\n  index: -1\n1/lz_11\n  rotate: true\n  xy: 604, 429\n  size: 20, 26\n  orig: 48, 90\n  offset: 13, 26\n  index: -1\nglow\n  rotate: false\n  xy: 182, 197\n  size: 28, 30\n  orig: 30, 30\n  offset: 1, 0\n  index: -1\nglow2\n  rotate: false\n  xy: 604, 451\n  size: 77, 62\n  orig: 77, 77\n  offset: 0, 8\n  index: -1\nglow3\n  rotate: false\n  xy: 647, 348\n  size: 30, 30\n  orig: 38, 38\n  offset: 4, 4\n  index: -1\nqg_00\n  rotate: false\n  xy: 526, 444\n  size: 76, 75\n  orig: 90, 90\n  offset: 7, 7\n  index: -1\ntx/skill/caopingdise/j\n  rotate: false\n  xy: 435, 17\n  size: 177, 56\n  orig: 181, 56\n  offset: 2, 0\n  index: -1\ntx/skill/caoxunhuan/i0001\n  rotate: true\n  xy: 237, 129\n  size: 34, 24\n  orig: 39, 33\n  offset: 3, 0\n  index: -1\ntx/skill/caoxunhuan/i0003\n  rotate: true\n  xy: 237, 93\n  size: 34, 24\n  orig: 39, 33\n  offset: 3, 0\n  index: -1\ntx/skill/caoxunhuan/i0005\n  rotate: false\n  xy: 331, 15\n  size: 34, 24\n  orig: 39, 33\n  offset: 3, 0\n  index: -1\ntx/skill/caoxunhuan/i0007\n  rotate: false\n  xy: 261, 14\n  size: 33, 25\n  orig: 39, 33\n  offset: 3, 0\n  index: -1\ntx/skill/caoxunhuan/i0009\n  rotate: false\n  xy: 113, 9\n  size: 34, 25\n  orig: 39, 33\n  offset: 2, 0\n  index: -1\ntx/skill/caoxunhuan/i0011\n  rotate: false\n  xy: 2, 6\n  size: 34, 25\n  orig: 39, 33\n  offset: 2, 0\n  index: -1\ntx/skill/caoxunhuan/i0013\n  rotate: false\n  xy: 38, 6\n  size: 34, 25\n  orig: 39, 33\n  offset: 2, 0\n  index: -1\ntx/skill/caoxunhuan/i0015\n  rotate: false\n  xy: 261, 41\n  size: 34, 25\n  orig: 39, 33\n  offset: 2, 0\n  index: -1\ntx/skill/caoxunhuan/i0017\n  rotate: false\n  xy: 2, 33\n  size: 35, 25\n  orig: 39, 33\n  offset: 1, 0\n  index: -1\ntx/skill/caoxunhuan/i0019\n  rotate: false\n  xy: 39, 33\n  size: 35, 25\n  orig: 39, 33\n  offset: 1, 0\n  index: -1\ntx/skill/caoxunhuan/i0021\n  rotate: true\n  xy: 237, 165\n  size: 35, 24\n  orig: 39, 33\n  offset: 1, 0\n  index: -1\ntx/skill/caoxunhuan/i0023\n  rotate: false\n  xy: 76, 33\n  size: 35, 25\n  orig: 39, 33\n  offset: 1, 0\n  index: -1\ntx/skill/caoxunhuan/i0025\n  rotate: true\n  xy: 171, 13\n  size: 36, 24\n  orig: 39, 33\n  offset: 0, 0\n  index: -1\ntx/skill/caoxunhuan/i0027\n  rotate: false\n  xy: 74, 7\n  size: 36, 24\n  orig: 39, 33\n  offset: 0, 0\n  index: -1\ntx/skill/caoxunhuan/i0029\n  rotate: true\n  xy: 210, 160\n  size: 35, 25\n  orig: 39, 33\n  offset: 1, 0\n  index: -1\ntx/skill/caoxunhuan/i0031\n  rotate: false\n  xy: 212, 202\n  size: 35, 25\n  orig: 39, 33\n  offset: 1, 0\n  index: -1\ntx/skill/caoxunhuan/i0033\n  rotate: true\n  xy: 210, 123\n  size: 35, 25\n  orig: 39, 33\n  offset: 1, 0\n  index: -1\ntx/skill/caoxunhuan/i0035\n  rotate: true\n  xy: 210, 86\n  size: 35, 25\n  orig: 39, 33\n  offset: 1, 0\n  index: -1\ntx/skill/caoxunhuan/i0037\n  rotate: false\n  xy: 297, 41\n  size: 34, 25\n  orig: 39, 33\n  offset: 2, 0\n  index: -1\ntx/skill/caoxunhuan/i0039\n  rotate: false\n  xy: 333, 41\n  size: 34, 25\n  orig: 39, 33\n  offset: 2, 0\n  index: -1\ntx/skill/caoxunhuan/i0041\n  rotate: false\n  xy: 369, 41\n  size: 34, 25\n  orig: 39, 33\n  offset: 2, 0\n  index: -1\ntx/skill/caoxunhuan/i0043\n  rotate: true\n  xy: 405, 32\n  size: 34, 25\n  orig: 39, 33\n  offset: 2, 0\n  index: -1\ntx/skill/caoxunhuan/i0045\n  rotate: false\n  xy: 296, 14\n  size: 33, 25\n  orig: 39, 33\n  offset: 3, 0\n  index: -1\ntx/skill/caoxunhuan/i0047\n  rotate: true\n  xy: 367, 5\n  size: 34, 24\n  orig: 39, 33\n  offset: 3, 0\n  index: -1\ntx/skill/caoxunhuan/i0049\n  rotate: false\n  xy: 393, 6\n  size: 34, 24\n  orig: 39, 33\n  offset: 3, 0\n  index: -1\ntx/skill/hua/g\n  rotate: false\n  xy: 2, 586\n  size: 274, 107\n  orig: 274, 112\n  offset: 0, 2\n  index: -1\ntx/skill/xx1/e0017\n  rotate: true\n  xy: 263, 335\n  size: 178, 84\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0018\n  rotate: true\n  xy: 526, 161\n  size: 178, 83\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0019\n  rotate: true\n  xy: 611, 161\n  size: 178, 82\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0021\n  rotate: true\n  xy: 611, 161\n  size: 178, 82\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0020\n  rotate: false\n  xy: 2, 60\n  size: 178, 82\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0022\n  rotate: false\n  xy: 2, 144\n  size: 178, 83\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0023\n  rotate: true\n  xy: 440, 161\n  size: 178, 84\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0024\n  rotate: true\n  xy: 604, 515\n  size: 178, 84\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0025\n  rotate: true\n  xy: 263, 68\n  size: 178, 84\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0026\n  rotate: true\n  xy: 349, 68\n  size: 178, 84\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0027\n  rotate: false\n  xy: 435, 75\n  size: 178, 84\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0028\n  rotate: true\n  xy: 278, 515\n  size: 178, 85\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0029\n  rotate: true\n  xy: 2, 406\n  size: 178, 85\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0030\n  rotate: true\n  xy: 89, 406\n  size: 178, 85\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0031\n  rotate: true\n  xy: 176, 406\n  size: 178, 85\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0032\n  rotate: true\n  xy: 2, 229\n  size: 175, 85\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0033\n  rotate: true\n  xy: 89, 229\n  size: 175, 85\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0034\n  rotate: true\n  xy: 176, 229\n  size: 175, 85\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0035\n  rotate: false\n  xy: 263, 248\n  size: 175, 85\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0036\n  rotate: false\n  xy: 349, 428\n  size: 175, 85\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0037\n  rotate: false\n  xy: 365, 608\n  size: 175, 85\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0038\n  rotate: false\n  xy: 365, 521\n  size: 175, 85\n  orig: 178, 85\n  offset: 0, 0\n  index: -1\ntx/skill/xx1/e0039\n  rotate: false\n  xy: 349, 341\n  size: 157, 85\n  orig: 178, 85\n  offset: 2, 0\n  index: -1\ntx/skill/xx1/e0040\n  rotate: false\n  xy: 508, 341\n  size: 137, 85\n  orig: 178, 85\n  offset: 2, 0\n  index: -1\nx_00\n  rotate: false\n  xy: 542, 619\n  size: 30, 74\n  orig: 48, 78\n  offset: 9, 3\n  index: -1\nzw_mj_1\n  rotate: false\n  xy: 615, 83\n  size: 66, 76\n  orig: 221, 246\n  offset: 72, 21\n  index: -1\n", ["ani_35601_fuhuo.png"], [0]], -1], 0, 0, [0], [-1], [60]], [[{"name": "bg_tx_1", "rect": {"x": 0, "y": 0, "width": 660, "height": 1400}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 660, "height": 1400}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-330, -700, 0, 330, -700, 0, -330, 700, 0, 330, 700, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1400, 660, 1400, 0, 0, 660, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -330, "y": -700, "z": 0}, "maxPos": {"x": 330, "y": 700, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [8], 0, [0], [10], [61]], [[{"name": "ss_vs_2_left", "rect": {"x": 0, "y": 186, "width": 394, "height": 410}, "offset": {"x": -123, "y": -45}, "originalSize": {"width": 640, "height": 692}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-197, -205, 0, 197, -205, 0, -197, 205, 0, 197, 205, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 506, 394, 506, 0, 96, 394, 96], "nuv": [0, 0.13872832369942195, 0.615625, 0.13872832369942195, 0, 0.7312138728323699, 0.615625, 0.7312138728323699], "minPos": {"x": -197, "y": -205, "z": 0}, "maxPos": {"x": 197, "y": 205, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [8], 0, [0], [10], [62]], [[{"name": "ss_vs_2_right", "rect": {"x": 269, "y": 186, "width": 371, "height": 410}, "offset": {"x": 134.5, "y": -45}, "originalSize": {"width": 640, "height": 692}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-185.5, -205, 0, 185.5, -205, 0, -185.5, 205, 0, 185.5, 205, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [269, 506, 640, 506, 269, 96, 640, 96], "nuv": [0.4203125, 0.13872832369942195, 1, 0.13872832369942195, 0.4203125, 0.7312138728323699, 1, 0.7312138728323699], "minPos": {"x": -185.5, "y": -205, "z": 0}, "maxPos": {"x": 185.5, "y": 205, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [8], 0, [0], [10], [63]], [[[30, "DropHP"], [38, "DropHP", [-4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16], [[2, -2, [0, "4e70i6P8ZNgJ38TtVGnIGU"], [5, 200, 15]], [113, -3, [0, "fe2EoKJPtL4oUhBtHAl4Tq"]]], [1, "1cUvQ/gQlEi4NZShyeEYEO", null, null, null, -1, 0]], [81, "txt_crit", false, 33554432, 1, [-21, -22], [[2, -17, [0, "06aoXi5+JCSpj+NPH3k2pR"], [5, 73.85203621111604, 40]], [20, 1, 1, -3.8, true, -18, [0, "05DQedoG5E17377Ad/wNij"]], [12, -19, [0, "14p0n/VZBP04VHA7dsGvRD"]], [16, -20, [0, "c29UU840ZAR6YTzpZQw5KY"], [8], 9]], [1, "d6/BCttIpCW7mOlUbQMP2s", null, null, null, 1, 0]], [19, "state1", false, 1, [-27, -28], [[2, -23, [0, "f6orQ/RDhAHJnLJwt3aEpz"], [5, 60.852036211116044, 36]], [20, 1, 1, -3.8, true, -24, [0, "f8WlCD9WREYa/WJL6AjWVg"]], [16, -25, [0, "a7rR16PddIdb3sApkKngHx"], [12], 13], [12, -26, [0, "d4eBYTuyJHrqknR7AAeTRV"]]], [1, "f4l7JMxMVFHJ+J0V6UnA2h", null, null, null, 1, 0]], [19, "state2", false, 1, [-33, -34], [[2, -29, [0, "62GpwnrL1Ft5dVUNvu5An1"], [5, 157.95945808611603, 36]], [20, 1, 1, -3.8, true, -30, [0, "813YkJzOxM36NMl/SlBid3"]], [12, -31, [0, "0eo1yLT1ZPKr8CNWYP3UiG"]], [16, -32, [0, "edrrUyA2RCuoKWeUiSTD/o"], [16], 17]], [1, "b7p/n+A5tDwoAi3IIMFVZ2", null, null, null, 1, 0]], [19, "state3", false, 1, [-39, -40], [[2, -35, [0, "93VycBaExFK7iFmGSarG2H"], [5, 57.852036211116044, 36]], [20, 1, 1, -3.8, true, -36, [0, "9drmZaK4VFmLz6ou09UJ6Y"]], [12, -37, [0, "07XOjaiSxM6JGCTJek5rhq"]], [16, -38, [0, "5fAZioVqJCM6GI/D2biOmv"], [20], 21]], [1, "adm+2Gua5PSbtlzVOmLGyG", null, null, null, 1, 0]], [19, "state4", false, 1, [-45, -46], [[2, -41, [0, "6bye7IkxlAGL/atXA2wtS4"], [5, 53.852036211116044, 36]], [20, 1, 1, -3.8, true, -42, [0, "7bnAei7PlJspfoL3lmRcu4"]], [12, -43, [0, "d2/XhftTpOGaZjlw3YRodK"]], [16, -44, [0, "5flyfUaIxDnYCPawZhh0Wm"], [24], 25]], [1, "a7lb+z8gBDULlix6IHLLtZ", null, null, null, 1, 0]], [19, "state5", false, 1, [-51, -52], [[2, -47, [0, "e79BpS3aNCHJrbxeI3uigH"], [5, 53.852036211116044, 36]], [20, 1, 1, -3.8, true, -48, [0, "d09yTeqkNFz6J3VbxHU+Jb"]], [12, -49, [0, "3as+3XApNA0rBCIxw4Fm3v"]], [16, -50, [0, "6ch9wARcZM/Jpem3Ldnq51"], [28], 29]], [1, "4ey+dtlqxP9LROXzINLu1y", null, null, null, 1, 0]], [19, "state6", false, 1, [-57, -58], [[2, -53, [0, "4bSKNV9YJLvINB2A4x01ok"], [5, 53.852036211116044, 36]], [20, 1, 1, -3.8, true, -54, [0, "f4VRGbS9tLZaNGkDJgM3Je"]], [12, -55, [0, "ffDbisdR9IKZTslVTvseMR"]], [16, -56, [0, "6fB9P+AjNHeopSBzapIl8u"], [32], 33]], [1, "b1ah5foehJg5/kOwCYHsoe", null, null, null, 1, 0]], [19, "state7", false, 1, [-63, -64], [[2, -59, [0, "cfcv4xT9hHYbTLzd5isrRO"], [5, 59.852036211116044, 36]], [20, 1, 1, -3.8, true, -60, [0, "d9ZI8/5ChAS7zW0pIy3+Uj"]], [12, -61, [0, "e6pYLcSl9PkJTqBysoSo0L"]], [16, -62, [0, "faovGO0WpAdLwSPlk4peDn"], [36], 37]], [1, "92XS9caLVJu6EhebjECYFw", null, null, null, 1, 0]], [19, "state8", false, 1, [-69, -70], [[2, -65, [0, "06qpts88RDuICbeFPLK8nw"], [5, 80.85203621111604, 36]], [20, 1, 1, -3.8, true, -66, [0, "cbaGzGHFpN8azwS+vI1B9h"]], [12, -67, [0, "d9fzyIQCREKLDP4wEdKHJx"]], [16, -68, [0, "beEKafZNtOzKa0XgDdMWT9"], [40], 41]], [1, "b64qHpG+dDUq+Y0JA5Z8Sr", null, null, null, 1, 0]], [19, "state9", false, 1, [-75, -76], [[2, -71, [0, "51KQqyOb1E96jJy2xmLXbg"], [5, 59.852036211116044, 36]], [20, 1, 1, -3.8, true, -72, [0, "a4U1q/HIVEEKDENa/zaA0/"]], [12, -73, [0, "2cFc7tQeRDO7Rw9ySgPtV7"]], [16, -74, [0, "c7dvGMmx9ISoulhjrlCYhS"], [44], 45]], [1, "078wzlss9B8pI+Afiw3TcV", null, null, null, 1, 0]], [19, "hurt", false, 1, [-78, -79, -80, -81, -82], [[2, -77, [0, "cetN25ILdHSr0nfYtWbzZz"], [5, 98.7, 36]]], [1, "157X+rlMBNuY35wWABxy9s", null, null, null, 1, 0]], [33, "down", false, 1, [[2, -83, [0, "9cdt73GrVA0qe2xC9CGPb1"], [5, 0, 100]], [89, "", 26, 26, 100, false, false, -5, true, true, -84, [0, "92AqLjKERPkLicMKgX7MFP"], [4, 4282895860], [4, 4279966745], 0], [16, -85, [0, "9dOI8W7PhKkLjPnnx/AWrE"], [1], 2], [12, -86, [0, "f15oPWBvJMJosFbn0RlyKx"]]], [1, "1612lLn69FS6tgeaDJSFHU", null, null, null, 1, 0]], [33, "up", false, 1, [[2, -87, [0, "a0IRplfupLV6aUPMRb0eX0"], [5, 0, 100]], [89, "", 26, 26, 100, false, false, -5, true, true, -88, [0, "8e0YndvS5FFqngnEi3FG2p"], [4, 4285593504], [4, 4278985235], 3], [16, -89, [0, "061loOAL1GGIOX+jZvpfpf"], [4], 5], [12, -90, [0, "f2PTUABh1GsovynuC/nfGH"]]], [1, "b2/FumrlxJy6D4LKJYZeMY", null, null, null, 1, 0]], [8, "txt_state1", 3, [[2, -91, [0, "c8UQTZ5YVLcrokco0vDLTM"], [5, 25.304072422232082, 110.8]], [22, "", 72, 72, 80, true, true, true, 5, -92, [0, "25I+W24m1Cw6N0K3x+NhDE"], [4, 4281139249]], [13, 0.45, 0.74, 1, 1, -93, [0, "93iERP1kNK05/sxAD2Yykk"], [[[4, 4294946808], [4, 4293591272]], 8, 8], 11], [12, -94, [0, "73X4co5BNGBaetkQZmi7+J"]]], [1, "89SeMIEHpJU6+BWbvsh5Cf", null, null, null, 1, 0], [1, 24.099999999999998, 0, 0], [1, 0.5, 0.5, 1]], [8, "txt_crit_sz1", 2, [[2, -95, [0, "acSJJ684JCyZosHfDqfZJk"], [5, 25.304072422232082, 110.8]], [22, "", 72, 72, 80, true, true, true, 5, -96, [0, "13/uHMPcpHwr+HM7Oy+jZP"], [4, 4278193993]], [13, 0.36, 0.92, 1, 1, -97, [0, "86bivrCIJE/6/DCkeirs6t"], [[[4, 4278222591], [4, 4278201087]], 8, 8], 7]], [1, "bctm+A+JFLQpveoB/G6/wZ", null, null, null, 1, 0], [1, 30.599999999999994, 0, 0], [1, 0.5, 0.5, 1]], [8, "txt_state2", 4, [[2, -98, [0, "59nVAIwIRPBpkflga/h/uW"], [5, 225.51891617223208, 110.8]], [22, "00000", 72, 72, 80, true, true, true, 5, -99, [0, "devF5CmAdEZohthVKZdvyG"], [4, 4278193993]], [13, 0.36, 0.92, 1, 1, -100, [0, "8eWE4+Lm9Mg5soDVJmCsFD"], [[[4, 4278222591], [4, 4278201087]], 8, 8], 15]], [1, "a7p3NgJvBA96OO1eUlRG0z", null, null, null, 1, 0], [1, 22.60000000000001, 0, 0], [1, 0.5, 0.5, 1]], [8, "txt_state3", 5, [[2, -101, [0, "3fvtsFxuRAGL1MYSGWpVth"], [5, 25.304072422232082, 110.8]], [22, "", 72, 72, 80, true, true, true, 5, -102, [0, "6cSmBifaRFUqbnyASop5Cm"], [4, 4278193993]], [13, 0.36, 0.92, 1, 1, -103, [0, "7d4FGT6k9DLJu0wZmGvWN5"], [[[4, 4278222591], [4, 4278201087]], 8, 8], 19]], [1, "5f7v0x3kpK3IehwF/O/9hM", null, null, null, 1, 0], [1, 22.599999999999998, 0, 0], [1, 0.5, 0.5, 1]], [8, "txt_state4", 6, [[2, -104, [0, "41RhmHMghPj7As3edM+7nZ"], [5, 25.304072422232082, 110.8]], [22, "", 72, 72, 80, true, true, true, 5, -105, [0, "233v2r90FOCIztJ6WEf186"], [4, 4278196809]], [13, 0.36, 0.76, 1, 1, -106, [0, "77GN+WY6xEHYceg81QOSfG"], [[[4, 4286574335], [4, 4280390911]], 8, 8], 23]], [1, "fdyGztdO5L5JST/OV3ygwT", null, null, null, 1, 0], [1, 20.599999999999998, 0, 0], [1, 0.5, 0.5, 1]], [8, "txt_state5", 7, [[2, -107, [0, "226oBaTZ1LX6xZUAeF7xAJ"], [5, 25.304072422232082, 110.8]], [22, "", 72, 72, 80, true, true, true, 5, -108, [0, "c5KRv2GlZBLroa6sL6ouas"], [4, 4278196809]], [13, 0.36, 0.76, 1, 1, -109, [0, "c76D+yNXhJQ5e92OoEc7Z3"], [[[4, 4294375158], [4, 4291743438]], 8, 8], 27]], [1, "2fnzMKH8NIF6R0Wj8G0nnv", null, null, null, 1, 0], [1, 20.599999999999998, 0, 0], [1, 0.5, 0.5, 1]], [8, "txt_state6", 8, [[2, -110, [0, "f3ji2hGepK2Kzvz54+37Hv"], [5, 25.304072422232082, 110.8]], [22, "", 72, 72, 80, true, true, true, 5, -111, [0, "69yOWiMipK0pw/+IhW4Y1g"], [4, 4278196809]], [13, 0.36, 0.76, 1, 1, -112, [0, "6feqXZJllGRosQGldZbd18"], [[[4, 4286574335], [4, 4280390911]], 8, 8], 31]], [1, "17WPz1riVFwbAwlcHdiy3p", null, null, null, 1, 0], [1, 20.599999999999998, 0, 0], [1, 0.5, 0.5, 1]], [8, "txt_state7", 9, [[2, -113, [0, "a5/B11nrdJILFESeP0mFZW"], [5, 25.304072422232082, 110.8]], [22, "", 72, 72, 80, true, true, true, 5, -114, [0, "4e8F9I6aJLOqnFOre3Da+K"], [4, 4278196809]], [13, 0.36, 0.76, 1, 1, -115, [0, "791niBdBJLupmNXeLLVNaY"], [[[4, 4286574335], [4, 4280390911]], 8, 8], 35]], [1, "0fUwOl4UlARZrDNz2RzmBv", null, null, null, 1, 0], [1, 23.599999999999998, 0, 0], [1, 0.5, 0.5, 1]], [8, "txt_state8", 10, [[2, -116, [0, "e6nZCZU/ROb7PT4+NIGzL8"], [5, 25.304072422232082, 110.8]], [22, "", 72, 72, 80, true, true, true, 5, -117, [0, "59pw0YYVFEfqTYyD7tKPwQ"], [4, 4278196809]], [13, 0.36, 0.76, 1, 1, -118, [0, "a0SwVjySFEqaCJgmrd/DWO"], [[[4, 4286574335], [4, 4280390911]], 8, 8], 39]], [1, "377ZKlloRGIb9I1Jr2fU4g", null, null, null, 1, 0], [1, 34.1, 0, 0], [1, 0.5, 0.5, 1]], [8, "txt_state9", 11, [[2, -119, [0, "70eOrk6vdMtbweBUtNQp/S"], [5, 25.304072422232082, 110.8]], [22, "", 72, 72, 80, true, true, true, 5, -120, [0, "e8J48ZnqlDZZOSEzo7MSS/"], [4, 4278196809]], [13, 0.36, 0.76, 1, 1, -121, [0, "89XcG3oYRL95ISDcO1o3Dj"], [[[4, 4286574335], [4, 4280390911]], 8, 8], 43]], [1, "a3I32WuONLp79wRzA4CAPp", null, null, null, 1, 0], [1, 23.599999999999998, 0, 0], [1, 0.5, 0.5, 1]], [44, "txt_hurt1", false, 12, [[2, -122, [0, "90XgdiTJVJyp26+Q0pcOrU"], [5, 153.84375, 139.04]], [49, 64, 64, 104, true, true, 4, -123, [0, "6clQT9jqVABYXwvq8vIDrw"], [4, 4278193993]], [13, 0.36, 0.81, 1, 1, -124, [0, "6dLavSw4FLma9G6+ZKJcgW"], [[[4, 4278222591], [4, 4278201087]], 8, 8], 46]], [1, "bbcIEPFBlBM4FR30tV9lKL", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [44, "txt_hurt2", false, 12, [[2, -125, [0, "95IrT8bdFJ5rEDAYORtntl"], [5, 153.84375, 139.04]], [49, 64, 64, 104, true, true, 4, -126, [0, "d7S/dDx9pMUqpRH5s5cY31"], [4, 4280430848]], [13, 0.36, 0.71, 1, 1, -127, [0, "107zD/e8JCzIU3m52QRqVF"], [[[4, 4294442927], [4, 4285131520]], 8, 8], 47]], [1, "950JunZ+VFU4W5vCBh6RxI", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [44, "txt_hurt3", false, 12, [[2, -128, [0, "10A0TRue5M/b5jc1Aa+CFI"], [5, 153.84375, 139.04]], [49, 64, 64, 104, true, true, 4, -129, [0, "8bmjXaLJJMEI2vVuW2aZyB"], [4, 4278201402]], [13, 0.38, 0.8, 1, 1, -130, [0, "47XNmulRdOhLQokl58Iiql"], [[[4, 4292868607], [4, 4278250239]], 8, 8], 48]], [1, "5776GnzuZKCbiPHQSln3p/", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [44, "txt_hurt4", false, 12, [[2, -131, [0, "bcUemOQM5Cz7mvoSkZpjHt"], [5, 153.84375, 139.04]], [49, 64, 64, 104, true, true, 4, -132, [0, "2774uFs1ZP9JW+7Y5M/5CK"], [4, 4281139249]], [13, 0.38, 0.8, 1, 1, -133, [0, "97yA6vCStKypZdonllYkNR"], [[[4, 4294946808], [4, 4293591272]], 8, 8], 49]], [1, "c6QFHAPEFFDrZ0lgi+Uktf", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [44, "txt_hurt5", false, 12, [[2, -134, [0, "f3mAzooVJCvKxyge5V6leU"], [5, 153.84375, 139.04]], [49, 64, 64, 104, true, true, 4, -135, [0, "3b+EXtampJwaQps7icwL1A"], [4, 4279569970]], [13, 0.38, 0.8, 1, 1, -136, [0, "75X3VKbWNLmLcpdcra5E+8"], [[[4, 4294375158], [4, 4291743438]], 8, 8], 50]], [1, "bc88w0zpZPhoeojdrTWAmz", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [9, "img_crit", 33554432, 2, [[2, -137, [0, "b7F+PmrVNCga1p0R61p8IN"], [5, 65, 65]], [3, -138, [0, "dfaIYXJ+VP4bDFL+QiCyYD"], 6]], [1, "6bZKhc2D5JIrz3p5L1CEWU", null, null, null, 1, 0], [1, -4.426018105558025, 0, 0]], [6, "ico_zd_zt1", 3, [[2, -139, [0, "cbkBwLPVZFVop816idQsEJ"], [5, 52, 52]], [3, -140, [0, "86ZMR/YONG26iWM/uj9is/"], 10]], [1, "50odzH74pOqJW8CupuoUeW", null, null, null, 1, 0], [1, -4.426018105558022, 0, 0]], [6, "ico_zd_zt2", 4, [[2, -141, [0, "daMn4kZZFFabt3D8BSSv6X"], [5, 49, 55]], [3, -142, [0, "80SFdIr1pEPIQRAdrkem4f"], 14]], [1, "52Oxh6q3BFcrZ7tvg56LTt", null, null, null, 1, 0], [1, -54.479729043058015, 0, 0]], [6, "ico_zd_zt3", 5, [[2, -143, [0, "b3F5vYeyRE3aLZAqjHrNE+"], [5, 49, 52]], [3, -144, [0, "92D27qXxpHh5uiTJEUy5Xz"], 18]], [1, "a0ibfwCDtBt74vluZ2UHFW", null, null, null, 1, 0], [1, -4.426018105558022, 0, 0]], [6, "ico_zd_zt4", 6, [[2, -145, [0, "f8EAMpccxEtKEdSkCkEGQI"], [5, 45, 55]], [3, -146, [0, "ffR47DUiBBBrg/06hy7nAO"], 22]], [1, "4cdod6FkpOkJR5AfLE9cIW", null, null, null, 1, 0], [1, -4.426018105558022, 0, 0]], [6, "ico_zd_zt5", 7, [[2, -147, [0, "9dx8V2lRtK2rosLy3ZSDhn"], [5, 45, 54]], [3, -148, [0, "90KXTPWmdArYELw5B9o1DT"], 26]], [1, "c9+fMwLPJNmLAR4IkgiJc/", null, null, null, 1, 0], [1, -4.426018105558022, 0, 0]], [6, "ico_zd_zt6", 8, [[2, -149, [0, "6cti3j28VIwLq86VRcInik"], [5, 45, 52]], [3, -150, [0, "ae7gWeTIlDFqLTeaHdi0Nl"], 30]], [1, "08XlgO4WdLbIlALtTebWxG", null, null, null, 1, 0], [1, -4.426018105558022, 0, 0]], [6, "ico_zd_zt7", 9, [[2, -151, [0, "55H416OvZG9JXTqDGUUaDq"], [5, 51, 55]], [3, -152, [0, "1bYW5/Qd5CEYSZsa9psSHu"], 34]], [1, "71yBDgN11IOLnq6yHUfTDB", null, null, null, 1, 0], [1, -4.426018105558022, 0, 0]], [6, "ico_zd_zt8", 10, [[2, -153, [0, "e9jzLd1fdGmbHTc6JsFIcY"], [5, 72, 51]], [3, -154, [0, "bdfbLfZkZOiKIvummAnk8/"], 38]], [1, "f8QKR2JDBNtZ9K+aVsSGRd", null, null, null, 1, 0], [1, -4.426018105558022, 0, 0]], [6, "ico_zd_zt9", 11, [[2, -155, [0, "f7tkAxeMxKBbG305VvSnrG"], [5, 51, 63]], [3, -156, [0, "977VOrnvlB/KZ7W579lEvl"], 42]], [1, "71yaymkw5AYKwuLvWNLYwF", null, null, null, 1, 0], [1, -4.426018105558022, 0, 0]]], 0, [0, 6, 1, 0, 0, 1, 0, 0, 1, 0, -1, 13, 0, -2, 14, 0, -3, 2, 0, -4, 3, 0, -5, 4, 0, -6, 5, 0, -7, 6, 0, -8, 7, 0, -9, 8, 0, -10, 9, 0, -11, 10, 0, -12, 11, 0, -13, 12, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 30, 0, -2, 16, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 31, 0, -2, 15, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 32, 0, -2, 17, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 33, 0, -2, 18, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 34, 0, -2, 19, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 35, 0, -2, 20, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 36, 0, -2, 21, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 37, 0, -2, 22, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, -1, 38, 0, -2, 23, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, -1, 39, 0, -2, 24, 0, 0, 12, 0, -1, 25, 0, -2, 26, 0, -3, 27, 0, -4, 28, 0, -5, 29, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 8, 1, 156], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [12, -1, 5, 12, -1, 5, 1, 4, -1, 5, 1, 4, -1, 5, 1, 4, -1, 5, 1, 4, -1, 5, 1, 4, -1, 5, 1, 4, -1, 5, 1, 4, -1, 5, 1, 4, -1, 5, 1, 4, -1, 5, 1, 4, -1, 5, 4, 4, 4, 4, 4], [64, 7, 7, 65, 7, 7, 66, 2, 22, 22, 67, 2, 1, 1, 68, 2, 1, 1, 69, 2, 1, 1, 23, 2, 1, 1, 70, 2, 1, 1, 71, 2, 1, 1, 72, 2, 1, 1, 73, 2, 1, 1, 74, 2, 1, 1, 2, 2, 2, 2, 2]], [[[63, "ani_hougong_buff", ".bin", "\nani_hougong_buff.png\nsize: 993,575\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nbachong\n  rotate: false\n  xy: 360, 382\n  size: 267, 191\n  orig: 267, 191\n  offset: 0, 0\n  index: -1\nbachong2\n  rotate: true\n  xy: 612, 20\n  size: 136, 96\n  orig: 137, 96\n  offset: 1, 0\n  index: -1\nbaiqi\n  rotate: false\n  xy: 2, 25\n  size: 259, 177\n  orig: 259, 177\n  offset: 0, 0\n  index: -1\nbaiqi2\n  rotate: true\n  xy: 893, 145\n  size: 133, 89\n  orig: 133, 89\n  offset: 0, 0\n  index: -1\nhuaban\n  rotate: false\n  xy: 2, 2\n  size: 21, 21\n  orig: 26, 28\n  offset: 3, 3\n  index: -1\nkuang\n  rotate: false\n  xy: 295, 178\n  size: 208, 196\n  orig: 209, 197\n  offset: 1, 0\n  index: -1\nkuang2\n  rotate: false\n  xy: 629, 353\n  size: 232, 220\n  orig: 277, 289\n  offset: 23, 34\n  index: -1\nleimiya\n  rotate: false\n  xy: 263, 5\n  size: 177, 171\n  orig: 177, 171\n  offset: 0, 0\n  index: -1\nleimiya2\n  rotate: false\n  xy: 710, 3\n  size: 90, 86\n  orig: 92, 86\n  offset: 1, 0\n  index: -1\ntianshan\n  rotate: true\n  xy: 442, 14\n  size: 142, 168\n  orig: 144, 168\n  offset: 1, 0\n  index: -1\ntianshan2\n  rotate: false\n  xy: 802, 4\n  size: 74, 85\n  orig: 75, 85\n  offset: 1, 0\n  index: -1\ntiao\n  rotate: false\n  xy: 505, 158\n  size: 219, 193\n  orig: 219, 193\n  offset: 0, 0\n  index: -1\ntiao2\n  rotate: true\n  xy: 893, 280\n  size: 111, 98\n  orig: 112, 98\n  offset: 0, 0\n  index: -1\nxin\n  rotate: true\n  xy: 726, 178\n  size: 173, 165\n  orig: 173, 165\n  offset: 0, 0\n  index: -1\nxinx\n  rotate: true\n  xy: 878, 24\n  size: 119, 111\n  orig: 173, 165\n  offset: 27, 29\n  index: -1\nxiya\n  rotate: false\n  xy: 2, 376\n  size: 356, 197\n  orig: 356, 197\n  offset: 0, 0\n  index: -1\nxiya2\n  rotate: true\n  xy: 863, 393\n  size: 180, 99\n  orig: 181, 99\n  offset: 1, 0\n  index: -1\nyue\n  rotate: false\n  xy: 2, 204\n  size: 291, 170\n  orig: 291, 170\n  offset: 0, 0\n  index: -1\nyue2\n  rotate: false\n  xy: 726, 91\n  size: 148, 85\n  orig: 149, 85\n  offset: 1, 0\n  index: -1\n", ["ani_hougong_buff.png"], [0]], -1], 0, 0, [0], [-1], [75]], [[[30, "Role"], [83, "Role", [[-17, -18, -19, -20, -21, -22, -23, [94, "drop_hp", -25, [1, "33qQ5wExtIDoVrtowE4bdq", null, null, null, -24, 0]], -26, -27, -28, -29, -30], 1, 1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 1], [[133, -14, [0, "ed5zFxWwJNLKaI06yMe7+/"], -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, -2], [12, -15, [0, "560+FvCsZAGb7rvcnm2arc"]], [7, -16, [0, "83ybydjC9Df5P/0dn9QSaY"]]], [1, "4dntOGz/RLQbYHCrgZgTWH", null, null, null, -1, 0]], [31, "bar", 33554432, 1, [-33, -34, -35, -36, -37, -38], [[18, -31, [0, "ecNZCX3M1ASrAyHBcllSvg"], [0, 0.5, 0]], [85, 0, -32, [0, "ceRKQSRqhP14hUubq4Zd4s"]]], [1, "0aG4bobkxFoaA4sDVbtQ5x", null, null, null, 1, 0], [1, 0, -39.906019561888115, 0]], [35, "bar1", 2, [-42, -43, -44, -45, -46], [[2, -39, [0, "86Bw5XHgBJ85iPkJxgKFQN"], [5, 67, 21]], [134, false, 1, -40, [0, "aev4kdQzNDXZ7F/QvlwJQg"]], [135, false, -41, [0, "6blNzttipGVLfjnYtOR7Dy"], [4, 16777215]]], [1, "48eRurx+NOj5WhZkkdpj54", null, null, null, 1, 0], [1, 1.387, 107.592, 0]], [100, "spine", 1, [[45, "bd_head", -49, [1, "689/fnIwpBdIYcOa3dNir9", null, null, null, 1, 0], [1, -1.1674081179080531e-05, 133.53591918945312, 0], [1, 0, 0, 2.5044780654876575e-06]], [45, "bd_body", -50, [1, "03wC8GYKRC1KDkvYf753Ha", null, null, null, 1, 0], [1, 0, 45.58196258544922, 0], [1, 0, 0, 2.5044780654876575e-06]], [95, "bd_foot", -51, [1, "b5LB9t64VEQpXnY6SOB7M0", null, null, null, 1, 0], [1, 0, 0, 2.5044780654876575e-06]]], [[[18, -47, [0, "69IFaIFh1AKYXjdnB4X4yN"], [0, 0.5, 0.29443501527525406]], -48], 4, 1], [1, "38/pon2XlJkJcTKs7lEk44", null, null, null, 1, 0]], [35, "roll_layout", 1, [-54, -55], [[18, -52, [0, "c4LkPm+HxNLLKf5jGcRRT2"], [0, 0.5, 0]], [126, false, 1, 2, 0, true, true, -53, [0, "05PVKU6EVFjr2gsiCHSbLi"]]], [1, "88CgPc2wNJXKI2ppV99NpD", null, null, null, 1, 0], [1, 0, 96.94, 0]], [47, "hpchangebar", 33554432, 3, [-59], [[[2, -56, [0, "7b8MGwTIxKrKg7SwJ43QJE"], [5, 67.4, 8]], [37, 1, 0, -57, [0, "abKPo61w1Ehrq0rT7LI3Yc"], 2], -58], 4, 4, 1], [1, "76AW7OimVCS53CITSoEIeX", null, null, null, 1, 0], [1, -0.9850000000000136, 3.7749999999999773, 0]], [47, "nuqibar", 33554432, 3, [-63], [[[2, -60, [0, "f3tgKaWTtKpIjovKTSU9LB"], [5, 67.4, 8]], [86, 1, 0, -61, [0, "87K9nGFfZNl664QB4I5FDs"], [4, 4278190080], 5], -62], 4, 4, 1], [1, "e6kVM9AHJIbbFQ3Ct6ga/C", null, null, null, 1, 0], [1, -0.9850000000000136, -4.225000000000023, 0]], [35, "buff_lay", 1, [-66], [[5, -64, [0, "b61+lZdr9KnKRPahIFZMxN"], [5, 100, 23], [0, 0.5, 0]], [127, 1, 3, 1, 4, 0, 2, 3, -65, [0, "e59dK/iQRIq7gWs7fo5vK4"]]], [1, "fe+MCI6pBJVLsu0wLkYy09", null, null, null, 1, 0], [1, 88.768, 169.658, 0]], [47, "hpbar", 33554432, 3, [-69], [[[2, -67, [0, "26A/ZPzfpLI7rrDYG/nryp"], [5, 67.4, 8]], -68], 4, 1], [1, "6fZsZPhmpE4pccMzXhGJPQ", null, null, null, 1, 0], [1, -0.9850000000000136, 3.7749999999999773, 0]], [46, "Bar", 33554432, 9, [[[5, -70, [0, "b2/oArfYdCKZnVpR3a3G0B"], [5, 67, 7], [0, 0, 0.5]], -71, [10, -72, [0, "cfQZj5MFNJ/rxh1Q+6faZ8"], [3, 4]]], 4, 1, 4], [1, "5516UbSRFMIrvkrPh/Rj/s", null, null, null, 1, 0], [1, -33.162, 0.1, 0]], [47, "hpMs", 33554432, 3, [-75], [[[2, -73, [0, "d8Yw7V0uBLgqUcoRyz7RpC"], [5, 67.4, 8]], -74], 4, 1], [1, "25fo7dp0ZDW57tV789+aHG", null, null, null, 1, 0], [1, -0.9850000000000136, 3.7749999999999773, 0]], [47, "hudun<PERSON>", 33554432, 3, [-78], [[[5, -76, [0, "9eEp7i+pJAcYWstedK1vBm"], [5, 67.4, 6], [0, 0, 0.5]], -77], 4, 1], [1, "f8WMGnXSNFnbjPO9BdlD5O", null, null, null, 1, 0], [1, -35.296, 3.775, 0]], [31, "bg_HP_2", 33554432, 2, [-81], [[2, -79, [0, "f0+/qoRTlA0Lo96Rp3t8SB"], [5, 72, 23]], [3, -80, [0, "98knwgBelIYp90u88FJCWg"], 7]], [1, "57j03ngMNOsat97xlx7jOr", null, null, null, 1, 0], [1, 1.387, 107.483, 0]], [21, "icon_hb_zy1", 33554432, 2, [[2, -82, [0, "29/amd/TxDZ7ogPl+8+FDa"], [5, 76, 81]], [58, 0, -83, [0, "36V9X1GRpIfIrG3mYJq+LZ"], 8], [10, -84, [0, "59Heqg3xlLNIKVIcVDXBoF"], [9, 10, 11, 12, 13, 14]]], [1, "bc+o3LnoFNqZW0eeHvlGOv", null, null, null, 1, 0], [1, -46.847, 106.765, 0], [1, 0.5, 0.5, 1]], [31, "buff", 33554432, 8, [-86, -87], [[2, -85, [0, "d5RndquVRCPLcgwa03RPWi"], [5, 23, 23]]], [1, "5dwAfBNndGo6Ro8vZNUMj9", null, null, null, 1, 0], [1, -38.5, 11.5, 0]], [6, "ms_spine", 1, [[18, -88, [0, "b3IHnd2klMAJmMeHG2dGwM"], [0, 0.5, 0.2806022169858787]], [24, false, 0, -89, [0, "46KaSN+qVLvL8wFDCNh1CT"]]], [1, "54dHFs7jlIR4V1Rd5S0ohx", null, null, null, 1, 0], [1, -97.278, 0, 0]], [34, "animals_lay", 5, [[5, -90, [0, "7e9HyYlJZHJ6mdBVVr4q/f"], [5, 32, 50], [0, 0.5, 0]], [128, 1, 1, -32, 1, 1, -91, [0, "58lxNHfIlP+LxxXdko6MA5"]]], [1, "abFfc9OxVPJpdd4/pveQcR", null, null, null, 1, 0]], [6, "hougong_lay", 5, [[5, -92, [0, "852vjakk9HsZtAFtwbAx0c"], [5, 62, 50], [0, 0.5, 0]], [129, 1, 1, -62, -93, [0, "09fvhzXf9EkZz12PtH/zxL"]]], [1, "dew7dkKlxJgKW+xauScWTk", null, null, null, 1, 0], [1, 0, 50, 0]], [32, "fazhen_hou", 33554432, 1, [[7, -94, [0, "b4fYzUrLBHeqcgdnMH8WHK"]], [24, false, 0, -95, [0, "00jyAmpy5MY5T0LBm6PqlR"]]], [1, "45Ynkjl/RChJtsp6AZVlzY", null, null, null, 1, 0]], [101, "buff_ms_hd", 1, [[[18, -96, [0, "cfipiFLGdCG71lTNmMJCnw"], [0, 0.5, 0.4977088554972477]], -97], 4, 1], [1, "e7aPGUVXxHkbsIBaG7CSJ1", null, null, null, 1, 0], [1, 0, 45, 0]], [32, "fazhen_qian", 33554432, 1, [[7, -98, [0, "6eYV3QJBtGUIfTb+bOp9X6"]], [24, false, 0, -99, [0, "79G7Rt9YxMirui3Obr2/I/"]]], [1, "67Ze3eB5FN64/HX5FH9mBN", null, null, null, 1, 0]], [33, "buff_ss_ani", false, 1, [[7, -100, [0, "e07oqYqwBOjKLhNSAQI4eU"]], [72, false, -101, [0, "c8lJOplk9Ks5cz5656xxWu"]]], [1, "b8LPwzeqBP6Kv1+qsbTgwq", null, null, null, 1, 0]], [9, "bg_HP_2_mask", 33554432, 2, [[2, -102, [0, "9euMsr9V5LvYrcpN/8qHE1"], [5, 66.8, 18.2]], [58, 0, -103, [0, "7dTKVHXyZGzL/cIOowBMwf"], 0]], [1, "eevkndrzBIKKUuqSE2+WWV", null, null, null, 1, 0], [1, 1.387, 107.428, 0]], [9, "bg_HP_1_1", 33554432, 2, [[2, -104, [0, "45feq5zfJIZr0Fr13Q6Yp3"], [5, 42, 42]], [3, -105, [0, "adNlQNCsNPFoqocgvbBGXD"], 1]], [1, "79E+a8k0xHa5SvliZ6IFdD", null, null, null, 1, 0], [1, -25.478, 118.285, 0]], [46, "Bar", 33554432, 6, [[[5, -106, [0, "e1MdOFWpFGcKufeqezphIJ"], [5, 67, 7], [0, 0, 0.5]], -107], 4, 1], [1, "b3gEigOFlNu4m+sO1oYKSd", null, null, null, 1, 0], [1, -33.162, 0.1, 0]], [46, "Bar", 33554432, 11, [[[5, -108, [0, "0bhm6Jij1BeKTt9W8fAfsB"], [5, 0, 7], [0, 0, 0.5]], -109], 4, 1], [1, "6ebqhKV/NN/qgWes8aMKKv", null, null, null, 1, 0], [1, -33.162, 0.1, 0]], [46, "Bar", 33554432, 12, [[[5, -110, [0, "fdYkwFYF5PYra+4w4lI8oB"], [5, 0, 7], [0, 0, 0.5]], -111], 4, 1], [1, "444MUHBqNFCqDPg/jGVlxE", null, null, null, 1, 0], [1, 0, 0.1, 0]], [46, "Bar", 33554432, 7, [[[5, -112, [0, "59QNxUrbBFRrzNZyTFTrj1"], [5, 67, 9], [0, 0, 0.5]], -113], 4, 1], [1, "cbuaa3LM5GiKGNCU1K20ID", null, null, null, 1, 0], [1, -33.162, 0.1, 0]], [9, "bg_HP_1", 33554432, 13, [[2, -114, [0, "92nuea/L1EYL7zPXoywi/n"], [5, 52, 52]], [3, -115, [0, "4aP+QEUMtN46rcfbPEspUp"], 6]], [1, "e6CodjF8JKG7MpA1opfNeX", null, null, null, 1, 0], [1, -48.007, -0.055, 0]], [96, "icon_ms1", false, 33554432, 2, [[2, -116, [0, "e01wkRUqhE07K5P9ntPQI5"], [5, 62, 72]], [3, -117, [0, "cfdI43VGdEOLH+KLsq4Kh7"], 15]], [1, "c1zn+jOfBGOY8c3jSrKePU", null, null, null, 1, 0], [1, -46.847, 106.765, 0], [1, 0.5, 0.5, 1]], [32, "icon", 33554432, 15, [[2, -118, [0, "ba87htF5tEa57nJE426S0W"], [5, 23, 23]], [36, -119, [0, "8afwx/sVFIlZoQvq1mua9f"]]], [1, "56V2s4gxJIl7tu4vChG3v9", null, null, null, 1, 0]], [21, "num", 33554432, 15, [[5, -120, [0, "3bKq1z8tBB36R+mFjQXlt6"], [5, 6, 46.32], [0, 1, 0.5]], [118, "", 32, 32, 32, false, true, true, 3, -121, [0, "6648xaYjdNJZ0hQc/W9Hu1"]]], [1, "0d3MgF/KJDMqei+2cGdJXC", null, null, null, 1, 0], [1, 11, -3, 0], [1, 0.5, 0.5, 1]], [8, "txt_name", 1, [[2, -122, [0, "2ajRS9avxOHKTGzthljXr4"], [5, 231.652344, 50.4]], [119, "", 41, 2, true, true, 3, -123, [0, "1bSs64WNlOLZsAgL5iJUAE"], [4, 4279998975], [4, 4280098330]]], [1, "415xFTLXNGjrySCSS/2fxe", null, null, null, 1, 0], [1, -10.913, 156.118, 0], [1, 0.5, 0.5, 1]], [33, "animal_ani", false, 1, [[5, -124, [0, "0egrZUnblG65WceIRT7NHG"], [5, 87.53099060058594, 61.13117980957031], [0, 0.2660122644201595, 0.30315818139112644]], [42, "default", false, 0, -125, [0, "eex99BWiROorHV12vkLEYY"], 16]], [1, "d2O30gOaxLL5w+VHJ/RFSJ", null, null, null, 1, 0]], [33, "hougong_ani", false, 1, [[5, -126, [0, "12P5UFILVKrqc+480Vrvfm"], [5, 121.0111083984375, 122.49732971191406], [0, 0.4999999684764703, 0.5852970577299285]], [42, "default", false, 0, -127, [0, "aft8qK20tE5oTlMLomy/Er"], 17]], [1, "d7Wv10TvNPJK89PDdE37Mj", null, null, null, 1, 0]], [124, false, 0, false, 4, [0, "63NcfGEFJO+YTXSDM1h9m9"], [[62], [62], [62], [62]]], [24, false, 0, 20, [0, "804/u40wNG9Z9ay19QOskj"]], [48, 1, 0, 25, [0, "c8E087k15O26NN91mNhs7t"]], [51, 67, 1, 6, [0, "369mOGZPlEA5LITXsXzGZG"], 38], [48, 1, 0, 10, [0, "d5UBXb6tZN05BX9Z+C0UsW"]], [51, 67, 1, 9, [0, "b8/3UMxT1LI72YP8wJp1VP"], 40], [48, 1, 0, 26, [0, "c845gAXGpGf7FVheTcYHJE"]], [51, 67, 0, 11, [0, "4dGHdbWrxGgIBnWek2mVFP"], 42], [48, 1, 0, 27, [0, "0e8mSKTqhDt7P18YhuCqaa"]], [51, 67, 0, 12, [0, "ddJ2ngXS1HGok4ncQQEmgg"], 44], [48, 1, 0, 28, [0, "878sbxyEVOP5pDjJbOPY/u"]], [51, 67, 1, 7, [0, "f7fFOUGDRIW6cxJy99CCxp"], 46]], 0, [0, 6, 1, 0, 38, 18, 0, 39, 5, 0, 40, 17, 0, 41, 37, 0, 42, 43, 0, 43, 16, 0, 44, 8, 0, 45, 47, 0, 46, 45, 0, 47, 41, 0, 48, 39, 0, 13, 36, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 19, 0, -2, 4, 0, -3, 20, 0, -4, 21, 0, -5, 22, 0, -6, 2, 0, -7, 8, 0, 6, 1, 0, 3, 1, 0, -9, 33, 0, -10, 16, 0, -11, 5, 0, -12, 34, 0, -13, 35, 0, 0, 2, 0, 0, 2, 0, -1, 23, 0, -2, 24, 0, -3, 3, 0, -4, 13, 0, -5, 14, 0, -6, 30, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 6, 0, -2, 9, 0, -3, 11, 0, -4, 12, 0, -5, 7, 0, 0, 4, 0, -2, 36, 0, 3, 4, 0, 3, 4, 0, 3, 4, 0, 0, 5, 0, 0, 5, 0, -1, 17, 0, -2, 18, 0, 0, 6, 0, 0, 6, 0, -3, 39, 0, -1, 25, 0, 0, 7, 0, 0, 7, 0, -3, 47, 0, -1, 28, 0, 0, 8, 0, 0, 8, 0, -1, 15, 0, 0, 9, 0, -2, 41, 0, -1, 10, 0, 0, 10, 0, -2, 40, 0, 0, 10, 0, 0, 11, 0, -2, 43, 0, -1, 26, 0, 0, 12, 0, -2, 45, 0, -1, 27, 0, 0, 13, 0, 0, 13, 0, -1, 29, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -1, 31, 0, -2, 32, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, -2, 37, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, -2, 38, 0, 0, 26, 0, -2, 42, 0, 0, 27, 0, -2, 44, 0, 0, 28, 0, -2, 46, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 8, 1, 127], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 40, 42, 44, 46], [1, 1, 1, -1, -2, 1, 1, 1, 1, -1, -2, -3, -4, -5, -6, 1, 7, 7, 1, 1, 1, 1, 1], [24, 76, 25, 26, 77, 25, 78, 79, 27, 27, 80, 81, 82, 83, 84, 85, 86, 87, 88, 26, 89, 90, 91]], [[[30, "SkillTips"], [67, "SkillTips", 33554432, [-11, -12, -13, -14, -15, -16], [[2, -2, [0, "a8O7BzqgBF3Y2L1gkp84h9"], [5, 640, 1280]], [55, 45, 640, 1280, -3, [0, "87HTVxanpDkq7LYiA/Z/E6"]], [136, -10, [0, "9c8B/z8ONGr5fTsS3IJv8Y"], -9, -8, -7, -6, -5, -4]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [82, "bg_skill_1", [-22, -23, -24, -25], [[2, -17, [0, "d482bn0W9MlolRyVw+LltL"], [5, 325, 117]], [3, -18, [0, "1bYC8+AqdF06z1j8lB0E9r"], 3], [10, -19, [0, "6cxhfmZGdMfJDoPXkwpmi+"], [4, 5]], [66, 0, "1", ["1", "2"], -21, [0, "f2GqcGPKNAJbjxUo/wRS5R"], [-20], [[14, "1", "57d2JSKzdHVb5ExJSfGvdA", 60, null, null, [1, 158, -12, 0], [3, 0, 0, 0, 1], [1, -0.5, 0.5, 1], [0, 0, 0.5], [0, 18.7533936851934, 84.12], [1, 255, 239, 0]], [14, "2", "57d2JSKzdHVb5ExJSfGvdA", 60, null, null, [1, 158, -12, 0], [3, 0, 0, 0, 1], [1, -0.5, 0.5, 1], [0, 0, 0.5], [0, 18.7533936851934, 84.12], [1, 249, 191, 249]]]]], [1, "88fgcCQ/RD3ZTDrVk9FJ7z", null, null, null, 1, 0], [1, -468.7540283203125, 0, 0], [1, -1, 1.0000000000000009, 1], [1, 0, 0, 1.252239032743832e-06]], [82, "bg_skill_1", [-31, -32, -33, -34], [[2, -26, [0, "acDiGBNxpJuZbSgy72JhVB"], [5, 325, 117]], [3, -27, [0, "a1kHELlGBHG7DiqiPO4crs"], 17], [10, -28, [0, "1982eirp1P8or9pUrpg5cT"], [18, 19]], [66, 0, "1", ["1", "2"], -30, [0, "99QWGV0bVFdqsUXzs3PdS6"], [-29], [[14, "1", "57d2JSKzdHVb5ExJSfGvdA", 60, null, null, [1, 158, -12, 0], [3, 0, 0, 0, 1], [1, 0.5, 0.5, 1], [0, 1, 0.5], [0, 18.7533936851934, 84.12], [1, 255, 239, 0]], [14, "2", "57d2JSKzdHVb5ExJSfGvdA", 60, null, null, [1, 158, -12, 0], [3, 0, 0, 0, 1], [1, 0.5, 0.5, 1], [0, 1, 0.5], [0, 18.7533936851934, 84.12], [1, 249, 191, 249]]]]], [1, "c9c2nQGxBKO4GM45Y2JtQh", null, null, null, 1, 0], [1, -181.62701416015625, 0, 0], [1, -1.5545766353607178, 1.554576635360719, 1], [1, 0, 0, 1.2522390671432434e-06]], [70, "bg_skill_1", [-40, -41, -42], [[2, -35, [0, "41NyMj42FNRbcPUf1ZZRaN"], [5, 224, 75]], [3, -36, [0, "a2AppwWeVNNZe21r+QIm4S"], 7], [10, -37, [0, "83m5y2gaJG34TwqmdlXt32"], [8, 9, 10, 11, 12, 13]], [66, 1, "绿", ["白", "绿", "蓝", "紫", "橙", "红"], -39, [0, "6ehDwgac9O/aLMjKdLJ7eF"], [-38], [[14, "白", "57d2JSKzdHVb5ExJSfGvdA", 40, null, null, [1, 38, 2, 0], [3, 0, 0, 0, 1], [1, -0.5, 0.5, 1], [0, 0, 0.5], [0, 6, 56.4], [1, 255, 85, 85]], [14, "绿", "57d2JSKzdHVb5ExJSfGvdA", 40, null, null, [1, 38, 2, 0], [3, 0, 0, 0, 1], [1, -0.5, 0.5, 1], [0, 0, 0.5], [0, 6, 56.4], [1, 126, 255, 217]], [14, "蓝", "57d2JSKzdHVb5ExJSfGvdA", 40, null, null, [1, 38, 2, 0], [3, 0, 0, 0, 1], [1, -0.5, 0.5, 1], [0, 0, 0.5], [0, 6, 56.4], [1, 120, 241, 255]], [14, "紫", "57d2JSKzdHVb5ExJSfGvdA", 40, null, null, [1, 38, 2, 0], [3, 0, 0, 0, 1], [1, -0.5, 0.5, 1], [0, 0, 0.5], [0, 6, 56.4], [1, 255, 120, 247]], [14, "橙", "57d2JSKzdHVb5ExJSfGvdA", 40, null, null, [1, 38, 2, 0], [3, 0, 0, 0, 1], [1, -0.5, 0.5, 1], [0, 0, 0.5], [0, 6, 56.4], [1, 255, 250, 121]], [14, "红", "57d2JSKzdHVb5ExJSfGvdA", 40, null, null, [1, 38, 2, 0], [3, 0, 0, 0, 1], [1, -0.5, 0.5, 1], [0, 0, 0.5], [0, 6, 56.4], [1, 255, 85, 85]]]]], [1, "e8HAT99L5CTJOy+arZk3iD", null, null, null, 1, 0], [1, 468.7540283203125, 0, 0], [1, 0, 0, 3.75671689467597e-06]], [70, "bg_skill_1", [-48, -49, -50], [[2, -43, [0, "e2yUXbliVDHbN9b3s9zu9q"], [5, 224, 75]], [3, -44, [0, "577ARclF1DNZx7MTrZ26FA"], 21], [10, -45, [0, "4e3uNhHbZHbJ3+zvmHHq3y"], [22, 23, 24, 25, 26, 27]], [66, 0, "白", ["白", "绿", "蓝", "紫", "橙", "红"], -47, [0, "34Diz6XetLNK1wwlnZOmVq"], [-46], [[14, "白", "57d2JSKzdHVb5ExJSfGvdA", 40, null, null, [1, 38, 2, 0], [3, 0, 0, 0, 1], [1, 0.5, 0.5, 1], [0, 1, 0.5], [0, 6, 56.4], [1, 255, 85, 85]], [14, "绿", "57d2JSKzdHVb5ExJSfGvdA", 40, null, null, [1, 38, 2, 0], [3, 0, 0, 0, 1], [1, 0.5, 0.5, 1], [0, 1, 0.5], [0, 6, 56.4], [1, 126, 255, 217]], [14, "蓝", "57d2JSKzdHVb5ExJSfGvdA", 40, null, null, [1, 38, 2, 0], [3, 0, 0, 0, 1], [1, 0.5, 0.5, 1], [0, 1, 0.5], [0, 6, 56.4], [1, 120, 241, 255]], [14, "紫", "57d2JSKzdHVb5ExJSfGvdA", 40, null, null, [1, 38, 2, 0], [3, 0, 0, 0, 1], [1, 0.5, 0.5, 1], [0, 1, 0.5], [0, 6, 56.4], [1, 255, 120, 247]], [14, "橙", "57d2JSKzdHVb5ExJSfGvdA", 40, null, null, [1, 38, 2, 0], [3, 0, 0, 0, 1], [1, 0.5, 0.5, 1], [0, 1, 0.5], [0, 6, 56.4], [1, 255, 250, 121]], [14, "红", "57d2JSKzdHVb5ExJSfGvdA", 40, null, null, [1, 38, 2, 0], [3, 0, 0, 0, 1], [1, 0.5, 0.5, 1], [0, 1, 0.5], [0, 6, 56.4], [1, 255, 85, 85]]]]], [1, "354YZjRtBFzI+OUGxjFgHu", null, null, null, 1, 0], [1, 468.7540283203125, 0, 0], [1, 0, 0, 3.75671689467597e-06]], [54, "mask", 2, [-56], [[2, -51, [0, "b4oYA6CxlMSYjukvOCuZ4p"], [5, 300, 200]], [28, -52, [0, "531D/yfVlPbK+V8U744IBu"]], [64, -53, [0, "75+50oPRZFDIZAXpJkaxOw"]], [65, -54, [0, "bf933PoahOA7md2jTqTF9V"], [4, 16777215]], [40, 32, -55, [0, "91HzKyoSZI1a87NXWmQWxk"]]], [1, "10HD9iHPZLpbVh6nOLdAUd", null, null, null, 1, 0], [1, 12.5, 64, 0], [1, -1, 1, 1]], [35, "mask", 4, [-62], [[2, -57, [0, "10aNYVwuJJeJB9LUGHzso7"], [5, 90, 90]], [28, -58, [0, "6aSceWFPBAhINdXYz7Kc/o"]], [64, -59, [0, "192j4gu9dGeKD60lw3+aUw"]], [65, -60, [0, "24Bfq7vHNAdKWLB91lJLh2"], [4, 16777215]], [40, 32, -61, [0, "95EI7NdZpNLZb93zj4MZ3I"]]], [1, "feZ1WgqLFNNb5BUNJT6R9/", null, null, null, 1, 0], [1, 67, 24, 0]], [35, "mask", 3, [-68], [[2, -63, [0, "81ZbfIPypDK4Pn6tDgnpda"], [5, 300, 200]], [28, -64, [0, "60cKe1QMlMTKnWpWW7Rirt"]], [64, -65, [0, "eetzmEnrdNDpEHYItExzM5"]], [65, -66, [0, "e7kjy8/0BDwb6TDaJfe1+6"], [4, 16777215]], [40, 32, -67, [0, "2ekk35XlVI3qMu+nnLdGS+"]]], [1, "20NagUeeJD9ZH9fUD0xnpI", null, null, null, 1, 0], [1, 12.5, 64, 0]], [35, "mask", 5, [-74], [[2, -69, [0, "1eR+oe7sdFh6EmvOBmN3sD"], [5, 90, 90]], [28, -70, [0, "5ak+VxI0xCyZlU/aQB8h6n"]], [64, -71, [0, "b99n0mvWJERJ41XQje0nUp"]], [65, -72, [0, "098phyDBhGD49leHemONSc"], [4, 16777215]], [40, 32, -73, [0, "a8s/fw1h9G/q+5s4u/D7fw"]]], [1, "1fwM4ECB1FqIkZr+/jrU7O", null, null, null, 1, 0], [1, 67, 24, 0]], [39, "Skill_Dz_L", false, 1, [-77], [[5, -75, [0, "4cWBqmwj5JKKAtcGMJWkxP"], [5, 325, 117], [0, 0, 0.5]], [28, -76, [0, "f27TyevvRKmY2REPaZvopm"]]], [1, "cfQ9fFPY5MG54EFEtUKwAo", null, null, null, 1, 0], [1, 0, 241.5, 0]], [39, "Skill_hz_L", false, 1, [-80], [[5, -78, [0, "91cza0RZxE8ZIsFZRERCZ0"], [5, 224, 75], [0, 0, 0.5]], [28, -79, [0, "cfW39CohRPkb/Z0hrnlfYS"]]], [1, "92NgmmjDlLM7XfN0yfb6me", null, null, null, 1, 0], [1, 0, 262.5, 0]], [39, "Skill_Dz_R", false, 1, [-83], [[5, -81, [0, "321YoDc65JL54b1xoWoiOM"], [5, 325, 117], [0, 1, 0.5]], [28, -82, [0, "bdGSP+WnxGhY5rxb0qaVrq"]]], [1, "98jIfDvzdGhZINJb9L15y4", null, null, null, 1, 0], [1, 0, 241.5, 0]], [39, "Skill_hz_R", false, 1, [-86], [[5, -84, [0, "f7ujiXGRZPvpRsFH/4jMyy"], [5, 224, 75], [0, 1, 0.5]], [28, -85, [0, "0fLDDYNBZIK5Zecg6/VGYl"]]], [1, "c4kQ+pkVFH9L2jODyhguss", null, null, null, 1, 0], [1, 0, 145.5, 0]], [9, "leftside", 33554432, 1, [[5, -87, [0, "52Za4IHdhCr4p/YnGKi7ux"], [5, 320, -5], [0, 0, 1]], [130, 1, 2, 5, true, -88, [0, "21F9asah5LUrBwffcHOJxP"]], [104, 9, 290, 675, 100, -89, [0, "b27iy+yc9PDpZXt33UvpSV"]]], [1, "3fPGh1mxxI15VNKweedlB8", null, null, null, 1, 0], [1, -320, 350, 0]], [9, "rightside", 33554432, 1, [[5, -90, [0, "90325zux9HjawC69u84qVL"], [5, 320, 0], [0, 1, 1]], [131, 1, 2, true, -91, [0, "2eyHR9uvVOyY3tdgj44sjr"]], [105, 33, 290, 775, -92, [0, "cbwhYzA7hOyom5+g/804sr"]]], [1, "6auYQxkTNJwr3u9aFs+XwH", null, null, null, 1, 0], [1, 320, 350, 0]], [54, "spine_dz", 10, [2], [[7, -93, [0, "23C7XKotNGEL0KbazpDvlM"]], [50, false, 0, -94, [0, "6d105G9CxP16nco8bQkGzG"], [[29, "root/all/youchu", 2]]]], [1, "2cjov36tZK3Y6cP0K09MCe", null, null, null, 1, 0], [1, 280, 0, 0], [1, 0.65, 0.65, 1]], [34, "bg_skill_dz1_1", 2, [[2, -95, [0, "775pT20A1IU5vplmeRA5Wk"], [5, 325, 117]], [59, 2, false, -96, [0, "e4ISyiLClA1bFM3TipDu41"], 0], [10, -97, [0, "61TAXFDZtCA6iPCtTXfQUR"], [1, 2]]], [1, "eaKgs88/tIAq7CrbILBbEM", null, null, null, 1, 0]], [8, "txt_skill_name1", 2, [[5, -98, [0, "76fxcgKnlBPKLnh4YtWtAx"], [5, 18.7533936851934, 84.12], [0, 0, 0.5]], [90, "", 60, 60, 62, true, true, true, 3, -99, [0, "afgHJI3FJPO5J/YXajDc12"], [4, 4278251519]]], [1, "fa6fzXgGtIJrsTNPeKnzCe", null, null, null, 1, 0], [1, 158, -12, 0], [1, -0.5, 0.5, 1]], [54, "spine_hz", 11, [4], [[7, -100, [0, "baRVUSHhxG3ahlxOrYc9QO"]], [50, false, 0, -101, [0, "3c8NNj+bxMn4ediZQUO0fK"], [[29, "root/all/youchu", 4]]]], [1, "c0LTGnEJFObYVMloTr0jRY", null, null, null, 1, 0], [1, 293, 0, 0], [1, -1, 1, 1]], [8, "txt_skill_name1", 4, [[5, -102, [0, "89//R6mxlI3ZRmxqLpxNen"], [5, 14.502262456795602, 56.4], [0, 0, 0.5]], [91, "", 40, true, true, true, 3, -103, [0, "46bWJnEPZExoyQuOcORjmc"], [4, 4292476798]]], [1, "62ypPLAq5Ge6PoflHJAQy8", null, null, null, 1, 0], [1, 38, 2, 0], [1, -0.5, 0.5, 1]], [54, "spine_dz", 12, [3], [[7, -104, [0, "a9EjMqVlpPBK7ZAKJSYMhB"]], [50, false, 0, -105, [0, "67t82zFilEaoO9JvUh9DGn"], [[29, "root/all/youchu", 3]]]], [1, "c7EyTdAfFDTYQuCXknl7N5", null, null, null, 1, 0], [1, -280, 0, 0], [1, -0.65, 0.65, 1]], [34, "bg_skill_dz1_1", 3, [[2, -106, [0, "f3t7CVFrxJ+Jo7y3piZIhm"], [5, 325, 117]], [59, 2, false, -107, [0, "31Zhx1NmhG/KkJutmHk9WP"], 14], [10, -108, [0, "02/R0eJ+1EAJBZcWScv5ls"], [15, 16]]], [1, "b3T9qGsRhLbaRDYUygFY/O", null, null, null, 1, 0]], [8, "txt_skill_name1", 3, [[5, -109, [0, "bawCrEVkdJFIlQZznXqswr"], [5, 18.7533936851934, 84.12], [0, 1, 0.5]], [90, "", 60, 60, 62, true, true, true, 3, -110, [0, "2fQaR5SOhN24uCvrh5T5Y8"], [4, 4278251519]]], [1, "57Cg48GyNMa64WVBGqBxxB", null, null, null, 1, 0], [1, 158, -12, 0], [1, 0.5, 0.5, 1]], [35, "spine_hz", 13, [5], [[7, -111, [0, "46qHF6tNtPApuG1NLa6eib"]], [50, false, 0, -112, [0, "bbt2m04PpCz5dkL8ld7I0N"], [[29, "root/all/youchu", 5]]]], [1, "30s/UFaWRHl6TsQAjbqW0E", null, null, null, 1, 0], [1, -293, 0, 0]], [8, "txt_skill_name1", 5, [[5, -113, [0, "b3HXdP9fBGDb1TpJXnggpR"], [5, 14.502262456795602, 56.4], [0, 1, 0.5]], [91, "", 40, true, true, true, 3, -114, [0, "26LGtEWGlMSLlHSCxs9lV1"], [4, 4283782655]]], [1, "ceCqc2Z7dC0IDi3GSZAEMD", null, null, null, 1, 0], [1, 38, 2, 0], [1, 0.5, 0.5, 1]], [8, "lihui1", 6, [[18, -115, [0, "c0EYgd/EhJO61x0/0VNdJj"], [0, 0.5, 0.005657163343741572]], [24, false, 0, -116, [0, "bd4HdD9P9DyIn04Y0Ojkrp"]]], [1, "92fLTibjBFJ5iq7+OEfp+P", null, null, null, 1, 0], [1, 30, -200, 0], [1, 0.4, 0.4, 1]], [8, "txt_1", 2, [[5, -117, [0, "9aH+XUv3NLMLfnuyw5gr7b"], [5, 121.65203621111604, 56.4], [0, 0, 0.5]], [92, "必殺技", 36, 36, true, true, true, 3, -118, [0, "85YhD6+hZCuZu94yqNIq+w"]]], [1, "4dQmxvJE1F/7Jf8g8DiyaB", null, null, null, 1, 0], [1, 158, 22, 0], [1, -0.5, 0.5, 1]], [8, "SD_lihui1", 7, [[18, -119, [0, "849piVN19JOLpLb0tMinpF"], [0, 0.5, 0.29443501527525406]], [24, false, 0, -120, [0, "66cPd7Z0pOpJsuqMlWJEHB"]]], [1, "34ll/K1qdIlI6O8/EC3fBl", null, null, null, 1, 0], [1, 10.5, -106, 0], [1, -1.2, 1.2, 1]], [6, "bg_skill_di1", 4, [[2, -121, [0, "bec6QbXxBHcYKm6WHWEY/g"], [5, 242, 45]], [3, -122, [0, "57VDFYb6JGJZK2LvhD+wTV"], 6]], [1, "43uE2V1E1EPocv0/PygUBO", null, null, null, 1, 0], [1, -9, 0, 0]], [8, "lihui1", 8, [[18, -123, [0, "42eoLBA3xHxKsl1chPmXfe"], [0, 0.5, 0.005657163343741572]], [24, false, 0, -124, [0, "12jB+iwexEqYbI9lZZbOW2"]]], [1, "faitap6w1B7rk6uzKKeHjY", null, null, null, 1, 0], [1, -50, -200, 0], [1, 0.4, 0.4, 1]], [8, "txt_1", 3, [[5, -125, [0, "6dbzGMdoJKKKhEXfJgz3e1"], [5, 121.65203621111604, 56.4], [0, 1, 0.5]], [92, "必殺技", 36, 36, true, true, true, 3, -126, [0, "3c0D8aOXBGBrcKn+BNqPT7"]]], [1, "c5XBMJk+tC55r/H5NfeeZK", null, null, null, 1, 0], [1, 158, 22, 0], [1, 0.5, 0.5, 1]], [8, "SD_lihui1", 9, [[18, -127, [0, "0dxtpkb0RBpr0ZdgFJVZdC"], [0, 0.5, 0.29443501527525406]], [24, false, 0, -128, [0, "73URZinA5Cjan4aDnP0p68"]]], [1, "68aIpLPcVLXKQDszwCVwYc", null, null, null, 1, 0], [1, 10.5, -106, 0], [1, 1.2, 1.2, 1]], [6, "bg_skill_di1", 5, [[2, -129, [0, "0fvr8bQf5CRYAOW8tesn/3"], [5, 242, 45]], [3, -130, [0, "8dWBePojRKEpDqZ15NsHXp"], 20]], [1, "d2sBVjOutOyJ06t5YRjSf2", null, null, null, 1, 0], [1, -9, 0, 0]]], 0, [0, 6, 1, 0, 0, 1, 0, 0, 1, 0, 49, 15, 0, 50, 14, 0, 51, 13, 0, 52, 12, 0, 53, 11, 0, 54, 10, 0, 0, 1, 0, -1, 10, 0, -2, 11, 0, -3, 12, 0, -4, 13, 0, -5, 14, 0, -6, 15, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 18, 0, 0, 2, 0, -1, 6, 0, -2, 17, 0, -3, 18, 0, -4, 27, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 23, 0, 0, 3, 0, -1, 8, 0, -2, 22, 0, -3, 23, 0, -4, 31, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 20, 0, 0, 4, 0, -1, 7, 0, -2, 29, 0, -3, 20, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 25, 0, 0, 5, 0, -1, 9, 0, -2, 33, 0, -3, 25, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 26, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 28, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 30, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 32, 0, 0, 10, 0, 0, 10, 0, -1, 16, 0, 0, 11, 0, 0, 11, 0, -1, 19, 0, 0, 12, 0, 0, 12, 0, -1, 21, 0, 0, 13, 0, 0, 13, 0, -1, 24, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 8, 1, 2, 3, 16, 3, 3, 21, 4, 3, 19, 5, 3, 24, 130], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, -1, -2, 1, -1, -2, 1, 1, -1, -2, -3, -4, -5, -6, 1, -1, -2, 1, -1, -2, 1, 1, -1, -2, -3, -4, -5, -6], [9, 9, 28, 10, 10, 29, 30, 11, 11, 31, 32, 33, 34, 35, 9, 9, 28, 10, 10, 29, 30, 11, 11, 31, 32, 33, 34, 35]], [[[30, "BuffPZ"], [38, "BuffPZ", [-3, -4, -5], [[2, -2, [0, "bcTz/XC9JFtaWb7JxyfPTy"], [5, 200, 20]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [19, "state1", false, 1, [-10, -11], [[2, -6, [0, "7aJTZrZkdAKILyDa1YzRna"], [5, 53.852036211116044, 36]], [20, 1, 1, -3.8, true, -7, [0, "cbJJTPrtRA/pmuy2Ok255N"]], [12, -8, [0, "56P5npJlRPB4S1f1mtBbOh"]], [16, -9, [0, "77eNU0LWROzKVw9qQza9fd"], [4], 5]], [1, "46jm0X259Bpr48OqN7PIcj", null, null, null, 1, 0]], [19, "state2", false, 1, [-15, -16], [[2, -12, [0, "21+O6KT8FDtaXRUFW3vi4m"], [5, 173.7, 36]], [132, false, 1, 1, -3.8, true, -13, [0, "2b5vjdr59OO6AWWZk3LJRt"]], [12, -14, [0, "96J2h458NNb65g6ESFnB2F"]]], [1, "76NjRBBOdLU7JePPiDomVG", null, null, null, 1, 0]], [34, "Label", 1, [[2, -17, [0, "7aql9yeixOl7ufbk23MXyt"], [5, 119.6796875, 130]], [120, "12312314", 26, 26, 100, false, true, true, -18, [0, "20TEwKN1VN6Ic6/AoT7Cg2"]], [12, -19, [0, "cdIpgN8pZGeJs0NUvHagrU"]], [16, -20, [0, "22SuDN8j5CQIztgTBVnEa9"], [0], 1]], [1, "2adugfdyJK/YtC1064xv1L", null, null, null, 1, 0]], [97, "ico_zd_mfz", 3, [[2, -21, [0, "d0v4x65+BC7rJzlto6HCqB"], [5, 99, 100]], [36, -22, [0, "b7plrFSL1L2prBCdgPBYPC"]], [12, -23, [0, "fap+0TE7dGEJrGNfOykKXv"]], [16, -24, [0, "8dDoIibldBCbbPRoGTGDCI"], [6], 7]], [1, "7bmcekWxxHMbSEQjFCNRpb", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [8, "txt_state1", 2, [[2, -25, [0, "85c3U0c6hNNqXfn0JxC7S8"], [5, 25.304072422232082, 110.8]], [22, "", 72, 72, 80, true, true, true, 5, -26, [0, "ddPwBaBORP3JogNym2Cw7L"], [4, 4278196809]], [13, 0.36, 0.76, 1, 1, -27, [0, "bcWVO/P1NOWawZ3ProoPzY"], [[[4, 4286574335], [4, 4280390911]], 8, 8], 3]], [1, "58yQkBTy1NZIJdTzFZiv1J", null, null, null, 1, 0], [1, 20.599999999999998, 0, 0], [1, 0.5, 0.5, 1]], [6, "ico_zd_zt1", 2, [[2, -28, [0, "dblbX7jsJK1IGjDyAMLA9I"], [5, 45, 55]], [3, -29, [0, "faDLqQxgNGb6FBJV59qPfX"], 2]], [1, "e5Sik55UlJNYzXeEkdGol0", null, null, null, 1, 0], [1, -4.426018105558022, 0, 0]], [34, "spine_mfz", 3, [[2, -30, [0, "68OJQgkkJO66Zm7mML509H"], [5, 128, 128]], [42, "default", false, 0, -31, [0, "79sLgihoFJPJ7NygkseEai"], 8]], [1, "eb8xRnjIFB1aJqb09OPFcj", null, null, null, 1, 0]]], 0, [0, 6, 1, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, -3, 3, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, -2, 6, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 5, 0, -2, 8, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 8, 1, 31], [0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 5, 1, 4, -1, 5, -1, 5, 7], [7, 7, 23, 2, 1, 1, 36, 36, 92]], [[[63, "35601_buff", ".bin", "\n35601_buff.png\nsize: 458,458\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nCW\n  rotate: true\n  xy: 357, 338\n  size: 118, 99\n  orig: 118, 99\n  offset: 0, 0\n  index: -1\nCW_m\n  rotate: true\n  xy: 240, 213\n  size: 118, 113\n  orig: 218, 199\n  offset: 50, 43\n  index: -1\nCuo\n  rotate: false\n  xy: 234, 12\n  size: 77, 77\n  orig: 99, 79\n  offset: 10, 1\n  index: -1\nDui\n  rotate: false\n  xy: 313, 14\n  size: 75, 75\n  orig: 99, 79\n  offset: 11, 2\n  index: -1\nHL\n  rotate: false\n  xy: 121, 198\n  size: 117, 124\n  orig: 117, 124\n  offset: 0, 0\n  index: -1\nHL_m\n  rotate: false\n  xy: 2, 176\n  size: 117, 138\n  orig: 217, 224\n  offset: 50, 43\n  index: -1\nLu\n  rotate: false\n  xy: 2, 47\n  size: 108, 127\n  orig: 128, 128\n  offset: 9, 1\n  index: -1\nLu_m\n  rotate: false\n  xy: 2, 316\n  size: 108, 140\n  orig: 228, 228\n  offset: 59, 44\n  index: -1\nQP\n  rotate: true\n  xy: 357, 225\n  size: 111, 87\n  orig: 172, 87\n  offset: 0, 0\n  index: -1\nQP_1\n  rotate: true\n  xy: 355, 112\n  size: 111, 87\n  orig: 172, 87\n  offset: 0, 0\n  index: -1\nSS\n  rotate: true\n  xy: 240, 91\n  size: 120, 110\n  orig: 120, 110\n  offset: 0, 0\n  index: -1\nSS_m\n  rotate: false\n  xy: 112, 50\n  size: 120, 124\n  orig: 220, 210\n  offset: 50, 43\n  index: -1\nZMN\n  rotate: true\n  xy: 237, 333\n  size: 123, 118\n  orig: 123, 118\n  offset: 0, 0\n  index: -1\nZMN_m\n  rotate: false\n  xy: 112, 324\n  size: 123, 132\n  orig: 223, 218\n  offset: 50, 43\n  index: -1\n", ["35601_buff.png"], [0]], -1], 0, 0, [0], [-1], [93]], [[[63, "ani_ss_vs", ".bin", "\nani_ss_vs.png\nsize: 309,309\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\njjdz_dian\n  rotate: false\n  xy: 2, 146\n  size: 72, 161\n  orig: 72, 161\n  offset: 0, 0\n  index: -1\ntx/1/d1_03\n  rotate: false\n  xy: 255, 4\n  size: 42, 27\n  orig: 75, 75\n  offset: 1, 24\n  index: -1\ntx/1/d1_04\n  rotate: false\n  xy: 76, 182\n  size: 67, 28\n  orig: 75, 75\n  offset: 3, 22\n  index: -1\ntx/1/d1_05\n  rotate: false\n  xy: 152, 30\n  size: 68, 38\n  orig: 75, 75\n  offset: 1, 18\n  index: -1\ntx/1/d1_06\n  rotate: false\n  xy: 222, 33\n  size: 67, 39\n  orig: 75, 75\n  offset: 2, 15\n  index: -1\ntx/1/d1_08\n  rotate: false\n  xy: 79, 30\n  size: 71, 38\n  orig: 75, 75\n  offset: 4, 16\n  index: -1\ntx/1/d1_09\n  rotate: true\n  xy: 272, 97\n  size: 67, 35\n  orig: 75, 75\n  offset: 6, 18\n  index: -1\ntx/1/d1_11\n  rotate: false\n  xy: 79, 4\n  size: 41, 24\n  orig: 75, 75\n  offset: 12, 25\n  index: -1\ntx/1/d1_13\n  rotate: true\n  xy: 291, 49\n  size: 30, 16\n  orig: 75, 75\n  offset: 15, 27\n  index: -1\ntx/10/shanguang_00\n  rotate: false\n  xy: 181, 74\n  size: 89, 90\n  orig: 150, 150\n  offset: 31, 30\n  index: -1\ntx/10/shanguang_01\n  rotate: false\n  xy: 2, 70\n  size: 150, 74\n  orig: 150, 150\n  offset: 0, 38\n  index: -1\ntx/10/shanguang_02\n  rotate: true\n  xy: 222, 166\n  size: 141, 76\n  orig: 150, 150\n  offset: 7, 37\n  index: -1\ntx/10/shanguang_03\n  rotate: true\n  xy: 76, 212\n  size: 95, 75\n  orig: 150, 150\n  offset: 36, 38\n  index: -1\ntx/10/shanguang_04\n  rotate: false\n  xy: 2, 3\n  size: 75, 65\n  orig: 150, 150\n  offset: 37, 47\n  index: -1\ntx/10/shanguang_05\n  rotate: false\n  xy: 153, 257\n  size: 50, 50\n  orig: 150, 150\n  offset: 39, 61\n  index: -1\ntx/glow\n  rotate: false\n  xy: 145, 176\n  size: 34, 34\n  orig: 38, 38\n  offset: 2, 2\n  index: -1\ntx/xd/xd_00\n  rotate: false\n  xy: 272, 81\n  size: 35, 14\n  orig: 75, 75\n  offset: 34, 35\n  index: -1\ntx/xd/xd_02\n  rotate: false\n  xy: 129, 151\n  size: 50, 13\n  orig: 75, 75\n  offset: 19, 35\n  index: -1\ntx/xd/xd_04\n  rotate: false\n  xy: 76, 151\n  size: 51, 13\n  orig: 75, 75\n  offset: 18, 35\n  index: -1\ntx/xd/xd_06\n  rotate: false\n  xy: 176, 15\n  size: 56, 13\n  orig: 75, 75\n  offset: 11, 35\n  index: -1\ntx/xd/xd_07\n  rotate: false\n  xy: 122, 13\n  size: 52, 15\n  orig: 75, 75\n  offset: 11, 35\n  index: -1\ntx/xd/xd_08\n  rotate: true\n  xy: 154, 118\n  size: 31, 15\n  orig: 75, 75\n  offset: 33, 35\n  index: -1\ntx/xd/xd_10\n  rotate: false\n  xy: 176, 3\n  size: 40, 10\n  orig: 75, 75\n  offset: 14, 35\n  index: -1\ntx/xd/xd_12\n  rotate: false\n  xy: 218, 3\n  size: 35, 10\n  orig: 75, 75\n  offset: 15, 35\n  index: -1\n", ["ani_ss_vs.png"], [0]], -1], 0, 0, [0], [-1], [94]], [[[30, "<PERSON><PERSON><PERSON>"], [83, "<PERSON><PERSON><PERSON>", [[-5, -6, -7, -8, [98, "drop_hp", -10, [1, "78XgPxHltKKJ76/qc1NH6X", null, null, null, -9, 0], [1, 0, -29.029, 0]], -11], 1, 1, 1, 1, 4, 1], [[137, -3, [0, "8dmnX83FtPAbu/UuZqM7ci"], -2], [2, -4, [0, "ef5vKGTO9MpravRHyihRaW"], [5, 150, 200]]], [1, "4dntOGz/RLQbYHCrgZgTWH", null, null, null, -1, 0]], [102, "spine", 1, [[45, "bd_head", -14, [1, "b06qQEVLhAOb3rZKbVXMMb", null, null, null, 1, 0], [1, -1.1674081179080531e-05, 205.53591918945312, 0], [1, 0, 0, 2.5044780654876575e-06]], [45, "bd_body", -15, [1, "c88rOQWupBmLw5RAMGk4cF", null, null, null, 1, 0], [1, 5.135982036590576, 117.58196258544922, 0], [1, 0, 0, 2.5044780654876575e-06]], [45, "bd_foot", -16, [1, "76qRfx3BJPXZmpiTiw1spF", null, null, null, 1, 0], [1, 0, 72, 0], [1, 0, 0, 2.5044780654876575e-06]]], [[[18, -12, [0, "69IFaIFh1AKYXjdnB4X4yN"], [0, 0.5, 0.5926232300756874]], -13], 4, 1], [1, "38/pon2XlJkJcTKs7lEk44", null, null, null, 1, 0], [1, 0, -72, 0]], [39, "bg_hp_jdt2", false, 1, [-19, -20, -21], [[2, -17, [0, "daVzcyXv5F8LrAgVtMV0CO"], [5, 91, 15]], [3, -18, [0, "22AVN5DWVHH5iTcdW4HJmR"], 3]], [1, "a9eyvDnepJNpZvXTEjvQgV", null, null, null, 1, 0], [1, 14, 72.971, 0]], [39, "bg_time1", false, 1, [-25], [[2, -22, [0, "6bwD0rbP1GwaeCIttZUlEg"], [5, 135.5, 38]], [37, 1, 0, -23, [0, "ccIPWtLy5CTaTcCyG5TMGB"], 4], [60, 3, 0.9, -24, [0, "2ayGiZMT5K4Lq0SbwB2Nqy"]]], [1, "5d3E2QAuFMJ5G7WkpjyhN0", null, null, null, 1, 0], [1, 0, 103.554, 0]], [81, "img_dz_di", false, 33554432, 1, [-29], [[5, -26, [0, "eawhJyKY9P+aLp9tf9whLS"], [5, 36, 36], [0, 0, 0.5]], [36, -27, [0, "aaGuWZAzRGC5s3cIK9PXBv"]], [20, 1, 1, 3, true, -28, [0, "85BMUfLXxICqC7KsiiQy+k"]]], [1, "b08q/cP8RPt7a8XNGAJBRe", null, null, null, 1, 0]], [74, "item_dz1", 33554432, 5, [-32, -33], [[5, -30, [0, "08S+yfYrVH3YZgic174XAW"], [5, 36, 36], [0, 0, 0.5]], [3, -31, [0, "d9dQrfyz1Ja5Ro6WZv26oy"], 6]], [1, "dewcd81QRKGqXK9v34iB3i", null, null, null, 1, 0]], [6, "img_hp_jdt5", 3, [[2, -34, [0, "6daXrltRRAW6GI0Jc9Zmrv"], [5, 86, 11]], [109, 3, 1, -35, [0, "19x/WSTg5H3aR5jLMNhRy8"], 0]], [1, "baS3AvqjpMHJ6+bHm77liJ", null, null, null, 1, 0], [1, 0.5, 0, 0]], [6, "ani_glow", 3, [[5, -36, [0, "8a7YUKTK5BibHxQLfKtb+h"], [5, 91, 18], [0, 0.5054945054945055, 0.5]], [42, "default", false, 0, -37, [0, "64VjoDt2FAw7EaL2JL/GWL"], 1]], [1, "acxo62399PYYA6eLZI+F08", null, null, null, 1, 0], [1, 1, 0, 0]], [6, "bg_hp_jdt3", 3, [[2, -38, [0, "d0SaDopxpE0YYSWFd5ACsm"], [5, 29.000001907348633, 30]], [125, "default", "wait", false, 0, -39, [0, "8egKrkLChGiIim2Rljaui/"], 2]], [1, "a4pYbQP1VFHbodQp1uAH4j", null, null, null, 1, 0], [1, -56, 0, 0]], [8, "txt_djs1", 4, [[2, -40, [0, "b43HE8u6RIjpsgaOP20WX0"], [5, 164.952344, 58.92]], [121, "", 41, 42, 2, true, true, 3, -41, [0, "edY2xXR29MBL6IS/XEsLyi"], [4, 4280821546]]], [1, "dclTsU4g9EgLCcPY7lv3Hq", null, null, null, 1, 0], [1, -10.293000000000006, 0.8949999999999818, 0], [1, 0.5, 0.5, 1]], [15, "ani_shifa", false, 1, [[18, -42, [0, "f3OdjzGcRP77Y8wYh3JWug"], [0, 0.5, 0.7207488299531981]], [24, false, 0, -43, [0, "8fYsvs9hZF2plRTorwUkAm"]]], [1, "14NGGS65xP67iFcboPamcV", null, null, null, 1, 0], [1, 14, 72.971, 0]], [21, "ico_dj", 33554432, 6, [[2, -44, [0, "29AT4i7vRCCr2m26nr5Jdg"], [5, 110, 110]], [36, -45, [0, "66nLvWBuZNqZT6Wp7bjwFX"]]], [1, "1ewK0CHylL+rkGRebskw9b", null, null, null, 1, 0], [1, 18, 0, 0], [1, 0.3, 0.3, 1]], [99, "img_cd_di", 33554432, 6, [[2, -46, [0, "b7aHzwwkZCk4Uom9U6A0rd"], [5, 36, 36]], [110, 3, 2, 1, -47, [0, "26Pz7slZxNxbFO90+jkrWk"], [4, 3372220415], [0, 0.5, 0.5], 5]], [1, "91HzLkBrJLDrYIui+XRZQp", null, null, null, 1, 0], [1, 18, 0, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [24, false, 0, 2, [0, "eciHur46dG+5OFV0duAoYK"]]], 0, [0, 6, 1, 0, 13, 14, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 11, 0, 6, 1, 0, 3, 1, 0, -6, 5, 0, 0, 2, 0, -2, 14, 0, 3, 2, 0, 3, 2, 0, 3, 2, 0, 0, 3, 0, 0, 3, 0, -1, 7, 0, -2, 8, 0, -3, 9, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 10, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, 0, 6, 0, -1, 12, 0, -2, 13, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 8, 1, 47], [0, 0, 0, 0, 0, 0, 0], [1, 7, 7, 1, 1, 1, 1], [95, 96, 97, 24, 98, 99, 100]]]]