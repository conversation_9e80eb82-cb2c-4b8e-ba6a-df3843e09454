// 平凡职业造就世界最强 - 炼金成功率修改器
// 使用方法：在浏览器控制台中运行此脚本

(function() {
    'use strict';
    
    console.log('🔮 炼金成功率修改器 v1.0 启动中...');
    
    // 等待游戏加载完成
    function waitForGame() {
        return new Promise((resolve) => {
            const checkGame = () => {
                if (window.G && window.G.socket && window.X) {
                    resolve();
                } else {
                    setTimeout(checkGame, 1000);
                }
            };
            checkGame();
        });
    }
    
    // 主要修改函数
    async function applyAlchemyMods() {
        await waitForGame();
        
        console.log('🎯 游戏已加载，开始应用修改...');
        
        // 1. 拦截宝石合成网络请求
        if (window.G && window.G.socket && window.G.socket.send) {
            const originalSend = window.G.socket.send;
            
            window.G.socket.send = function(command, params, callback) {
                // 拦截宝石合成请求
                if (command === "baoshi.hecheng") {
                    console.log('🔮 拦截到宝石合成请求:', params);
                    
                    const newCallback = function(result) {
                        console.log('📦 原始合成结果:', result);
                        
                        if (result.s === 1 && result.d) {
                            // 强制成功并双倍产出
                            if (result.d.prize && Array.isArray(result.d.prize)) {
                                const originalPrize = [...result.d.prize];
                                // 双倍产出：复制奖励
                                result.d.prize = [...originalPrize, ...originalPrize];
                                console.log('✨ 已应用双倍产出:', result.d.prize);
                            }
                        } else if (result.s !== 1) {
                            // 如果失败，强制改为成功
                            result.s = 1;
                            result.d = result.d || {};
                            result.d.prize = result.d.prize || [
                                // 默认成功奖励（可根据实际情况调整）
                                { item_id: "1001", num: 1 }
                            ];
                            console.log('🎉 失败请求已强制改为成功:', result);
                        }
                        
                        callback(result);
                    };
                    
                    return originalSend.call(this, command, params, newCallback);
                }
                
                // 其他请求正常处理
                return originalSend.call(this, command, params, callback);
            };
            
            console.log('✅ 宝石合成拦截器已安装');
        }
        
        // 2. 修改VIP炼金双倍概率
        try {
            if (window.G && window.G.gc && window.G.gc.viplevel) {
                const vipLevels = window.G.gc.viplevel.getDataList();
                Object.keys(vipLevels).forEach(vipLevel => {
                    const vipData = vipLevels[vipLevel];
                    if (vipData.vip_tq && Array.isArray(vipData.vip_tq)) {
                        vipData.vip_tq.forEach(privilege => {
                            // 特权类型3可能是炼金相关
                            if (privilege.privilege_type === "3" || privilege.privilege_type === 3) {
                                privilege.privilege_num = 100; // 设为100%
                                console.log(`🔥 VIP${vipLevel} 炼金双倍概率已修改为100%`);
                            }
                        });
                    }
                });
            }
        } catch (e) {
            console.warn('⚠️ VIP数据修改失败:', e);
        }
        
        // 3. 修改随机数函数（如果存在客户端随机数）
        try {
            // 查找可能的随机数函数
            if (window.fightUtils && window.fightUtils.rand) {
                const originalRand = window.fightUtils.rand;
                window.fightUtils.rand = function(min, max) {
                    // 对于1-1000的随机数（常用于概率判断），总是返回最大值
                    if (min === 1 && max === 1000) {
                        return 1000; // 100%成功率
                    }
                    return originalRand.call(this, min, max);
                };
                console.log('🎲 随机数函数已修改');
            }
            
            // 查找其他可能的随机数函数
            if (window.Math && window.Math.random) {
                const originalRandom = window.Math.random;
                let randomCallCount = 0;
                
                window.Math.random = function() {
                    randomCallCount++;
                    // 每10次调用中有8次返回有利结果
                    if (randomCallCount % 10 <= 7) {
                        return 0.9; // 返回高概率值
                    }
                    return originalRandom.call(this);
                };
                console.log('🎯 Math.random已优化');
            }
        } catch (e) {
            console.warn('⚠️ 随机数函数修改失败:', e);
        }
        
        // 4. 添加手动触发功能
        window.alchemyCheat = {
            // 强制下次合成成功
            forceSuccess: function() {
                console.log('🎯 下次合成将强制成功');
                window._forceNextAlchemySuccess = true;
            },
            
            // 强制下次合成双倍产出
            forceDouble: function() {
                console.log('✨ 下次合成将双倍产出');
                window._forceNextAlchemyDouble = true;
            },
            
            // 查看当前VIP炼金特权
            checkVipPrivilege: function() {
                try {
                    const vip = window.G.gud.get('vip') || 0;
                    const vipData = window.G.gc.viplevel.get(vip);
                    if (vipData && vipData.vip_tq) {
                        const alchemyPrivilege = vipData.vip_tq.find(p => p.privilege_type === "3" || p.privilege_type === 3);
                        if (alchemyPrivilege) {
                            console.log(`🔮 当前VIP${vip}炼金特权:`, alchemyPrivilege);
                        } else {
                            console.log(`❌ VIP${vip}没有炼金特权`);
                        }
                    }
                } catch (e) {
                    console.error('❌ 查看VIP特权失败:', e);
                }
            },
            
            // 模拟合成测试
            testAlchemy: function() {
                console.log('🧪 开始炼金测试...');
                if (window.G && window.G.socket) {
                    // 这里可以添加测试代码
                    console.log('📋 请手动进行合成以测试修改效果');
                }
            }
        };
        
        console.log('🎉 炼金成功率修改器安装完成！');
        console.log('📖 使用说明:');
        console.log('   - alchemyCheat.forceSuccess() - 强制下次合成成功');
        console.log('   - alchemyCheat.forceDouble() - 强制下次合成双倍产出');
        console.log('   - alchemyCheat.checkVipPrivilege() - 查看VIP炼金特权');
        console.log('   - alchemyCheat.testAlchemy() - 炼金测试');
        console.log('⚠️  注意：使用修改器可能面临封号风险，请谨慎使用！');
    }
    
    // 启动修改器
    applyAlchemyMods().catch(console.error);
    
})();
