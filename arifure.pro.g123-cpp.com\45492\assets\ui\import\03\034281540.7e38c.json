[1, ["10I8iCpWVA062UoMj6NsQ4", "28bfwm51dOHoLVvMxcWZRh@f9941", "71258ra4RHm6xvUj8s7rsU@f9941", "53kpP3zLVCbK8i2lPaQtHk@f9941", "91r+DCnc1D7L49HhrhpSJh@f9941", "09Yng9V0ZOC6g+bFLu3bRL", "9a7ojTp0dCYo0YJERsgFoY@f9941", "095hSqoQBBZZ3Wb8cNV8ds@f9941", "30aCdoZbFMfKaov0sSqPb2@f9941", "d8ET5+mNpPcJTPfRfl+SfA@f9941", "e4J7S2ksFCN73VIByY576d@f9941", "aad+cCVBNN5avEaDfwTpHP@f9941", "a1OOrIHTVDHLJPkNNA9mZI@f9941", "d2WYCMqcpFhJYWcbKC7xGF@f9941", "fc8/rYR3RBJLUZhgioY7zQ@f9941", "de7gyzWr5PB76UTMXb0u+5@f9941", "fcfATM00NP6aMUfMeuFPxG", "30f6AnHTZKLJQAtgL/akE0@f9941", "22GZ9DJdFEjZ2Dt0DYmu/G@f9941", "095hSqoQBBZZ3Wb8cNV8ds@6c48a", "d2WYCMqcpFhJYWcbKC7xGF@6c48a", "de7gyzWr5PB76UTMXb0u+5@6c48a", "6bkRuePQJBe5K6Oo/UJdOG@f9941", "fc8/rYR3RBJLUZhgioY7zQ@6c48a"], ["node", "targetInfo", "_spriteFrame", "_parent", "target", "source", "root", "_textureSource", "data", "asset", "_content", "_defaultClip", "value"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_lpos", "_children", "_lscale"], -2, 4, 9, 1, 5, 2, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_affectedByScale", "_spacingX", "_spacingY", "_paddingLeft", "_paddingRight", "_enabled", "_paddingTop", "_paddingBottom", "node", "__prefab"], -7, 1, 4], ["cc.Sprite", ["_type", "_sizeMode", "_enabled", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Widget", ["_alignFlags", "_top", "_originalHeight", "_bottom", "_originalWidth", "_left", "_right", "node", "__prefab"], -4, 1, 4], ["cc.Label", ["_string", "_actualFontSize", "_lineHeight", "_isBold", "_fontSize", "_enableOutline", "_outlineWidth", "_overflow", "_enableWrapText", "node", "__prefab", "_color", "_outlineColor"], -6, 1, 4, 5, 5], "cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_components", "_prefab", "_parent", "_lscale", "_children", "_lpos"], 1, 12, 4, 1, 5, 2, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedChil<PERSON>n", "mountedComponents", "removedComponents"], 2, 1, 9, 9, 9, 9], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetOverrideInfo", ["propertyPath", "source", "sourceInfo", "target", "targetInfo"], 2, 1, 4, 1, 4], ["cc.TargetInfo", ["localID"], 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 6], ["<PERSON><PERSON>", ["bounceDuration", "brake", "horizontal", "node", "__prefab", "_content"], 0, 1, 4, 1], ["ad253TrMIpHFZgL3Bp6KTbe", ["node", "__prefab", "tmpNode", "pageChangeEvent", "renderEvent", "selectedEvent"], 3, 1, 4, 1, 4, 4, 4], ["cc.ClickEvent", [], 3], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["cc.Animation", ["playOnLoad", "node", "__prefab", "_clips", "_defaultClip"], 2, 1, 4, 3, 6], ["cc.MountedChildrenInfo", ["targetInfo", "nodes"], 3, 4, 2], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 2], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab"], 1, 1, 4], ["7563fP+WuxL1p5rM8Q2NGlY", ["node", "__prefab", "spfs"], 3, 1, 4, 3], ["cc.LabelOutline", ["node", "__prefab"], 3, 1, 4]], [[10, 0, 2], [15, 0, 2], [12, 0, 1, 2, 3, 4, 5, 5], [7, 0, 1, 2, 1], [16, 0, 1, 2, 3], [18, 0, 1, 2, 2], [17, 0, 1, 2, 2], [0, 0, 1, 7, 6, 5, 8, 10, 3], [2, 3, 4, 5, 1], [0, 0, 1, 9, 6, 5, 8, 3], [0, 0, 1, 7, 9, 6, 5, 8, 3], [7, 0, 1, 2, 3, 1], [0, 0, 2, 1, 7, 9, 6, 5, 8, 4], [14, 0, 1, 2, 3, 4, 2], [28, 0, 1, 2, 2], [0, 0, 1, 7, 9, 6, 5, 3], [0, 0, 2, 1, 7, 6, 5, 8, 4], [0, 0, 2, 1, 7, 6, 5, 8, 10, 4], [3, 0, 7, 8, 2], [1, 10, 11, 1], [2, 0, 1, 3, 4, 5, 3], [2, 0, 3, 4, 5, 2], [23, 1], [27, 0, 1, 1], [4, 0, 1, 4, 2, 3, 6, 9, 10, 12, 7], [33, 0, 1, 1], [9, 0, 2], [0, 0, 1, 9, 6, 5, 3], [0, 0, 1, 7, 6, 5, 8, 3], [0, 0, 1, 7, 9, 6, 5, 8, 10, 3], [3, 0, 4, 2, 7, 8, 4], [3, 0, 1, 3, 2, 7, 8, 5], [3, 0, 3, 7, 8, 3], [3, 0, 1, 7, 8, 3], [13, 0, 1, 2, 3, 4, 5, 4], [19, 0, 1, 2, 3], [2, 3, 4, 1], [31, 0, 1, 2, 3, 3], [0, 3, 4, 5, 3], [0, 3, 4, 7, 5, 3], [6, 0, 1, 6, 2, 3, 3], [6, 0, 1, 4, 2, 3, 5, 3], [6, 0, 1, 4, 2, 3, 7, 5, 3], [7, 0, 1, 1], [3, 0, 5, 6, 1, 3, 4, 2, 7, 8, 8], [3, 0, 5, 1, 7, 8, 4], [11, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 3, 2, 10, 11, 5], [1, 0, 1, 8, 9, 4, 10, 11, 6], [1, 0, 1, 4, 2, 10, 11, 5], [1, 0, 1, 2, 10, 11, 4], [1, 0, 1, 5, 6, 2, 10, 11, 6], [1, 0, 1, 3, 10, 11, 4], [1, 7, 0, 1, 2, 10, 11, 5], [1, 7, 0, 1, 5, 6, 2, 10, 11, 7], [8, 0, 1, 2, 2], [8, 0, 1, 3, 4, 2, 5, 2], [20, 0, 1, 2, 2], [2, 0, 1, 3, 4, 3], [2, 0, 3, 4, 2], [2, 1, 3, 4, 5, 2], [2, 2, 3, 4, 5, 2], [21, 0, 1, 2, 3, 4, 5, 4], [22, 0, 1, 2, 3, 4, 5, 1], [24, 0, 1, 1], [25, 0, 1, 2, 3, 4, 2], [26, 0, 1, 1], [4, 0, 1, 4, 2, 3, 9, 10, 11, 6], [4, 0, 1, 4, 2, 7, 3, 5, 6, 9, 10, 11, 12, 9], [4, 0, 1, 2, 3, 9, 10, 5], [4, 0, 1, 4, 2, 3, 5, 9, 10, 12, 7], [4, 0, 1, 2, 3, 9, 10, 11, 5], [4, 0, 1, 4, 2, 7, 8, 3, 5, 6, 9, 10, 11, 12, 10], [29, 0, 1, 1], [30, 0, 1, 2, 1], [32, 0, 1, 2, 1]], [[[[26, "pt_tzjl"], [27, "pt_tzjl", 33554432, [-14], [[3, -12, [0, "6fUyudUT1IFo6FvRHKFGRJ"], [5, 640, 1280]], [30, 45, 720, 1280, -13, [0, "6b1cvTbIZFyJGZXVrsGtve"]]], [46, "95ptoTxBhATrAjGlSVUme9", null, -11, 0, [[13, ["tmpNode"], -4, [1, ["13eNdKY0ZLGbYQqpfkyvTj"]], -3, [1, ["36Xk5okZJLhbJhJ9aB8kEk"]]], [13, ["statusNodes", "0"], -6, [1, ["b2QoCvpR9PQ7/byygakvuO"]], -5, [1, ["749LXfhDlNKpFGD9kWraJg"]]], [13, ["statusNodes", "1"], -8, [1, ["b2QoCvpR9PQ7/byygakvuO"]], -7, [1, ["9dyECLwZ5AGL1Hkq5Woil1"]]], [13, ["statusNodes", "2"], -10, [1, ["b2QoCvpR9PQ7/byygakvuO"]], -9, [1, ["f8cpP/4edI1rgT8n8IFJLE"]]]], [-1, -2]]], [9, "item", 33554432, [-17, -18, -19, -20, -21, -22], [[3, -15, [0, "399W+Mo0hEd6mkQX8yb404"], [5, 494, 118]], [19, -16, [0, "3cHy3rhKtKPZcbFpmcPqSZ"]]], [2, "36Xk5okZJLhbJhJ9aB8kEk", null, null, null, 1, 0], [1, 0, -63, 0]], [38, 0, {}, [34, "c46/YsCPVOJYA4mWEpNYRx", null, null, -29, [55, "6eUMpVdClNOp5oRgWQrCBg", 1, [[4, "ty_zl1", ["_name"], -23], [6, ["_lpos"], -24, [1, -45.53000000000003, -18.9, 0]], [6, ["_lrot"], -25, [3, 0, 0, 0, 1]], [6, ["_euler"], -26, [1, 0, 0, 0]], [4, "wz_zl", ["_name"], -27], [5, ["_anchorPoint"], [1, ["2bDFBJv7lANLtDj5//xSlA"]], [0, 0, 0.5]], [5, ["_anchorPoint"], [1, ["0d7m5j+c1OM5qtEDlJDkZr"]], [0, 0, 0.5]], [6, ["_lpos"], -28, [1, 168.898, 0.5, 0]]]], 18]], [1, ["daLVJEkn9IhpizEzbnEOJb"]], [9, "ScrollView", 33554432, [-36], [[3, -30, [0, "5aHNxhQJFAxY8SittCksap"], [5, 533.4, 558]], [58, 1, 0, -31, [0, "2eG3YZPsFPE6WnDbPZiehA"]], [62, 0.23, 0.75, false, -33, [0, "84+n8YYM5FcI4quq7hQ+sQ"], -32], [63, -34, [0, "13eNdKY0ZLGbYQqpfkyvTj"], 2, [22], [22], [22]], [31, 5, 18, 205, 580, -35, [0, "eemNt3UQdMs6N4DvNq1AMl"]]], [2, "29YoHMGFhMXIf6wRgssarg", null, null, null, 1, 0], [1, 0, 93.5, 0]], [9, "neirong", 33554432, [-40, 5, -41], [[3, -37, [0, "aaJpjQcuxBA6G/l2SbxiF5"], [5, 589, 781]], [19, -38, [0, "6708FesS9JAovuydwwVcvT"]], [44, 45, 20.5, 20.5, 170.48000000000002, 36.51999999999998, 482, 833, -39, [0, "5eZdaeA3xHQI4CPyYgk/kQ"]]], [2, "1fsEC25OBJgoi9GEKoIKfz", null, null, null, 1, 0], [1, 0, -66.98000000000002, 0]], [9, "bg_tips_1", 33554432, [-45, -46, 6], [[3, -42, [0, "a8yqhYeWNJcLI3gVLCtZZ1"], [5, 630, 988]], [20, 1, 0, -43, [0, "6ar4qEo+1BvqNyFi7XMf3l"], 6], [64, -44, [0, "08Pgego05LQpoLd8O0Y5ah"]]], [2, "adU4hcIF1KBIngaH7EmOZ6", null, null, null, 1, 0], [1, 0, 37, 0]], [40, "ani_in", 33554432, [-53], [[[3, -47, [0, "d9OrvkIAVDmbEjJx3Cr0Xo"], [5, 452, 535]], [19, -48, [0, "08NLvQ4JpHR42gPYCLSfbv"]], [65, true, -49, [0, "a6rFwUB3dLSJ2bJ71B4og3"], [1], 2], [36, -50, [0, "a26BcbLtpAboaZx3mV/JuP"]], [18, 2, -51, [0, "188ss4RxFKpI0DWLudxjoO"]], -52], 4, 4, 4, 4, 4, 1], [2, "24tCD+YXhKCLwFSDt4YRnG", null, null, null, 1, 0]], [10, "bg_tips_4", 33554432, 6, [-57, -58], [[3, -54, [0, "9aA/F4t+FOfaibQGZVGeR6"], [5, 508, 131]], [20, 1, 0, -55, [0, "2b4LZ+NqtGmp+Cv7vz8QFJ"], 21], [32, 4, 12, -56, [0, "90tbOc6Y1Nspt1zLzh7MI2"]]], [2, "8dsJm6s7xLcJaqDOshDHkR", null, null, null, 1, 0], [1, 0, -313, 0]], [39, 0, {}, 1, [34, "79BcNar8tDmZucBHLjoa8/", null, null, -83, [56, "445fHfLeRLm692m27mHuIa", 1, [[66, [1, ["1fsEC25OBJgoi9GEKoIKfz"]], [-82, 5, 9]]], [[23, [1, ["24tCD+YXhKCLwFSDt4YRnG"]], [-79]], [23, [1, ["78n3BOogNNKaIOv5y9wQoP"]], [-80]], [23, [1, ["b0Z2p+e89KwbvfAK7hx5EH"]], [-81]]], [[4, "ty_tips_1", ["_name"], -59], [6, ["_lpos"], -60, [1, 0, 0, 0]], [6, ["_lrot"], -61, [3, 0, 0, 0, 1]], [6, ["_euler"], -62, [1, 0, 0, 0]], [5, ["_contentSize"], [1, ["47Cs6IYElOBK5jyf6MFKqL"]], [5, 640, 1280]], [4, true, ["_active"], -63], [5, ["_lpos"], [1, ["adU4hcIF1KBIngaH7EmOZ6"]], [1, 0, 37, 0]], [4, true, ["_active"], -64], [4, false, ["_active"], -65], [5, ["_lpos"], [1, ["24tCD+YXhKCLwFSDt4YRnG"]], [1, 0, 0, 0]], [4, "挑战记录", ["_string"], 4], [6, ["_lpos"], -66, [1, 0, 422.5, 0]], [35, 96, ["_top"], [1, ["dasWxlv1RNrrhMkFumI+ux"]]], [6, ["_lpos"], -67, [1, 0, -66.98000000000002, 0]], [4, 170.48000000000002, ["_top"], -68], [4, 36.51999999999998, ["_bottom"], -69], [5, ["_contentSize"], [1, ["a8yqhYeWNJcLI3gVLCtZZ1"]], [5, 630, 988]], [6, ["_lpos"], -70, [1, 0, 374, 0]], [5, ["_contentSize"], [1, ["aaJpjQcuxBA6G/l2SbxiF5"]], [5, 589, 781]], [5, ["_lpos"], [1, ["78n3BOogNNKaIOv5y9wQoP"]], [1, 0, 0, 0]], [4, 95, ["_top"], -71], [4, 56, ["_fontSize"], 4], [4, 57, ["_actualFontSize"], 4], [4, true, ["_enabled"], -72], [4, 70, ["_lineHeight"], 4], [4, 2, ["_overflow"], 4], [5, ["_contentSize"], [1, ["8a1ZxWWyxPyIyY//yWxbVN"]], [5, 800, 100]], [5, ["_contentSize"], [1, ["0baviFw+VGYZTyKzUPGLoM"]], [5, 484, 3]], [5, ["_contentSize"], [1, ["88MR1y1E9HsLmj95IcTT4P"]], [5, 523, 1062]], [5, ["_lpos"], [1, ["9aMeua5xxHo7Wg7Ik8OTd0"]], [1, 276, 5, 0]], [57, ["_spriteFrame"], [1, ["6ar4qEo+1BvqNyFi7XMf3l"]], 23], [4, -30.5, ["_top"], -73], [4, 463.5, ["_left"], -74], [4, 45, ["_alignFlags"], -75], [4, true, ["_enabled"], -76], [4, 1, ["_sizeMode"], -77], [5, ["_lpos"], [1, ["29Ph8qd5NBALHzar7VE2v1"]], [1, 0, -519, 0]], [6, ["_color"], 4, [4, 4278782508]], [4, true, ["_enableOutline"], 4], [4, 3, ["_outlineWidth"], 4], [6, ["_outlineColor"], 4, [4, 4294967295]], [35, 50, ["_spacingY"], [1, ["bbY45haOlM5qLSY7/7nS7G"]]], [4, 833, ["_originalHeight"], -78]], [[1, ["2eT+ei3AFMmJmP1HwMiZR/"]], [1, ["55Ep1ODopEoL1hDCCwgIEw"]], [1, ["9aPiokJJRAq7jIIUc3Iz5/"]]]], 0]], [9, "click_close", 33554432, [-86, -87, -88], [[3, -84, [0, "e4D+Prbk5MErMgRCU2caSo"], [5, 270, 24]], [47, 1, 1, 2, true, -85, [0, "8ezZ89Ni9LR7x96Xo6WnJk"]]], [2, "29Ph8qd5NBALHzar7VE2v1", null, null, null, 1, 0], [1, 0, -519, 0]], [41, "txt_gb", 33554432, 11, [[[3, -89, [0, "4fWK/e/RpHoIUitppwo9Rm"], [5, 308, 75.6]], [67, "點擊空白處關閉", 44, 44, 60, true, -90, [0, "b3J8hPlwRGIZBp39Y3kOHz"], [4, 3221225471]], [32, 2, -125.39999999999998, -91, [0, "16bUIVxypFRrhyDk8X1FnZ"]], [14, "djkb", -92, [0, "4eBrrFUMxBB5WWmciQILOX"]], -93], 4, 4, 4, 4, 1], [2, "78n3BOogNNKaIOv5y9wQoP", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [10, "view", 33554432, 5, [-98], [[11, -94, [0, "88n/ABmx9Hp5HaNArmYCfm"], [5, 533.4, 558], [0, 0.5, 1]], [73, -95, [0, "e7xBsbZIVCTrdRBz9cGNGw"]], [74, -96, [0, "7fRcswdxZH14NU8Z2TSUlN"], [4, 16777215]], [30, 45, 240, 250, -97, [0, "e7aM2qo+JLYooO4BwQj30j"]]], [2, "e62W9FxDdGOIvoDBDupKZ6", null, null, null, 1, 0], [1, 0, 279, 0]], [15, "content", 33554432, 13, [2], [[11, -99, [0, "cbgNkOmFNPSLXyn/8WnkrX"], [5, 508, 126], [0, 0.5, 1]], [18, 1, -100, [0, "8fihJgz85MbrV5BLXibE/p"]], [48, 1, 2, 4, 4, 6, -101, [0, "bb4173qOxFD6s3c/NoJFRn"]]], [2, "9aSrUC/sVOfJlIe4x3e2G2", null, null, null, 1, 0]], [10, "pm", 33554432, 2, [-103, -104, -105, -106], [[43, -102, [0, "5a0M8cD9tOK4sfJi5ariJb"]]], [2, "4fJh4Bi9VIqLkMb8Kk0jyV", null, null, null, 1, 0], [1, -211.661, 0, 0]], [28, "bg_tips_3", 33554432, 6, [[3, -107, [0, "3aiwO0JLJDxKnxuQpDG0i9"], [5, 542, 579]], [20, 1, 0, -108, [0, "f3I6QtqVpA0INWLMRO9hnn"], 22], [31, 5, 8, 194, 600, -109, [0, "4feAemnTlE/6M/mWMygkHA"]]], [2, "d69DmD56lOurrPempHIrh4", null, null, null, 1, 0], [1, 0, 93, 0]], [15, "<PERSON>_suofang", 33554432, 8, [7, 11], [[3, -110, [0, "88MR1y1E9HsLmj95IcTT4P"], [5, 523, 1062]], [49, 1, 2, 50, true, -111, [0, "bbY45haOlM5qLSY7/7nS7G"]]], [2, "10U9/vnbBONoruSblOhHzL", null, null, null, 1, 0]], [42, "txt_title1", 33554432, 7, [[[3, -112, [0, "8a1ZxWWyxPyIyY//yWxbVN"], [5, 800, 100]], [68, "挑战记录", 57, 56, 70, 2, true, true, 3, -113, [0, "daLVJEkn9IhpizEzbnEOJb"], [4, 4278782508], [4, 4294967295]], [33, 1, 95, -114, [0, "70/3vA5OpN8LaNyd5FUwzR"]], -115], 4, 4, 4, 1], [2, "b0Z2p+e89KwbvfAK7hx5EH", null, null, null, 1, 0], [1, 0, 374, 0], [1, 0.5, 0.5, 1]], [12, "img_xian_2", false, 33554432, 7, [-119], [[3, -116, [0, "0baviFw+VGYZTyKzUPGLoM"], [5, 484, 3]], [59, 1, -117, [0, "46ptCyrGlH9oRpEAXLnZIn"]], [33, 1, 96, -118, [0, "dasWxlv1RNrrhMkFumI+ux"]]], [2, "ecdLmaKdFMOqhExD1woACw", null, null, null, 1, 0], [1, 0, 422.5, 0]], [16, "btn_close", false, 33554432, 19, [[3, -120, [0, "69dLSBEyBP/6SdzwdLZ0KL"], [5, 59, 61]], [8, -121, [0, "f252Zzgq9C/6SR8iAEnw25"], 5], [45, 9, 463.5, -30.5, -122, [0, "f22AcbXlNFxINSS0W0MnO0"]], [37, 3, 0.9, -123, [0, "e0ES++bdRON7wqagLbPS/g"]]], [2, "9aMeua5xxHo7Wg7Ik8OTd0", null, null, null, 1, 0], [1, 276, 5, 0]], [15, "bg_di4", 33554432, 2, [-127], [[3, -124, [0, "19CQRbuIFPTLRQrsWb/b5S"], [5, 520, 121]], [21, 1, -125, [0, "fcvnMO8cNHkKp0hXdk0hJR"], 8], [75, -126, [0, "ce94rU1xdFpbdwYTk7FIyB"], [9, 10, 11, 12]]], [2, "33KVJ0vTxDAJiRonagGPzL", null, null, null, 1, 0]], [29, "wz", 33554432, 2, [-130, 3], [[3, -128, [0, "ec9mjOEj9DIYW0QPoPGNs9"], [5, 100, 81.8]], [50, 1, 2, true, -129, [0, "ecE5z0bhlNNpXytmQYYEtH"]]], [2, "4f1YGYeYVENbUjczOpe0ZQ", null, null, null, 1, 0], [1, -0.913, 1.966, 0], [1, 0.9, 0.9, 1]], [10, "bg_di_title1", 33554432, 9, [-134], [[11, -131, [0, "18GOf0cDdOpqCZnabCgmMz"], [5, 180, 37], [0, 0, 0.5]], [60, 0, -132, [0, "48NMVmtD9CuoJZIyG5GRAh"], 20], [51, 1, 1, 40, 60, true, -133, [0, "98793/qbRDarxIN5cQ3nP3"]]], [2, "ae1lFmBS1Og6HpS7zR69MI", null, null, null, 1, 0], [1, -271, 90.46500000000003, 0]], [7, "btn", 33554432, 21, [[3, -135, [0, "0e4o3XIDJNPJn3/PK8zoRf"], [5, 80, 84]], [8, -136, [0, "4aV+ecYq5OX6ehFQmcKCpW"], 7], [37, 3, 0.9, -137, [0, "21BV68SIZAXq3WyupifLIy"]]], [2, "e3aN2KzwJC57XV7Ibz4KKo", null, null, null, 1, 0], [1, 205.368, 1.354, 0], [1, 0.8, 0.8, 1]], [12, "pm1", false, 33554432, 15, [-140], [[3, -138, [0, "68WKM0TAROpY8SWx2jIqfB"], [5, 67, 67]], [8, -139, [0, "73dh4FoDlEsbRaLD6ZbOSV"], 15]], [2, "ae+sFSB9dCHZPS4LBaxGAU", null, null, null, 1, 0], [1, 0.33600000000001273, 1, 0]], [17, "wz_pm1", false, 33554432, 25, [[3, -141, [0, "aenwBMYuJPF4DFic/k7b2V"], [5, 32.6953125, 106.8]], [24, "1", 48, 48, 80, true, 3, -142, [0, "8d6aTn4ohFnKGTnwfXffVc"], [4, 4278202984]], [25, -143, [0, "5aJjAYmQpJ+IZAiwlqeSSm"]]], [2, "c62oayQgZPaaKmMNxYIF5S", null, null, null, 1, 0], [1, -1.373, 2.872, 0], [1, 0.5, 0.5, 1]], [12, "pm2", false, 33554432, 15, [-146], [[3, -144, [0, "a2SUvotdtDGrB9YxQ8m6i8"], [5, 67, 67]], [8, -145, [0, "57nnsXyb5DJY0HDDrlrlBy"], 16]], [2, "f690z2u+VMIKb+NWIsOJwR", null, null, null, 1, 0], [1, 0.33600000000001273, 1, 0]], [17, "wz_pm2", false, 33554432, 27, [[3, -147, [0, "18hUkfQgxGAJoG1doQhLTw"], [5, 32.6953125, 106.8]], [24, "1", 48, 48, 80, true, 3, -148, [0, "11eusJ+z5IjqLQ2YSicS3v"], [4, 4282843208]], [25, -149, [0, "2aB10DOUhPcrSk7bODcAFf"]]], [2, "a7KxnCTT1KMbECWylqm8oa", null, null, null, 1, 0], [1, -1.373, 2.872, 0], [1, 0.5, 0.5, 1]], [12, "pm3", false, 33554432, 15, [-152], [[3, -150, [0, "ffERyNz7JAJqe8PODBIrPz"], [5, 67, 67]], [8, -151, [0, "02JtuZMCFDhJA8xfq7aFV5"], 17]], [2, "b7YqL872tCla11Oxqmh8sH", null, null, null, 1, 0], [1, 0.33600000000001273, 1, 0]], [17, "wz_pm3", false, 33554432, 29, [[3, -153, [0, "76Iz5eOkRDjKxZP/HSD0SI"], [5, 32.6953125, 106.8]], [24, "1", 48, 48, 80, true, 3, -154, [0, "05IRnDZWtF55QqBz6+P18F"], [4, 4282394624]], [25, -155, [0, "4dKUGKKLtJGZQf/Xs5FiDl"]]], [2, "ecMT6NgeRA2bPMWLouGZNd", null, null, null, 1, 0], [1, -1.373, 2.872, 0], [1, 0.5, 0.5, 1]], [1, ["c46/YsCPVOJYA4mWEpNYRx"]], [10, "xs", 33554432, 9, [-158], [[3, -156, [0, "87Ahxo+0xCebxZi+JDMEsy"], [5, 639, 207]], [21, 1, -157, [0, "b7qpRSietJ7pQP6TUasiPe"], 19]], [2, "0eLGHxr+lLiIjr9cyPEdJk", null, null, null, 1, 0], [1, 0, 28.98, 0]], [7, "wz_tgjl", 33554432, 23, [[11, -159, [0, "8cPp4OagdJSp/eMwZiEz6B"], [5, 160, 100.8], [0, 0, 0.5]], [69, "通关奖励", 40, 80, true, -160, [0, "4fM7u0Y3VDcKKwspQd5mZK"]], [14, "zhongzuta_8", -161, [0, "a915VcrIFCMpNziGyMZ06H"]]], [2, "342UvRumRFBIcbxsbctS1U", null, null, null, 1, 0], [1, 40, 3.066, 0], [1, 0.5, 0.5, 1]], [1, ["79BcNar8tDmZucBHLjoa8/"]], [1, ["5eZdaeA3xHQI4CPyYgk/kQ"]], [7, "img_jt1", 33554432, 11, [[3, -162, [0, "eee/9eN+NJzKI628HWK7x1"], [5, 56, 31]], [8, -163, [0, "68BOlH9JlMJJNVsCv0L5TE"], 3]], [2, "5cO5jcQ/dJF502OHYmrDg3", null, null, null, 1, 0], [1, -107, 0, 0], [1, -1, 1, 1]], [28, "img_jt2", 33554432, 11, [[3, -164, [0, "881UsWnGJCfapWV3Ch/EkM"], [5, 56, 31]], [8, -165, [0, "3bWlSrUlRPELoUCq41DPmO"], 4]], [2, "a1v7P3vClBB7uq9CDdFhA7", null, null, null, 1, 0], [1, 107, 0, 0]], [16, "img_xian_shu1", false, 33554432, 2, [[3, -166, [0, "f9Q+ckJUFBZJvXpFixZDTL"], [5, 1, 100]], [8, -167, [0, "5fVBFHM9xDjrjVAC+pYNT+"], 13]], [2, "7519Wx0k5O+5OLs4DDSAPw", null, null, null, 1, 0], [1, -132.5, 1, 0]], [16, "img_mfz", false, 33554432, 2, [[3, -168, [0, "61fa9mCSxI9b4MB/SwGxN+"], [5, 96, 96]], [61, false, -169, [0, "f96U9a8+9EdqNWggNY+nDO"], 14]], [2, "2cJsgLzX1Dj5w0OEGW7qB0", null, null, null, 1, 0], [1, -187.858, 1.11, 0]], [7, "txt_dj1", 33554432, 15, [[3, -170, [0, "d4g3IPUQRB3ZPTzKkJxBGG"], [5, 32.919921875, 74.56]], [70, "1", 52, 52, 56, true, true, -171, [0, "e0SRX2z6JIk7M0f1Q7ipXw"], [4, 4280098330]]], [2, "51bx0psKZP35A0ULDJ/F8u", null, null, null, 1, 0], [1, 0.33600000000001273, 1, 0], [1, 0.5, 0.5, 1]], [7, "tb_js", 33554432, 2, [[3, -172, [0, "0bpC7SkYVL54s/tViELA0r"], [5, 90, 93]], [36, -173, [0, "01ZuU3B/BB4K5+iel3PoBR"]]], [2, "f8XdmTv0RCzKYVTO/9cWFE", null, null, null, 1, 0], [1, -101.407, 0.585, 0], [1, 0.9, 0.9, 1]], [7, "wz_name", 33554432, 22, [[11, -174, [0, "74qM7kTKVFB7aulUSFWleV"], [5, 120, 75.6], [0, 0, 0.5]], [71, "安其拉", 40, 60, true, -175, [0, "3aJ5+XXfpIBLIrvcV5nBlI"], [4, 4280098330]]], [2, "4epzapJQpKkZM8goBlfvw2", null, null, null, 1, 0], [1, -45.53000000000003, 22, 0], [1, 0.5, 0.5, 1]], [7, "layout_wdjl", 33554432, 32, [[3, -176, [0, "d73zzqVTNEap2qdT+pG9sE"], [5, -15, 100]], [52, 1, 1, 15, -177, [0, "2fNsFP/nBFAa1Rgt6bxpRV"]]], [2, "2fUw6KjnlBPaK2v80V+ycv", null, null, null, 1, 0], [1, 0, -35.22, 0], [1, 0.8, 0.8, 1]], [18, 2, 8, [0, "c5QKxu8btJL7lfiFv6O44Z"]], [14, "djkb", 12, [0, "541tenswRPiJb/aqVAXAGH"]], [14, "mnxl_18", 18, [0, "b02CQIGYVNv5rm9kxwNCo5"]], [1, ["749LXfhDlNKpFGD9kWraJg"]], [1, ["1fsEC25OBJgoi9GEKoIKfz"]], [1, ["b0Z2p+e89KwbvfAK7hx5EH"]], [1, ["ecdLmaKdFMOqhExD1woACw"]], [1, ["70/3vA5OpN8LaNyd5FUwzR"]], [1, ["f22AcbXlNFxINSS0W0MnO0"]], [1, ["46ptCyrGlH9oRpEAXLnZIn"]]], 0, [0, -1, 10, 0, -2, 3, 0, 4, 10, 0, 5, 10, 0, 4, 3, 0, 5, 3, 0, 4, 3, 0, 5, 3, 0, 4, 3, 0, 5, 3, 0, 6, 1, 0, 0, 1, 0, 0, 1, 0, -1, 10, 0, 0, 2, 0, 0, 2, 0, -1, 21, 0, -2, 38, 0, -3, 39, 0, -4, 15, 0, -5, 41, 0, -6, 22, 0, 1, 31, 0, 1, 31, 0, 1, 31, 0, 1, 31, 0, 1, 47, 0, 1, 47, 0, 6, 3, 0, 0, 5, 0, 0, 5, 0, 10, 14, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 13, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 16, 0, -3, 9, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 18, 0, -2, 19, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -6, 44, 0, -1, 17, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 32, 0, -2, 23, 0, 1, 34, 0, 1, 34, 0, 1, 34, 0, 1, 34, 0, 1, 48, 0, 1, 49, 0, 1, 50, 0, 1, 50, 0, 1, 48, 0, 1, 35, 0, 1, 35, 0, 1, 49, 0, 1, 51, 0, 1, 51, 0, 1, 52, 0, 1, 52, 0, 1, 35, 0, 1, 53, 0, 1, 53, 0, 1, 35, 0, -1, 44, 0, -1, 45, 0, -1, 46, 0, -1, 16, 0, 6, 10, 0, 0, 11, 0, 0, 11, 0, -1, 36, 0, -2, 12, 0, -3, 37, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, -5, 45, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, -1, 14, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -1, 25, 0, -2, 27, 0, -3, 29, 0, -4, 40, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, -4, 46, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, -1, 20, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, -1, 24, 0, 0, 22, 0, 0, 22, 0, -1, 42, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, -1, 33, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, -1, 26, 0, 0, 26, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, -1, 28, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, -1, 30, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 0, 32, 0, 0, 32, 0, -1, 43, 0, 0, 33, 0, 0, 33, 0, 0, 33, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 8, 1, 2, 3, 14, 3, 3, 22, 5, 3, 6, 6, 3, 7, 7, 3, 17, 8, 3, 10, 11, 3, 17, 177], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [9, -1, 11, 2, 2, 2, 2, 2, 2, -1, -2, -3, -4, 2, 2, 2, 2, 2, 9, 2, 2, 2, 2, 12], [5, 0, 0, 1, 1, 6, 2, 7, 3, 3, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 4, 4, 2]], [[{"name": "btn_ph", "rect": {"x": 0, "y": 0, "width": 80, "height": 84}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 80, "height": 84}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-40, -42, 0, 40, -42, 0, -40, 42, 0, 40, 42, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 84, 80, 84, 0, 0, 80, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -40, "y": -42, "z": 0}, "maxPos": {"x": 40, "y": 42, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [7], [19]], [[{"name": "img_pm1", "rect": {"x": 0, "y": 0, "width": 67, "height": 67}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 67, "height": 67}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-33.5, -33.5, 0, 33.5, -33.5, 0, -33.5, 33.5, 0, 33.5, 33.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 67, 67, 67, 0, 0, 67, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -33.5, "y": -33.5, "z": 0}, "maxPos": {"x": 33.5, "y": 33.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [7], [20]], [[{"name": "img_pm3", "rect": {"x": 0, "y": 0, "width": 67, "height": 67}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 67, "height": 67}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-33.5, -33.5, 0, 33.5, -33.5, 0, -33.5, 33.5, 0, 33.5, 33.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 67, 67, 67, 0, 0, 67, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -33.5, "y": -33.5, "z": 0}, "maxPos": {"x": 33.5, "y": 33.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [7], [21]], [[[26, "ty_zl1"], [27, "ty_zl1", 33554432, [-4], [[3, -2, [0, "2bDFBJv7lANLtDj5//xSlA"], [5, 223, 44]], [53, false, 1, 1, true, -3, [0, "e4h2IXquNMBJ/AkWbnzgY2"]]], [2, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [29, "bg_zl1", 33554432, 1, [-8], [[3, -5, [0, "0d7m5j+c1OM5qtEDlJDkZr"], [5, 372, 80]], [21, 1, -6, [0, "cbE/sWisBId6+fW1rcT+Qa"], 0], [54, false, 1, 1, 45, 60, true, -7, [0, "09ZUgMeS9ER615VCyFcuOR"]]], [2, "d4HiuUWCxEkYe/Pz7S6SbU", null, null, null, 1, 0], [1, 0, 0.5, 0], [1, 0.5, 0.5, 1]], [7, "wz_zl1", 33554432, 2, [[3, -9, [0, "9auYdjWVRM8p/9d58mRFQ3"], [5, 493, 140]], [72, "", 96, 96, 100, 2, false, true, true, 6, -10, [0, "dbf558ErpD24fb8rDfwYgY"], [4, 4288806655], [4, 4281282418]]], [2, "749LXfhDlNKpFGD9kWraJg", null, null, null, 1, 0], [1, 0, 1, 0], [1, 0.5, 0.5, 1]]], 0, [0, 6, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, 0, 3, 0, 0, 3, 0, 8, 1, 10], [0], [2], [22]], [[{"name": "img_pm2", "rect": {"x": 0, "y": 0, "width": 67, "height": 67}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 67, "height": 67}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-33.5, -33.5, 0, 33.5, -33.5, 0, -33.5, 33.5, 0, 33.5, 33.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 67, 67, 67, 0, 0, 67, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -33.5, "y": -33.5, "z": 0}, "maxPos": {"x": 33.5, "y": 33.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [7], [23]]]]