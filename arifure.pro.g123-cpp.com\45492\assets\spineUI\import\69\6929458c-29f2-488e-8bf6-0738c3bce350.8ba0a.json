[1, ["faVWRVym5AH5K/raDPJS65@6c48a"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "img_ty_cd", "\nimg_ty_cd.png\nsize: 150,150\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\ncd1\n  rotate: true\n  xy: 114, 112\n  size: 36, 28\n  orig: 38, 30\n  offset: 1, 1\n  index: -1\ncd2\n  rotate: true\n  xy: 114, 93\n  size: 17, 19\n  orig: 19, 21\n  offset: 1, 1\n  index: -1\ncd3\n  rotate: false\n  xy: 2, 17\n  size: 97, 66\n  orig: 99, 68\n  offset: 1, 1\n  index: -1\ncd4\n  rotate: false\n  xy: 2, 85\n  size: 110, 63\n  orig: 112, 65\n  offset: 1, 1\n  index: -1\nhuaban2\n  rotate: true\n  xy: 135, 93\n  size: 17, 13\n  orig: 28, 19\n  offset: 7, 2\n  index: -1\nyan\n  rotate: false\n  xy: 101, 37\n  size: 45, 46\n  orig: 45, 46\n  offset: 0, 0\n  index: -1\n", ["img_ty_cd.png"], {"skeleton": {"hash": "Q30Iy0uYtO9lJjS0FaBoZcLmVwA", "spine": "3.8.99", "x": -56, "y": -49, "width": 112, "height": 120.94, "images": "./images/", "audio": "F:/pingfanzhiye_work/界面/主界面图标动效/茶道"}, "bones": [{"name": "root"}, {"name": "yan", "parent": "root", "x": -21.25, "y": -13.66, "scaleX": -1}, {"name": "yan2", "parent": "root", "x": -21.25, "y": -13.66, "scaleX": -1}, {"name": "all_piaoye", "parent": "root"}, {"name": "piaoye1", "parent": "all_piaoye", "x": 38.37, "y": 45.78}, {"name": "piaoye2", "parent": "all_piaoye", "x": 43.29, "y": 33.75, "scaleX": 0.6, "scaleY": 0.6}, {"name": "piaoye3", "parent": "all_piaoye", "x": 10.47, "y": 55.08}, {"name": "piaoye4", "parent": "all_piaoye", "x": 25.79, "y": 50.7, "scaleX": 0.6, "scaleY": 0.6}, {"name": "piaoye5", "parent": "all_piaoye", "x": 32.9, "y": 40.86}], "slots": [{"name": "cd4", "bone": "root", "attachment": "cd4"}, {"name": "cd3", "bone": "root", "attachment": "cd3"}, {"name": "cd2", "bone": "root", "attachment": "cd2"}, {"name": "cd1", "bone": "root", "attachment": "cd1"}, {"name": "yan", "bone": "yan", "attachment": "yan"}, {"name": "yan2", "bone": "yan2", "attachment": "yan"}, {"name": "huaban2", "bone": "piaoye1", "color": "ffffff00", "attachment": "huaban2"}, {"name": "huaban3", "bone": "piaoye2", "color": "ffffff00", "attachment": "huaban2"}, {"name": "huaban4", "bone": "piaoye3", "color": "ffffff00", "attachment": "huaban2"}, {"name": "huaban5", "bone": "piaoye4", "color": "ffffff00", "attachment": "huaban2"}, {"name": "huaban6", "bone": "piaoye5", "color": "ffffff00", "attachment": "huaban2"}], "skins": [{"name": "default", "attachments": {"cd1": {"cd1": {"x": -20, "y": -18, "width": 38, "height": 30}}, "cd2": {"cd2": {"x": 25.5, "y": -16.5, "width": 19, "height": 21}}, "cd3": {"cd3": {"x": 5.5, "y": 12, "width": 99, "height": 68}}, "cd4": {"cd4": {"y": -16.5, "width": 112, "height": 65}}, "huaban2": {"huaban2": {"rotation": -128.73, "width": 28, "height": 19}}, "huaban3": {"huaban2": {"rotation": -128.73, "width": 28, "height": 19}}, "huaban4": {"huaban2": {"rotation": -128.73, "width": 28, "height": 19}}, "huaban5": {"huaban2": {"rotation": -128.73, "width": 28, "height": 19}}, "huaban6": {"huaban2": {"rotation": -128.73, "width": 28, "height": 19}}, "yan": {"yan": {"x": 8.49, "y": 19.34, "width": 45, "height": 46}}, "yan2": {"yan": {"x": 8.49, "y": 19.34, "width": 45, "height": 46}}}}], "animations": {"animation": {"slots": {"huaban2": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00"}]}, "huaban3": {"color": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.4, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00"}, {"time": 3.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "huaban4": {"color": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4667, "color": "ffffff00"}, {"time": 2.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "huaban5": {"color": [{"time": 1.3333, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8333, "color": "ffffffff"}, {"time": 2.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}, "huaban6": {"color": [{"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.3, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}]}, "yan": {"color": [{"color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}, "yan2": {"color": [{"color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffff"}]}}, "bones": {"yan": {"scale": [{}, {"time": 1.6333, "x": 2, "y": 2}, {"time": 1.6667}, {"time": 3.3, "x": 2, "y": 2}, {"time": 3.3333}]}, "yan2": {"scale": [{"x": 1.51, "y": 1.51}, {"time": 0.8, "x": 2, "y": 2}, {"time": 0.8333}, {"time": 1.6667, "x": 1.51, "y": 1.51}, {"time": 2.4667, "x": 2, "y": 2}, {"time": 2.5}, {"time": 3.3333, "x": 1.51, "y": 1.51}]}, "piaoye1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -19.21, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.89, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{}, {"time": 1.6333, "x": -73.25, "y": -57.99, "curve": "stepped"}, {"time": 1.6667}]}, "piaoye2": {"rotate": [{"angle": -5.45, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "angle": -19.21, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -4.89, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 3.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": -5.45}], "translate": [{"x": -3.9, "y": -8.57}, {"time": 1.4, "x": -27.31, "y": -60, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 3.1}, {"time": 3.3333, "x": -3.9, "y": -8.57}]}, "piaoye3": {"rotate": [{"angle": -16.37, "curve": 0.336, "c2": 0.34, "c3": 0.758}, {"time": 0.4667, "angle": -4.89, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.6333, "curve": "stepped"}, {"time": 2.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -19.21, "curve": 0.274, "c3": 0.62, "c4": 0.4}, {"time": 3.3333, "angle": -16.37}], "translate": [{"x": -26.04, "y": -39.05}, {"time": 0.7667, "x": -49.07, "y": -73.59, "curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.6333, "curve": "stepped"}, {"time": 2.4667}, {"time": 3.3333, "x": -26.04, "y": -39.05}]}, "piaoye4": {"rotate": [{"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -19.21, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -4.89, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "curve": "stepped"}, {"time": 3}], "translate": [{"time": 1.3333}, {"time": 2.9667, "x": -37.38, "y": -77.73, "curve": "stepped"}, {"time": 3}]}, "piaoye5": {"rotate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -19.21, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -4.89, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "curve": "stepped"}, {"time": 2.3333}], "translate": [{"time": 0.6667}, {"time": 2.3, "x": -73.25, "y": -57.99, "curve": "stepped"}, {"time": 2.3333}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]