<html><head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>g123-ma-auxin</title>
  <script crossorigin="">import('https://platform-sc.g123.jp/micro-app/auxin/1.16.2/assets/index.js').finally(() => {
            
    const qiankunLifeCycle = window.moudleQiankunAppLifeCycles && window.moudleQiankunAppLifeCycles['g123-ma-auxin'];
    if (qiankunLifeCycle) {
      window.proxy.vitemount((props) => qiankunLifeCycle.mount(props));
      window.proxy.viteunmount((props) => qiankunLifeCycle.unmount(props));
      window.proxy.vitebootstrap(() => qiankunLifeCycle.bootstrap());
      window.proxy.viteupdate((props) => qiankunLifeCycle.update(props));
    }
  
          })</script>
  <link rel="stylesheet" href="https://platform-sc.g123.jp/micro-app/auxin/1.16.2/assets/style.css">
</head>
<body style="background-color: rgba(0, 0, 0, 0.3)">
  <div id="auxin"></div>
  

<script>
  const createDeffer = (hookName) => {
    const d = new Promise((resolve, reject) => {
      window.proxy && (window.proxy[`vite${hookName}`] = resolve)
    })
    return props => d.then(fn => fn(props));
  }
  const bootstrap = createDeffer('bootstrap');
  const mount = createDeffer('mount');
  const unmount = createDeffer('unmount');
  const update = createDeffer('update');

  ;(global => {
    global.qiankunName = 'g123-ma-auxin';
    global['g123-ma-auxin'] = {
      bootstrap,
      mount,
      unmount,
      update
    };
  })(window);
</script></body></html>