(()=>{try{self["workbox:core:7.2.0"]&&_()}catch{}var Ue=(e,...t)=>{let a=e;return t.length>0&&(a+=` :: ${JSON.stringify(t)}`),a},Te=Ue,d=class extends Error{constructor(e,t){let a=Te(e,t);super(a),this.name=e,this.details=t}},Pe=e=>new URL(String(e),location.href).href.replace(new RegExp(`^${location.origin}`),"");try{self["workbox:cacheable-response:7.2.0"]&&_()}catch{}var Ie=class{constructor(e={}){this._statuses=e.statuses,this._headers=e.headers}isResponseCacheable(e){let t=!0;return this._statuses&&(t=this._statuses.includes(e.status)),this._headers&&t&&(t=Object.keys(this._headers).some(a=>e.headers.get(a)===this._headers[a])),t}},S=class{constructor(e){this.cacheWillUpdate=async({response:t})=>this._cacheableResponse.isResponseCacheable(t)?t:null,this._cacheableResponse=new Ie(e)}},ee=new Set;function Oe(e){ee.add(e)}var m={googleAnalytics:"googleAnalytics",precache:"precache-v2",prefix:"workbox",runtime:"runtime",suffix:typeof registration<"u"?registration.scope:""},O=e=>[m.prefix,e,m.suffix].filter(t=>t&&t.length>0).join("-"),Ae=e=>{for(let t of Object.keys(m))e(t)},f={updateDetails:e=>{Ae(t=>{typeof e[t]=="string"&&(m[t]=e[t])})},getGoogleAnalyticsName:e=>e||O(m.googleAnalytics),getPrecacheName:e=>e||O(m.precache),getPrefix:()=>m.prefix,getRuntimeName:e=>e||O(m.runtime),getSuffix:()=>m.suffix};function te(e,t){let a=new URL(e);for(let s of t)a.searchParams.delete(s);return a.href}async function je(e,t,a,s){let r=te(t.url,a);if(t.url===r)return e.match(t,s);let n=Object.assign(Object.assign({},s),{ignoreSearch:!0}),i=await e.keys(t,n);for(let o of i){let c=te(o.url,a);if(r===c)return e.match(o,s)}}var R;function Me(){if(R===void 0){let e=new Response("");if("body"in e)try{new Response(e.body),R=!0}catch{R=!1}R=!1}return R}function ae(e){e.then(()=>{})}var Ke=class{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}};async function Be(){for(let e of ee)await e()}function se(e){return new Promise(t=>setTimeout(t,e))}function re(e,t){let a=t();return e.waitUntil(a),a}var N={get googleAnalytics(){return f.getGoogleAnalyticsName()},get precache(){return f.getPrecacheName()},get prefix(){return f.getPrefix()},get runtime(){return f.getRuntimeName()},get suffix(){return f.getSuffix()}};async function We(e,t){let a=null;if(e.url&&(a=new URL(e.url).origin),a!==self.location.origin)throw new d("cross-origin-copy-response",{origin:a});let s=e.clone(),r={headers:new Headers(s.headers),status:s.status,statusText:s.statusText},n=t?t(r):r,i=Me()?s.body:await s.blob();return new Response(i,n)}function Fe(e){f.updateDetails(e)}var He=(e,t)=>t.some(a=>e instanceof a),ne,ie;function $e(){return ne||(ne=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])}function Qe(){return ie||(ie=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])}var oe=new WeakMap,A=new WeakMap,ce=new WeakMap,j=new WeakMap,M=new WeakMap;function Ve(e){let t=new Promise((a,s)=>{let r=()=>{e.removeEventListener("success",n),e.removeEventListener("error",i)},n=()=>{a(w(e.result)),r()},i=()=>{s(e.error),r()};e.addEventListener("success",n),e.addEventListener("error",i)});return t.then(a=>{a instanceof IDBCursor&&oe.set(a,e)}).catch(()=>{}),M.set(t,e),t}function Ge(e){if(A.has(e))return;let t=new Promise((a,s)=>{let r=()=>{e.removeEventListener("complete",n),e.removeEventListener("error",i),e.removeEventListener("abort",i)},n=()=>{a(),r()},i=()=>{s(e.error||new DOMException("AbortError","AbortError")),r()};e.addEventListener("complete",n),e.addEventListener("error",i),e.addEventListener("abort",i)});A.set(e,t)}var K={get(e,t,a){if(e instanceof IDBTransaction){if(t==="done")return A.get(e);if(t==="objectStoreNames")return e.objectStoreNames||ce.get(e);if(t==="store")return a.objectStoreNames[1]?void 0:a.objectStore(a.objectStoreNames[0])}return w(e[t])},set(e,t,a){return e[t]=a,!0},has(e,t){return e instanceof IDBTransaction&&(t==="done"||t==="store")?!0:t in e}};function ze(e){K=e(K)}function Je(e){return e===IDBDatabase.prototype.transaction&&!("objectStoreNames"in IDBTransaction.prototype)?function(t,...a){let s=e.call(B(this),t,...a);return ce.set(s,t.sort?t.sort():[t]),w(s)}:Qe().includes(e)?function(...t){return e.apply(B(this),t),w(oe.get(this))}:function(...t){return w(e.apply(B(this),t))}}function Xe(e){return typeof e=="function"?Je(e):(e instanceof IDBTransaction&&Ge(e),He(e,$e())?new Proxy(e,K):e)}function w(e){if(e instanceof IDBRequest)return Ve(e);if(j.has(e))return j.get(e);let t=Xe(e);return t!==e&&(j.set(e,t),M.set(t,e)),t}var B=e=>M.get(e);function le(e,t,{blocked:a,upgrade:s,blocking:r,terminated:n}={}){let i=indexedDB.open(e,t),o=w(i);return s&&i.addEventListener("upgradeneeded",c=>{s(w(i.result),c.oldVersion,c.newVersion,w(i.transaction),c)}),a&&i.addEventListener("blocked",c=>a(c.oldVersion,c.newVersion,c)),o.then(c=>{n&&c.addEventListener("close",()=>n()),r&&c.addEventListener("versionchange",l=>r(l.oldVersion,l.newVersion,l))}).catch(()=>{}),o}function Ye(e,{blocked:t}={}){let a=indexedDB.deleteDatabase(e);return t&&a.addEventListener("blocked",s=>t(s.oldVersion,s)),w(a).then(()=>{})}var Ze=["get","getKey","getAll","getAllKeys","count"],et=["put","add","delete","clear"],W=new Map;function ue(e,t){if(!(e instanceof IDBDatabase&&!(t in e)&&typeof t=="string"))return;if(W.get(t))return W.get(t);let a=t.replace(/FromIndex$/,""),s=t!==a,r=et.includes(a);if(!(a in(s?IDBIndex:IDBObjectStore).prototype)||!(r||Ze.includes(a)))return;let n=async function(i,...o){let c=this.transaction(i,r?"readwrite":"readonly"),l=c.store;return s&&(l=l.index(o.shift())),(await Promise.all([l[a](...o),r&&c.done]))[0]};return W.set(t,n),n}ze(e=>({...e,get:(t,a,s)=>ue(t,a)||e.get(t,a,s),has:(t,a)=>!!ue(t,a)||e.has(t,a)}));try{self["workbox:expiration:7.2.0"]&&_()}catch{}var tt="workbox-expiration",x="cache-entries",he=e=>{let t=new URL(e,location.href);return t.hash="",t.href},at=class{constructor(e){this._db=null,this._cacheName=e}_upgradeDb(e){let t=e.createObjectStore(x,{keyPath:"id"});t.createIndex("cacheName","cacheName",{unique:!1}),t.createIndex("timestamp","timestamp",{unique:!1})}_upgradeDbAndDeleteOldDbs(e){this._upgradeDb(e),this._cacheName&&Ye(this._cacheName)}async setTimestamp(e,t){e=he(e);let a={url:e,timestamp:t,cacheName:this._cacheName,id:this._getId(e)},s=(await this.getDb()).transaction(x,"readwrite",{durability:"relaxed"});await s.store.put(a),await s.done}async getTimestamp(e){return(await(await this.getDb()).get(x,this._getId(e)))?.timestamp}async expireEntries(e,t){let a=await this.getDb(),s=await a.transaction(x).store.index("timestamp").openCursor(null,"prev"),r=[],n=0;for(;s;){let o=s.value;o.cacheName===this._cacheName&&(e&&o.timestamp<e||t&&n>=t?r.push(s.value):n++),s=await s.continue()}let i=[];for(let o of r)await a.delete(x,o.id),i.push(o.url);return i}_getId(e){return this._cacheName+"|"+he(e)}async getDb(){return this._db||(this._db=await le(tt,1,{upgrade:this._upgradeDbAndDeleteOldDbs.bind(this)})),this._db}},st=class{constructor(e,t={}){this._isRunning=!1,this._rerunRequested=!1,this._maxEntries=t.maxEntries,this._maxAgeSeconds=t.maxAgeSeconds,this._matchOptions=t.matchOptions,this._cacheName=e,this._timestampModel=new at(e)}async expireEntries(){if(this._isRunning){this._rerunRequested=!0;return}this._isRunning=!0;let e=this._maxAgeSeconds?Date.now()-this._maxAgeSeconds*1e3:0,t=await this._timestampModel.expireEntries(e,this._maxEntries),a=await self.caches.open(this._cacheName);for(let s of t)await a.delete(s,this._matchOptions);this._isRunning=!1,this._rerunRequested&&(this._rerunRequested=!1,ae(this.expireEntries()))}async updateTimestamp(e){await this._timestampModel.setTimestamp(e,Date.now())}async isURLExpired(e){if(this._maxAgeSeconds){let t=await this._timestampModel.getTimestamp(e),a=Date.now()-this._maxAgeSeconds*1e3;return t!==void 0?t<a:!0}else return!1}async delete(){this._rerunRequested=!1,await this._timestampModel.expireEntries(1/0)}},de=class{constructor(e={}){this.cachedResponseWillBeUsed=async({event:t,request:a,cacheName:s,cachedResponse:r})=>{if(!r)return null;let n=this._isResponseDateFresh(r),i=this._getCacheExpiration(s);ae(i.expireEntries());let o=i.updateTimestamp(a.url);if(t)try{t.waitUntil(o)}catch{}return n?r:null},this.cacheDidUpdate=async({cacheName:t,request:a})=>{let s=this._getCacheExpiration(t);await s.updateTimestamp(a.url),await s.expireEntries()},this._config=e,this._maxAgeSeconds=e.maxAgeSeconds,this._cacheExpirations=new Map,e.purgeOnQuotaError&&Oe(()=>this.deleteCacheAndMetadata())}_getCacheExpiration(e){if(e===f.getRuntimeName())throw new d("expire-custom-caches-only");let t=this._cacheExpirations.get(e);return t||(t=new st(e,this._config),this._cacheExpirations.set(e,t)),t}_isResponseDateFresh(e){if(!this._maxAgeSeconds)return!0;let t=this._getDateHeaderTimestamp(e);if(t===null)return!0;let a=Date.now();return t>=a-this._maxAgeSeconds*1e3}_getDateHeaderTimestamp(e){if(!e.headers.has("date"))return null;let t=e.headers.get("date"),a=new Date(t).getTime();return isNaN(a)?null:a}async deleteCacheAndMetadata(){for(let[e,t]of this._cacheExpirations)await self.caches.delete(e),await t.delete();this._cacheExpirations=new Map}};try{self["workbox:background-sync:7.2.0"]&&_()}catch{}var fe=3,rt="workbox-background-sync",y="requests",E="queueName",nt=class{constructor(){this._db=null}async addEntry(e){let t=(await this.getDb()).transaction(y,"readwrite",{durability:"relaxed"});await t.store.add(e),await t.done}async getFirstEntryId(){return(await(await this.getDb()).transaction(y).store.openCursor())?.value.id}async getAllEntriesByQueueName(e){return await(await this.getDb()).getAllFromIndex(y,E,IDBKeyRange.only(e))||new Array}async getEntryCountByQueueName(e){return(await this.getDb()).countFromIndex(y,E,IDBKeyRange.only(e))}async deleteEntry(e){await(await this.getDb()).delete(y,e)}async getFirstEntryByQueueName(e){return await this.getEndEntryFromIndex(IDBKeyRange.only(e),"next")}async getLastEntryByQueueName(e){return await this.getEndEntryFromIndex(IDBKeyRange.only(e),"prev")}async getEndEntryFromIndex(e,t){return(await(await this.getDb()).transaction(y).store.index(E).openCursor(e,t))?.value}async getDb(){return this._db||(this._db=await le(rt,fe,{upgrade:this._upgradeDb})),this._db}_upgradeDb(e,t){t>0&&t<fe&&e.objectStoreNames.contains(y)&&e.deleteObjectStore(y),e.createObjectStore(y,{autoIncrement:!0,keyPath:"id"}).createIndex(E,E,{unique:!1})}},it=class{constructor(e){this._queueName=e,this._queueDb=new nt}async pushEntry(e){delete e.id,e.queueName=this._queueName,await this._queueDb.addEntry(e)}async unshiftEntry(e){let t=await this._queueDb.getFirstEntryId();t?e.id=t-1:delete e.id,e.queueName=this._queueName,await this._queueDb.addEntry(e)}async popEntry(){return this._removeEntry(await this._queueDb.getLastEntryByQueueName(this._queueName))}async shiftEntry(){return this._removeEntry(await this._queueDb.getFirstEntryByQueueName(this._queueName))}async getAll(){return await this._queueDb.getAllEntriesByQueueName(this._queueName)}async size(){return await this._queueDb.getEntryCountByQueueName(this._queueName)}async deleteEntry(e){await this._queueDb.deleteEntry(e)}async _removeEntry(e){return e&&await this.deleteEntry(e.id),e}},ot=["method","referrer","referrerPolicy","mode","credentials","cache","redirect","integrity","keepalive"],pe=class Z{static async fromRequest(t){let a={url:t.url,headers:{}};t.method!=="GET"&&(a.body=await t.clone().arrayBuffer());for(let[s,r]of t.headers.entries())a.headers[s]=r;for(let s of ot)t[s]!==void 0&&(a[s]=t[s]);return new Z(a)}constructor(t){t.mode==="navigate"&&(t.mode="same-origin"),this._requestData=t}toObject(){let t=Object.assign({},this._requestData);return t.headers=Object.assign({},this._requestData.headers),t.body&&(t.body=t.body.slice(0)),t}toRequest(){return new Request(this._requestData.url,this._requestData)}clone(){return new Z(this.toObject())}},me="workbox-background-sync",ct=60*24*7,F=new Set,we=e=>{let t={request:new pe(e.requestData).toRequest(),timestamp:e.timestamp};return e.metadata&&(t.metadata=e.metadata),t},lt=class{constructor(e,{forceSyncFallback:t,onSync:a,maxRetentionTime:s}={}){if(this._syncInProgress=!1,this._requestsAddedDuringSync=!1,F.has(e))throw new d("duplicate-queue-name",{name:e});F.add(e),this._name=e,this._onSync=a||this.replayRequests,this._maxRetentionTime=s||ct,this._forceSyncFallback=!!t,this._queueStore=new it(this._name),this._addSyncListener()}get name(){return this._name}async pushRequest(e){await this._addRequest(e,"push")}async unshiftRequest(e){await this._addRequest(e,"unshift")}async popRequest(){return this._removeRequest("pop")}async shiftRequest(){return this._removeRequest("shift")}async getAll(){let e=await this._queueStore.getAll(),t=Date.now(),a=[];for(let s of e){let r=this._maxRetentionTime*60*1e3;t-s.timestamp>r?await this._queueStore.deleteEntry(s.id):a.push(we(s))}return a}async size(){return await this._queueStore.size()}async _addRequest({request:e,metadata:t,timestamp:a=Date.now()},s){let r={requestData:(await pe.fromRequest(e.clone())).toObject(),timestamp:a};switch(t&&(r.metadata=t),s){case"push":await this._queueStore.pushEntry(r);break;case"unshift":await this._queueStore.unshiftEntry(r);break}this._syncInProgress?this._requestsAddedDuringSync=!0:await this.registerSync()}async _removeRequest(e){let t=Date.now(),a;switch(e){case"pop":a=await this._queueStore.popEntry();break;case"shift":a=await this._queueStore.shiftEntry();break}if(a){let s=this._maxRetentionTime*60*1e3;return t-a.timestamp>s?this._removeRequest(e):we(a)}else return}async replayRequests(){let e;for(;e=await this.shiftRequest();)try{await fetch(e.request.clone())}catch{throw await this.unshiftRequest(e),new d("queue-replay-failed",{name:this._name})}}async registerSync(){if("sync"in self.registration&&!this._forceSyncFallback)try{await self.registration.sync.register(`${me}:${this._name}`)}catch{}}_addSyncListener(){"sync"in self.registration&&!this._forceSyncFallback?self.addEventListener("sync",e=>{if(e.tag===`${me}:${this._name}`){let t=async()=>{this._syncInProgress=!0;let a;try{await this._onSync({queue:this})}catch(s){if(s instanceof Error)throw a=s,a}finally{this._requestsAddedDuringSync&&!(a&&!e.lastChance)&&await this.registerSync(),this._syncInProgress=!1,this._requestsAddedDuringSync=!1}};e.waitUntil(t())}}):this._onSync({queue:this})}static get _queueNames(){return F}},ut=class{constructor(e,t){this.fetchDidFail=async({request:a})=>{await this._queue.pushRequest({request:a})},this._queue=new lt(e,t)}};try{self["workbox:routing:7.2.0"]&&_()}catch{}var ye="GET",L=e=>e&&typeof e=="object"?e:{handle:e},p=class{constructor(e,t,a=ye){this.handler=L(t),this.match=e,this.method=a}setCatchHandler(e){this.catchHandler=L(e)}},ge=class{constructor(){this._routes=new Map,this._defaultHandlerMap=new Map}get routes(){return this._routes}addFetchListener(){self.addEventListener("fetch",e=>{let{request:t}=e,a=this.handleRequest({request:t,event:e});a&&e.respondWith(a)})}addCacheListener(){self.addEventListener("message",e=>{if(e.data&&e.data.type==="CACHE_URLS"){let{payload:t}=e.data,a=Promise.all(t.urlsToCache.map(s=>{typeof s=="string"&&(s=[s]);let r=new Request(...s);return this.handleRequest({request:r,event:e})}));e.waitUntil(a),e.ports&&e.ports[0]&&a.then(()=>e.ports[0].postMessage(!0))}})}handleRequest({request:e,event:t}){let a=new URL(e.url,location.href);if(!a.protocol.startsWith("http"))return;let s=a.origin===location.origin,{params:r,route:n}=this.findMatchingRoute({event:t,request:e,sameOrigin:s,url:a}),i=n&&n.handler,o=[],c=e.method;if(!i&&this._defaultHandlerMap.has(c)&&(i=this._defaultHandlerMap.get(c)),!i)return;let l;try{l=i.handle({url:a,request:e,event:t,params:r})}catch(u){l=Promise.reject(u)}let h=n&&n.catchHandler;return l instanceof Promise&&(this._catchHandler||h)&&(l=l.catch(async u=>{if(h)try{return await h.handle({url:a,request:e,event:t,params:r})}catch(q){q instanceof Error&&(u=q)}if(this._catchHandler)return this._catchHandler.handle({url:a,request:e,event:t});throw u})),l}findMatchingRoute({url:e,sameOrigin:t,request:a,event:s}){let r=this._routes.get(a.method)||[];for(let n of r){let i,o=n.match({url:e,sameOrigin:t,request:a,event:s});if(o)return i=o,(Array.isArray(i)&&i.length===0||o.constructor===Object&&Object.keys(o).length===0||typeof o=="boolean")&&(i=void 0),{route:n,params:i}}return{}}setDefaultHandler(e,t=ye){this._defaultHandlerMap.set(t,L(e))}setCatchHandler(e){this._catchHandler=L(e)}registerRoute(e){this._routes.has(e.method)||this._routes.set(e.method,[]),this._routes.get(e.method).push(e)}unregisterRoute(e){if(!this._routes.has(e.method))throw new d("unregister-route-but-not-found-with-method",{method:e.method});let t=this._routes.get(e.method).indexOf(e);if(t>-1)this._routes.get(e.method).splice(t,1);else throw new d("unregister-route-route-not-registered")}};try{self["workbox:strategies:7.2.0"]&&_()}catch{}var _e={cacheWillUpdate:async({response:e})=>e.status===200||e.status===0?e:null};function U(e){return typeof e=="string"?new Request(e):e}var ht=class{constructor(e,t){this._cacheKeys={},Object.assign(this,t),this.event=t.event,this._strategy=e,this._handlerDeferred=new Ke,this._extendLifetimePromises=[],this._plugins=[...e.plugins],this._pluginStateMap=new Map;for(let a of this._plugins)this._pluginStateMap.set(a,{});this.event.waitUntil(this._handlerDeferred.promise)}async fetch(e){let{event:t}=this,a=U(e);if(a.mode==="navigate"&&t instanceof FetchEvent&&t.preloadResponse){let n=await t.preloadResponse;if(n)return n}let s=this.hasCallback("fetchDidFail")?a.clone():null;try{for(let n of this.iterateCallbacks("requestWillFetch"))a=await n({request:a.clone(),event:t})}catch(n){if(n instanceof Error)throw new d("plugin-error-request-will-fetch",{thrownErrorMessage:n.message})}let r=a.clone();try{let n;n=await fetch(a,a.mode==="navigate"?void 0:this._strategy.fetchOptions);for(let i of this.iterateCallbacks("fetchDidSucceed"))n=await i({event:t,request:r,response:n});return n}catch(n){throw s&&await this.runCallbacks("fetchDidFail",{error:n,event:t,originalRequest:s.clone(),request:r.clone()}),n}}async fetchAndCachePut(e){let t=await this.fetch(e),a=t.clone();return this.waitUntil(this.cachePut(e,a)),t}async cacheMatch(e){let t=U(e),a,{cacheName:s,matchOptions:r}=this._strategy,n=await this.getCacheKey(t,"read"),i=Object.assign(Object.assign({},r),{cacheName:s});a=await caches.match(n,i);for(let o of this.iterateCallbacks("cachedResponseWillBeUsed"))a=await o({cacheName:s,matchOptions:r,cachedResponse:a,request:n,event:this.event})||void 0;return a}async cachePut(e,t){let a=U(e);await se(0);let s=await this.getCacheKey(a,"write");if(!t)throw new d("cache-put-with-no-response",{url:Pe(s.url)});let r=await this._ensureResponseSafeToCache(t);if(!r)return!1;let{cacheName:n,matchOptions:i}=this._strategy,o=await self.caches.open(n),c=this.hasCallback("cacheDidUpdate"),l=c?await je(o,s.clone(),["__WB_REVISION__"],i):null;try{await o.put(s,c?r.clone():r)}catch(h){if(h instanceof Error)throw h.name==="QuotaExceededError"&&await Be(),h}for(let h of this.iterateCallbacks("cacheDidUpdate"))await h({cacheName:n,oldResponse:l,newResponse:r.clone(),request:s,event:this.event});return!0}async getCacheKey(e,t){let a=`${e.url} | ${t}`;if(!this._cacheKeys[a]){let s=e;for(let r of this.iterateCallbacks("cacheKeyWillBeUsed"))s=U(await r({mode:t,request:s,event:this.event,params:this.params}));this._cacheKeys[a]=s}return this._cacheKeys[a]}hasCallback(e){for(let t of this._strategy.plugins)if(e in t)return!0;return!1}async runCallbacks(e,t){for(let a of this.iterateCallbacks(e))await a(t)}*iterateCallbacks(e){for(let t of this._strategy.plugins)if(typeof t[e]=="function"){let a=this._pluginStateMap.get(t);yield s=>{let r=Object.assign(Object.assign({},s),{state:a});return t[e](r)}}}waitUntil(e){return this._extendLifetimePromises.push(e),e}async doneWaiting(){let e;for(;e=this._extendLifetimePromises.shift();)await e}destroy(){this._handlerDeferred.resolve(null)}async _ensureResponseSafeToCache(e){let t=e,a=!1;for(let s of this.iterateCallbacks("cacheWillUpdate"))if(t=await s({request:this.request,response:t,event:this.event})||void 0,a=!0,!t)break;return a||t&&t.status!==200&&(t=void 0),t}},T=class{constructor(e={}){this.cacheName=f.getRuntimeName(e.cacheName),this.plugins=e.plugins||[],this.fetchOptions=e.fetchOptions,this.matchOptions=e.matchOptions}handle(e){let[t]=this.handleAll(e);return t}handleAll(e){e instanceof FetchEvent&&(e={event:e,request:e.request});let t=e.event,a=typeof e.request=="string"?new Request(e.request):e.request,s="params"in e?e.params:void 0,r=new ht(this,{event:t,request:a,params:s}),n=this._getResponse(r,a,t),i=this._awaitComplete(n,r,a,t);return[n,i]}async _getResponse(e,t,a){await e.runCallbacks("handlerWillStart",{event:a,request:t});let s;try{if(s=await this._handle(t,e),!s||s.type==="error")throw new d("no-response",{url:t.url})}catch(r){if(r instanceof Error){for(let n of e.iterateCallbacks("handlerDidError"))if(s=await n({error:r,event:a,request:t}),s)break}if(!s)throw r}for(let r of e.iterateCallbacks("handlerWillRespond"))s=await r({event:a,request:t,response:s});return s}async _awaitComplete(e,t,a,s){let r,n;try{r=await e}catch{}try{await t.runCallbacks("handlerDidRespond",{event:s,request:a,response:r}),await t.doneWaiting()}catch(i){i instanceof Error&&(n=i)}if(await t.runCallbacks("handlerDidComplete",{event:s,request:a,response:r,error:n}),t.destroy(),n)throw n}},P=class extends T{constructor(e={}){super(e),this.plugins.some(t=>"cacheWillUpdate"in t)||this.plugins.unshift(_e),this._networkTimeoutSeconds=e.networkTimeoutSeconds||0}async _handle(e,t){let a=[],s=[],r;if(this._networkTimeoutSeconds){let{id:o,promise:c}=this._getTimeoutPromise({request:e,logs:a,handler:t});r=o,s.push(c)}let n=this._getNetworkPromise({timeoutId:r,request:e,logs:a,handler:t});s.push(n);let i=await t.waitUntil((async()=>await t.waitUntil(Promise.race(s))||await n)());if(!i)throw new d("no-response",{url:e.url});return i}_getTimeoutPromise({request:e,logs:t,handler:a}){let s;return{promise:new Promise(r=>{s=setTimeout(async()=>{r(await a.cacheMatch(e))},this._networkTimeoutSeconds*1e3)}),id:s}}async _getNetworkPromise({timeoutId:e,request:t,logs:a,handler:s}){let r,n;try{n=await s.fetchAndCachePut(t)}catch(i){i instanceof Error&&(r=i)}return e&&clearTimeout(e),(r||!n)&&(n=await s.cacheMatch(t)),n}},dt=class extends T{constructor(e={}){super(e),this._networkTimeoutSeconds=e.networkTimeoutSeconds||0}async _handle(e,t){let a,s;try{let r=[t.fetch(e)];if(this._networkTimeoutSeconds){let n=se(this._networkTimeoutSeconds*1e3);r.push(n)}if(s=await Promise.race(r),!s)throw new Error(`Timed out the network response after ${this._networkTimeoutSeconds} seconds.`)}catch(r){r instanceof Error&&(a=r)}if(!s)throw new d("no-response",{url:e.url,error:a});return s}};try{self["workbox:google-analytics:7.2.0"]&&_()}catch{}var ft="workbox-google-analytics",pt=60*48,ve="www.google-analytics.com",be="www.googletagmanager.com",mt="/analytics.js",wt="/gtag/js",yt="/gtm.js",gt=/^\/(\w+\/)?collect/,_t=e=>async({queue:t})=>{let a;for(;a=await t.shiftRequest();){let{request:s,timestamp:r}=a,n=new URL(s.url);try{let i=s.method==="POST"?new URLSearchParams(await s.clone().text()):n.searchParams,o=r-(Number(i.get("qt"))||0),c=Date.now()-o;if(i.set("qt",String(c)),e.parameterOverrides)for(let l of Object.keys(e.parameterOverrides)){let h=e.parameterOverrides[l];i.set(l,h)}typeof e.hitFilter=="function"&&e.hitFilter.call(null,i),await fetch(new Request(n.origin+n.pathname,{body:i.toString(),method:"POST",mode:"cors",credentials:"omit",headers:{"Content-Type":"text/plain"}}))}catch(i){throw await t.unshiftRequest(a),i}}},vt=e=>{let t=({url:s})=>s.hostname===ve&&gt.test(s.pathname),a=new dt({plugins:[e]});return[new p(t,a,"GET"),new p(t,a,"POST")]},bt=e=>{let t=({url:s})=>s.hostname===ve&&s.pathname===mt,a=new P({cacheName:e});return new p(t,a,"GET")},qt=e=>{let t=({url:s})=>s.hostname===be&&s.pathname===wt,a=new P({cacheName:e});return new p(t,a,"GET")},Rt=e=>{let t=({url:s})=>s.hostname===be&&s.pathname===yt,a=new P({cacheName:e});return new p(t,a,"GET")},xt=(e={})=>{let t=f.getGoogleAnalyticsName(e.cacheName),a=new ut(ft,{maxRetentionTime:pt,onSync:_t(e)}),s=[Rt(t),bt(t),qt(t),...vt(a)],r=new ge;for(let n of s)r.registerRoute(n);r.addFetchListener()};try{self["workbox:precaching:7.2.0"]&&_()}catch{}var Et="__WB_REVISION__";function kt(e){if(!e)throw new d("add-to-cache-list-unexpected-type",{entry:e});if(typeof e=="string"){let n=new URL(e,location.href);return{cacheKey:n.href,url:n.href}}let{revision:t,url:a}=e;if(!a)throw new d("add-to-cache-list-unexpected-type",{entry:e});if(!t){let n=new URL(a,location.href);return{cacheKey:n.href,url:n.href}}let s=new URL(a,location.href),r=new URL(a,location.href);return s.searchParams.set(Et,t),{cacheKey:s.href,url:r.href}}var Dt=class{constructor(){this.updatedURLs=[],this.notUpdatedURLs=[],this.handlerWillStart=async({request:e,state:t})=>{t&&(t.originalRequest=e)},this.cachedResponseWillBeUsed=async({event:e,state:t,cachedResponse:a})=>{if(e.type==="install"&&t&&t.originalRequest&&t.originalRequest instanceof Request){let s=t.originalRequest.url;a?this.notUpdatedURLs.push(s):this.updatedURLs.push(s)}return a}}},Ct=class{constructor({precacheController:e}){this.cacheKeyWillBeUsed=async({request:t,params:a})=>{let s=a?.cacheKey||this._precacheController.getCacheKeyForURL(t.url);return s?new Request(s,{headers:t.headers}):t},this._precacheController=e}},H=class C extends T{constructor(t={}){t.cacheName=f.getPrecacheName(t.cacheName),super(t),this._fallbackToNetwork=t.fallbackToNetwork!==!1,this.plugins.push(C.copyRedirectedCacheableResponsesPlugin)}async _handle(t,a){return await a.cacheMatch(t)||(a.event&&a.event.type==="install"?await this._handleInstall(t,a):await this._handleFetch(t,a))}async _handleFetch(t,a){let s,r=a.params||{};if(this._fallbackToNetwork){let n=r.integrity,i=t.integrity,o=!i||i===n;if(s=await a.fetch(new Request(t,{integrity:t.mode!=="no-cors"?i||n:void 0})),n&&o&&t.mode!=="no-cors"){this._useDefaultCacheabilityPluginIfNeeded();let c=await a.cachePut(t,s.clone())}}else throw new d("missing-precache-entry",{cacheName:this.cacheName,url:t.url});return s}async _handleInstall(t,a){this._useDefaultCacheabilityPluginIfNeeded();let s=await a.fetch(t);if(!await a.cachePut(t,s.clone()))throw new d("bad-precaching-response",{url:t.url,status:s.status});return s}_useDefaultCacheabilityPluginIfNeeded(){let t=null,a=0;for(let[s,r]of this.plugins.entries())r!==C.copyRedirectedCacheableResponsesPlugin&&(r===C.defaultPrecacheCacheabilityPlugin&&(t=s),r.cacheWillUpdate&&a++);a===0?this.plugins.push(C.defaultPrecacheCacheabilityPlugin):a>1&&t!==null&&this.plugins.splice(t,1)}};H.defaultPrecacheCacheabilityPlugin={async cacheWillUpdate({response:e}){return!e||e.status>=400?null:e}},H.copyRedirectedCacheableResponsesPlugin={async cacheWillUpdate({response:e}){return e.redirected?await We(e):e}};var St=class{constructor({cacheName:e,plugins:t=[],fallbackToNetwork:a=!0}={}){this._urlsToCacheKeys=new Map,this._urlsToCacheModes=new Map,this._cacheKeysToIntegrities=new Map,this._strategy=new H({cacheName:f.getPrecacheName(e),plugins:[...t,new Ct({precacheController:this})],fallbackToNetwork:a}),this.install=this.install.bind(this),this.activate=this.activate.bind(this)}get strategy(){return this._strategy}precache(e){this.addToCacheList(e),this._installAndActiveListenersAdded||(self.addEventListener("install",this.install),self.addEventListener("activate",this.activate),this._installAndActiveListenersAdded=!0)}addToCacheList(e){let t=[];for(let a of e){typeof a=="string"?t.push(a):a&&a.revision===void 0&&t.push(a.url);let{cacheKey:s,url:r}=kt(a),n=typeof a!="string"&&a.revision?"reload":"default";if(this._urlsToCacheKeys.has(r)&&this._urlsToCacheKeys.get(r)!==s)throw new d("add-to-cache-list-conflicting-entries",{firstEntry:this._urlsToCacheKeys.get(r),secondEntry:s});if(typeof a!="string"&&a.integrity){if(this._cacheKeysToIntegrities.has(s)&&this._cacheKeysToIntegrities.get(s)!==a.integrity)throw new d("add-to-cache-list-conflicting-integrities",{url:r});this._cacheKeysToIntegrities.set(s,a.integrity)}if(this._urlsToCacheKeys.set(r,s),this._urlsToCacheModes.set(r,n),t.length>0){let i=`Workbox is precaching URLs without revision info: ${t.join(", ")}
This is generally NOT safe. Learn more at https://bit.ly/wb-precache`;console.warn(i)}}}install(e){return re(e,async()=>{let t=new Dt;this.strategy.plugins.push(t);for(let[r,n]of this._urlsToCacheKeys){let i=this._cacheKeysToIntegrities.get(n),o=this._urlsToCacheModes.get(r),c=new Request(r,{integrity:i,cache:o,credentials:"same-origin"});await Promise.all(this.strategy.handleAll({params:{cacheKey:n},request:c,event:e}))}let{updatedURLs:a,notUpdatedURLs:s}=t;return{updatedURLs:a,notUpdatedURLs:s}})}activate(e){return re(e,async()=>{let t=await self.caches.open(this.strategy.cacheName),a=await t.keys(),s=new Set(this._urlsToCacheKeys.values()),r=[];for(let n of a)s.has(n.url)||(await t.delete(n),r.push(n.url));return{deletedURLs:r}})}getURLsToCacheKeys(){return this._urlsToCacheKeys}getCachedURLs(){return[...this._urlsToCacheKeys.keys()]}getCacheKeyForURL(e){let t=new URL(e,location.href);return this._urlsToCacheKeys.get(t.href)}getIntegrityForCacheKey(e){return this._cacheKeysToIntegrities.get(e)}async matchPrecache(e){let t=e instanceof Request?e.url:e,a=this.getCacheKeyForURL(t);if(a)return(await self.caches.open(this.strategy.cacheName)).match(a)}createHandlerBoundToURL(e){let t=this.getCacheKeyForURL(e);if(!t)throw new d("non-precached-url",{url:e});return a=>(a.request=new Request(e),a.params=Object.assign({cacheKey:t},a.params),this.strategy.handle(a))}},$,qe=()=>($||($=new St),$),Nt=class extends p{constructor(e,t,a){let s=({url:r})=>{let n=e.exec(r.href);if(n&&!(r.origin!==location.origin&&n.index!==0))return n.slice(1)};super(s,t,a)}},k,Lt=()=>(k||(k=new ge,k.addFetchListener(),k.addCacheListener()),k);function D(e,t,a){let s;if(typeof e=="string"){let r=new URL(e,location.href),n=({url:i})=>i.href===r.href;s=new p(n,t,a)}else if(e instanceof RegExp)s=new Nt(e,t,a);else if(typeof e=="function")s=new p(e,t,a);else if(e instanceof p)s=e;else throw new d("unsupported-route-type",{moduleName:"workbox-routing",funcName:"registerRoute",paramName:"capture"});return Lt().registerRoute(s),s}function Ut(e,t=[]){for(let a of[...e.searchParams.keys()])t.some(s=>s.test(a))&&e.searchParams.delete(a);return e}function*Tt(e,{ignoreURLParametersMatching:t=[/^utm_/,/^fbclid$/],directoryIndex:a="index.html",cleanURLs:s=!0,urlManipulation:r}={}){let n=new URL(e,location.href);n.hash="",yield n.href;let i=Ut(n,t);if(yield i.href,a&&i.pathname.endsWith("/")){let o=new URL(i.href);o.pathname+=a,yield o.href}if(s){let o=new URL(i.href);o.pathname+=".html",yield o.href}if(r){let o=r({url:n});for(let c of o)yield c.href}}var Pt=class extends p{constructor(e,t){let a=({request:s})=>{let r=e.getURLsToCacheKeys();for(let n of Tt(s.url,t)){let i=r.get(n);if(i){let o=e.getIntegrityForCacheKey(i);return{cacheKey:i,integrity:o}}}};super(a,e.strategy)}};function It(e){let t=qe(),a=new Pt(t,e);D(a)}function Ot(e){qe().precache(e)}function At(e,t){Ot(e),It(t)}var Q=class extends T{constructor(e={}){super(e),this.plugins.some(t=>"cacheWillUpdate"in t)||this.plugins.unshift(_e)}async _handle(e,t){let a=[],s=t.fetchAndCachePut(e).catch(()=>{});t.waitUntil(s);let r=await t.cacheMatch(e),n;if(!r)try{r=await s}catch(i){i instanceof Error&&(n=i)}if(!r)throw new d("no-response",{url:e.url,error:n});return r}},Re=function e(t){function a(r,n,i){var o,c={};if(Array.isArray(r))return r.concat(n);for(o in r)c[i?o.toLowerCase():o]=r[o];for(o in n){var l=i?o.toLowerCase():o,h=n[o];c[l]=l in c&&typeof h=="object"?a(c[l],h,l=="headers"):h}return c}function s(r,n,i,o,c){var l=typeof r!="string"?(n=r).url:r,h={config:n},u=a(t,n),q={};o=o||u.data,(u.transformRequest||[]).map(function(g){o=g(o,u.headers)||o}),u.auth&&(q.authorization=u.auth),o&&typeof o=="object"&&typeof o.append!="function"&&typeof o.text!="function"&&(o=JSON.stringify(o),q["content-type"]="application/json");try{q[u.xsrfHeaderName]=decodeURIComponent(document.cookie.match(RegExp("(^|; )"+u.xsrfCookieName+"=([^;]*)"))[2])}catch{}return u.baseURL&&(l=l.replace(/^(?!.*\/\/)\/?/,u.baseURL+"/")),u.params&&(l+=(~l.indexOf("?")?"&":"?")+(u.paramsSerializer?u.paramsSerializer(u.params):new URLSearchParams(u.params))),(u.fetch||fetch)(l,{method:(i||u.method||"get").toUpperCase(),body:o,headers:a(u.headers,q,!0),credentials:u.withCredentials?"include":c}).then(function(g){for(var Y in g)typeof g[Y]!="function"&&(h[Y]=g[Y]);return u.responseType=="stream"?(h.data=g.body,h):g[u.responseType||"text"]().then(function(Le){h.data=Le,h.data=JSON.parse(Le)}).catch(Object).then(function(){return(u.validateStatus?u.validateStatus(g.status):g.ok)?h:Promise.reject(h)})})}return t=t||{},s.request=s,s.get=function(r,n){return s(r,n,"get")},s.delete=function(r,n){return s(r,n,"delete")},s.head=function(r,n){return s(r,n,"head")},s.options=function(r,n){return s(r,n,"options")},s.post=function(r,n,i){return s(r,i,"post",n)},s.put=function(r,n,i){return s(r,i,"put",n)},s.patch=function(r,n,i){return s(r,i,"patch",n)},s.all=Promise.all.bind(Promise),s.spread=function(r){return r.apply.bind(r,r)},s.CancelToken=typeof AbortController=="function"?AbortController:Object,s.defaults=t,s.create=e,s}();function xe(){if(typeof crypto<"u"&&typeof crypto.randomUUID=="function")return crypto.randomUUID();let e="",t,a;for(t=0;t<32;t+=1)a=Math.random()*16|0,(t===8||t===12||t===16||t===20)&&(e+="-"),e+=(t===12?4:t===16?a&3|8:a).toString(16);return e}var v={};self.crashMonitorStore=v;var jt=xe();async function Mt(e,t){let a=xe(),{custom:s,reportParams:r}=t;if(!r)return;let n={type:"p_crash",uuid:a,ctwid:r.ctwid,appid:r.appid,time:`${Date.now()}`,page:r.page,payload:{sessionId:e,sessionDuration:Math.round(Date.now()/1e3-s.issuedAt)}};Re.post("/reports",n)}async function Kt(e,t){console.info(`SW[${jt}] checkCrash SendReport`,e,t),Re.post("/api/crash",{sessionId:e,custom:t.custom}),t.reportParams&&Mt(e,t)}function Bt(e,t,a){let s=v[e]||{};v[e]={lastSeen:Date.now(),custom:s.custom,reportParams:a||v[e].reportParams},t&&(v[e].custom=t)}function Ee(e){delete v[e]}function Wt(){let e=Object.entries(v),t=Date.now();for(let[a,s]of e)t-s.lastSeen>2e3&&(Kt(a,s),Ee(a))}async function ke(){let e=await self.clients.matchAll(),t={type:"sw/heartbeat",kind:"ping"};for(let a of e)a.postMessage(t)}function Ft(e){return!!e&&e.type==="sw/heartbeat"}var I=!1,V,G;self.addEventListener("message",e=>{if(!I||!e?.data||!e.source||!Ft(e.data))return;let{data:t}=e;if(t.action.kind==="mount")Bt(t.action.sessionId,t.action.custom,t.action.reportParams);else if(t.action.kind==="unmount")Ee(t.action.sessionId);else throw new Error(`Unknown action.kind ${t.action}`)}),self.addEventListener("activate",()=>{I&&ke()});function Ht(){I||(V&&clearInterval(V),V=setInterval(Wt,3e3),G&&clearInterval(G),G=setInterval(ke,1e3),I=!0)}function z(e){return new Promise(function(t,a){e.oncomplete=e.onsuccess=function(){return t(e.result)},e.onabort=e.onerror=function(){return a(e.error)}})}function $t(e,t){var a,s=function(){if(a)return a;var r=indexedDB.open(e);return r.onupgradeneeded=function(){return r.result.createObjectStore(t)},a=z(r),a.then(function(n){n.onclose=function(){return a=void 0}},function(){}),a};return function(r,n){return s().then(function(i){return n(i.transaction(t,r).objectStore(t))})}}var J;function De(){return J||(J=$t("keyval-store","keyval")),J}function Qt(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:De();return t("readonly",function(a){return z(a.get(e))})}function Vt(e,t){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:De();return a("readwrite",function(s){return s.put(t,e),z(s.transaction)})}var Ce=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce((t,a)=>(a&=63,a<36?t+=a.toString(36):a<62?t+=(a-26).toString(36).toUpperCase():a>62?t+="-":t+="_",t),""),b;async function Se(){console.log("[service-worker-sw] initsw");try{b=await Qt("swid"),b||(b=`swid_${Ce()}`,await Vt("swid",b))}catch(e){b=Ce(),console.error(`[service-worker-sw] initsw error: ${e}`)}return b}async function Gt(){return b||Se()}var zt="v2",X={cachedResponseWillBeUsed:async e=>{let{cachedResponse:t}=e;if(!t)return null;let a=e.event?.request||e.request;return a.mode==="cors"&&t.type==="opaque"?(console.warn("[service-worker-sw] Mode mismatch",a.url,"request.mode",a.mode,"cachedResponse.type",t.type,e),null):t}};Fe({prefix:"g123",suffix:zt,precache:"precache",runtime:"runtime",googleAnalytics:"google-analytics"});var Ne=e=>[N.prefix,e,N.suffix].filter(t=>t&&t.length>0).join("-"),Jt=Ne("page"),Xt=Ne("thirdparty");async function Yt(){let e=(await caches.keys()).filter(t=>!t.startsWith(N.prefix)||!t.endsWith(N.suffix));await Promise.all(e.map(t=>(console.info("[service-worker-sw] Remove cache",t),caches.delete(t))))}xt(),At(precacheAssets||[],{ignoreURLParametersMatching:[/.*/]}),RUN_ENV!=="staging"&&D(e=>e.url.pathname===`/game/${appCode}`,new P({cacheName:Jt,networkTimeoutSeconds:3,matchOptions:{ignoreSearch:!1},fetchOptions:{credentials:"include"},plugins:[new de({maxEntries:100,purgeOnQuotaError:!0,matchOptions:{ignoreSearch:!1}}),new S({statuses:[0,200]})]})),D(e=>e.sameOrigin&&e.url.pathname.startsWith("/static/"),new Q({plugins:[X,new S({statuses:[0,200]})]})),D(e=>e.url.hostname.endsWith(".g123.jp")&&(e.url.hostname.startsWith("cdn-new.")||e.url.hostname.startsWith("platform-sc.")||e.url.hostname.startsWith("platform-ik.")),new Q({plugins:[X,new S({statuses:[0,200]})]})),D(e=>e.url.origin==="https://www.gstatic.com"||e.url.origin==="https://ajax.googleapis.com",new Q({cacheName:Xt,plugins:[X,new de({maxAgeSeconds:60*60*24*365,purgeOnQuotaError:!0}),new S({statuses:[0,200]})]})),self.addEventListener("fetch",e=>{new URL(e.request.url).pathname==="/api/v1/session"&&e.respondWith((async()=>{try{let t=new Headers;e.request.headers.forEach((r,n)=>{t.append(n,r)});let a=await Gt();t.append("x-request-swid",a);let s=new Request(e.request.url,{method:e.request.method,headers:t,mode:e.request.mode,credentials:e.request.credentials,cache:e.request.cache,redirect:e.request.redirect,referrer:e.request.referrer,integrity:e.request.integrity});return fetch(s)}catch(t){return console.error(t),new Response("Service unavailable",{status:503})}})())}),self.addEventListener("install",e=>{console.info("[service-worker-sw][event] install"),self.skipWaiting(),e.waitUntil(Promise.all([Se(),Ht()]).then(()=>{console.info("[service-worker-sw][event] install done.")}).catch(t=>{console.error("[service-worker-sw][event] install failed.",t)}))}),self.addEventListener("activate",e=>{console.info("[service-worker-sw][event] activate");let t=async()=>{try{await Yt()}catch(a){console.error(a)}console.info("[service-worker-sw][event] activate done."),await self.clients.claim().then(()=>{console.info("[service-worker-sw][event] activate clients claim done.")}).catch(a=>{console.error("[service-worker-sw][event] activate clients claim failed.",a)})};e.waitUntil(t())})})();
