/**
 * Copyright (c) 2023 Apple Inc. All rights reserved.
 * 
 * # Sign In with Apple License
 * 
 * **IMPORTANT:** This Sign In with Apple software is supplied to you by Apple Inc. ("Apple") in consideration of your agreement to the following terms, and your use, reproduction, or installation of this Apple software constitutes acceptance of these terms. If you do not agree with these terms, please do not use, reproduce or install this Apple software.
 * 
 * This software is licensed to you only for use with Sign In with Apple that you are authorized or legally permitted to embed or display on your website.
 *
 * The Sign In with Apple software is only licensed and intended for the purposes set forth above and may not be used for other purposes or in other contexts without Apple's prior written permission. For the sake of clarity, you may not and agree not to or enable others to, modify or create derivative works of the Sign In with Apple software.
 *
 * You may only use the Sign In with Apple software if you are enrolled in the Apple Developer Program.
 * 
 * Neither the name, trademarks, service marks or logos of Apple Inc. may be used to endorse or promote products, services without specific prior written permission from Apple. Except as expressly stated in this notice, no other rights or licenses, express or implied, are granted by Apple herein.
 * 
 * The Sign In with Apple software software is provided by Apple on an "AS IS" basis. APPLE MAKES NO WARRANTIES, EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION THE IMPLIED WARRANTIES OF NON-INFRINGEMENT, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE, REGARDING THE SIGN IN WITH APPLE SOFTWARE OR ITS USE AND OPERATION ALONE OR IN COMBINATION WITH YOUR PRODUCTS, SYSTEMS, OR SERVICES.  APPLE DOES NOT WARRANT THAT THE SIGN IN WITH APPLE SOFTWARE WILL MEET YOUR REQUIREMENTS, THAT THE OPERATION OF THE SIGN IN WITH APPLE SOFTWARE WILL BE UNINTERRUPTED OR ERROR-FREE, THAT DEFECTS IN THE SIGN IN WITH APPLE SOFTWARE WILL BE CORRECTED, OR THAT THE SIGN IN WITH APPLE SOFTWARE WILL BE COMPATIBLE WITH FUTURE APPLE PRODUCTS, SOFTWARE OR SERVICES. NO ORAL OR WRITTEN INFORMATION OR ADVICE GIVEN BY APPLE OR AN APPLE AUTHORIZED REPRESENTATIVE WILL CREATE A WARRANTY.
 * 
 * IN NO EVENT SHALL APPLE BE LIABLE FOR ANY DIRECT, SPECIAL, INDIRECT, INCIDENTAL OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) RELATING TO OR ARISING IN ANY WAY OUT OF THE USE, REPRODUCTION, OR INSTALLATION, OF THE SIGN IN WITH APPLE SOFTWARE BY YOU OR OTHERS, HOWEVER CAUSED AND WHETHER UNDER THEORY OF CONTRACT, TORT (INCLUDING NEGLIGENCE), STRICT LIABILITY OR OTHERWISE, EVEN IF APPLE HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. SOME JURISDICTIONS DO NOT ALLOW THE LIMITATION OF LIABILITY FOR PERSONAL INJURY, OR OF INCIDENTAL OR CONSEQUENTIAL DAMAGES, SO THIS LIMITATION MAY NOT APPLY TO YOU. In no event shall Apple's total liability to you for all damages (other than as may be required by applicable law in cases involving personal injury) exceed the amount of fifty dollars ($50.00). The foregoing limitations will apply even if the above stated remedy fails of its essential purpose.
 * 
 * **ACKNOWLEDGEMENTS:**
 * https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/acknowledgements.txt
 * 
 * v1.5.5
 */

!function(A,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((A=A||self).AppleID={})}(this,function(A){"use strict";function e(A,e,t){return e in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A}var t,n=function(A){return"[object Array]"===Object.prototype.toString.call(A)},i=function(A,e){var t="string"==typeof A?document.getElementById(A):A;if(null!==t)return t.innerHTML=e,t},o=function(A,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";("string"==typeof e||n(e))&&(t=e,e={}),t||(t="");var i="";for(var o in e)void 0!==e[o]&&e.hasOwnProperty(o)&&(i+=" "+o+'="'+e[o]+'"');return n(t)&&(t=t.join("")),"<"+A+i+">"+t+"</"+A+">"},r=function(A){var e="";for(var t in A)A[t]&&A.hasOwnProperty(t)&&(e+=" "+t+": "+A[t]+";");return e},a=function(A){return"number"!=typeof A||isNaN(A)?"100%":Math.floor(A)+"px"},l=function(A){var e=A.color,t=A.borderRadius,n=void 0===t?15:t,i=A.border,l=void 0!==i&&i,d=A.width,g=void 0===d?"100%":d,s=A.height,h=void 0===s?"100%":s,u=A.isSquare,f=void 0!==u&&u;return o("svg",{xmlns:"http://www.w3.org/2000/svg",style:r({overflow:"visible"}),width:a(g),height:a(h),viewBox:f?"0 0 50 50":void 0,preserveAspectRatio:f?"xMidYMin meet":void 0},o("rect",{width:a(g),height:a(h),ry:"".concat(n,"%"),fill:c(e),stroke:l?"black":void 0,"stroke-width":l?"1":void 0,"stroke-linecap":l?"round":void 0}))},d=function(A){return"black"===A?"#fff":"#000"},c=function(A){return"black"===A?"#000":"#fff"},g={"sign-in":{text:"Appleでサインイン",centerAlignBoundingBox:{x:0,y:-11,width:98.296875,height:14},leftAlignBoundingBox:{x:0,y:-12,width:92.5,height:15},fontFamily:"applied-button-font-1,applied-button-font-0",rtl:!1,letterSpacing:"-.022em"},continue:{text:"Appleで続ける",centerAlignBoundingBox:{x:0,y:-11,width:83.015625,height:14},leftAlignBoundingBox:{x:0,y:-12,width:75.890625,height:15},fontFamily:"applied-button-font-1,applied-button-font-0",rtl:!1,letterSpacing:"-.022em"},"sign-up":{text:"Appleでサインアップ",centerAlignBoundingBox:{x:0,y:-11,width:106.875,height:14},leftAlignBoundingBox:{x:0,y:-12,width:101.828125,height:15},fontFamily:"applied-button-font-1,applied-button-font-0",rtl:!1,letterSpacing:"-.022em"}},s=function(A){return g},h=function(A){var e=A.color,t=void 0===e?"black":e,n=A.type,i=void 0===n?"sign-in":n,c=A.border,g=void 0!==c&&c,h=A.width,u=A.height,f=A.borderRadius,w=(A.locale,s()[i]),p=function(A){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"black",t=s()[A],n=t.text,i=t.rtl,o=t.fontFamily,r=t.centerAlignBoundingBox,a=r.width,l=r.height,c=r.y,g=r.x;return'\n  <svg xmlns="http://www.w3.org/2000/svg" style="pointer-events: none; overflow: visible;" width="100%" height="100%">\n    <g>\n      <svg xmlns="http://www.w3.org/2000/svg" style="overflow: visible;" width="100%" height="50%" y="25%" viewBox="'.concat(g," ").concat(c," ").concat(a," ").concat(l,'" fill="').concat(d(e),'">\n        <defs>\n          <style>\n            ').concat('\n  @font-face {\n    font-family: "applied-button-font-1";\n    src: url(data:application/x-font-woff;charset=utf-8;base64,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) format("woff")\n  }\n\n  @font-face {\n    font-family: "applied-button-font-0";\n    src: url(data:application/x-font-woff;charset=utf-8;base64,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) format("woff")\n  }','\n          </style>\n        </defs>\n        <text font-size="12px" ').concat("0em"!==t.letterSpacing?'textLength="'.concat(a,'"'):"",' font-family="').concat(o,'" direction="').concat(i?"rtl":"ltr",'"> ').concat(n,"</text>\n      </svg>\n    </g>\n  </svg>\n  ")}(i,t),v=r({"font-synthesis":"none","-moz-font-feature-settings":"kern","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale",width:a(h),height:a(u),"min-width":"130px","max-width":"375px","min-height":"30px","max-height":"64px",position:"relative","letter-spacing":"initial"});return o("div",{style:v,role:"button",tabindex:"0","aria-label":w.text},"\n    ".concat(o("div",{style:r({"padding-right":"8%","padding-left":"8%",position:"absolute","box-sizing":"border-box",width:"100%",height:"100%"})},p),"\n    ").concat(o("div",{style:r({padding:g?"1px":void 0,width:"100%",height:"100%","box-sizing":"border-box"})},l({color:t,borderRadius:f,border:g})),"\n    "))},u=[],f=[],w=function(A,e){var t=u.indexOf(A);if(t>=0){var n=f[t];if(n)return n[e]}},p=function(A,e,t){var n=u.indexOf(A);if(n<0){var i={};i[e]=t,u.push(A),f.push(i)}else f[n]||(f[n]={}),f[n][e]=t},v=[],C=!1,Y=function(A){if(null===A)return null;var e=A.getBoundingClientRect();return{width:e.width,height:e.height}},B=function(A){return A.contentBoxSize?{width:A.contentBoxSize.inlineSize,height:A.contentBoxSize.blockSize}:{width:A.contentRect.width,height:A.contentRect.height}},b=function(A){var e,t,n=w(A,"lastScheduleResizeCheckSize"),i=n||Y(A),o=w(A,"lastKnownSize");o&&(t=i,(e=o).width===t.width&&e.height===t.height)||(w(A,"resizeCallback")(i),p(A,"lastKnownSize",i));p(A,"resizeCheckIsScheduled",!1)},x=function(A,e){p(A,"lastScheduleResizeCheckSize",e),w(A,"resizeCheckIsScheduled")||(p(A,"resizeCheckIsScheduled",!0),"function"==typeof requestAnimationFrame?window.requestAnimationFrame(function(){b(A)}):setTimeout(function(){b(A)},1e3/60))},m=function(A,e){p(A,"resizeCallback",e),w(A,"isObserved")||(p(A,"isObserved",!0),"undefined"!=typeof ResizeObserver?(t||(t=new ResizeObserver(function(A){var e=!0,t=!1,n=void 0;try{for(var i,o=A[Symbol.iterator]();!(e=(i=o.next()).done);e=!0){var r=i.value;x(r.target,B(r))}}catch(A){t=!0,n=A}finally{try{e||null==o.return||o.return()}finally{if(t)throw n}}})),t.observe(A)):C||(window.addEventListener("resize",function(){v.forEach(function(A){return x(A)})}),"undefined"!=typeof MutationObserver&&new MutationObserver(function(){v.forEach(function(A){return x(A)})}).observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0}),C=!0),v.push(A))},I={small:{height:44,width:24,logoWidth:12,path:"M12.2337427,16.9879688 C12.8896607,16.9879688 13.7118677,16.5445313 14.2014966,15.9532812 C14.6449341,15.4174609 14.968274,14.6691602 14.968274,13.9208594 C14.968274,13.8192383 14.9590357,13.7176172 14.9405591,13.6344727 C14.2107349,13.6621875 13.3330982,14.1241016 12.8065162,14.7430664 C12.3907935,15.2142188 12.012024,15.9532812 12.012024,16.7108203 C12.012024,16.8216797 12.0305005,16.9325391 12.0397388,16.9694922 C12.0859302,16.9787305 12.1598365,16.9879688 12.2337427,16.9879688 Z M9.92417241,28.1662891 C10.8202857,28.1662891 11.2175318,27.5658008 12.3353638,27.5658008 C13.4716724,27.5658008 13.721106,28.1478125 14.7188404,28.1478125 C15.6980982,28.1478125 16.3540162,27.2424609 16.972981,26.3555859 C17.6658521,25.339375 17.9522388,24.3416406 17.9707154,24.2954492 C17.9060474,24.2769727 16.0306763,23.5101953 16.0306763,21.3576758 C16.0306763,19.491543 17.5088013,18.6508594 17.5919459,18.5861914 C16.612688,17.1819727 15.1253248,17.1450195 14.7188404,17.1450195 C13.6194849,17.1450195 12.7233716,17.8101758 12.1598365,17.8101758 C11.5501099,17.8101758 10.7463794,17.1819727 9.79483648,17.1819727 C7.98413335,17.1819727 6.14571538,18.6785742 6.14571538,21.5054883 C6.14571538,23.2607617 6.8293482,25.1176563 7.67003179,26.3186328 C8.39061773,27.3348438 9.01882085,28.1662891 9.92417241,28.1662891 Z"},medium:{height:44,width:31,logoWidth:17,path:"M15.7099491,14.8846154 C16.5675461,14.8846154 17.642562,14.3048315 18.28274,13.5317864 C18.8625238,12.8312142 19.2852829,11.852829 19.2852829,10.8744437 C19.2852829,10.7415766 19.2732041,10.6087095 19.2490464,10.5 C18.2948188,10.5362365 17.1473299,11.140178 16.4588366,11.9494596 C15.9152893,12.56548 15.4200572,13.5317864 15.4200572,14.5222505 C15.4200572,14.6671964 15.4442149,14.8121424 15.4562937,14.8604577 C15.5166879,14.8725366 15.6133185,14.8846154 15.7099491,14.8846154 Z M12.6902416,29.5 C13.8618881,29.5 14.3812778,28.714876 15.8428163,28.714876 C17.3285124,28.714876 17.6546408,29.4758423 18.9591545,29.4758423 C20.2395105,29.4758423 21.0971074,28.292117 21.9063891,27.1325493 C22.8123013,25.8038779 23.1867451,24.4993643 23.2109027,24.4389701 C23.1263509,24.4148125 20.6743484,23.4122695 20.6743484,20.5979021 C20.6743484,18.1579784 22.6069612,17.0588048 22.7156707,16.974253 C21.4353147,15.1382708 19.490623,15.0899555 18.9591545,15.0899555 C17.5217737,15.0899555 16.3501271,15.9596313 15.6133185,15.9596313 C14.8161157,15.9596313 13.7652575,15.1382708 12.521138,15.1382708 C10.1536872,15.1382708 7.75,17.0950413 7.75,20.7911634 C7.75,23.0861411 8.64383344,25.513986 9.74300699,27.0842339 C10.6851558,28.4129053 11.5065162,29.5 12.6902416,29.5 Z"},large:{height:44,width:39,logoWidth:21,path:"M19.8196726,13.1384615 C20.902953,13.1384615 22.2608678,12.406103 23.0695137,11.4296249 C23.8018722,10.5446917 24.3358837,9.30883662 24.3358837,8.07298156 C24.3358837,7.9051494 24.3206262,7.73731723 24.2901113,7.6 C23.0847711,7.64577241 21.6353115,8.4086459 20.7656357,9.43089638 C20.0790496,10.2090273 19.4534933,11.4296249 19.4534933,12.6807374 C19.4534933,12.8638271 19.4840083,13.0469167 19.4992657,13.1079466 C19.5755531,13.1232041 19.6976128,13.1384615 19.8196726,13.1384615 Z M16.0053051,31.6 C17.4852797,31.6 18.1413509,30.6082645 19.9875048,30.6082645 C21.8641736,30.6082645 22.2761252,31.5694851 23.923932,31.5694851 C25.5412238,31.5694851 26.6245041,30.074253 27.6467546,28.6095359 C28.7910648,26.9312142 29.2640464,25.2834075 29.2945613,25.2071202 C29.1877591,25.1766052 26.0904927,23.9102352 26.0904927,20.3552448 C26.0904927,17.2732359 28.5316879,15.8848061 28.6690051,15.7780038 C27.0517133,13.4588684 24.5952606,13.3978385 23.923932,13.3978385 C22.1082931,13.3978385 20.6283185,14.4963764 19.6976128,14.4963764 C18.6906198,14.4963764 17.36322,13.4588684 15.7917006,13.4588684 C12.8012365,13.4588684 9.765,15.9305785 9.765,20.5993643 C9.765,23.4982835 10.8940528,26.565035 12.2824825,28.548506 C13.4725652,30.2268277 14.5100731,31.6 16.0053051,31.6 Z"}},M=function(A,e,t,n){var i=I[A],r=(i.width-i.logoWidth)/2;return o("svg",{xmlns:"http://www.w3.org/2000/svg",height:a(n),width:a(t),viewBox:"".concat(r," 0 ").concat(i.logoWidth," ").concat(i.height)},o("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},o("path",{fill:d(e),"fill-rule":"nonzero",d:i.path})))},z=function(A){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"black",t=arguments.length>3?arguments[3]:void 0,n=arguments.length>4?arguments[4]:void 0,i=s()[A],l=i.text,c=i.rtl,g=i.fontFamily,h=i.leftAlignBoundingBox,u=h.width,f=h.x;return o("svg",{xmlns:"http://www.w3.org/2000/svg",style:r({overflow:"visible"}),width:a(t),height:a(n),preserveAspectRatio:c?"xMaxYMid meet":"xMinYMid meet",viewBox:"".concat(f," ").concat(-30*.655," ").concat(u," ").concat(30),fill:"".concat(d(e))},[o("defs",o("style",'\n  @font-face {\n    font-family: "applied-button-font-1";\n    src: url(data:application/x-font-woff;charset=utf-8;base64,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) format("woff")\n  }\n\n  @font-face {\n    font-family: "applied-button-font-0";\n    src: url(data:application/x-font-woff;charset=utf-8;base64,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) format("woff")\n  }')),o("text",{"font-size":"13px",textLength:"0em"!==i.letterSpacing?u:void 0,"font-family":g,direction:c?"rtl":"ltr"},l)])},D=function(A){return"number"==typeof A&&!isNaN(A)},G=function(A,e){return e?"left"===A?"right":"left":A},y=function(A){var e=A.width,t=A.height,n=A.logoSize,i=A.labelPosition,o=A.logoPosition;(e=Math.floor(e))>375?e=375:e<130&&(e=130),(t=Math.floor(t))>64?t=64:t<30&&(t=30),i=Math.floor(i),o=Math.floor(o);var r,a,l=(a=t/(r=I[n]).height,Math.floor(r.logoWidth*a)),d=Math.floor(.5*l),c=Math.floor(.7*l),g=d+l+c,s=Math.floor(e/2);i>s?i=s:i<g&&(i=g);var h=i-c-l;o>h?o=h:o<d&&(o=d);var u=o,f=Math.floor(.08*e),w=i-u-l;return{width:e,height:t,leftMargin:u,logoWidth:l,middleMargin:w,labelWidth:e-u-f-w-l,rightMargin:f,contentWidth:e-f-u}},R=function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=A.id,n=void 0===t?"appleid-button":t,d=A.color,c=void 0===d?"black":d,g=A.type,h=void 0===g?"sign-in":g,u=A.border,f=void 0!==u&&u,w=A.width,p=void 0===w?"100%":w,v=A.height,C=void 0===v?"100%":v,B=A.borderRadius,b=void 0===B?15:B,x=A.labelPosition,I=void 0===x?0:x,R=A.logoPosition,S=void 0===R?0:R,N=A.logoSize,Z=void 0===N?"small":N,Q=A.locale,E=void 0===Q?"":Q,U=document.getElementById(n),P="100%"===p,W="100%"===C;if(P||W){var F=Y(U);p=P?F.width:p,C=W?F.height:C,m(U,function(A){!function(A,e){var t=e.width,n=e.height,i=e.logoPosition,o=e.labelPosition,r=e.logoSize,l=(e.locale,e.type),d=s()[l].rtl,c=A.firstChild.childNodes,g=c[0],h=c[1],u=y({width:t,height:n,logoSize:r,labelPosition:o,logoPosition:i});g.style.width=a(u.contentWidth),g.style.height=a(u.height),g.style["padding-".concat(G("right",d))]=a(u.rightMargin),g.style["padding-".concat(G("left",d))]=a(u.leftMargin);var f=g.childNodes,w=f[0],p=f[1],v=f[2];w.setAttribute("width",a(u.logoWidth)),w.setAttribute("height",a(u.height)),p.style.width=a(u.middleMargin),p.style.height=a(u.height),v.setAttribute("width",a(u.labelWidth)),v.setAttribute("height",a(u.height)),h.setAttribute("width",a(u.width)),h.setAttribute("height",a(u.height)),h.firstChild.setAttribute("width",a(u.width)),h.firstChild.setAttribute("height",a(u.height))}(U,{width:P?A.width:p,height:W?A.height:C,logoPosition:S,labelPosition:I,logoSize:Z,locale:E,type:h})})}var V=function(A){var t,n=A.color,i=A.type,d=A.border,c=A.width,g=A.height,h=A.borderRadius,u=A.labelPosition,f=A.logoPosition,w=A.logoSize,p=A.locale;if(!D(c)||!D(g))throw new Error("width and height have to be numbers");if(!D(u)||!D(f))throw new Error("labelPosition and logoPosition have to be numbers");var v=s()[i],C=v.rtl,Y=y({width:c,height:g,logoSize:w,labelPosition:u,logoPosition:f}),B=[M(w,n,Y.logoWidth,Y.height),o("span",{style:r({display:"inline-block",width:a(Y.middleMargin),height:a(Y.height)})}),z(i,n,p,Y.labelWidth,g)];return C&&B.reverse(),o("div",{style:r({"font-synthesis":"none","-moz-font-feature-settings":"kern","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale",position:"relative","letter-spacing":"initial"}),role:"button",tabindex:"0","aria-label":v.text},[o("div",{style:r((t={position:"absolute","box-sizing":"content-box","-webkit-box-sizing":"content-box","-moz-box-sizing":"content-box",width:a(Y.contentWidth),height:a(g)},e(t,"padding-".concat(G("right",C)),a(Y.rightMargin)),e(t,"padding-".concat(G("left",C)),a(Y.leftMargin)),t))},B),l({color:n,borderRadius:h,border:d,width:Y.width,height:Y.height})])}({color:c,type:h,border:f,width:p,height:C,borderRadius:b,labelPosition:I,logoPosition:S,logoSize:Z,locale:E});i(U,V)},S=function(A){var e=A.color,t=void 0===e?"black":e,n=A.size,i=A.border,c=void 0!==i&&i,g=A.borderRadius,h=(A.locale,s()["sign-in"]),u=function(A){return'\n  <svg xmlns="http://www.w3.org/2000/svg" style="overflow:visible" width="100%" height="100%" viewBox="6 6 44 44">\n      <g fill="none" fill-rule="evenodd">\n          <path fill="'.concat(d(A),'" fill-rule="nonzero" d="M28.2226562,20.3846154 C29.0546875,20.3846154 30.0976562,19.8048315 30.71875,19.0317864 C31.28125,18.3312142 31.6914062,17.352829 31.6914062,16.3744437 C31.6914062,16.2415766 31.6796875,16.1087095 31.65625,16 C30.7304687,16.0362365 29.6171875,16.640178 28.9492187,17.4494596 C28.421875,18.06548 27.9414062,19.0317864 27.9414062,20.0222505 C27.9414062,20.1671964 27.9648438,20.3121424 27.9765625,20.3604577 C28.0351562,20.3725366 28.1289062,20.3846154 28.2226562,20.3846154 Z M25.2929688,35 C26.4296875,35 26.9335938,34.214876 28.3515625,34.214876 C29.7929688,34.214876 30.109375,34.9758423 31.375,34.9758423 C32.6171875,34.9758423 33.4492188,33.792117 34.234375,32.6325493 C35.1132812,31.3038779 35.4765625,29.9993643 35.5,29.9389701 C35.4179688,29.9148125 33.0390625,28.9122695 33.0390625,26.0979021 C33.0390625,23.6579784 34.9140625,22.5588048 35.0195312,22.474253 C33.7773438,20.6382708 31.890625,20.5899555 31.375,20.5899555 C29.9804688,20.5899555 28.84375,21.4596313 28.1289062,21.4596313 C27.3554688,21.4596313 26.3359375,20.6382708 25.1289062,20.6382708 C22.8320312,20.6382708 20.5,22.5950413 20.5,26.2911634 C20.5,28.5861411 21.3671875,31.013986 22.4335938,32.5842339 C23.3476562,33.9129053 24.1445312,35 25.2929688,35 Z"></path>\n      </g>\n  </svg>')}(t),f=r({"font-synthesis":"none","-moz-font-feature-settings":"kern","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale",width:a(n),height:a(n),"min-width":"30px","max-width":"64px","min-height":"30px","max-height":"64px",position:"relative"});return o("div",{style:f,role:"button",tabindex:"0","aria-label":h.text},"\n    ".concat(o("div",{style:r({position:"absolute","box-sizing":"border-box",width:"100%",height:"100%"})},u),"\n    ").concat(o("div",{style:r({padding:c?"1px":void 0,width:"100%",height:"100%","box-sizing":"border-box"})},l({color:t,borderRadius:g,border:c,isSquare:!0})),"\n    "))},N=["0","0"],Z=function(){for(var A={},e=0;e<arguments.length;e+=1)for(var t=e<0||arguments.length<=e?void 0:arguments[e],n=Object.keys(t),i=0;i<n.length;i+=1){var o=n[i];A[o]=t[o]}return A},Q={isInit:!1},E={baseURI:"https://appleid.apple.com",path:"/auth/authorize",originURI:"",env:"prod",usePopup:!1,responseType:"code id_token",responseMode:"form_post",client:{clientId:"",scope:"",redirectURI:"",state:"",nonce:""}},U="user_trigger_new_signin_flow",P="popup_closed_by_user",W="popup_blocked_by_browser",F="AppleIDSigInLoaded",V="AppleIDSignInOnSuccess",L="AppleIDSignInOnFailure",K=function(A){var e="".concat(A.baseURI).concat(A.path,"?client_id=")+encodeURIComponent(A.client.clientId)+"&redirect_uri="+encodeURIComponent(A.client.redirectURI)+"&response_type="+encodeURIComponent(A.responseType);return["state","scope","nonce"].forEach(function(t){A.client[t]&&(e="".concat(e,"&").concat(t,"=").concat(encodeURIComponent(A.client[t])))}),A.client.codeChallenge&&(e+="&code_challenge=".concat(encodeURIComponent(A.client.codeChallenge))),A.client.codeChallengeMethod&&(e+="&code_challenge_method=".concat(encodeURIComponent(A.client.codeChallengeMethod))),e=(e=(e=A.usePopup?e+"&response_mode="+encodeURIComponent("web_message"):e+"&response_mode="+encodeURIComponent(A.responseMode))+"&frame_id="+"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(A){var e=16*Math.random()|0;return("x"==A?e:3&e|8).toString(16)}))+"&m="+N[0]+N[1],e+="&v=1.5.5"},J={},H={},O={},X=function(A){J[A]&&(J[A]=null),k(A,"closed"),O[A]&&(clearInterval(O[A]),O[A]=null)},j=function(A){return H[A]||(H[A]=[]),H[A]},k=function(A,e){j(A).forEach(function(A){return A(e)})},T=function(A,e){var t=window.innerWidth?window.innerWidth:document.documentElement.clientWidth?document.documentElement.clientWidth:screen.width,n=window.innerHeight?window.innerHeight:document.documentElement.clientHeight?document.documentElement.clientHeight:screen.height;return{left:t/2-A/2+window.screenLeft,top:n/2-e/2+window.screenTop}},q=T(700,700).left,_=T(700,700).top,$={strWindowFeatures:"width=".concat(700,",height=").concat(700,",left=").concat(q,",top=").concat(_,",resizable=no,location=no,menubar=no"),windowName:"AppleAuthentication"},AA=function(){var A;J[A=$.windowName]&&("function"==typeof J[A].close&&J[A].close(),X(A))},eA=function(A){return AA(),e=A,t=$.windowName,n=$.strWindowFeatures,(i=window.open(e,t,n))&&(J[t]=i,O[t]=setInterval(function(){i.closed&&X(t)},300)),i;var e,t,n,i},tA=function(A){return function(A,e){j(A).push(e)}($.windowName,A)},nA=[],iA=[],oA=function(A){var e=nA.indexOf(A);nA.splice(e,1),iA.splice(e,1)},rA=function(A){var e=nA.indexOf(A);return iA[e]},aA=function(A){return-1!==nA.indexOf(A)},lA=function(){var A,e,t;return function(A,e){nA.push(A),iA.push(e)}(A=new Promise(function(A,n){t=A,e=n}),{reject:e,resolve:t}),A},dA={},cA=E.baseURI;window.addEventListener("message",function(A){try{if(A.origin!==cA)return;var e=JSON.parse(A.data);e.method in dA&&dA[e.method](e.data)}catch(A){}},!1);var gA=function(A){"dev"===A.env&&(cA=A.baseURI)},sA=function(A,e){dA[A]=e},hA=function(A,e){document.dispatchEvent(function(A,e){return new CustomEvent(A,{detail:e})}(A,e))},uA=null,fA=!0,wA=!1,pA=function(){return aA(uA)},vA=function(A){hA(V,A),pA()&&fA&&function(A,e){aA(A)&&(rA(A).resolve(e),oA(A))}(uA,A)},CA=function(A){var e,t;hA(L,A),pA()&&fA&&(t=A,aA(e=uA)&&(rA(e).reject(t),oA(e)))};tA(function(A){"closed"===A&&wA&&(CA({error:P}),wA=!1)}),sA("oauthDone",function(A){!function(A){"error"in A?CA(A):vA(A),wA=!1,AA()}(A)});var YA,BA,bA=function(A){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];pA()&&CA({error:U}),fA=e,"2"!==N[1]&&(N[1]="1");var t=K(A);N[1]="0";var n,i,o=!!window.Promise;if(A.usePopup){if(e&&!o)throw new Error("Promise is required to use popup, please use polyfill.");if(eA(t)){if(wA=!0,e)return i=lA(),uA=i}else if(hA(L,{error:W}),e)return Promise.reject({error:W})}else n=t,window.location.assign(n)},xA=function(A){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:E;["scope","state","nonce","usePopup","codeChallenge","codeChallengeMethod"].forEach(function(t){if(A[t])if("usePopup"===t){if("boolean"!=typeof A[t])throw new Error('The "'+t+'" should be boolean.');e[t]=A[t]}else{if("string"!=typeof A[t])throw new Error('The "'+t+'" should be a string.');e.client[t]=A[t]}})},mA=function(){var A,e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=E;if(!Q.isInit)throw new Error('The "init" function must be called first.');if(t){if(!(t instanceof Object)||Array.isArray(t))throw new Error('The "signinConfig" must be "object".');A=t,(e=Object.create(E)).client=Object.create(E.client),A.scope&&"string"==typeof A.scope&&(e.client.scope=A.scope),A.redirectURI&&"string"==typeof A.redirectURI&&(e.client.redirectURI=A.redirectURI),xA(t,i=e)}return bA(i,n)},IA=function(A){if(!A.clientId||"string"!=typeof A.clientId)throw new Error('The "clientId" should be a string.');if(E.client.clientId=A.clientId,!A.redirectURI||"string"!=typeof A.redirectURI)throw new Error('The "redirectURI" should be a string.');E.client.redirectURI=A.redirectURI,xA(A),yA(),Q.isInit=!0},MA=function(){N[1]="2",mA(null,!1)},zA=function(){MA()},DA=function(A){32===A.keyCode?A.preventDefault():13===A.keyCode&&(A.preventDefault(),MA())},GA=function(A){32===A.keyCode&&(A.preventDefault(),MA())},yA=function(){var A,e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=(A=t.id,document.getElementById(A||"appleid-signin"));if(n){(e=n)&&e.firstChild&&e.removeChild(e.firstChild);var o=function(A){var e,t,n,i,o,r=A.dataset,a="center-align",l="black",d=!0,c="sign-in",g="small",s=15;return null!=r&&(r.locale&&(a=r.locale),r.mode&&(a=r.mode),r.color&&(l=r.color),r.border&&(d="false"!==r.border),r.type&&(c=r.type),r.logoSize&&(g=r.logoSize),r.borderRadius&&!isNaN(parseInt(r.borderRadius,10))&&(s=parseInt(r.borderRadius,10)),"100%"===r.width?e=r.width:r.width&&!isNaN(parseInt(r.width,10))&&(e=parseInt(r.width,10)),"100%"===r.height?t=r.height:r.height&&!isNaN(parseInt(r.height,10))&&(t=parseInt(r.height,10)),"100%"===r.size?n=r.size:r.size&&!isNaN(parseInt(r.size,10))&&(n=parseInt(r.size,10)),r.logoPosition&&!isNaN(parseInt(r.logoPosition,10))&&(i=parseInt(r.logoPosition,10)),r.labelPosition&&!isNaN(parseInt(r.labelPosition,10))&&(o=parseInt(r.labelPosition,10))),"sign in"===c&&(c="sign-in"),{mode:a,locale:"",color:l,border:d,type:c,borderRadius:s,width:e,height:t,size:n,logoPosition:i,labelPosition:o,logoSize:g}}(n),r=Z({id:"appleid-signin"},o,t);"center-align"===o.mode?function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=A.id,t=void 0===e?"appleid-button":e,n=A.color,o=void 0===n?"black":n,r=A.type,a=void 0===r?"sign-in":r,l=A.border,d=void 0!==l&&l,c=A.width,g=void 0===c?"100%":c,s=A.height,u=void 0===s?"100%":s,f=A.borderRadius,w=void 0===f?15:f,p=A.locale,v=h({color:o,type:a,border:d,width:g,height:u,borderRadius:w,locale:void 0===p?"":p});i(t,v)}(r):"left-align"===o.mode?R(r):"logo-only"===o.mode&&function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=A.id,t=void 0===e?"appleid-button":e,n=A.color,o=void 0===n?"black":n,r=A.size,a=void 0===r?"100%":r,l=A.border,d=void 0!==l&&l,c=A.borderRadius,g=void 0===c?15:c,s=A.locale,h=S({color:o,size:a,border:d,borderRadius:g,locale:void 0===s?"":s});i(t,h)}(r),n.addEventListener("click",zA),n.addEventListener("keydown",DA),n.addEventListener("keyup",GA)}};!function(A){A.ClientId="appleid-signin-client-id",A.Scope="appleid-signin-scope",A.RedirectURI="appleid-signin-redirect-uri",A.State="appleid-signin-state",A.Nonce="appleid-signin-nonce",A.UsePopup="appleid-signin-use-popup",A.CodeChallenge="appleid-signin-code-challenge",A.CodeChallengeMethod="appleid-signin-code-challenge-method",A.DEV_URI="appleid-signin-dev-uri",A.DEV_ENV="appleid-signin-dev-env",A.DEV_PATH="appleid-signin-dev-path"}(YA||(YA={}));var RA,SA=function(){if(!BA){BA={};for(var A=function(){var A={};return Object.keys(YA).forEach(function(e){return A[YA[e]]=!0}),A}(),e=document.getElementsByTagName("meta"),t="",n=0;n<e.length;n++)A[t=e[n].getAttribute("name")]&&(BA[t]=e[n].getAttribute("content"))}return BA},NA={},ZA={init:function(A){"2"===N[0]?N[0]="3":N[0]="1",NA=Z({},NA,A),IA(A)},signIn:function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return mA(A)},renderButton:yA},QA=function(){if(e=SA(),Object.keys(e).length>0){"1"===N[0]?N[0]="4":N[0]="2";var A=function(){var A={clientId:"",scope:"",redirectURI:"",state:"",nonce:""},e=SA();e[YA.ClientId]&&(A.clientId=e[YA.ClientId]),e[YA.Scope]&&(A.scope=e[YA.Scope]),e[YA.RedirectURI]&&(A.redirectURI=e[YA.RedirectURI]),e[YA.State]&&(A.state=e[YA.State]),e[YA.Nonce]&&(A.nonce=e[YA.Nonce]),e[YA.UsePopup]&&(A.usePopup="true"===e[YA.UsePopup]),e[YA.CodeChallenge]&&(A.codeChallenge=e[YA.CodeChallenge]),e[YA.CodeChallengeMethod]&&(A.codeChallengeMethod=e[YA.CodeChallengeMethod]);var t=e[YA.DEV_ENV],n=e[YA.DEV_PATH],i=e[YA.DEV_URI];return(t||n||i)&&(t&&(E.env=t),n&&(E.path=n),i&&(E.baseURI=i,gA(E))),A}();IA(Z({},A,NA))}var e};"complete"===document.readyState||"loaded"===document.readyState||"interactive"===document.readyState?QA():document.addEventListener("DOMContentLoaded",function(){QA()}),RA=F,setTimeout(function(){return hA(RA)}),A.auth=ZA,Object.defineProperty(A,"__esModule",{value:!0})});
