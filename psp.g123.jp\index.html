<!DOCTYPE html>
<html style="width: 100%; height: 100%">
  <head>
    <title>PSP</title>
    <meta name="robots" content="noindex,nofollow" />
    
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1, maximum-scale=1"
    />
    <script>
      window.__dynamic_base_psp__ = 'https://platform-sc.g123.jp/psp';
      window.__psp_process_env__ = JSON.parse("{\"NODE_ENV\":\"production\",\"APP_VERSION\":\"20250730-26701e9\",\"SHD_G123_PSP_URL\":\"https://psp.g123.jp\",\"SHD_G123_GAME_URL\":\"https://h5.g123.jp\",\"SHD_G123_WEB_URL\":\"https://g123.jp\",\"SHD_PROVIDER_AMAZON_WIDGETS_URL\":\"https://static-fe.payments-amazon.com/OffAmazonPayments/jp/lpa/js/Widgets.js\",\"SHD_PROVIDER_PAIDY_URL\":\"https://apps.paidy.com/\",\"SHD_PROVIDER_PAIDY_API_KEY\":\"pk_live_53njvt9o4fhoi98t3v96rrqeuo\",\"SHD_ENABLE_DEVTOOLS\":false,\"SHD_G123_PSP_CDN_URL\":\"https://platform-sc.g123.jp/psp\",\"SHD_G123_CP_SDK_URL\":\"https://platform-sc.g123.jp/cp-sdk/g123-cp-sdk.umd.js\",\"SHD_G123_AUXIN_ENDPOINT\":\"https://auxin.g123.jp\",\"SHD_STRIPE_PUBLIC_KEY\":\"pk_live_51IpUoKDeftaD6cSrqkaCvumY0EoNaRtRkzG3ma22SYaGu4wXLZPkjow7LAOXuSRkOu4vXcUj4N0RIEYIcJkOGD02003pAVlJgd\",\"SHD_DD_RUM_APPLICATION_ID\":\"27ab06cd-a76d-45ad-9ccb-9848c3ac839a\",\"SHD_DD_RUM_CLIENT_TOKEN\":\"pub5de7c466d995e28828a2ded6ca62d1a5\",\"SHD_GOOGLE_RECAPTCHA_SITE_KEY\":\"6LfgP9ckAAAAAA5sKCf7xolh8ZLzinroZhL25j3X\"}");
      window.OPTION = JSON.parse("{\"recaptchaSiteKey\":\"6LfgP9ckAAAAAA5sKCf7xolh8ZLzinroZhL25j3X\",\"version\":\"20250730-26701e9\",\"datadogRumOption\":{\"applicationId\":\"27ab06cd-a76d-45ad-9ccb-9848c3ac839a\",\"clientToken\":\"pub5de7c466d995e28828a2ded6ca62d1a5\",\"site\":\"datadoghq.com\",\"service\":\"g123-psp\",\"env\":\"production\",\"version\":\"20250730-26701e9\",\"sessionSampleRate\":0.01,\"sessionReplaySampleRate\":0,\"startSessionReplayRecordingManually\":true,\"defaultPrivacyLevel\":\"mask-user-input\",\"traceContextInjection\":\"sampled\"}}");
    </script>
      
    <script
      type="module"
      src="https://platform-sc.g123.jp/psp/assets/psp_app-8G24NdES.js"
    ></script>
    
    <script
      type="module"
      src="https://platform-sc.g123.jp/psp/assets/psp-f1fd60fd-CDYNsoiC.js"
    ></script>
    
    <script
      type="module"
      src="https://platform-sc.g123.jp/psp/assets/psp-cd6a1b8d-CSw1A9l7.js"
    ></script>
    
    <script
      type="module"
      src="https://platform-sc.g123.jp/psp/assets/psp-cedf9120-DYhRxuMb.js"
    ></script>
    
    <script
      type="module"
      src="https://platform-sc.g123.jp/psp/assets/psp-8bd70de5-DJvM4uTZ.js"
    ></script>
    
    <script
      type="module"
      src="https://platform-sc.g123.jp/psp/assets/psp-80684902-PJUjq1z_.js"
    ></script>
    
    <script
      type="module"
      src="https://platform-sc.g123.jp/psp/assets/psp-3eb6f324-DoGfxEZz.js"
    ></script>
    
    <script
      type="module"
      src="https://platform-sc.g123.jp/psp/assets/psp-3c7ed04b-BuqB0BnI.js"
    ></script>
    

    <!-- VITE -->
    

    <script>
      window.top.postMessage(
        {
          event: 'PaymentSdkMonitor',
          payload: {
            type: 'app',
            status: 'page_loading',
            version: window.OPTION.version,
            time: Date.now(),
          },
        },
        '*',
      );
      function sendUnloadReport() {
        window.top.postMessage(
          {
            event: 'PaymentSdkMonitor',
            payload: {
              type: 'app',
              status: 'page_unload',
              version: window.OPTION.version,
              time: Date.now(),
            },
          },
          '*',
        );
      }
    </script>
  </head>
  <body
    style="width: 100%; height: 100%; margin: 0; padding: 0"
    onbeforeunload="sendUnloadReport()"
  >
    <div id="app"></div>
  </body>
</html>
