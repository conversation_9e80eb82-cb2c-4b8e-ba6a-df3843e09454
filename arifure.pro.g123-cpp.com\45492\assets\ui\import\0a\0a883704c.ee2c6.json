[1, ["a91+3ZzMRBK7dtsZtEmerV"], ["_effectAsset"], [["cc.Material", ["_name", "_props", "_states", "_defines"], -1], ["cc.EffectAsset", ["_name", "shaders", "techniques"], 0]], [[0, 0, 1, 2, 3, 5], [1, 0, 1, 2, 4]], [[[[0, "material", [{"inverseDegree": 0}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{}]]], 0, 0, [0], [0], [0]], [[[1, "../bundles/effect/bossdeadeffetc/inverse/inverse", [{"hash": 1023862735, "name": "../bundles/effect/bossdeadeffetc/inverse/inverse|vs:vert|fs:frag", "blocks": [{"name": "Constant", "stageFlags": 16, "binding": 0, "members": [{"name": "inverseDegree", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 44, "location": 0, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 1, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [{"name": "cc_spriteTexture", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constant", "stageFlags": 16, "binding": 0, "members": [{"name": "inverseDegree", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nin vec4 a_position;\nin vec2 a_texCoord;\nout vec2 v_texCoord;\nvec4 vert() {\n  vec4 pos = cc_matViewProj * a_position;\n  v_texCoord = a_texCoord;\n  v_texCoord = cc_cameraPos.w > 1.0 ? vec2(v_texCoord.x, 1.0 - v_texCoord.y) : v_texCoord;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nin vec2 v_texCoord;\nuniform sampler2D cc_spriteTexture;\nlayout(std140) uniform Constant {\n  float inverseDegree;\n};\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  o *= texture(cc_spriteTexture, v_texCoord);\n  float r = (255.0 - o.r * 256.0) / 256.0;\n  float g = (255.0 - o.g * 256.0) / 256.0;\n  float b = (255.0 - o.b * 256.0) / 256.0;\n  o = o + (vec4(r, g, b, o.a)-o) * inverseDegree;\n  return o;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision highp float;\nuniform highp mat4 cc_matViewProj;\n  uniform highp vec4 cc_cameraPos;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nattribute vec4 a_position;\nattribute vec2 a_texCoord;\nvarying vec2 v_texCoord;\nvec4 vert() {\n  vec4 pos = cc_matViewProj * a_position;\n  v_texCoord = a_texCoord;\n  v_texCoord = cc_cameraPos.w > 1.0 ? vec2(v_texCoord.x, 1.0 - v_texCoord.y) : v_texCoord;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvarying vec2 v_texCoord;\nuniform sampler2D cc_spriteTexture;\n  uniform float inverseDegree;\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  o *= texture2D(cc_spriteTexture, v_texCoord);\n  float r = (255.0 - o.r * 256.0) / 256.0;\n  float g = (255.0 - o.g * 256.0) / 256.0;\n  float b = (255.0 - o.b * 256.0) / 256.0;\n  o = o + (vec4(r, g, b, o.a)-o) * inverseDegree;\n  return o;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [{"name": "cc_spriteTexture", "defines": []}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 42, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 1}}, "defines": []}], [{"name": "opaque", "passes": [{"program": "../bundles/effect/bossdeadeffetc/inverse/inverse|vs:vert|fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendSrcAlpha": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"inverseDegree": {"type": 13, "value": [1]}}}]}]]], 0, 0, [], [], []]]]