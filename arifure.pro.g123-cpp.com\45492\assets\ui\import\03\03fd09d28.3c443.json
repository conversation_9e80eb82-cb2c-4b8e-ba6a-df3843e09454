[1, ["45xApT/tpBeapADMssidmJ@f9941", "acRgoobHtML7AFF8LTYl8m", "f1OA5eT45N5qIhY8fUK6Yo@f9941", "56d3tPvSVJAKGKPBEFkjYH@f9941", "d9+eRv9ftMJYX2vX8I9h8Y@f9941", "a5ZzwtWU9Fl6c7nF0glN/U", "e7+tLiN0BHSono3fSjgDnV", "cbFQsi489Lt4kwqASWs8lw@f9941", "4fKHmw9qpA1pqzGScp2Xjc@f9941", "2aJyXiuZ5Clq2H/5uu3BcT", "30dOYtDihERqDtUFL/I/QP@f9941", "17+i13FL9LYrTbiEqgWp/r@f9941", "70bdJuJ7NPy78rH0bmZ32h@f9941", "70bdJuJ7NPy78rH0bmZ32h@6c48a", "f1mp7pKTpGMoGmsQB3Lrvd@6c48a"], ["node", "_spriteFrame", "_skeletonData", "root", "data", "_parent", "_defaultClip", "_textureSource"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_children", "_lpos", "_lscale"], 0, 9, 4, 1, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "__prefab", "_spriteFrame", "_color"], 0, 1, 4, 6, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_bottom", "_left", "_right", "_top", "node", "__prefab"], -4, 1, 4], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_paddingBottom", "_affectedByScale", "node", "__prefab"], -1, 1, 4], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["sp.Skeleton", ["defaultSkin", "_premultipliedAlpha", "_preCacheMode", "defaultAnimation", "node", "__prefab", "_skeletonData"], -1, 1, 4, 6], ["cc.Label", ["_string", "_horizontalAlign", "_actualFontSize", "_lineHeight", "_overflow", "_isBold", "node", "__prefab", "_color"], -3, 1, 4, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["<PERSON><PERSON>", ["bounceDuration", "brake", "horizontal", "node", "__prefab", "_content"], 0, 1, 4, 1], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.Animation", ["node", "__prefab", "_clips", "_defaultClip"], 3, 1, 4, 3, 6], ["cc.UIOpacity", ["node", "__prefab"], 3, 1, 4], ["sp.SkeletonData", ["_name", "_native", "_atlasText", "textureNames", "textures"], -1, 3]], [[10, 0, 2], [11, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [3, 0, 1, 2, 3, 1], [0, 0, 1, 5, 3, 4, 7, 3], [1, 1, 0, 3, 4, 5, 3], [0, 0, 1, 5, 3, 4, 3], [1, 0, 3, 4, 6, 5, 2], [0, 0, 1, 5, 6, 3, 4, 7, 3], [4, 4, 5, 1], [1, 3, 4, 5, 1], [0, 0, 2, 5, 6, 3, 4, 7, 3], [0, 0, 1, 5, 6, 3, 4, 3], [0, 0, 5, 3, 4, 7, 8, 2], [2, 0, 4, 5, 6, 3, 1, 2, 7, 8, 8], [2, 0, 3, 7, 8, 3], [13, 0, 1, 2, 1], [6, 0, 1, 2, 4, 5, 6, 4], [7, 0, 1, 2, 3, 4, 5, 6, 7, 8, 7], [9, 0, 2], [0, 0, 6, 3, 4, 2], [0, 0, 2, 1, 5, 6, 3, 4, 4], [0, 0, 1, 6, 3, 4, 3], [0, 0, 5, 6, 3, 4, 7, 2], [0, 0, 2, 5, 3, 4, 7, 8, 3], [0, 0, 2, 5, 3, 4, 3], [0, 0, 2, 1, 5, 3, 4, 7, 4], [0, 0, 1, 5, 3, 4, 7, 8, 3], [0, 0, 5, 6, 3, 4, 2], [0, 0, 5, 3, 4, 2], [3, 0, 1, 1], [2, 0, 1, 2, 7, 8, 4], [2, 0, 7, 8, 2], [4, 0, 1, 2, 3, 4, 5, 5], [1, 0, 3, 4, 2], [1, 0, 3, 4, 5, 2], [1, 0, 2, 3, 4, 5, 3], [1, 3, 4, 1], [12, 0, 1, 2, 3, 4, 5, 4], [5, 1, 2, 1], [5, 0, 1, 2, 2], [14, 0, 1, 2, 3, 1], [6, 0, 3, 1, 2, 4, 5, 6, 5], [15, 0, 1, 1], [7, 0, 1, 2, 3, 4, 5, 6, 7, 7], [16, 0, 1, 2, 3, 4, 5]], [[[[19, "guide"], [20, "guide", [-4, -5, -6, -7, -8, -9, -10], [[2, -2, [0, "2fqIt+x/pIro5JIN/P3P0a"], [5, 640, 1280]], [31, 45, 640, 1280, -3, [0, "83m/4UOoNKLrAPOI/Amfte"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [21, "mask_dir", false, 33554432, 1, [-14, -15, -16, -17, -18], [[2, -11, [0, "00pfjTnMFLfLrUPjrj8FY7"], [5, 1280, 2880]], [9, -12, [0, "fbe6LgkeFCe6/QIIynEM4/"]], [14, 45, -320, -320, -800, -800, 640, 1280, -13, [0, "81liAzcDRCFb5GX7wKxC1C"]]], [1, "50D2D1e9lMZ6pt7thSLQ3X", null, null, null, 1, 0]], [8, "<PERSON><PERSON><PERSON>", 33554432, 1, [-22, -23, -24, -25], [[2, -19, [0, "4dnol0/QJOSqqLZoSFrNjy"], [5, 596, 318]], [9, -20, [0, "8dV03CRx5Lzo00knq4jka5"]], [15, 4, 221.11, -21, [0, "f6j0fbd15OAZHya4rA4n/D"]]], [1, "42aHHdRHVHv4QB+ER/66CQ", null, null, null, 1, 0], [1, 0, -259.89, 0]], [8, "img_xs_di1", 33554432, 3, [-28, -29, -30], [[3, -26, [0, "b7Y4w2YNxNALyEyqR9XkzG"], [5, 387, 131], [0, 0.5, 0]], [5, 1, 0, -27, [0, "a2Y6fKHbJFXZOraplfO/cq"], 12]], [1, "e6cAWh7FVMMJZH9pqiri4T", null, null, null, 1, 0], [1, 94.47, -132.632, 0]], [22, "content", 33554432, [-34], [[3, -31, [0, "30msdCuLtIqrpCjgI4gCWL"], [5, 346.7, 39.84], [0, 0.5, 1]], [33, 1, 2, -3, true, -32, [0, "2etAq6t0JHS7+o5xB2OGSc"]], [32, 1, -33, [0, "19C3n9f7NDSLnck8g8by/Q"]]], [1, "baStofLUtP9rd5MGDvnTjs", null, null, null, 1, 0]], [11, "img_xs_di7", false, 1, [-37, -38, -39], [[2, -35, [0, "bckJoQ+PBDq7Z7dmKlvh6H"], [5, 591, 123]], [5, 1, 0, -36, [0, "105amm1VVD6IYl/ESxnh+H"], 15]], [1, "bbi9XxcJBFRJ6hcb5jVmA9", null, null, null, 1, 0], [1, 0, -167.874, 0]], [12, "mask", 33554432, 1, [-43], [[2, -40, [0, "c3zP+xu6RPYoTn/yITGUF8"], [5, 1280, 2880]], [9, -41, [0, "42WlSCIAxOTrqf7+rZRCXd"]], [14, 45, -320, -320, -800, -800, 640, 1280, -42, [0, "35/8T3ftVIBpOOGa27FTzy"]]], [1, "99BBcpbJFBGY8hddk1+VKC", null, null, null, 1, 0]], [8, "scrollView", 33554432, 4, [-47], [[3, -44, [0, "d79qVM3W5C94Cdx7d1lHwe"], [5, 346.7, 110.745], [0, 0.5, 1]], [34, 0, -45, [0, "6fxmH7HJ9NRqi1iSzBstgY"]], [38, 0.23, 0.75, false, -46, [0, "f4XU9Tgw5GaIuqp0+tfk8u"], 5]], [1, "eeVafSV2FLM6uPuEYJhtgX", null, null, null, 1, 0], [1, 10.517, 123.15, 0]], [12, "view", 33554432, 8, [5], [[3, -48, [0, "d1zqVu/wNCM5Y+a4z5mUx2"], [5, 346.7, 110.745], [0, 0.5, 1]], [39, -49, [0, "a3KJqQF4NA/KNy0uB+r0/7"]], [16, -50, [0, "4d5SeM6JxHpo1IK6/yB72C"], [4, 16777215]]], [1, "97hjhotklElL+tg08ZrHS6", null, null, null, 1, 0]], [23, "head1", 6, [-54], [[2, -51, [0, "a5Tc8hhz1Ad4PS13tMdShP"], [5, 105, 105]], [40, 1, -52, [0, "31qKvwRANKObbhTn6QORT9"]], [16, -53, [0, "e1F7Lh7+dKC425DFRqrW8X"], [4, 16777215]]], [1, "89KVYHPBhEN73ggUCQsJw8", null, null, null, 1, 0], [1, -234.034, -0.283, 0]], [11, "img_xs_di8", false, 1, [-57, -58], [[2, -55, [0, "b2oo01yodAIZ59VDI8yK39"], [5, 591, 123]], [5, 1, 0, -56, [0, "ecta8d6NBJjKgUopZheeD5"], 17]], [1, "74v4ghF1ZEgaaVQk5W42zd", null, null, null, 1, 0], [1, 0, -167.874, 0]], [6, "img_kuang", 33554432, 2, [[2, -59, [0, "dfTy4hnoBIN6CJgBYdldXG"], [5, 244, 112]], [5, 1, 0, -60, [0, "4fh9HAv5tP35Juk9Aj+/lB"], 5], [41, -61, [0, "5eIsAonLRM1bvhMlUNWU8d"], [6], 7]], [1, "37bzs4v+5BV4ChEthlr4II", null, null, null, 1, 0]], [24, "img_xs_rw2", false, 3, [[3, -62, [0, "52DKK0hepB37rgHMccOELa"], [5, 581.7600708007812, 940.7808837890625], [0, 0.3664466099295215, 0.0060338237524745755]], [17, "default", false, 0, -63, [0, "93nlTCB/pHBawaNdIjnUo8"], 9], [15, 4, -497.99403013610834, -64, [0, "f5RKwzZrVK/ImIL3PXqCvk"]]], [1, "65Eem06TdDfZ6afaFRkx8r", null, null, null, 1, 0], [1, -189.326, -652.169, 0], [1, 0.85, 0.85, 1]], [25, "click_mask", false, 1, [[2, -65, [0, "195pNdzEBFNrZ6KK3mzXBA"], [5, 750, 1500]], [5, 1, 0, -66, [0, "50Eid95FNIOZdDjxeJRj5u"], 18], [43, -67, [0, "f8HoCk7lNPrrvXOf6DvIUF"]]], [1, "e9qd6ZV+ROCY1wZ8+vCVXZ", null, null, null, 1, 0]], [6, "img_xs_di2", 33554432, 7, [[2, -68, [0, "e1W36Ez4dIMJjag9QQ7O4/"], [5, 640, 1500]], [35, 0, -69, [0, "3a6Wi+/PBGDYQXVUpaeMv5"], 0]], [1, "f7QX59ai9OjI18M7OzZlnn", null, null, null, 1, 0]], [4, "img_mask_l", 33554432, 2, [[3, -70, [0, "01RuUB049MH7RNEP99TNV/"], [5, 200, 112], [0, 1, 0.5]], [7, 0, -71, [0, "33bCCYHUpPNoi1B4sqbIkk"], [4, 3439329279], 1]], [1, "c0yWGS3mpLvbGObO0Wsndk", null, null, null, 1, 0], [1, -120.771, 0, 0]], [4, "img_mask_r", 33554432, 2, [[3, -72, [0, "c1dk0qYkZAVI/nG1ARVFV3"], [5, 200, 112], [0, 0, 0.5]], [7, 0, -73, [0, "0fKE6Zl/FLrpx0NOA3tzgC"], [4, 3439329279], 2]], [1, "ecIBeZW7VJvKsjIg7yn9XA", null, null, null, 1, 0], [1, 129.608, 0, 0]], [4, "img_mask_u", 33554432, 2, [[3, -74, [0, "ec1qbJklZInrDdx2X+RkjG"], [5, 640, 800], [0, 0.5, 0]], [7, 0, -75, [0, "d9qEK5IvVNS65tQhmM5O2/"], [4, 3439329279], 3]], [1, "bePg1U9fNH4aGsvBRBUYCv", null, null, null, 1, 0], [1, 0, 47.13, 0]], [4, "img_mask_d", 33554432, 2, [[3, -76, [0, "84L2DBualO95JgjDcuaSY9"], [5, 640, 700], [0, 0.5, 1]], [7, 0, -77, [0, "bfvi56m+VNDp/rDche9mXX"], [4, 3439329279], 4]], [1, "874dI+S7ZNMIr5yuihA+3s", null, null, null, 1, 0], [1, 0, -53.022, 0]], [4, "img_xs_rw1", 33554432, 3, [[2, -78, [0, "eagtsn34RAkI7P86qMFZYA"], [5, 315, 317]], [36, 2, false, -79, [0, "0bLvDKFRJJKLfAG4jVFs0p"], 8]], [1, "be6zBsQttEnIXe20sVNqfx", null, null, null, 1, 0], [1, -158.555, -13.028, 0]], [26, "img_xs_rw3", false, 33554432, 3, [[3, -80, [0, "f8rToVJelCdbw8RPOI4ROh"], [5, 261.9996643066406, 511.53564453125], [0, 0.5083348038864535, 0.07890365111561866]], [17, "default", false, 0, -81, [0, "e39H9SRstHFrCLVnn055pJ"], 10]], [1, "e6lTZGGdFGDYGDPni5de4G", null, null, null, 1, 0], [1, -185.122, -329.8, 0]], [4, "img_tx_arrow1", 33554432, 4, [[2, -82, [0, "e3vnZ607VIF7Eu39w+UNsd"], [5, 28, 34]], [10, -83, [0, "398UScZ7tA4IyAqLMW8jJg"], 11]], [1, "dcFsBNhHxIj5R6yPU+/mk6", null, null, null, 1, 0], [1, 167.087, 30.255, 0]], [27, "txt_xs_ms1", 33554432, 5, [[3, -84, [0, "93TDy0fmtOvr3b4zW22kUP"], [5, 625.6, 85.68], [0, 0, 1]], [44, "", 0, 40, 68, 3, true, -85, [0, "87vuMRAUVPprVpGAplmp8M"]]], [1, "8acu6qGuJBnLuCaHjafMIt", null, null, null, 1, 0], [1, -159.842, 0, 0], [1, 0.5, 0.5, 1]], [28, "hand_pos", 1, [-87], [[30, -86, [0, "12RjSHkgdJ3ILg34/HN76P"]]], [1, "edO9bQ2h1JSb/GM0OVnwwN", null, null, null, 1, 0]], [6, "img_zhiying1", 33554432, 24, [[3, -88, [0, "256AYMHTNDTov9gawSNSR1"], [5, 640, 1280], [0, 0.5, 0.32001724243164065]], [42, "default", "animation", false, 0, -89, [0, "bbH05iwIpHzJwChvLKJjOs"], 13]], [1, "c4qW2PNdpInIHgihc5GYeD", null, null, null, 1, 0]], [29, "ico_head", 10, [[2, -90, [0, "4clUanCIlI+KgC+YQNO1w+"], [5, 106, 106]], [37, -91, [0, "efi1+/Y6tMGrNKdRaxydvh"]]], [1, "20q3BsmDRMKo3lxJtKeE0n", null, null, null, 1, 0]], [13, "txt_ydxx1", 6, [[3, -92, [0, "82w4L2gwVDP7FfB0fEFHnO"], [5, 851.7, 117.51999999999998], [0, 0, 0.5]], [18, "在鍊金研習社中達到指定時間後可以獲得一次歷練經驗獎勵。", 0, 40, 52, 3, true, -93, [0, "74GeuLWxFC2pyACDq2n5tJ"], [4, 4278190080]]], [1, "afAOy7qN5MZbNInt5RGuH4", null, null, null, 1, 0], [1, -161.173, -12.2, 0], [1, 0.5, 0.5, 1]], [4, "img_tx_arrow1", 33554432, 6, [[2, -94, [0, "eb/5+ze/FDKKg7jjn7Zm3a"], [5, 16, 17]], [10, -95, [0, "c0JKrgvEJMSoVpNWq/7IUQ"], 14]], [1, "969Gr8UfFKJLKqrqtqkQl4", null, null, null, 1, 0], [1, 271, -38, 0]], [13, "txt_ydxx2", 11, [[3, -96, [0, "6b/U5gPVpOfqnlkxqPmxmU"], [5, 1056.5, 117.51999999999998], [0, 0, 0.5]], [18, "在鍊金研習社中達到指定時間後可以獲得一次歷練經驗獎勵。", 0, 40, 52, 3, true, -97, [0, "eeWYfH5vxBFb95XsFnlYzu"], [4, 4278190080]]], [1, "8d8L7nZ95ENbwWwMpkqAVt", null, null, null, 1, 0], [1, -265.5, 1.6195000000000013, 0], [1, 0.5, 0.5, 1]], [4, "img_tx_arrow2", 33554432, 11, [[2, -98, [0, "30r4fUomdHZZYIaM2BiZsf"], [5, 16, 17]], [10, -99, [0, "88253nl89JOrmgLZQKjcqh"], 16]], [1, "432pAeIjtED6ACyNM5dPbN", null, null, null, 1, 0], [1, 271, -38, 0]], [6, "talk_touch", 33554432, 4, [[3, -100, [0, "2blfizrBZLf57buZUN5h/M"], [5, 387, 131], [0, 0.5, 0]]], [1, "f8lnDYObBCCrap24ZTukS9", null, null, null, 1, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 7, 0, -2, 2, 0, -3, 3, 0, -4, 24, 0, -5, 6, 0, -6, 11, 0, -7, 14, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, -4, 19, 0, -5, 12, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 20, 0, -2, 13, 0, -3, 21, 0, -4, 4, 0, 0, 4, 0, 0, 4, 0, -1, 22, 0, -2, 8, 0, -3, 31, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 23, 0, 0, 6, 0, 0, 6, 0, -1, 10, 0, -2, 27, 0, -3, 28, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 15, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, -1, 26, 0, 0, 11, 0, 0, 11, 0, -1, 29, 0, -2, 30, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, -1, 25, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 4, 1, 5, 5, 9, 100], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, -1, 6, 1, 2, 2, 1, 1, 2, 1, 1, 1, 1, 1], [0, 0, 0, 0, 0, 3, 1, 1, 4, 5, 6, 7, 8, 9, 2, 10, 2, 11, 12]], [[{"name": "click_mask", "rect": {"x": 0, "y": 0, "width": 750, "height": 1500}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 750, "height": 1500}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-375, -750, 0, 375, -750, 0, -375, 750, 0, 375, 750, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1500, 750, 1500, 0, 0, 750, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -375, "y": -750, "z": 0}, "maxPos": {"x": 375, "y": 750, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [8], 0, [0], [7], [13]], [[[45, "ani_glm_lihui", ".bin", "\nani_glm_lihui.png\nsize: 620,620\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nbiaoqing_1a\n  rotate: true\n  xy: 596, 126\n  size: 73, 13\n  orig: 74, 22\n  offset: 1, 5\n  index: -1\nbiaoqing_1b\n  rotate: true\n  xy: 197, 6\n  size: 19, 16\n  orig: 22, 20\n  offset: 1, 2\n  index: -1\nbiaoqing_1c\n  rotate: true\n  xy: 338, 13\n  size: 56, 17\n  orig: 59, 29\n  offset: 2, 6\n  index: -1\nbiaoqing_2a\n  rotate: true\n  xy: 91, 1\n  size: 65, 41\n  orig: 67, 43\n  offset: 1, 1\n  index: -1\nbiaoqing_2b\n  rotate: true\n  xy: 493, 55\n  size: 72, 82\n  orig: 74, 85\n  offset: 1, 1\n  index: -1\nbiaoqing_3a\n  rotate: true\n  xy: 611, 527\n  size: 35, 8\n  orig: 35, 8\n  offset: 0, 0\n  index: -1\nbiaoqing_3b\n  rotate: false\n  xy: 133, 1\n  size: 63, 24\n  orig: 66, 27\n  offset: 1, 1\n  index: -1\nbiaoqing_4a\n  rotate: true\n  xy: 356, 27\n  size: 42, 13\n  orig: 42, 13\n  offset: 0, 0\n  index: -1\nbiaoqing_4b\n  rotate: true\n  xy: 576, 55\n  size: 72, 15\n  orig: 72, 15\n  offset: 0, 0\n  index: -1\nbiaoqing_4c\n  rotate: false\n  xy: 356, 1\n  size: 24, 25\n  orig: 24, 25\n  offset: 0, 0\n  index: -1\nbiaoqing_4d\n  rotate: false\n  xy: 592, 99\n  size: 23, 26\n  orig: 23, 26\n  offset: 0, 0\n  index: -1\nxiaoren_1\n  rotate: true\n  xy: 389, 57\n  size: 93, 103\n  orig: 96, 104\n  offset: 2, 0\n  index: -1\nxiaoren_10\n  rotate: false\n  xy: 511, 317\n  size: 93, 122\n  orig: 93, 122\n  offset: 0, 0\n  index: -1\nxiaoren_11\n  rotate: false\n  xy: 1, 1\n  size: 89, 65\n  orig: 89, 65\n  offset: 0, 0\n  index: -1\nxiaoren_12\n  rotate: true\n  xy: 261, 70\n  size: 80, 127\n  orig: 80, 127\n  offset: 0, 0\n  index: -1\nxiaoren_13\n  rotate: false\n  xy: 511, 128\n  size: 84, 71\n  orig: 88, 75\n  offset: 2, 2\n  index: -1\nxiaoren_2\n  rotate: false\n  xy: 511, 200\n  size: 96, 116\n  orig: 105, 117\n  offset: 7, 1\n  index: -1\nxiaoren_3\n  rotate: true\n  xy: 511, 563\n  size: 56, 108\n  orig: 76, 109\n  offset: 1, 0\n  index: -1\nxiaoren_4\n  rotate: false\n  xy: 1, 151\n  size: 254, 468\n  orig: 254, 471\n  offset: 0, 0\n  index: -1\nxiaoren_4_noty\n  rotate: false\n  xy: 256, 151\n  size: 254, 468\n  orig: 254, 471\n  offset: 0, 0\n  index: -1\nxiaoren_5\n  rotate: false\n  xy: 511, 440\n  size: 99, 122\n  orig: 107, 123\n  offset: 4, 0\n  index: -1\nxiaoren_6\n  rotate: true\n  xy: 133, 26\n  size: 40, 80\n  orig: 49, 98\n  offset: 8, 16\n  index: -1\nxiaoren_7\n  rotate: false\n  xy: 214, 1\n  size: 59, 53\n  orig: 63, 57\n  offset: 2, 2\n  index: -1\nxiaoren_8\n  rotate: false\n  xy: 274, 12\n  size: 63, 57\n  orig: 68, 62\n  offset: 2, 2\n  index: -1\nxiaoren_9\n  rotate: false\n  xy: 1, 67\n  size: 259, 83\n  orig: 261, 85\n  offset: 1, 1\n  index: -1\n", ["ani_glm_lihui.png"], [0]], -1], 0, 0, [0], [-1], [14]]]]