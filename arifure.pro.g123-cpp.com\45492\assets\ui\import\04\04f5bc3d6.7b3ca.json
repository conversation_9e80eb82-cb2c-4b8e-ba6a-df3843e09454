[1, ["758eLKt55KsrR1YJqJWkQ2", "18b+nIathPmqAk8d6M+DWR@f9941", "418HFqISpIK72Ty7Law4yc@f9941", "5cyPbr1tJHWYIXzXCgjGso@f9941", "7cOvik9N5C8Kr+NxtXvaBK@f9941", "68Zui2hoNLLrkC/3PE/4Hu", "67DgQFd49NXoHYmGI7Mc5k@f9941", "25BxDIJ/ZKeo3HT7mkMVy5@f9941", "48l+d1s79A4K8QJudIY80q", "232ZrQzX5HUaG6iT/1PGim", "31m6oJF7JAy5Wknpx5jo/x@f9941", "d3psIeKIZBubCZ340S9OPB@f9941", "4dfUWCUhZH6rpiHgls7e8S@f9941", "25BxDIJ/ZKeo3HT7mkMVy5@6c48a", "5cyPbr1tJHWYIXzXCgjGso@6c48a", "67DgQFd49NXoHYmGI7Mc5k@6c48a", "fe6D20pTBPT71g89g5GZrg"], ["node", "_spriteFrame", "_parent", "_skeletonData", "root", "_textureSource", "data", "material", "asset", "_effectAsset"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos", "_lscale"], -2, 4, 9, 1, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_originalHeight", "_originalWidth", "_top", "_bottom", "node", "__prefab"], -2, 1, 4], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_isBold", "_fontSize", "_lineHeight", "_enableOutline", "_outlineWidth", "_enableShadow", "_overflow", "_enableWrapText", "node", "__prefab", "_outlineColor", "_color", "_shadowColor", "_shadowOffset"], -7, 1, 4, 5, 5, 5, 5], "cc.SpriteFrame", ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Layout", ["_layoutType", "_spacingX", "_spacingY", "node", "__prefab"], 0, 1, 4], ["sp.Skeleton", ["defaultSkin", "_premultipliedAlpha", "_preCacheMode", "defaultAnimation", "node", "__prefab", "_skeletonData"], -1, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4], ["80801awInNCm55tEhtITSKB", ["mix1", "mix2", "mix3", "mix4", "node", "__prefab", "color", "material"], -1, 1, 4, 12, 6], ["cc.TargetInfo", ["localID"], 2], ["7563fP+WuxL1p5rM8Q2NGlY", ["node", "__prefab", "spfs"], 3, 1, 4, 3], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab"], 1, 1, 4], ["cc.Material", ["_name", "_states", "_defines", "_props"], 0, 12], ["cc.EffectAsset", ["_name", "shaders", "techniques"], 0]], [[9, 0, 2], [11, 0, 1, 2, 3, 4, 5, 5], [0, 0, 1, 7, 6, 5, 9, 10, 3], [5, 0, 1, 2, 1], [6, 3, 4, 1], [5, 0, 1, 1], [0, 0, 1, 8, 6, 5, 3], [5, 0, 1, 2, 3, 1], [2, 2, 3, 4, 1], [0, 0, 1, 7, 8, 6, 5, 9, 3], [2, 0, 1, 2, 3, 4, 3], [15, 0, 2], [0, 0, 2, 1, 8, 6, 5, 4], [0, 0, 1, 7, 6, 5, 9, 3], [2, 0, 2, 3, 4, 2], [3, 0, 1, 3, 4, 2, 10, 11, 13, 6], [3, 0, 1, 3, 4, 2, 5, 6, 10, 11, 13, 12, 8], [7, 0, 3, 1, 2, 4, 5, 6, 5], [19, 0, 1, 2, 2], [8, 0, 2], [0, 0, 1, 7, 6, 5, 3], [1, 0, 2, 1, 5, 6, 4], [13, 0, 1, 2, 2], [18, 0, 1, 2, 3], [20, 0, 1, 2, 2], [0, 0, 2, 1, 7, 8, 6, 5, 4], [0, 0, 1, 7, 6, 5, 10, 3], [0, 0, 2, 1, 7, 6, 5, 4], [0, 3, 4, 7, 5, 3], [0, 0, 2, 1, 7, 6, 5, 9, 4], [1, 0, 1, 5, 6, 3], [1, 0, 3, 5, 6, 3], [1, 0, 5, 6, 2], [1, 0, 4, 5, 6, 3], [10, 0, 1, 2, 3, 4, 5, 4], [12, 0, 1, 2, 3, 4, 5, 4], [2, 2, 3, 1], [6, 0, 1, 2, 3, 4, 4], [3, 0, 1, 3, 4, 2, 5, 6, 7, 10, 11, 12, 14, 15, 9], [3, 0, 1, 8, 9, 2, 5, 6, 10, 11, 12, 8], [14, 0, 1, 2, 3, 4, 5, 6, 7, 5], [16, 0, 1, 2, 1], [7, 0, 1, 2, 4, 5, 6, 4], [17, 0, 1, 2, 2], [21, 0, 1, 2, 3], [22, 0, 1, 2, 3, 3], [23, 0, 1, 2, 3, 4], [24, 0, 1, 2, 4]], [[[[19, "damaoxian_wb"], [6, "damaoxian_wb", 33554432, [-5, -6, -7, -8], [[3, -3, [0, "40zi8y8wdOdKaT5SrsX5Vz"], [5, 640, 1280]], [21, 45, 640, 1280, -4, [0, "1csnHttBxA0KXM92CnIjgy"]]], [34, "c46/YsCPVOJYA4mWEpNYRx", null, null, -2, 0, [-1]]], [9, "bg_htpd_6", 33554432, 1, [-11, -12, -13, -14, -15, -16], [[3, -9, [0, "c07yTgeRdKDrza0hlBYbEq"], [5, 640, 963]], [8, -10, [0, "8bgg3lGdZA7rETEXtIW1QS"], 11]], [1, "0eYxUEjk5Nw4RK7fGB3WBo", null, null, null, 1, 0], [1, 0, 20, 0]], [12, "bg_htpd_wb3", false, 33554432, [-19, -20, -21, -22, -23], [[3, -17, [0, "3b6XuTpn1Gu6vP+O/Khsuf"], [5, 472, 74]], [14, 1, -18, [0, "9dki7ak0pB0qoSXwunWnAM"], 3]], [1, "edCvSgrRxB6qlqcaaUQu/V", null, null, null, 1, 0]], [12, "bg_htpd_wb3", false, 33554432, [-26, -27, -28, -29, -30], [[3, -24, [0, "51OLVV6BNCh4PbZnYX28ja"], [5, 472, 74]], [14, 1, -25, [0, "51FlWZK/9AoKPm4TI0Px+l"], 6]], [1, "40G1F/ZGZFvYbscx3Gy8TQ", null, null, null, 1, 0]], [12, "bg_htpd_wb3", false, 33554432, [-33, -34, -35, -36, -37], [[3, -31, [0, "40pdbnq3NDgZdxiw7xbuLd"], [5, 472, 74]], [14, 1, -32, [0, "a5AGL9kJxBVo1p9CTFY8eY"], 9]], [1, "f1fUFnCYhInIS5Y7Qz0eSi", null, null, null, 1, 0]], [6, "bg_htpd_wb5", 33554432, [-40, -41, -42, -43], [[3, -38, [0, "6duMXyFJ5KIrXfaxqt+t0M"], [5, 370, 40]], [10, 1, 0, -39, [0, "6diokygaNNuaSIM4yKuxyX"], 4]], [1, "52YwIQYMFI56tqXbRSq2w/", null, null, null, 1, 0]], [6, "bg_htpd_wb5", 33554432, [-46, -47, -48, -49], [[3, -44, [0, "bekoP+A3RJj5x2tyopJSJw"], [5, 370, 40]], [10, 1, 0, -45, [0, "f8Y1oSNoFHjY63ohrJXw0s"], 7]], [1, "c5xPNNFkdPr7gPMu06mnz6", null, null, null, 1, 0]], [6, "bg_htpd_wb5", 33554432, [-52, -53, -54, -55], [[3, -50, [0, "d9LTp4bCtBdbU+ie5x3qcM"], [5, 370, 40]], [10, 1, 0, -51, [0, "2dLxVI9RZIVZOC6jvmm4gy"], 10]], [1, "33aE+XP9hJQ5bWdQ3oyaWH", null, null, null, 1, 0]], [25, "item_icon", false, 33554432, 1, [-58, -59, -60], [[5, -56, [0, "d8IMfk6LtJ2641Rr1kmDQw"]], [4, -57, [0, "0a4KpfCyJBj5wuay52XMFs"]]], [1, "6foQQWU7ZIr56FReHStxt5", null, null, null, 1, 0]], [2, "txt_title1", 33554432, 2, [[3, -61, [0, "69gU81i41IS6yMG5D73wPF"], [5, 218, 186.4]], [38, "挖宝", 104, 104, 140, true, true, 5, true, -62, [0, "3e8O+mUrtJ4bTFYZ5bNma/"], [4, 4280558668], [4, 4280558668], [0, 0, -4]], [22, "htdj_5", -63, [0, "21Os/GKTlMl6PRn4l0vXgH"]], [40, 0.12, 0.47, 1, 1, -64, [0, "a5Cag1fpZPp5xfdm+zdAF5"], [[[4, 4294967295], [4, 4284284415], [4, 4285319679], [4, 4284284415]], 8, 8, 8, 8], 1]], [1, "7eyPf6PWZEe7pS3EbxtnuJ", null, null, null, 1, 0], [1, 0, 300, 0], [1, 0.5, 0.5, 1]], [9, "item_title1", 33554432, 2, [3, 6], [[3, -65, [0, "f7AC6WtUNBq5DwIdOMnSf/"], [5, 448, 50]], [4, -66, [0, "81G/SJI25EUJbUDioji7RS"]]], [1, "77GN7hAEhE0J7zSzsZivZM", null, null, null, 1, 0], [1, 0, 199.538, 0]], [9, "item_title2", 33554432, 2, [4, 7], [[3, -67, [0, "6fGGtSFRdIv4VdmcHuDMf1"], [5, 448, 50]], [4, -68, [0, "bfOXmIuMRHcq1aNXV0MEYE"]]], [1, "96wiygJK9DVbaebN9V7FY1", null, null, null, 1, 0], [1, 0, 143.405, 0]], [9, "item_title3", 33554432, 2, [5, 8], [[3, -69, [0, "b7QGIyIJ1L25/qFU5j5ZVy"], [5, 448, 50]], [4, -70, [0, "2c5UmklsJDj4qBJNZJpehv"]]], [1, "07XO6xJkhLFbeVMVmZ05Eg", null, null, null, 1, 0], [1, 0, 88.591, 0]], [11, ["c46/YsCPVOJYA4mWEpNYRx"]], [20, "bg_stjl5", 33554432, 1, [[3, -71, [0, "58CAyyDJ5FKpDxDwyu6pAU"], [5, 640, 1280]], [10, 1, 0, -72, [0, "0aFA1Y3K5Ct7R0yEebqi6P"], 0], [30, 5, 1440, -73, [0, "78xOtPudVApKekwF2goIhK"]]], [1, "c5iW+Q7fZKNbKLDnm638yH", null, null, null, 1, 0]], [2, "txt_ms1", 33554432, 2, [[3, -74, [0, "23Nfn2IepEuLQjjW/0QW0i"], [5, 500, 60]], [39, "翻出3个相同卡牌获得奖励", 40, 2, false, true, true, 3, -75, [0, "04wVxktE5G0a4YUyeoCcbu"], [4, 4280624421]], [22, "htdj_6", -76, [0, "a39ivBHK1Ch4JnOLr3MF0F"]]], [1, "9c9wTbQINB/5dHtFDAg4G/", null, null, null, 1, 0], [1, 0, 254.126, 0], [1, 0.5, 0.5, 1]], [20, "icon_htpd_wb1", 33554432, 9, [[3, -77, [0, "34ppOgiypMuYWemxoIFVi8"], [5, 108, 108]], [8, -78, [0, "61Dgt997JGWIeapb2LnaNp"], 12], [41, -79, [0, "eedm/6XsRGkIapuPfiinKi"], [13, 14, 15]]], [1, "a9KozB+m9CCJHycCBWwSxi", null, null, null, 1, 0]], [2, "txt_name1", 33554432, 3, [[3, -80, [0, "762PrS4tdN370+YwY9v+5t"], [5, 156, 75.6]], [15, "大椰子", 52, 52, 60, true, -81, [0, "669BBKSdpJ+r/jfMZRQ9sK"], [4, 4279842396]]], [1, "6bX/JyA7pLZLlHurUEnt0g", null, null, null, 1, 0], [1, 108, 0, 0], [1, 0.5, 0.5, 1]], [2, "ani_stpd_fpdyz", 33554432, 3, [[7, -82, [0, "e7kCFvtipLL4ac5+Jj6Njw"], [5, 752, 1336], [0, 0.4867021276595745, 0.5681137724550899]], [17, "default", "animation", false, 0, -83, [0, "08dGIgNp1ORb6hsNxbF+kn"], 2]], [1, "71JPxSCKhOGpm3kOBnjQEb", null, null, null, 1, 0], [1, -24.5, -4.5, 0], [1, 0.86, 0.92, 1]], [2, "item1", 33554432, 3, [[5, -84, [0, "42288cWp5OQp/mfE9VGNE7"]], [4, -85, [0, "292qvj4tBPd7OFekXrIRT/"]]], [1, "c7/MM/NVNHqqqkvtw3YjlD", null, null, null, 1, 0], [1, -212, 0, 0], [1, 0.75, 0.75, 1]], [2, "item2", 33554432, 3, [[5, -86, [0, "10hFLHC3RNCbggiqk39WTx"]], [4, -87, [0, "0e7Ws3aC1A/ZjSPEJPKiRz"]]], [1, "97E9r/N9dIm5NzM0bIFYJ6", null, null, null, 1, 0], [1, -126, 0, 0], [1, 0.75, 0.75, 1]], [2, "item3", 33554432, 3, [[5, -88, [0, "bc4uJqUxRNtZ64VZr3qytt"]], [4, -89, [0, "b9/p0Xq8JN35kGuQ+9c1ns"]]], [1, "83DZPNZapJv6PRhrohpcqI", null, null, null, 1, 0], [1, -40, 0, 0], [1, 0.75, 0.75, 1]], [2, "item1", 33554432, 6, [[5, -90, [0, "dadbHpI1hHIIh86NQIWMC9"]], [4, -91, [0, "8410guFnROqqPt/3NF2P6P"]]], [1, "bdGiEPvLNMga2SjlCaJci2", null, null, null, 1, 0], [1, -160, 0, 0], [1, 0.6, 0.6, 1]], [2, "item2", 33554432, 6, [[5, -92, [0, "4ai+bHgG9MI64s8Ga1fUOD"]], [4, -93, [0, "a6thn1RUhN2oZ18cRPpVA9"]]], [1, "6biSA5SbROn4LsIdWY1tx7", null, null, null, 1, 0], [1, -100, 0, 0], [1, 0.6, 0.6, 1]], [2, "item3", 33554432, 6, [[5, -94, [0, "2auUXVyNBCTJ+Dn5aQr/By"]], [4, -95, [0, "d7eEwacAVIoKXwsFAyTboH"]]], [1, "a8ss5TckhMbJu0Cy8ZDCFB", null, null, null, 1, 0], [1, -40, 0, 0], [1, 0.6, 0.6, 1]], [2, "txt_name1", 33554432, 6, [[3, -96, [0, "65XTt+WtJDFaCE2eCXCEHN"], [5, 114, 81.6]], [16, "大椰子", 36, 36, 60, true, true, 3, -97, [0, "ffpjLWdVRGAKuznO9vUp7v"], [4, 4285587455], [4, 4279440411]]], [1, "b0L8u+GwpPoKqX4gKJHuNp", null, null, null, 1, 0], [1, 86, 0, 0], [1, 0.5, 0.5, 1]], [2, "txt_name1", 33554432, 4, [[3, -98, [0, "0btrB6BoxFW7cQSTGyDmhl"], [5, 156, 75.6]], [15, "大椰子", 52, 52, 60, true, -99, [0, "85sL/ZHi9KWp4XXs7zWtXz"], [4, 4279842396]]], [1, "b2Y/N7kthE/49GCNNvHWS7", null, null, null, 1, 0], [1, 108, 0, 0], [1, 0.5, 0.5, 1]], [2, "ani_stpd_fpdyz", 33554432, 4, [[7, -100, [0, "16zz6TFcpChYdnjYckNYqo"], [5, 752, 1336], [0, 0.4867021276595745, 0.5681137724550899]], [17, "default", "animation", false, 0, -101, [0, "64cEaTSAxNBp0Fxq+Qa/lG"], 5]], [1, "62p6vEmINBu6mQP1+jj/6d", null, null, null, 1, 0], [1, -24.5, -4.5, 0], [1, 0.86, 0.92, 1]], [2, "item1", 33554432, 4, [[5, -102, [0, "23WirV1AZDMpWSkrHhPaIG"]], [4, -103, [0, "2aVFvB2RFGa6X0oJjdCGeW"]]], [1, "e6iHJ2fOlO/rDeQm1l3q5w", null, null, null, 1, 0], [1, -212, 0, 0], [1, 0.75, 0.75, 1]], [2, "item2", 33554432, 4, [[5, -104, [0, "43gD2VUHRIrpGtdFvgyQHa"]], [4, -105, [0, "54Z5d7Gf5D46f1SHjJ3P1J"]]], [1, "faglpTJcBNxbM/BNWZZk+E", null, null, null, 1, 0], [1, -126, 0, 0], [1, 0.75, 0.75, 1]], [2, "item3", 33554432, 4, [[5, -106, [0, "d6x29y8Q9NA7AsS/xtM10d"]], [4, -107, [0, "96a8IFf2FGtISg0dVvV098"]]], [1, "80RMoW7q9HjpYz2gwtbWl/", null, null, null, 1, 0], [1, -40, 0, 0], [1, 0.75, 0.75, 1]], [2, "item1", 33554432, 7, [[5, -108, [0, "ef0lYVHOVHfpkasCqmSzV3"]], [4, -109, [0, "61igLtN59Cd68fSvwvE6ou"]]], [1, "9br3XW2n5D5awutJuXIgSz", null, null, null, 1, 0], [1, -160, 0, 0], [1, 0.6, 0.6, 1]], [2, "item2", 33554432, 7, [[5, -110, [0, "aeXaxkTGBBzJIhoPeknRrI"]], [4, -111, [0, "bcUa9wDPlEXZy21f85w1cc"]]], [1, "96npw2CRVIG7d2BSe/mLXo", null, null, null, 1, 0], [1, -100, 0, 0], [1, 0.6, 0.6, 1]], [2, "item3", 33554432, 7, [[5, -112, [0, "5es2Z4JChKA5xAB9UUuRfR"]], [4, -113, [0, "19bc31HZZMYL75ptBwXuMe"]]], [1, "af723nbzFNh4ALumrQWJbb", null, null, null, 1, 0], [1, -40, 0, 0], [1, 0.6, 0.6, 1]], [2, "txt_name1", 33554432, 7, [[3, -114, [0, "d816yyco1L75P2ZAxtyqId"], [5, 114, 81.6]], [16, "大椰子", 36, 36, 60, true, true, 3, -115, [0, "bdfGtVkhFJx7rElXd+IdIz"], [4, 4285587455], [4, 4279440411]]], [1, "addMjH6X1MDJ7srxTgGW9d", null, null, null, 1, 0], [1, 86, 0, 0], [1, 0.5, 0.5, 1]], [2, "txt_name1", 33554432, 5, [[3, -116, [0, "dfUATfvhRLt5BnMunRGHQx"], [5, 156, 75.6]], [15, "大椰子", 52, 52, 60, true, -117, [0, "194DqZlwJAkYzsXn+r59vm"], [4, 4279842396]]], [1, "12U+suhzNPtaPTl1/kF+Vv", null, null, null, 1, 0], [1, 108, 0, 0], [1, 0.5, 0.5, 1]], [2, "ani_stpd_fpdyz", 33554432, 5, [[7, -118, [0, "47qkFit0tAXJjVXVskKXqF"], [5, 752, 1336], [0, 0.4867021276595745, 0.5681137724550899]], [17, "default", "animation", false, 0, -119, [0, "539qiHS4ZKW5TFavRJMscu"], 8]], [1, "b99ZyhJvlNt4UWCvV1up5n", null, null, null, 1, 0], [1, -24.5, -4.5, 0], [1, 0.86, 0.92, 1]], [2, "item1", 33554432, 5, [[5, -120, [0, "07KEnGDlZDw7TB3vUpv/iD"]], [4, -121, [0, "14AkM/ydJFeZVU41ZyYoF0"]]], [1, "52oWlBUwZDgKzBCT/zHUsm", null, null, null, 1, 0], [1, -212, 0, 0], [1, 0.75, 0.75, 1]], [2, "item2", 33554432, 5, [[5, -122, [0, "c1KNW7V41IiLziSZltJCHF"]], [4, -123, [0, "12E0slqP9F+Yx5W07QmaZU"]]], [1, "78Wuk14nhJapsdbE05i865", null, null, null, 1, 0], [1, -126, 0, 0], [1, 0.75, 0.75, 1]], [2, "item3", 33554432, 5, [[5, -124, [0, "48N33d2d5K4Z3uTbvha2lV"]], [4, -125, [0, "05kFt3NSNBEryVp/zPOgAN"]]], [1, "3dAdPtVCpE3qMsAOdpsmJe", null, null, null, 1, 0], [1, -40, 0, 0], [1, 0.75, 0.75, 1]], [2, "item1", 33554432, 8, [[5, -126, [0, "690nhmRM5KbbFViKdDabD3"]], [4, -127, [0, "20VFuhBDBCe5GhlhpNrLvE"]]], [1, "91LLexxhhDsKrqqvR086QQ", null, null, null, 1, 0], [1, -160, 0, 0], [1, 0.6, 0.6, 1]], [2, "item2", 33554432, 8, [[5, -128, [0, "87dRVtDT9AWq7SUiRiCUCd"]], [4, -129, [0, "8eAas/sFlMPJacioDey2bY"]]], [1, "8aZjAc+jlPL6isBRRjUpPq", null, null, null, 1, 0], [1, -100, 0, 0], [1, 0.6, 0.6, 1]], [2, "item3", 33554432, 8, [[5, -130, [0, "116gwFQ3pAXrO8A+ODvC6E"]], [4, -131, [0, "64Ik7FcgxCz4dzRoGYpy8d"]]], [1, "1bi/FrNXVD47HomUD1EcUx", null, null, null, 1, 0], [1, -40, 0, 0], [1, 0.6, 0.6, 1]], [2, "txt_name1", 33554432, 8, [[3, -132, [0, "3euGJLfVlNboW+ulPt62ZA"], [5, 114, 81.6]], [16, "大椰子", 36, 36, 60, true, true, 3, -133, [0, "5bbs+n/tVOYaAT9jy8/cFH"], [4, 4285587455], [4, 4279440411]]], [1, "8bODgVvK1PkLU13/AkXXzf", null, null, null, 1, 0], [1, 86, 0, 0], [1, 0.5, 0.5, 1]], [13, "fanpai_list", 33554432, 2, [[3, -134, [0, "4250QRclZGo5wtg6tpLRSL"], [5, 460, 330]], [37, 3, 20, 12, -135, [0, "43jEyOOmVDRa1yZTmzEqVj"]]], [1, "65+oS5LpxOkKA5k2ykhOQS", null, null, null, 1, 0], [1, 0, -114.155, 0]], [26, "icon", 33554432, 9, [[3, -136, [0, "514cHouVBHTJu0wlgsSRc9"], [5, 83, 79]], [36, -137, [0, "c9bRHqjg5Fh5Y4YyeLb8ex"]]], [1, "49LiAT4hJCsquzsTqFLOzm", null, null, null, 1, 0], [1, 0.8, 0.8, 1]], [27, "ani_stpd_ansg3", false, 33554432, 9, [[7, -138, [0, "ce6g/T4nNLa5cbj4pSwvNE"], [5, 752, 1336], [0, 0.2360651746709296, 0.40688995544068113]], [42, "default", false, 0, -139, [0, "1d9ON6nIhDuplLm/xQodXP"], 16]], [1, "b9g5OTEltBq4KrbWWaQfFb", null, null, null, 1, 0]], [28, 0, {}, 1, [35, "c46/YsCPVOJYA4mWEpNYRx", null, null, -140, [43, "f2RRG9MGhEep4tPUEtGM2m", 1, [[23, "down_fh", ["_name"], 14], [18, ["_lpos"], 14, [1, 0, 0, 0]], [18, ["_lrot"], 14, [3, 0, 0, 0, 1]], [18, ["_euler"], 14, [1, 0, 0, 0]], [24, ["_contentSize"], [11, ["c4nQa0yTRP76nPEa6YIw4y"]], [5, 640, 1280]], [44, false, ["_active"], [11, ["5cOfy3CL1BQZvBPsCu6rre"]]], [24, ["_lpos"], [11, ["56CPYY6JJHPYG/6GkPu63R"]], [1, -320, -586.5, 0]], [23, false, ["_active"], 14]]], 17]]], 0, [0, -1, 48, 0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 15, 0, -2, 2, 0, -3, 9, 0, -4, 48, 0, 0, 2, 0, 0, 2, 0, -1, 16, 0, -2, 10, 0, -3, 11, 0, -4, 12, 0, -5, 13, 0, -6, 45, 0, 0, 3, 0, 0, 3, 0, -1, 18, 0, -2, 19, 0, -3, 20, 0, -4, 21, 0, -5, 22, 0, 0, 4, 0, 0, 4, 0, -1, 27, 0, -2, 28, 0, -3, 29, 0, -4, 30, 0, -5, 31, 0, 0, 5, 0, 0, 5, 0, -1, 36, 0, -2, 37, 0, -3, 38, 0, -4, 39, 0, -5, 40, 0, 0, 6, 0, 0, 6, 0, -1, 23, 0, -2, 24, 0, -3, 25, 0, -4, 26, 0, 0, 7, 0, 0, 7, 0, -1, 32, 0, -2, 33, 0, -3, 34, 0, -4, 35, 0, 0, 8, 0, 0, 8, 0, -1, 41, 0, -2, 42, 0, -3, 43, 0, -4, 44, 0, 0, 9, 0, 0, 9, 0, -1, 17, 0, -2, 46, 0, -3, 47, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 4, 48, 0, 6, 1, 3, 2, 11, 4, 2, 12, 5, 2, 13, 6, 2, 11, 7, 2, 12, 8, 2, 13, 140], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 7, 3, 1, 1, 3, 1, 1, 3, 1, 1, 1, 1, -1, -2, -3, 3, 8], [4, 5, 0, 1, 2, 0, 1, 2, 0, 1, 2, 6, 3, 3, 3, 7, 8, 9]], [[[19, "down_fh"], [6, "down_fh", 33554432, [-4, -5, -6], [[3, -2, [0, "c4nQa0yTRP76nPEa6YIw4y"], [5, 640, 1280]], [21, 45, 640, 1280, -3, [0, "b1C7Ulp5xIUpPc9FBA746E"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [13, "btn_fh", 33554432, 1, [[7, -7, [0, "d0Mr6ZLCJDUo6UwKeVF1SW"], [5, 113, 107], [0, 0, 0.5]], [8, -8, [0, "02WETcHrZI75QQt/w5RhWX"], 1], [31, 12, 586.5, -9, [0, "1ctvh9b/VI4LTXAebvfUzv"]], [45, 3, 0.9, -10, [0, "eah9Hf+7dPtJ0+RSCc9t1m"]]], [1, "56CPYY6JJHPYG/6GkPu63R", null, null, null, 1, 0], [1, -320, -586.5, 0]], [13, "bg_fh_di1", 33554432, 1, [[3, -11, [0, "69RVVQgP9AcIMTAilKyJaX"], [5, 640, 124]], [8, -12, [0, "22BKKRnQBBT5iOtcu0eRpG"], 0], [32, 20, -13, [0, "a2YtoBU8xIBL5wJ35pNVT7"]]], [1, "5cOfy3CL1BQZvBPsCu6rre", null, null, null, 1, 0], [1, 0, -578, 0]], [29, "bg_di_qz1", false, 33554432, 1, [[3, -14, [0, "f8chBO6p1Fk7AjA0ZQCvR+"], [5, 640, 73]], [8, -15, [0, "25YYzZX/VPnZmYHkxthZrW"], 2], [33, 4, 79.5, -16, [0, "52bSxrPJ1Eq6e8u4tQgEWD"]]], [1, "deaJfwLFBMAJKYYJGuEajx", null, null, null, 1, 0], [1, 0, -524, 0]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, -3, 4, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 6, 1, 16], [0, 0, 0], [1, 1, 1], [10, 11, 12]], [[{"name": "bg_dmx_wb5", "rect": {"x": 0, "y": 0, "width": 108, "height": 108}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 108, "height": 108}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-54, -54, 0, 54, -54, 0, -54, 54, 0, 54, 54, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 108, 108, 108, 0, 0, 108, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -54, "y": -54, "z": 0}, "maxPos": {"x": 54, "y": 54, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [5], [13]], [[{"name": "bg_dmx_wb7", "rect": {"x": 0, "y": 0, "width": 108, "height": 108}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 108, "height": 108}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-54, -54, 0, 54, -54, 0, -54, 54, 0, 54, 54, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 108, 108, 108, 0, 0, 108, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -54, "y": -54, "z": 0}, "maxPos": {"x": 54, "y": 54, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [5], [14]], [[{"name": "bg_dmx_zt1", "rect": {"x": 0, "y": 0, "width": 640, "height": 963}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 640, "height": 963}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-320, -481.5, 0, 320, -481.5, 0, -320, 481.5, 0, 320, 481.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 963, 640, 963, 0, 0, 640, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -320, "y": -481.5, "z": 0}, "maxPos": {"x": 320, "y": 481.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [5], [15]], [[[46, "<PERSON><PERSON><PERSON>", [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{}], [[[{"mix1": 0, "mix2": 0, "mix3": 0, "mix4": 0}, "color1", 8, [4, 4278190335], "color2", 8, [4, 4278910720], "color3", 8, [4, 4294967295]]], 11]]], 0, 0, [0], [9], [16]], [[[47, "../bundles/effect/jianbian", [{"hash": 2926571638, "name": "../bundles/effect/jianbian|sprite-vs:vert|sprite-fs:frag", "blocks": [{"name": "Constants", "stageFlags": 16, "binding": 0, "members": [{"name": "color1", "type": 16, "count": 1}, {"name": "color2", "type": 16, "count": 1}, {"name": "color3", "type": 16, "count": 1}, {"name": "mixParam", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 1, "defines": []}, {"name": "a_color", "format": 44, "location": 2, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [{"name": "cc_spriteTexture", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constants", "stageFlags": 16, "binding": 0, "members": [{"name": "color1", "type": 16, "count": 1}, {"name": "color2", "type": 16, "count": 1}, {"name": "color3", "type": 16, "count": 1}, {"name": "mixParam", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision mediump float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nin vec3 a_position;\nin vec2 a_texCoord;\nin vec4 a_color;\nout vec2 uv0;\nout vec4 color;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1.);\n  pos = cc_matViewProj * pos;\n  uv0 = a_texCoord;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nlayout(std140) uniform Constants {\n  vec4 color1;\n  vec4 color2;\n  vec4 color3;\n  vec4 mixParam;\n};\nin vec4 color;\nin vec2 uv0;\nuniform sampler2D cc_spriteTexture;\nvec4 frag () {\n  vec4 col = texture(cc_spriteTexture, uv0);\n  float range = mix(uv0.x,uv0.y,mixParam.z);\n  vec4 mixCol =\n  step(range,mixParam.x)*mix(color,color1,range/mixParam.x)\n  +(step(mixParam.x,range)*step(range,mixParam.y))*mix(color1,color2,(range-mixParam.x)/(mixParam.y-mixParam.x))\n  +(step(mixParam.y,range)*step(range,1.))*mix(color2,color3,(range-mixParam.y)/(1.-mixParam.y));\n  col *= mix(color,mixCol,mixParam.w);\n  return col;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision mediump float;\nuniform highp mat4 cc_matViewProj;\nattribute vec3 a_position;\nattribute vec2 a_texCoord;\nattribute vec4 a_color;\nvarying vec2 uv0;\nvarying vec4 color;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1.);\n  pos = cc_matViewProj * pos;\n  uv0 = a_texCoord;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\n       uniform vec4 color1;\n       uniform vec4 color2;\n       uniform vec4 color3;\n       uniform vec4 mixParam;\nvarying vec4 color;\nvarying vec2 uv0;\nuniform sampler2D cc_spriteTexture;\nvec4 frag () {\n  vec4 col = texture2D(cc_spriteTexture, uv0);\n  float range = mix(uv0.x,uv0.y,mixParam.z);\n  vec4 mixCol =\n  step(range,mixParam.x)*mix(color,color1,range/mixParam.x)\n  +(step(mixParam.x,range)*step(range,mixParam.y))*mix(color1,color2,(range-mixParam.x)/(mixParam.y-mixParam.x))\n  +(step(mixParam.y,range)*step(range,1.))*mix(color2,color3,(range-mixParam.y)/(1.-mixParam.y));\n  col *= mix(color,mixCol,mixParam.w);\n  return col;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [{"name": "cc_spriteTexture", "defines": []}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 42, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 4}}, "defines": []}], [{"passes": [{"program": "../bundles/effect/jianbian|sprite-vs:vert|sprite-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"color1": {"type": 16, "value": [0, 1, 1, 1]}, "color2": {"type": 16, "value": [1, 0, 1, 1]}, "color3": {"type": 16, "value": [1, 1, 0, 1]}, "mix1": {"type": 13, "value": [0.33], "handleInfo": ["mixParam", 0, 13]}, "mix2": {"type": 13, "value": [0.66], "handleInfo": ["mixParam", 1, 13]}, "mix3": {"type": 13, "value": [0.1], "handleInfo": ["mixParam", 2, 13]}, "mix4": {"type": 13, "value": [1], "handleInfo": ["mixParam", 3, 13]}, "mixParam": {"type": 16, "value": [0.33, 0.66, 0.1, 1]}}}]}]]], 0, 0, [], [], []]]]