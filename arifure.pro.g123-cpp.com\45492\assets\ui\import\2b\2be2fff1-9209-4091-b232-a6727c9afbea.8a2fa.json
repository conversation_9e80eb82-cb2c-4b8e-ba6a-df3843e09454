[1, ["d4JjgnNgZDyJLHNJ2qlIOJ@f9941", "59lzTY2x9KbYWNq6EBUu0b@f9941", "70zJHvIudIpLM5gq1XC/WK@f9941", "3276iurkpBrom1SP5gFImw@f9941", "b6Q19yg+1Fm7ZEsDYW2BN8@f9941", "d0L/GSg0BNJ7zKtuDrFUcZ@f9941", "e3veNvQelJRpU4CTWlZ/kg@f9941", "d3+Y4fJUpBNo3j6AR7eCDT@f9941", "04YDHfAJlB/buNlANWsUAX@f9941", "68Zui2hoNLLrkC/3PE/4Hu", "f9HPqToWlCf6jmzLRfstZw", "a5CL81ja1MpJlWv9b4DJdS@f9941", "f59pGrNw1P0Ie8kDNUzU+N", "44IPaK4DpK7qeQCdMQLjAf@f9941", "7dj5uJT9FMn6OrOOx83tfK@f9941"], ["node", "_spriteFrame", "spriteFrame", "_skeletonData", "material", "root", "data"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_lpos", "_children", "_lscale"], 0, 9, 4, 1, 5, 2, 5], ["cc.Sprite", ["_isTrimmedMode", "_sizeMode", "_type", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Widget", ["_alignFlags", "_originalHeight", "_top", "_bottom", "_originalWidth", "node", "__prefab"], -2, 1, 4], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isBold", "_outlineWidth", "_enableOutline", "_lineHeight", "node", "__prefab", "_outlineColor", "_color"], -4, 1, 4, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["sp.Skeleton", ["defaultSkin", "_premultipliedAlpha", "_preCacheMode", "defaultAnimation", "node", "__prefab", "_skeletonData"], -1, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["011c8MZ++JCbqPChKjiX2MO", ["_statusIndex", "currStatusName", "statusNameArray", "node", "__prefab", "statusNodes", "statusData"], 0, 1, 4, 2, 9], ["StatusData", ["status", "fileId", "label_font", "gradient_material", "position", "rotation", "scale", "anchor", "size", "spriteFrame"], -1, 5, 5, 5, 5, 5, 6], ["cc.Layout", ["node", "__prefab"], 3, 1, 4], ["cc.LabelOutline", ["node", "__prefab"], 3, 1, 4], ["80801awInNCm55tEhtITSKB", ["mix1", "mix2", "mix3", "mix4", "node", "__prefab", "color", "material"], -1, 1, 4, 12, 6], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4]], [[7, 0, 2], [8, 0, 1, 2, 3, 4, 5, 5], [4, 0, 1, 2, 1], [4, 0, 1, 2, 3, 1], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 5], [0, 0, 2, 1, 5, 3, 4, 6, 4], [0, 0, 1, 5, 3, 4, 3], [0, 0, 1, 5, 3, 4, 6, 3], [0, 0, 1, 5, 3, 4, 6, 8, 3], [1, 3, 4, 5, 1], [2, 0, 5, 6, 2], [0, 0, 1, 5, 7, 3, 4, 6, 3], [1, 2, 3, 4, 5, 2], [2, 0, 1, 5, 6, 3], [3, 0, 1, 2, 3, 5, 4, 7, 8, 7], [12, 0, 1, 1], [6, 0, 2], [0, 0, 1, 7, 3, 4, 3], [0, 0, 2, 1, 5, 3, 4, 6, 8, 4], [0, 0, 2, 1, 5, 7, 3, 4, 4], [4, 0, 1, 1], [1, 0, 3, 4, 5, 2], [1, 1, 3, 4, 5, 2], [1, 1, 0, 3, 4, 5, 3], [2, 0, 2, 5, 6, 3], [2, 0, 3, 5, 6, 3], [2, 0, 4, 1, 5, 6, 4], [9, 0, 1, 2, 3, 4, 5, 6, 4], [3, 0, 1, 2, 6, 3, 5, 4, 7, 8, 10, 9, 8], [3, 0, 1, 2, 6, 3, 4, 7, 8, 10, 9, 7], [3, 0, 1, 2, 3, 7, 8, 9, 5], [11, 0, 1, 1], [13, 0, 1, 2, 3, 4, 5, 6, 7, 5], [14, 0, 1, 2, 2], [5, 0, 3, 1, 2, 4, 5, 6, 5], [5, 0, 1, 2, 4, 5, 6, 4]], [[16, "preload"], [17, "preload", 33554432, [-4, -5, -6, -7, -8, -9, -10, -11], [[2, -2, [0, "51klx2JyFK/afrBkKIm1ov"], [5, 640, 1280]], [26, 45, 100, 100, -3, [0, "22VELJBH9IGLxcujFYmBfZ"]]], [1, "05WBu3u/5Hm4OXhkQF1mrH", null, null, null, -1, 0]], [11, "ProgressBar", 33554432, 1, [-15, -16, -17, -18, -19], [[3, -12, [0, "c1TNOJBHlPHpvRs+4yoK3t"], [5, 640, 89], [0, 0.12, 0.5]], [12, 1, -13, [0, "7769w9QmtBULkteOIIrD0i"], 12], [25, 20, 69.5, -14, [0, "c8ASZa6LtMV564RlMrjMwt"]]], [1, "3c5yzEzIpOYLnOmQ0fswjB", null, null, null, 1, 0], [1, -243.2, -526, 0]], [5, "logo", false, 33554432, 1, [[3, -20, [0, "e3xjM2yhpLIK81kEPcRWjj"], [5, 351, 208], [0, 0.5, 1]], [22, 0, -21, [0, "48BxFc3MVBFo+iIEMLcN8j"], 2], [24, 1, 66.94699999999989, -22, [0, "7fWifHLiVM1IafKvrJaKEO"]], [27, 0, "en", ["en", "ja", "ko", "zh", "zh-TW"], -24, [0, "29sSSrODJIQ7NLnISKg0Xa"], [-23], [[4, "en", "b9GIdUmhdMCbsXDGj89vFg", null, null, [1, 126.23900000000003, 573.0530000000001, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 0.5, 1], [0, 351, 208], 3], [4, "ja", "b9GIdUmhdMCbsXDGj89vFg", null, null, [1, 126.23900000000003, 573.053, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 0.5, 1], [0, 353, 221], 4], [4, "zh", "b9GIdUmhdMCbsXDGj89vFg", null, null, [1, 126.239, 573.053, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 0.5, 1], [0, 353, 221], 5], [4, "zh-TW", "b9GIdUmhdMCbsXDGj89vFg", null, null, [1, 126.239, 573.053, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 0.5, 1], [0, 353, 221], 6], [4, "ko", "b9GIdUmhdMCbsXDGj89vFg", null, null, [1, 126.239, 573.053, 0], [3, 0, 0, 0, 1], [1, 1, 1, 1], [0, 0.5, 1], [0, 351, 225], 7]]]], [1, "b9GIdUmhdMCbsXDGj89vFg", null, null, null, 1, 0], [1, 126.23900000000003, 573.0530000000001, 0]], [11, "Layout", 33554432, 1, [-28, -29], [[20, -25, [0, "83I+tMaXdJx4HWS7IAiVih"]], [31, -26, [0, "7akQ/0NLBPGa6LVlr80ri1"]], [10, 20, -27, [0, "057eyJwXZEjrSJUnbJrAx3"]]], [1, "cdjFHGyutG1b5LPK52Gn9N", null, null, null, 1, 0], [1, 0, -590, 0]], [5, "tip", false, 33554432, 2, [[2, -30, [0, "7528LtZ2RCJLBSsxoJyNKL"], [5, 4, 54.4]], [30, "", 16, 16, true, -31, [0, "eeqTmKHuFAAaTzjXZEVD+j"], [4, 4283965388]], [15, -32, [0, "52jFCcg4ZECKDEm3YhIueM"]], [32, 0.3, 0.8, 1, 1, -33, [0, "e7JbGSjB5MX5dAAIXsXZfh"], [[[4, 4294967295], [4, 4291859711]], 8, 8], 10], [33, "uilogin_10", -34, [0, "284y7zLHJMq6R6byHuYa2B"]]], [1, "4cXVh2GYlA8Jj9iqx2rJkR", null, null, null, 1, 0], [1, 302, -40, 0]], [19, "bg_login", false, 33554432, 1, [-38], [[2, -35, [0, "6ce66WInVPUr2W68verN7W"], [5, 640, 1280]], [23, 0, false, -36, [0, "54Z71UFyJDXqoRQ2QW/Lgm"], 15], [13, 5, 1440, -37, [0, "7b0oq7BitBwbAhxH1wbj8H"]]], [1, "84eHg2PIdJJ7O1y5ovsw6D", null, null, null, 1, 0]], [5, "bg", false, 33554432, 1, [[2, -39, [0, "bfW9+28EJM+I23IJogELqJ"], [5, 640, 1440]], [21, false, -40, [0, "0aV0qsjlZKA4E9Zo9Dyt8u"], 0], [13, 1, 1280, -41, [0, "9djkwSoapLcYPXXXrsDGa3"]]], [1, "b71Qg88ctJQr0ob8G1HqJK", null, null, null, 1, 0], [1, 0, -80, 0]], [7, "Bar", 33554432, 2, [[3, -42, [0, "24C7ZO/3xGMaIhZJRv3c4Q"], [5, 447, 22], [0, 0, 0.5]], [12, 3, -43, [0, "d8l6Uv99NMQ7wYnjNzT1Bq"], 9], [10, 16, -44, [0, "abYeYFSn5C4LFz4zddAeAI"]]], [1, "c7U1kghmdGvLKUqawOYViO", null, null, null, 1, 0], [1, 19.69999999999999, 1.608, 0]], [8, "loading", 33554432, 2, [[2, -45, [0, "54HNoTZhNIFbdPbTAioFba"], [5, 232.640625, 66.48]], [28, "Loading...", 48, 48, 48, true, true, 3, -46, [0, "dayH5gu6dJ6okVB4krxZql"], [4, 4289849858], [4, 4281154823]], [10, 16, -47, [0, "afb0G+KcNJIKM/FcJHy4qZ"]]], [1, "11QhwIEp9LE7QPhjz27w37", null, null, null, 1, 0], [1, 243.2, -30, 0], [1, 0.5, 0.5, 1]], [18, "jindu", false, 33554432, 2, [[2, -48, [0, "c3zvP/2eJAdIU85dsibdmH"], [5, 179.5078125, 66.48]], [29, "999/999", 48, 48, 48, true, 3, -49, [0, "e4R/RF9+RMHoX8FSpIMx2i"], [4, 16777215], [4, 4283965388]], [15, -50, [0, "77pH76yYNE+pmWBBFLY9go"]]], [1, "0cm3vyK3lDzpT89i5BWzaW", null, null, null, 1, 0], [1, 560.893, -0.8719999999999999, 0], [1, 0.5, 0.5, 1]], [6, "dl_bg2", 33554432, 1, [[2, -51, [0, "afoaf9azdMcLcSTPUu4sfy"], [5, 640, 1280]], [9, -52, [0, "64/EsLzkhAe60QdxugMcT0"], 1]], [1, "6cDdPI6mpLuqRYEZtAXc04", null, null, null, 1, 0]], [7, "logo_ld_zh", 33554432, 1, [[2, -53, [0, "12bi7NM0pJVrQMWcJMcwKc"], [5, 613, 164]], [9, -54, [0, "25aH2Fn/pNYZ39uAVJ2QkZ"], 8]], [1, "f7cY3b7A5A0JNilvFaGCNr", null, null, null, 1, 0], [1, 0, -328.074, 0]], [8, "txt_ms1", 33554432, 4, [[2, -55, [0, "8bORjJHXxG0K7Dt1hIJz2S"], [5, 828.109375, 66.4]], [14, "©佐藤大輔・佐藤ショウジ/KADOKAWA/H.O.T.D.製作委員会", 30, 30, true, true, 8, -56, [0, "6b0BEJMC9OEripznnPZrLK"]]], [1, "f74u3KeZdIoo4i2Ifg1WIT", null, null, null, 1, 0], [1, 0, -1.5880000000000791, 0], [1, 0.5, 0.5, 1]], [8, "txt_ms2", 33554432, 4, [[2, -57, [0, "1cRPDs3QdJJZCoGU8u631C"], [5, 676.*********, 66.4]], [14, "©白米良・オーバーラップ/ありふれた製作委員会", 30, 30, true, true, 8, -58, [0, "b6nnTtOUdFVbpabTLE6HTd"]]], [1, "7cLBqBS2RJBZj7xht655/c", null, null, null, 1, 0], [1, 0, -26.739000000000033, 0], [1, 0.5, 0.5, 1]], [7, "ani_ren", 33554432, 2, [[3, -59, [0, "449rF+ucRHaLNvTXQKRo5K"], [5, 45.84197235107422, 85.71095275878906], [0, 0.4607383934258219, 0.011215430496997322]], [34, "default", "animation", false, 0, -60, [0, "e9BUchpWlIe4qJcIrshIVV"], 11]], [1, "39ntwaVH9MuqZkmwQB8G3u", null, null, null, 1, 0], [1, 17, 14.845, 0]], [6, "ani_bg", 33554432, 1, [[3, -61, [0, "c4ZNx1xclLC7r0k353E/hk"], [5, 639.8496704101562, 1434.907470703125], [0, 0.5000499365800664, 0.4995532448891777]], [35, "default", false, 0, -62, [0, "8cG3FgKQFJx6ocK6OZIvTH"], 13]], [1, "49tggm+xNCz7/rHlhMrRKp", null, null, null, 1, 0]], [6, "txt_ty_login", 33554432, 6, [[2, -63, [0, "08GWy5BERDoLtWeJAa98mX"], [5, 522, 152]], [9, -64, [0, "ebGV74S35M0KeCUFfNvgp4"], 14]], [1, "f1N2svVLBJw6jadGiiuZa+", null, null, null, 1, 0]]], 0, [0, 5, 1, 0, 0, 1, 0, 0, 1, 0, -1, 7, 0, -2, 11, 0, -3, 3, 0, -4, 12, 0, -5, 4, 0, -6, 2, 0, -7, 16, 0, -8, 6, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 5, 0, -5, 15, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 13, 0, -2, 14, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 17, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 6, 1, 64], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 2, 2, 2, 2, 2, 1, 1, 4, 3, 1, 3, 1, 1], [1, 2, 0, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]]