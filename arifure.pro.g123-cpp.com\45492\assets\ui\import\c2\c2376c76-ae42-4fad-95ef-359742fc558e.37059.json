[1, ["a2wtSHnrJCBqaFoXZ2V/hZ@f9941", "10I8iCpWVA062UoMj6NsQ4", "fc5KYzrxRGZZFwHFncwS3y@f9941", "bbETP+byhBN4TeISsx9XKY@f9941", "d8Gr2xHhpDy4LeAnJB5Owr@f9941", "35oXJ1mXpC5qlpex7lRSvJ@f9941", "8esgF/4NJKA4U67TKFSc9s@f9941", "edp1EsOQ9JIpZ1ZtEKmRbl@f9941"], ["node", "_spriteFrame", "_parent", "_target", "_defaultClip", "root", "_checkMark", "data"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_children", "_lpos", "_lscale"], 0, 9, 4, 1, 2, 5, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_affectedByScale", "_spacingY", "node", "__prefab"], -2, 1, 4], ["cc.Widget", ["_alignFlags", "_top", "_originalWidth", "_originalHeight", "_left", "_right", "_bottom", "node", "__prefab"], -4, 1, 4], ["cc.Sprite", ["_type", "_sizeMode", "_isTrimmedMode", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isBold", "_overflow", "_enableOutline", "_outlineWidth", "_horizontalAlign", "node", "__prefab", "_outlineColor"], -6, 1, 4, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_target"], 1, 1, 4, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_active", "_layer", "_parent", "_children", "_components", "_prefab"], 0, 1, 2, 12, 4], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["7563fP+WuxL1p5rM8Q2NGlY", ["node", "__prefab", "spfs"], 3, 1, 4, 3], ["cc.RichText", ["_lineHeight", "_string", "_horizontalAlign", "_verticalAlign", "_fontSize", "_maxWidth", "node", "__prefab", "_fontColor"], -3, 1, 4, 5], ["cc.Toggle", ["_interactable", "_zoomScale", "_isChecked", "node", "__prefab", "_normalColor", "_target", "_checkMark"], 0, 1, 4, 5, 1, 1], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["cc.Animation", ["playOnLoad", "node", "__prefab", "_clips", "_defaultClip"], 2, 1, 4, 3, 6]], [[9, 0, 2], [10, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [0, 0, 1, 6, 3, 4, 3], [0, 0, 1, 5, 3, 4, 7, 8, 3], [0, 0, 1, 5, 6, 3, 4, 7, 3], [3, 3, 4, 5, 1], [0, 0, 1, 5, 6, 3, 4, 3], [0, 0, 1, 5, 3, 4, 7, 3], [2, 0, 2, 3, 7, 8, 4], [3, 0, 1, 3, 4, 5, 3], [1, 5, 6, 1], [1, 0, 2, 5, 6, 3], [15, 0, 1, 1], [7, 0, 2], [0, 0, 2, 1, 5, 6, 3, 4, 7, 4], [8, 0, 1, 2, 3, 4, 5, 6, 4], [5, 0, 1, 2, 3, 1], [4, 0, 1, 2, 3, 5, 4, 9, 10, 7], [4, 0, 1, 2, 3, 5, 4, 6, 7, 9, 10, 11, 9], [4, 0, 8, 1, 2, 3, 4, 6, 9, 10, 11, 8], [2, 0, 1, 7, 8, 3], [2, 0, 4, 5, 1, 6, 2, 3, 7, 8, 8], [2, 0, 7, 8, 2], [3, 2, 3, 4, 2], [3, 3, 4, 1], [11, 0, 1, 2, 1], [6, 0, 1, 2, 3, 3], [6, 0, 1, 2, 3, 4, 3], [1, 0, 1, 2, 5, 6, 4], [1, 0, 1, 2, 3, 5, 6, 5], [1, 0, 1, 4, 3, 5, 6, 5], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 7], [13, 0, 1, 2, 3, 4, 5, 6, 7, 4], [14, 0, 1, 2, 2], [16, 0, 1, 2, 3, 4, 2]], [[14, "com_gameVersion"], [3, "com_gameVersion", 33554432, [-5], [[2, -2, [0, "47Cs6IYElOBK5jyf6MFKqL"], [5, 640, 1280]], [12, 1, 2, -3, [0, "46wgLU6vlHt54vDxwmo6H/"]], [9, 45, 720, 1280, -4, [0, "720E8zx4RME5gMDNiIWVdB"]]], [1, "79BcNar8tDmZucBHLjoa8/", null, null, null, -1, 0]], [3, "ani_in", 33554432, [-11], [[2, -6, [0, "86QmWqEnVAfYh/Gt44NFwd"], [5, 452, 535]], [11, -7, [0, "e7xm0+81xFSYkioWG/t6Sc"]], [35, true, -8, [0, "0fG661BHlNx7lcR7qTFxxZ"], [7], 8], [25, -9, [0, "35KQmsGc9N0KZqTwSgCCdR"]], [23, 2, -10, [0, "2eVbb/hs5PNJD0kXI0ZdzG"]]], [1, "87IsbNI/FKx7A+51k5g9kC", null, null, null, 1, 0]], [3, "bg_tips_1", 33554432, [-15, -16, -17], [[2, -12, [0, "a2abxKychBxLQg+xUAaekq"], [5, 598, 439]], [10, 1, 0, -13, [0, "28Z+zPkD5GDJs1xQdp5Wxl"], 6], [13, -14, [0, "960Pc+tSpCub2Ws9nP9FV8"]]], [1, "a6cgos+VxNJpSZa47mG80s", null, null, null, 1, 0]], [5, "neirong", 33554432, 3, [-21, -22, -23], [[2, -18, [0, "1dPlGyfzZAu7CY7p8MLi+7"], [5, 557, 257]], [11, -19, [0, "774vECi8pH56jbm7hD8k8o"]], [22, 41, 20.5, 20.5, 86, -60.5, 482, 420, -20, [0, "34wqzgDs1BVZUoK/5Xb6xW"]]], [1, "6f0NvUi+1H/oCkMqC7UWeI", null, null, null, 1, 0], [1, 0, 5, 0]], [15, "btn_tgdh", false, 33554432, 3, [-28, -29], [[2, -24, [0, "59QJ+NhwVJH5tb25YzkjZm"], [5, 189, 60]], [30, 1, 1, 8, true, -25, [0, "5e+J1osyJFeKAkCOWtYwon"]], [28, 3, 0.9, -27, [0, "1c9R5ovhVHLa5L7wM7+PFv"], -26]], [1, "42YU8ah+dMX5TihdJ6MKau", null, null, null, 1, 0], [1, 0, -234.33799999999997, 0]], [7, "ty_tips_1", 33554432, 1, [2], [[2, -30, [0, "a0hDS9iwdHY5H3riD4ywwV"], [5, 640, 1280]], [12, 1, 2, -31, [0, "2335mlBXNGTqc6hzdWst0B"]], [9, 45, 720, 1280, -32, [0, "0cGzAK/QZKyqdfBYz99MPp"]], [13, -33, [0, "f6cSQxvnxMr7H8HSH+/15k"]]], [1, "b550x9bKVH/oMtWEHPZwhY", null, null, null, 1, 0]], [3, "btn_qd", 33554432, [-38], [[2, -34, [0, "8em62Abl5Gyqr2o8SY0KVl"], [5, 190, 64]], [6, -35, [0, "02N2pf/H1KJbZ6DweqZBxX"], 1], [26, -36, [0, "03ku8mEuZBt7g9etrCEhnC"], [2, 3]], [27, 3, 0.9, -37, [0, "86L5cCldFA6K50HvdpQHrP"]]], [1, "d4os0ZMrtH+IGiQgzB15pr", null, null, null, 1, 0]], [5, "Toggle1", 33554432, 5, [-44], [[2, -39, [0, "47ACnhRoZBv64fZzFmnmT1"], [5, 47, 50]], [6, -40, [0, "16mjtGr/5F0L+6tlnnWvlL"], 5], [33, false, 0.9, false, -43, [0, "0eYgkthWhEWZ+JtocaimeK"], [4, 4292269782], -42, -41]], [1, "e4U3BFI7ZLwocfNr3s9VjI", null, null, null, 1, 0], [1, -71, 0, 0]], [7, "<PERSON>_suofang", 33554432, 2, [3], [[2, -45, [0, "67pn7XSYdPApoaFPCmSrar"], [5, 523, 439]], [31, 1, 2, 60, true, -46, [0, "37NGIqPiNH9rhIaXfpIrwM"]]], [1, "4cv4siT1VEnpg6Ihz8WICa", null, null, null, 1, 0]], [4, "txt_title1", 33554432, 3, [[2, -47, [0, "f0fukqyHhJDLbMD+Mq7S8d"], [5, 800, 100]], [18, "", 53, 52, 70, 2, true, -48, [0, "17cj7p7u9KSYOFcFgUfXaa"]], [21, 1, 17, -49, [0, "9cm5b+BOFLCqvdScQBxsEd"]]], [1, "d0JDHsTq9BtI+7PI9USxOy", null, null, null, 1, 0], [1, 0, 177.5, 0], [1, 0.5, 0.5, 1]], [5, "btn_ty2", 33554432, 4, [7], [[2, -50, [0, "b310JQH/5HV5EprJCacnLD"], [5, 190, 73]], [29, 1, 1, 35, -51, [0, "d5abe1V85IU5exgF42fbBK"]]], [1, "14Ai6XlEVEYJFxYKxqkRJX", null, null, null, 1, 0], [1, 0, -128.177, 0]], [16, "Checkmark", false, 33554432, 8, [-54], [[[2, -52, [0, "c3Yt76tbJHV5baAGv6N4Eh"], [5, 47, 50]], -53], 4, 1], [1, "60jeFAOyROMbD3xlmp+AdP", null, null, null, 1, 0]], [4, "Label", 33554432, 5, [[17, -55, [0, "e49hD8t4dIC5niqFeMBgox"], [5, 268, 69.52], [0, 0, 0.5]], [20, "今日不再提示", 0, 44, 44, 52, true, true, -56, [0, "2axIC+6YJC14JgaiznWjMV"], [4, 4280229916]], [34, "tishi_3", -57, [0, "c82v5Enw9NEb4NEQ3Bd8PG"]]], [1, "80t1PC8tpINrOJqHU7q9sL", null, null, null, 1, 0], [1, -39.5, 0, 0], [1, 0.5, 0.5, 1]], [8, "img_k", 33554432, 4, [[2, -58, [0, "7dqDniSt1BUJAseLnwCWuj"], [5, 504, 168]], [10, 1, 0, -59, [0, "f2++Fn9aVPd6VZ+CL69IYC"], 0]], [1, "9aoWZxZ5NB14708N7HeUuy", null, null, null, 1, 0], [1, 0, 31.082, 0]], [4, "txt_tab_1", 33554432, 7, [[2, -60, [0, "6cAko0N85HwbR6vFdQervJ"], [5, 345, 80.64]], [19, "", 53, 52, 64, 2, true, true, 3, -61, [0, "cfODlCzctFbIRbQw6lkfFt"], [4, 4279270831]]], [1, "30TQu0s6NEGrgaBsX+32B9", null, null, null, 1, 0], [1, 0, 2, 0], [1, 0.5, 0.5, 1]], [4, "content1", 33554432, 4, [[2, -62, [0, "47Kg/UScRJJaZGB+f/Yi4T"], [5, 836, 80.64]], [32, 64, "", 1, 1, 44, 836, -63, [0, "afSQoUMUZBT7BvFhxeqfFx"], [4, 4281742902]]], [1, "d0p9zxfKtGJ4XzsDhVnR5D", null, null, null, 1, 0], [1, 0, 31.082, 0], [1, 0.5, 0.5, 1]], [8, "img_gou", 33554432, 12, [[2, -64, [0, "56TbGj371PCbWTV+P5eGbX"], [5, 34, 21]], [6, -65, [0, "c6tO5XxgZHpqSEyC1x33Zo"], 4]], [1, "cbp6vff+pJGrHZ0ypN1E+C", null, null, null, 1, 0], [1, 4, 2, 0]], [24, false, 12, [0, "c420qBUbpJJpT5N56QUB2Y"]]], 0, [0, 5, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 6, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 9, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 10, 0, -2, 4, 0, -3, 5, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 14, 0, -2, 11, 0, -3, 16, 0, 0, 5, 0, 0, 5, 0, 3, 5, 0, 0, 5, 0, -1, 8, 0, -2, 13, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 15, 0, 0, 8, 0, 0, 8, 0, 6, 18, 0, 3, 8, 0, 0, 8, 0, -1, 12, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -2, 18, 0, -1, 17, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 7, 1, 2, 2, 6, 3, 2, 9, 7, 2, 11, 65], [0, 0, 0, 0, 0, 0, 0, 0, 0, 18], [1, 1, -1, -2, 1, 1, 1, -1, 4, 1], [2, 0, 3, 0, 4, 5, 6, 1, 1, 7]]