[1, ["b17ob98oVCTp+QbkNs0l/p@f9941", "4dTTw6SglF9a3/bs9pS0Oy@f9941", "4aX7U1D9VHE7AtppAWTI2F@f9941", "61OGxbc5hCB4rExGB3pAd2@f9941", "96Z67YFWtGkrPFrfU0qU5U@f9941", "96C7v6TnFBCYtdg+F1UInO@f9941", "28bfwm51dOHoLVvMxcWZRh@f9941", "d1zMVqIaJOX7dGmpIJHnON@f9941", "a6+d8+n3xAdoeex0tIgM31@f9941", "e6aMFCCPVI2575+D5rYbMx", "6178KcN91PhqNa7A6Q7vkQ@f9941", "90r61kHORJx4XNropnnVgh@f9941", "b5MWm+PwVM+7Zo7EIxUXBL@f9941", "39HbthGCZOBIV4xH944ofg@f9941", "77w4ZvKfpG7ZuSL0O+YNra@f9941", "2aPiXZupBMeaA/NDYFP6K0@f9941", "dbjzTYQipC+KSsb5S6gxEj@f9941", "eeRVJgOD1CU718rpDZKyQK@f9941", "45Apt8CXlGALk02/LRrtYE@f9941", "bdoqKapHJEGIjdL4ES7ttt@f9941", "actIT3Do5F5o/wPE7v0HfP@f9941", "47nXKDMs1FyJeGWocq6CYh@f9941", "1c1onoCGhEFoKvdpIsuRQ/@f9941", "dfimrHIEtODYD5j/iEr3y3@f9941", "87OLpIDwBNGJkccJuFhGhA", "e8qpYuGdZOj5Ce7m5GXKRX", "dfgiExixxIPqMnRf6tPxcZ@f9941", "ffmO18BMJGFLWFBj1EJlyJ@f9941", "237kQ3U1dP4JH5zB5aPIZg@f9941", "fdVmrlHd1P9aYiDphOAlM8@f9941", "c1QB2jMWZKiqvAYDWV+2XB@f9941", "1b8OuJK3NGaI0tBJZgGTm0@f9941", "fcETiZw+BJzZLUCSkuvU8q@f9941", "31Ew+3Q2lBd7FbsJ6tBubI@f9941", "68+boD6RJMnqhlK7irZ1NS@f9941", "4bvUePSW1My72lVB0hkqFh", "2aPiXZupBMeaA/NDYFP6K0@6c48a", "39HbthGCZOBIV4xH944ofg@6c48a", "4aX7U1D9VHE7AtppAWTI2F@6c48a", "61OGxbc5hCB4rExGB3pAd2@6c48a", "77w4ZvKfpG7ZuSL0O+YNra@6c48a", "a6+d8+n3xAdoeex0tIgM31@6c48a", "b5MWm+PwVM+7Zo7EIxUXBL@6c48a", "d1zMVqIaJOX7dGmpIJHnON@6c48a", "dbjzTYQipC+KSsb5S6gxEj@6c48a"], ["node", "_spriteFrame", "_textureSource", "_parent", "_skeletonData", "root", "data", "asset"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_lpos", "_children", "_lscale", "_euler"], -2, 4, 9, 1, 5, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_left", "_top", "_originalHeight", "_bottom", "_originalWidth", "_alignMode", "_right", "_horizontalCenter", "_isAbsVerticalCenter", "node", "__prefab"], -7, 1, 4], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "__prefab", "_spriteFrame", "_color"], 0, 1, 4, 6, 5], ["cc.Layout", ["_layoutType", "_resizeMode", "_affectedByScale", "_spacingX", "_enabled", "_paddingLeft", "_paddingRight", "_spacingY", "node", "__prefab"], -5, 1, 4], ["cc.Label", ["_string", "_actualFontSize", "_lineHeight", "_fontSize", "_isBold", "_outlineWidth", "_overflow", "_isItalic", "_horizontalAlign", "_enableWrapText", "_enableOutline", "node", "__prefab", "_outlineColor", "_color"], -8, 1, 4, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["sp.Skeleton", ["_premultipliedAlpha", "_preCacheMode", "defaultSkin", "node", "__prefab", "_sockets", "_skeletonData"], 0, 1, 4, 9, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_lscale"], 1, 1, 12, 4, 5, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["7563fP+WuxL1p5rM8Q2NGlY", ["node", "__prefab", "spfs"], 3, 1, 4, 3], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4], ["6c48aAw3M5FnL5xB59xX7D0", ["node", "__prefab", "spfs"], 3, 1, 4, 3], ["cc.TargetInfo", ["localID"], 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "mountedComponents", "propertyOverrides"], 2, 1, 9, 9], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["sp.Skeleton.SpineSocket", ["path", "target"], 2, 1]], [[10, 0, 2], [12, 0, 1, 2, 3, 4, 5, 5], [6, 0, 1, 2, 1], [6, 0, 1, 2, 3, 1], [3, 3, 4, 5, 1], [0, 0, 1, 7, 9, 6, 5, 8, 3], [0, 0, 1, 7, 6, 5, 8, 10, 3], [0, 0, 1, 7, 6, 5, 8, 3], [0, 0, 1, 9, 6, 5, 8, 3], [0, 0, 2, 1, 7, 6, 5, 4], [14, 0, 1, 2, 1], [4, 0, 3, 2, 8, 9, 4], [5, 0, 1, 3, 2, 11, 12, 5], [15, 0, 1, 2, 2], [17, 0, 2], [1, 0, 2, 10, 11, 3], [3, 3, 4, 1], [3, 0, 3, 4, 5, 2], [7, 2, 0, 1, 3, 4, 5, 6, 4], [21, 0, 1, 2, 2], [24, 0, 1, 2], [0, 0, 1, 7, 6, 5, 3], [0, 0, 1, 7, 6, 5, 8, 11, 3], [0, 0, 2, 1, 7, 6, 5, 8, 4], [6, 0, 1, 3, 1], [1, 7, 10, 11, 2], [1, 0, 4, 10, 11, 3], [1, 0, 10, 11, 2], [7, 0, 1, 3, 4, 3], [20, 0, 1, 2, 3], [8, 0, 2], [0, 0, 1, 9, 6, 5, 3], [0, 0, 1, 7, 9, 6, 5, 8, 11, 3], [0, 0, 1, 7, 6, 5, 10, 3], [0, 0, 1, 7, 9, 6, 5, 11, 3], [0, 0, 2, 1, 7, 9, 6, 5, 8, 4], [0, 0, 2, 1, 7, 6, 5, 8, 10, 4], [0, 0, 1, 7, 9, 6, 5, 8, 10, 3], [0, 3, 4, 7, 5, 3], [9, 0, 1, 2, 3, 4, 5, 6, 3], [6, 0, 1, 1], [1, 0, 5, 3, 10, 11, 4], [1, 0, 1, 4, 6, 10, 11, 5], [1, 0, 1, 2, 10, 11, 4], [1, 0, 1, 10, 11, 3], [1, 1, 10, 11, 2], [1, 0, 2, 4, 6, 10, 11, 5], [1, 0, 1, 7, 5, 3, 10, 11, 6], [1, 0, 8, 10, 11, 3], [1, 0, 3, 10, 11, 3], [1, 0, 2, 9, 10, 11, 4], [11, 0, 1, 2, 3, 4, 5, 4], [13, 0, 1, 2, 3, 4, 5, 4], [4, 8, 9, 1], [4, 1, 0, 3, 8, 9, 4], [4, 4, 1, 0, 5, 6, 2, 8, 9, 7], [4, 1, 0, 7, 2, 8, 9, 5], [4, 1, 0, 3, 2, 8, 9, 5], [3, 1, 0, 3, 4, 5, 3], [3, 0, 2, 3, 4, 5, 3], [3, 1, 3, 4, 5, 2], [3, 1, 0, 3, 4, 6, 5, 3], [5, 0, 1, 3, 2, 4, 11, 12, 14, 6], [5, 0, 1, 3, 2, 7, 4, 11, 12, 13, 7], [5, 0, 1, 3, 2, 4, 5, 11, 12, 7], [5, 0, 8, 1, 2, 6, 9, 4, 5, 11, 12, 9], [5, 0, 1, 3, 2, 6, 4, 10, 5, 11, 12, 13, 9], [16, 0, 1, 2, 1], [7, 0, 3, 4, 2], [18, 0, 1, 2, 3, 2], [19, 0, 1, 1], [22, 0, 1, 2, 2], [23, 0, 1, 2, 3]], [[[[30, "huoban_xhb"], [31, "huoban_xhb", 33554432, [-5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16], [[2, -3, [0, "51klx2JyFK/afrBkKIm1ov"], [5, 640, 1280]], [41, 45, 100, 100, -4, [0, "22VELJBH9IGLxcujFYmBfZ"]]], [51, "05WBu3u/5Hm4OXhkQF1mrH", null, null, -2, 0, [-1]]], [32, "Layout", 33554432, 1, [-20, -21, -22, -23, -24, -25], [[2, -17, [0, "9fjW2uVWBBNasvyprvLG6Y"], [5, 330, 230]], [53, -18, [0, "a5PDngx2pNl42Av9QF45WB"]], [42, 20, 32, 72.52786254882767, 1, -19, [0, "19qtjMEzxHNb368rDfSh88"]]], [1, "73V/PMowxDmqHwpF39GSDb", null, null, null, 1, 0], [1, 0, -452.4721374511719, 0], [1, 0, 0, 2.504478065487657e-06]], [5, "img_huoban_xjs1", 33554432, 1, [-30, -31, -32], [[2, -26, [0, "cbf4LjYZ9FPaQhIOqWUcLG"], [5, 637, 218]], [4, -27, [0, "31Malji/5KgIUBmRnkMJvE"], 36], [43, 17, -27, -12.076999999999998, -28, [0, "70cmIeUdJA85F9jftUnWDk"]], [10, -29, [0, "efaB/lqvVH4KQoOmcJ8/9m"], [37, 38, 39]]], [1, "9etXTbuTVIH4WG88T0ZzTe", null, null, null, 1, 0], [1, 0, 543.077, 0]], [8, "layout", 33554432, [-35, -36, -37, -38], [[2, -33, [0, "bac1hNgmhNFbviMhTKasB2"], [5, 580, 100]], [54, 1, 1, 20, -34, [0, "day0fbAppD9YgaGdmLPsV9"]]], [1, "e4iFFrAXxHuoz/jKz8g8Th", null, null, null, 1, 0], [1, 8.627, 0, 0]], [5, "Layout-001", 33554432, 2, [-42, -43], [[3, -39, [0, "07sEctKYtA86R2UcAhvWAe"], [5, 96.8, 30], [0, 0, 0.5]], [55, false, 1, 1, -18, -8, true, -40, [0, "f9iLTgCBxEeZIg6C+zepRI"]], [58, 1, 0, -41, [0, "59vcI+Rn5J5rjjmaS7cE1x"], 14]], [1, "e8tSqmpVpCaIwk7fJGjzD4", null, null, null, 1, 0], [1, 214.41099997530227, 131.97198228405844, 0]], [8, "txt_jc_sx1", 33554432, [-47, -48], [[3, -44, [0, "78qCOJ57lHSatrEZCdxwn/"], [5, 130, 32], [0, 0, 0.5]], [11, 1, 4, true, -45, [0, "8fChk85yNG0aX2TNibpbj2"]], [44, 8, -12.375, -46, [0, "5bzALhkO5EgaL0e0sqI3gO"]]], [1, "6dyt5ZDOhBy4SVTMEjuxo1", null, null, null, 1, 0], [1, -77.375, 0, 0]], [8, "txt_jc_sx2", 33554432, [-52, -53], [[3, -49, [0, "22BropaQ9OzI1aJ2+YoZFo"], [5, 130, 32], [0, 0, 0.5]], [11, 1, 4, true, -50, [0, "19DUoQT8pKp6o0G2OgNxQn"]], [25, 10.275999999999996, -51, [0, "f5iehrNBZLyZeQzQWl3ABH"]]], [1, "8fl+Z6GNNM445QcaaFbrfS", null, null, null, 1, 0], [1, -75.911, 0, 0]], [8, "txt_jc_sx3", 33554432, [-57, -58], [[3, -54, [0, "74Nj9OaARND5ZBe9vUM9vu"], [5, 130, 32], [0, 0, 0.5]], [11, 1, 4, true, -55, [0, "5afGYYbTFC5Zh4Eaw8e1gJ"]], [45, 300, -56, [0, "39RihMRmRNp4rpAgG4AjZQ"]]], [1, "b2IdCLLoZG6Lu+U9eLPX8F", null, null, null, 1, 0], [1, -78.82600000000002, 0, 0]], [8, "txt_jc_sx4", 33554432, [-62, -63], [[3, -59, [0, "7cP0oAAiVL4KtzKK9DqROi"], [5, 130, 32], [0, 0, 0.5]], [11, 1, 4, true, -60, [0, "feGo6j7z9NO6cmURphVMrm"]], [25, 20.600000000000023, -61, [0, "5fRnUMgRxCxKiVeTY7xRvG"]]], [1, "cckX49l6JH85sMpdt7vcan", null, null, null, 1, 0], [1, -77.57000000000005, 0, 0]], [5, "layout", 33554432, 1, [-67, -68], [[3, -64, [0, "d4aYXN/uxHSoGS/Uv+imlK"], [5, 40, 50], [0, 0.5, 0]], [56, 1, 2, 12, true, -65, [0, "14EfrCRGRCB5vZFi+OsXx0"]], [46, 4, 1158.8229999999999, 28, 1, -66, [0, "deb+AIiQFK7bejPkfWHg15"]]], [1, "62VLrNT9NAI4hDM6kWYa+g", null, null, null, 1, 0], [1, 0, -612, 0]], [5, "click_close", 33554432, 10, [-71, -72, -73], [[2, -69, [0, "07KloQmGFFtbXkvQ7fYram"], [5, 270, 50]], [57, 1, 1, 2, true, -70, [0, "fe4jYo+ypKR4nGKjsj/NXw"]]], [1, "daCvB/AcdMBpKuG/CeAH06", null, null, null, 1, 0], [1, 0, 25, 0]], [33, "txt_gb", 33554432, 11, [[2, -74, [0, "f33iMazZFESaZ91cXbP83x"], [5, 308, 75.6]], [62, "點擊空白處關閉", 44, 44, 60, true, -75, [0, "33O69SFHFITYQaseNtrqAh"], [4, 3221225471]], [26, 2, -32, -76, [0, "e5ps4qJVFAm5x+WuhnzqYL"]], [13, "djkb", -77, [0, "2c/Mvtj8xJZYDYRTLMFq8s"]], [13, "djkb", -78, [0, "7e4qx0nslJHKaSi0yp5BXs"]]], [1, "2ckk90QJpHcYtUreyKiInM", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [21, "bg_huoban_xyx1", 33554432, 1, [[2, -79, [0, "bfW9+28EJM+I23IJogELqJ"], [5, 644, 1440]], [59, 0, false, -80, [0, "0aV0qsjlZKA4E9Zo9Dyt8u"], 0], [47, 42, -2, -2, 640, 1280, -81, [0, "66nPhg6L9Op7f4jzUyhp5I"]], [10, -82, [0, "d4eLWRCJ5HGb3XgQ7pA8Yx"], [1, 2, 3]]], [1, "b71Qg88ctJQr0ob8G1HqJK", null, null, null, 1, 0]], [34, "lihui_ani", 33554432, 1, [-85, -86], [[40, -83, [0, "f9xab2DMVBboJzYSgO4Hhv"]], [15, 1, 589.9999999999998, -84, [0, "00RU2UYKRFFpWdNBWNlLEs"]]], [1, "26h5fCrGlLuZiAnd42PudP", null, null, null, 1, 0], [1, 0, 0, 2.504478065487657e-06]], [35, "Mask", false, 33554432, 1, [-90], [[2, -87, [0, "963kgMvBRE6Lo8X7Gb0HNT"], [5, 224, 425]], [4, -88, [0, "cdDtcthh9EmrvL0DZpQND5"], 28], [26, 4, 139.975, -89, [0, "e5AOXHKZtJBIVwcKpN/OJN"]]], [1, "36RdO92IZGGI7gt6G9N4xR", null, null, null, 1, 0], [1, 208.258, -287.525, 0]], [36, "Label", false, 33554432, 3, [[2, -91, [0, "87F8smNPRFH76lDgA6WvyC"], [5, 372.75339368519343, 80.64]], [63, "获得新伙伴！", 60, 60, 64, true, true, -92, [0, "e1s71g6a9Ar6qkK5ixq42K"], [4, 4287148800]], [13, "txt_hb_xhb1", -93, [0, "e4pB5zqzNBHrPeoS+5d7c/"]], [27, 16, -94, [0, "c8oQgp4TBPiJTHlYl3yqlC"]]], [1, "97h82GGitAK6aaeGjdzXHD", null, null, null, 1, 0], [1, 0, 3, 0], [1, 0.5, 0.5, 1]], [7, "wz_title_hbhd", 33554432, 3, [[2, -95, [0, "daaCKwNP1ONqv7nYfd1t0m"], [5, 190, 86]], [4, -96, [0, "4bOnL5jFJHR5dJKEdZjGn6"], 29], [67, -97, [0, "40/UsbBZNOdosS1oCnGG+3"], [30, 31]], [15, 1, 75.842, -98, [0, "78o5ZtncxNta6JTYG1v4pd"]]], [1, "96YNK/r0lMzbHDBDffLBam", null, null, null, 1, 0], [1, 0, -9.841999999999999, 0]], [22, "wz_huoban_pz4", 33554432, 1, [[3, -99, [0, "f32A70B/pCio2U+0sSOT3R"], [5, 113, 81], [0, 0.2, 0.5]], [16, -100, [0, "b3LHUid+FDY4cGkPuqx424"]], [10, -101, [0, "0ddpn1UyhMxZjqylceh+Ah"], [40, 41, 42, 43]], [15, 1, 151.28820800781233, -102, [0, "3cYbVbuzxCJYrINEm4LUxd"]]], [1, "09tm0OPHdEoYgnCcRUTRjo", null, null, null, 1, 0], [1, -233.51904296875, 448.2117919921875, 0], [1, 0, 0, 2.504478065487657e-06]], [14, ["03XfDFaqNOdp6SCwdhIQpD"]], [37, "icon_zz_1", 33554432, 2, [-105], [[2, -103, [0, "9eI9vOh4tMjoK67V5xTKqg"], [5, 61, 61]], [4, -104, [0, "52GQRTUNJF34ZA9tqdy51R"], 6]], [1, "a4av84+nJJmb1f2DEdLHXy", null, null, null, 1, 0], [1, -262.0899998790941, 131.97200522820987, 0], [1, 0.75, 0.75, 1]], [6, "icon_zy1_1", 33554432, 5, [[2, -106, [0, "62YGrJdcJKBYegulJ6gBz6"], [5, 75, 75]], [4, -107, [0, "5aZVz3Vd9JK7C2Sf3ru8QQ"], 7], [10, -108, [0, "c60VbQUAZK0p+qw4H1hVV6"], [8, 9, 10, 11, 12, 13]]], [1, "5dtvVg1GdPHqsKy0ptfXfT", null, null, null, 1, 0], [1, 2.625, 0, 0], [1, 0.55, 0.55, 1]], [7, "txt_hb_name1", 33554432, 2, [[2, -109, [0, "b8eDvb8XlLYIzZwiYUU1/2"], [5, 0, 47.88]], [64, "", 32, 32, 38, true, 1, -110, [0, "d4fGTO7RpIYobwasEZMGOU"]], [48, 16, -7.43533519198536e-08, -111, [0, "4fxBpmG4pB67E9B5aCedoc"]]], [1, "9fdqi1ro1CMLWZygtdSD2c", null, null, null, 1, 0], [1, -7.43533519198536e-08, 131.9719937718921, 0]], [5, "bg_di_hb1", 33554432, 2, [4], [[2, -112, [0, "ca9zIvGcNKpa4NP5LyP3mU"], [5, 614, 58]], [60, 1, -113, [0, "c8unY9ZY1MaJljqnyD7MeZ"], 24]], [1, "4263wu0c5DH4/xUlEfM/kf", null, null, null, 1, 0], [1, 0, 48.323, 0]], [5, "img_hbhd4", 33554432, 4, [6], [[2, -114, [0, "46hs+9X+BGN6R4q0Nn7EuC"], [5, 130, 24]], [4, -115, [0, "8bKxM54rlFsImqXMtrmsqv"], 17]], [1, "04dIUXcRlGepZ6gkbVcJMo", null, null, null, 1, 0], [1, -225, 0, 0]], [5, "img_hbhd5", 33554432, 4, [7], [[2, -116, [0, "acJkr2+1hEs6F7rAS56W0l"], [5, 130, 24]], [4, -117, [0, "a7xQFNHHFGII+o8HIB6CwW"], 19]], [1, "aaSFwj5r9JSIPLZc93NkI8", null, null, null, 1, 0], [1, -75, 0, 0]], [5, "img_hbhd6", 33554432, 4, [8], [[2, -118, [0, "14Oh4I7DNHCbszJaVEXgrm"], [5, 130, 24]], [4, -119, [0, "29YaU0xE1HR7EdKFQy2OwA"], 21]], [1, "14244nIiJIjay6oghW0HJ1", null, null, null, 1, 0], [1, 75, 0, 0]], [5, "img_hbhd4", 33554432, 4, [9], [[2, -120, [0, "dcn8yFp3VNNqKDun6XNuBj"], [5, 130, 24]], [4, -121, [0, "edaVx/7J9HWZP4pkE76XvA"], 23]], [1, "d3mj6umTdHEq4BNfE/jRQk", null, null, null, 1, 0], [1, 225, 0, 0]], [9, "bg_ani", false, 33554432, 1, [[2, -122, [0, "4aVIUqSlNDI7eo+ZsOiQFd"], [5, 640, 1440]], [49, 18, 1280, -123, [0, "7bpAybQRdM77Dq1laQYFOI"]], [68, false, -124, [0, "f9q1wfjIhI4puweKzwWHIW"]]], [1, "2bJARq10VKHql1fV+7iHyl", null, null, null, 1, 0]], [21, "lihui", 33554432, 14, [[3, -125, [0, "f3vgSD6ehAL4utQWrXwJXY"], [5, 347, 966], [0, 0.5, 1]], [16, -126, [0, "f0tIunh9ZESYMrGEPnSEw9"]], [50, 1, 50, false, -127, [0, "eeOvxh311IPJId8oeaqNQU"]]], [1, "dbI6Cf22xBb45pq9bOU3Xh", null, null, null, 1, 0]], [22, "img_hbsx_mask", 33554432, 1, [[3, -128, [0, "11AqjhctlLO5/NfqI9Vrwn"], [5, 640, 321.7699999999992], [0, 0.5, 0]], [61, 1, 0, -129, [0, "aeYvGqAUNHnpz2zvzBw9p7"], [4, 4278190080], 27], [27, 4, -130, [0, "d27AZhvllEJJa7P24FqsHU"]]], [1, "4aYZoEBwVIoIdFxBc8WWJm", null, null, null, 1, 0], [1, 0, -640, 0], [1, 0, 0, -7.513433789351942e-06]], [23, "ico_zj_pf1", false, 33554432, 3, [[2, -131, [0, "26vnn2jlJI/adZdjVsoDX2"], [5, 104, 105]], [17, 0, -132, [0, "1eNNbioJtDN7QRD0+ix/CY"], 32], [10, -133, [0, "e5Q4+6SPNE/aBgWbxzXVZ3"], [33, 34, 35]]], [1, "69MFPHtu5Le5Us0VDqxiaD", null, null, null, 1, 0], [1, -212.255, -106.39799999999991, 0]], [38, 0, {}, 10, [52, "03XfDFaqNOdp6SCwdhIQpD", null, null, -135, [69, "3ehWDJRpJIJYleQl8m6HwW", 1, [[70, [14, ["5cvq8qCo5I7pXjdLcFwDsb"]], [-134]]], [[29, "btn_ty1", ["_name"], 19], [19, ["_lpos"], 19, [1, 0, 94, 0]], [19, ["_lrot"], 19, [3, 0, 0, 0, 1]], [19, ["_euler"], 19, [1, 0, 0, 0]], [71, ["_contentSize"], [14, ["1657nTijNEh7Bns5UYA0sV"]], [5, 190, 64]], [72, "", ["_string"], [14, ["bc648ctydDD5l0O0o5vV7i"]]], [29, false, ["_active"], 19]]], 44]], [9, "ani_hbhd_SR", false, 33554432, 1, [[3, -136, [0, "educ4ivlBHK7IVXQT1Dfxj"], [5, 1196.000244140625, 1572], [0, 0.4924305704826288, 0.4930826822916667]], [18, "default", false, 0, -137, [0, "5ecOx2QfRGDLahmKfjyS9s"], [[20, "root/GD2", 2]], 4]], [1, "e17HVdx5ZH965nLsnROlqS", null, null, null, 1, 0]], [7, "img_hbhd1", 33554432, 2, [[2, -138, [0, "61AthqsadL+Y42YUYXa5FW"], [5, 644, 260]], [4, -139, [0, "441X2OK6NNTI2KoikPeIAe"], 5]], [1, "f0eqqixUZFAojPvKBhe9Lk", null, null, null, 1, 0], [1, 0, 57.205, 0]], [7, "icon_sx_1", 33554432, 20, [[2, -140, [0, "92jRSBS+hGu5mA+njLwUuS"], [5, 36, 37]], [16, -141, [0, "a16szhzBtPGor9O8srIJFF"]]], [1, "5baStoTjRIda6tKKPWTcTu", null, null, null, 1, 0], [1, 0, 1, 0]], [6, "txt_hb_name2", 33554432, 5, [[3, -142, [0, "a50hWgQNVOTJvPS5BChTnP"], [5, 136, 65.52], [0, 0, 0.5]], [65, "", 0, 40, 52, 2, false, true, 1, -143, [0, "f4gelGotJOiZYbLTmTFRHm"]]], [1, "a8xsOXR+FMdKeFJr0UKbqU", null, null, null, 1, 0], [1, 23.25, 0, 0], [1, 0.5, 0.5, 1]], [23, "img_xian_1", false, 33554432, 2, [[2, -144, [0, "09HY8BkWhHUKOqd9pXfGAR"], [5, 330, 2]], [17, 0, -145, [0, "ce6qOKKtdD45dyP5TjpN3z"], 15]], [1, "e4ZRRmtgVKCon3M9AUsomy", null, null, null, 1, 0], [1, 3, 79.205, 0]], [7, "icon_sx_gj1", 33554432, 6, [[2, -146, [0, "d06slyhCtCR6a0QONQNZ8k"], [5, 32, 32]], [4, -147, [0, "11GUjCMptLnaCm+3rz/UmF"], 16]], [1, "6dE+r3h7JCHY37soW/wnUb", null, null, null, 1, 0], [1, 16, 0, 0]], [6, "txt_sx_gj1", 33554432, 6, [[3, -148, [0, "43+ypLRdFMV6RfhOwqPCZd"], [5, 0, 65.52], [0, 0, 0.5]], [12, "", 48, 48, 52, -149, [0, "d6V6DdLhNNVJ7GI3D9gxgu"]]], [1, "62AexsVDRNip06w5H6JAwp", null, null, null, 1, 0], [1, 36, 0, 0], [1, 0.5, 0.5, 1]], [7, "icon_sx_fy1", 33554432, 7, [[2, -150, [0, "52pThFoApIdbvXy/HBthye"], [5, 32, 32]], [4, -151, [0, "70VPjfmvBHNbZVPwX33uYw"], 18]], [1, "5dkGeFqnRLr6ru0FJNUKom", null, null, null, 1, 0], [1, 16, 0, 0]], [6, "txt_sx_fy1", 33554432, 7, [[3, -152, [0, "f7xC0dUR9Kd6o8tizUoZgg"], [5, 0, 65.52], [0, 0, 0.5]], [12, "", 48, 48, 52, -153, [0, "f6gEdiUZFGdqR+Wp93eMv7"]]], [1, "4eMG1/ptFMzr3th0SX+xv7", null, null, null, 1, 0], [1, 36, 0, 0], [1, 0.5, 0.5, 1]], [7, "icon_sx_sm1", 33554432, 8, [[2, -154, [0, "082iVMAGtGSp/iN2mA5f39"], [5, 32, 32]], [4, -155, [0, "b4POVt6LNNvYjvIFnkE3PG"], 20]], [1, "94a+IA5QpImZbjTFQ7+oEa", null, null, null, 1, 0], [1, 16, 0, 0]], [6, "txt_sx_sm1", 33554432, 8, [[3, -156, [0, "2eN3qKPQlPZ6/LOLNyZ1vw"], [5, 0, 65.52], [0, 0, 0.5]], [12, "", 48, 48, 52, -157, [0, "ccPGkb2HhHVI5AXorLpj2o"]]], [1, "3cz0ltHthK2IEOrCeTCKI8", null, null, null, 1, 0], [1, 36, 0, 0], [1, 0.5, 0.5, 1]], [6, "icon_sx_sd1", 33554432, 9, [[2, -158, [0, "79FLJnwq5EcrcDfYaT1zq8"], [5, 27, 27]], [17, 0, -159, [0, "145e+nBG1Ia4alah20Ye6g"], 22]], [1, "d46AqsyfBAlJVnY9HImIDA", null, null, null, 1, 0], [1, 16.2, 0, 0], [1, 1.2, 1.2, 1]], [6, "txt_sx_sd1", 33554432, 9, [[3, -160, [0, "a81BFLYgxAmr8HUXNU+gHh"], [5, 0, 65.52], [0, 0, 0.5]], [12, "", 48, 48, 52, -161, [0, "5eLlSwU0dJapDFG4N+VyzQ"]]], [1, "35bMxf62FD7LTNXcRfTXDH", null, null, null, 1, 0], [1, 36.4, 0, 0], [1, 0.5, 0.5, 1]], [9, "ani_hbhd_SSR", false, 33554432, 1, [[3, -162, [0, "27xDnwp7xJhYe8faYQmqqw"], [5, 1116.000244140625, 1468], [0, 0.49043306949671384, 0.4925926270861717]], [18, "default", false, 0, -163, [0, "0cJMCEiFpAFptry38PO+4A"], [[20, "root/GD2", 2]], 25]], [1, "04LEQcAadL/q3jCuQGMmvj", null, null, null, 1, 0]], [9, "ani_hbhd_UR", false, 33554432, 1, [[3, -164, [0, "bdaFGcxhBKKLGfeQpe/GdA"], [5, 1116.000244140625, 1468], [0, 0.49043306949671384, 0.4925926270861717]], [18, "default", false, 0, -165, [0, "5fQQug+mdBoL7XKKsSK/aj"], [[20, "root/GD2", 2]], 26]], [1, "e11YfrxOJCZK96S7EVA3Sg", null, null, null, 1, 0]], [9, "lihui2", false, 33554432, 14, [[24, -166, [0, "20iyCzXWtEDomGvBCzd2O4"], [0, 0.5, 0.0011653911842273903]], [28, false, 0, -167, [0, "34vBoVTalPC7IFav7Yuw4H"]]], [1, "b2ZxbE3aJJkoqJlRpbWh7h", null, null, null, 1, 0]], [6, "xia<PERSON>n", 33554432, 15, [[24, -168, [0, "9bXSjWd+RMcrMDxq9iAmkF"], [0, 0.5, 0.0832885750609226]], [28, false, 0, -169, [0, "46GjqUwlRBDpC2aC7pcsLP"]]], [1, "92TxNFCFZOqreJT8bSdrEP", null, null, null, 1, 0], [1, 17.897, 9.948, 0], [1, 1.5, 1.5, 1]], [39, "txt_tab_1", 33554432, 32, [[[2, -170, [0, "77Mt/A7RxGrJ0Q7zFDQxkY"], [5, 345, 80.64]], [66, "", 53, 52, 64, 2, true, true, 3, -171, [0, "bc648ctydDD5l0O0o5vV7i"], [4, 4282867204]], -172], 4, 4, 1], [1, "5cvq8qCo5I7pXjdLcFwDsb", null, null, null, 1, 0], [1, 0, 2, 0], [1, 0.5, 0.5, 1]], [6, "img_jt1", 33554432, 11, [[2, -173, [0, "8bvE1wds1GEo5LWXW4YMwH"], [5, 56, 31]], [4, -174, [0, "56MRCKWYJHtL5cmNA7XHDe"], 45]], [1, "35r3lkUQlG4o1W82VrE9uA", null, null, null, 1, 0], [1, -107, 0, 0], [1, -1, 1, 1]], [7, "img_jt2", 33554432, 11, [[2, -175, [0, "fc6+mORjhHCLK2XdCh9S6D"], [5, 56, 31]], [4, -176, [0, "21jzZALwhECpzLdMZMzx99"], 46]], [1, "44I6u0QY5PCJrIhp++8qXI", null, null, null, 1, 0], [1, 107, 0, 0]], [13, "uihuoban_93", 50, [0, "2eLdAEocJH0IvZ+AyhOkJk"]]], 0, [0, -1, 32, 0, 5, 1, 0, 0, 1, 0, 0, 1, 0, -1, 13, 0, -2, 33, 0, -3, 46, 0, -4, 47, 0, -5, 28, 0, -6, 14, 0, -7, 30, 0, -8, 15, 0, -9, 3, 0, -10, 18, 0, -11, 2, 0, -12, 10, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 34, 0, -2, 20, 0, -3, 5, 0, -4, 37, 0, -5, 22, 0, -6, 23, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 16, 0, -2, 17, 0, -3, 31, 0, 0, 4, 0, 0, 4, 0, -1, 24, 0, -2, 25, 0, -3, 26, 0, -4, 27, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 21, 0, -2, 36, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 38, 0, -2, 39, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 40, 0, -2, 41, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 42, 0, -2, 43, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 44, 0, -2, 45, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, -1, 32, 0, -2, 11, 0, 0, 11, 0, 0, 11, 0, -1, 51, 0, -2, 12, 0, -3, 52, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, -1, 29, 0, -2, 48, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, -1, 49, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 20, 0, 0, 20, 0, -1, 35, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 31, 0, -1, 53, 0, 5, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, -3, 53, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 6, 1, 4, 3, 23, 6, 3, 24, 7, 3, 25, 8, 3, 26, 9, 3, 27, 176], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, -1, -2, -3, 4, 1, 1, 1, -1, -2, -3, -4, -5, -6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 4, 1, 1, 1, -1, -2, 1, -1, -2, -3, 1, -1, -2, -3, -1, -2, -3, -4, 7, 1, 1], [2, 2, 7, 8, 9, 10, 11, 3, 3, 12, 13, 14, 15, 16, 17, 18, 19, 0, 20, 0, 21, 0, 22, 0, 23, 24, 25, 26, 27, 4, 4, 28, 5, 29, 30, 5, 1, 1, 1, 1, 31, 32, 33, 34, 35, 6, 6]], [[{"name": "icon_hb_zy5", "rect": {"x": 0, "y": 0, "width": 75, "height": 75}, "offset": {"x": -0.5, "y": 3}, "originalSize": {"width": 76, "height": 81}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-37.5, -37.5, 0, 37.5, -37.5, 0, -37.5, 37.5, 0, 37.5, 37.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 81, 75, 81, 0, 6, 75, 6], "nuv": [0, 0.07407407407407407, 0.9868421052631579, 0.07407407407407407, 0, 1, 0.9868421052631579, 1], "minPos": {"x": -37.5, "y": -37.5, "z": 0}, "maxPos": {"x": 37.5, "y": 37.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [36]], [[{"name": "icon_hb_zy3", "rect": {"x": 0, "y": 0, "width": 75, "height": 75}, "offset": {"x": -0.5, "y": 3}, "originalSize": {"width": 76, "height": 81}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-37.5, -37.5, 0, 37.5, -37.5, 0, -37.5, 37.5, 0, 37.5, 37.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 81, 75, 81, 0, 6, 75, 6], "nuv": [0, 0.07407407407407407, 0.9868421052631579, 0.07407407407407407, 0, 1, 0.9868421052631579, 1], "minPos": {"x": -37.5, "y": -37.5, "z": 0}, "maxPos": {"x": 37.5, "y": 37.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [37]], [[{"name": "bg_huoban_xyx4", "rect": {"x": 0, "y": 0, "width": 6, "height": 6}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 6, "height": 6}, "rotated": false, "capInsets": [0, 6, 0, 0], "vertices": {"rawPosition": [-3, -3, 0, 3, -3, 0, -3, 3, 0, 3, 3, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 6, 6, 6, 0, 0, 6, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -3, "y": -3, "z": 0}, "maxPos": {"x": 3, "y": 3, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [38]], [[{"name": "icon_hb_zy1", "rect": {"x": 0, "y": 0, "width": 75, "height": 75}, "offset": {"x": -0.5, "y": 3}, "originalSize": {"width": 76, "height": 81}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-37.5, -37.5, 0, 37.5, -37.5, 0, -37.5, 37.5, 0, 37.5, 37.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 81, 75, 81, 0, 6, 75, 6], "nuv": [0, 0.07407407407407407, 0.9868421052631579, 0.07407407407407407, 0, 1, 0.9868421052631579, 1], "minPos": {"x": -37.5, "y": -37.5, "z": 0}, "maxPos": {"x": 37.5, "y": 37.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [39]], [[{"name": "icon_hb_zy4", "rect": {"x": 0, "y": 0, "width": 75, "height": 75}, "offset": {"x": -0.5, "y": 3}, "originalSize": {"width": 76, "height": 81}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-37.5, -37.5, 0, 37.5, -37.5, 0, -37.5, 37.5, 0, 37.5, 37.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 81, 75, 81, 0, 6, 75, 6], "nuv": [0, 0.07407407407407407, 0.9868421052631579, 0.07407407407407407, 0, 1, 0.9868421052631579, 1], "minPos": {"x": -37.5, "y": -37.5, "z": 0}, "maxPos": {"x": 37.5, "y": 37.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [40]], [[{"name": "bg_huoban_xyx6", "rect": {"x": 0, "y": 0, "width": 6, "height": 6}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 6, "height": 6}, "rotated": false, "capInsets": [0, 6, 0, 0], "vertices": {"rawPosition": [-3, -3, 0, 3, -3, 0, -3, 3, 0, 3, 3, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 6, 6, 6, 0, 0, 6, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -3, "y": -3, "z": 0}, "maxPos": {"x": 3, "y": 3, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [41]], [[{"name": "icon_hb_zy2", "rect": {"x": 0, "y": 0, "width": 75, "height": 75}, "offset": {"x": -0.5, "y": 3}, "originalSize": {"width": 76, "height": 81}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-37.5, -37.5, 0, 37.5, -37.5, 0, -37.5, 37.5, 0, 37.5, 37.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 81, 75, 81, 0, 6, 75, 6], "nuv": [0, 0.07407407407407407, 0.9868421052631579, 0.07407407407407407, 0, 1, 0.9868421052631579, 1], "minPos": {"x": -37.5, "y": -37.5, "z": 0}, "maxPos": {"x": 37.5, "y": 37.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [42]], [[{"name": "bg_huoban_xyx5", "rect": {"x": 0, "y": 0, "width": 6, "height": 6}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 6, "height": 6}, "rotated": false, "capInsets": [0, 6, 0, 0], "vertices": {"rawPosition": [-3, -3, 0, 3, -3, 0, -3, 3, 0, 3, 3, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 6, 6, 6, 0, 0, 6, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -3, "y": -3, "z": 0}, "maxPos": {"x": 3, "y": 3, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [43]], [[{"name": "icon_hb_zy6", "rect": {"x": 0, "y": 0, "width": 75, "height": 75}, "offset": {"x": -0.5, "y": 3}, "originalSize": {"width": 76, "height": 81}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-37.5, -37.5, 0, 37.5, -37.5, 0, -37.5, 37.5, 0, 37.5, 37.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 81, 75, 81, 0, 6, 75, 6], "nuv": [0, 0.07407407407407407, 0.9868421052631579, 0.07407407407407407, 0, 1, 0.9868421052631579, 1], "minPos": {"x": -37.5, "y": -37.5, "z": 0}, "maxPos": {"x": 37.5, "y": 37.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [44]]]]