// 平凡职业造就世界最强 - 简易炼金修改器
// 复制此代码到浏览器控制台运行

// 等待游戏加载
function waitForGame() {
    if (window.G && window.G.socket) {
        console.log('🎮 游戏已加载，开始安装修改器...');
        installAlchemyCheat();
    } else {
        console.log('⏳ 等待游戏加载...');
        setTimeout(waitForGame, 2000);
    }
}

// 安装炼金修改器
function installAlchemyCheat() {
    try {
        // 保存原始函数
        const originalSend = window.G.socket.send;
        
        // 替换网络发送函数
        window.G.socket.send = function(command, params, callback) {
            // 拦截宝石合成
            if (command === "baoshi.hecheng") {
                console.log('🔮 拦截炼金请求');
                
                const modifiedCallback = function(result) {
                    if (result.s === 1 && result.d && result.d.prize) {
                        // 成功时双倍产出
                        const original = [...result.d.prize];
                        result.d.prize = [...original, ...original];
                        console.log('✨ 双倍产出已生效');
                    } else if (result.s !== 1) {
                        // 失败时强制成功
                        result.s = 1;
                        result.d = { prize: [{ item_id: "1001", num: 1 }] };
                        console.log('🎉 失败已转为成功');
                    }
                    callback(result);
                };
                
                return originalSend.call(this, command, params, modifiedCallback);
            }
            
            return originalSend.call(this, command, params, callback);
        };
        
        console.log('✅ 炼金修改器安装成功！');
        console.log('📝 现在进行宝石合成将获得以下效果：');
        console.log('   - 100% 成功率');
        console.log('   - 双倍产出奖励');
        console.log('⚠️  请适度使用，避免被检测');
        
    } catch (error) {
        console.error('❌ 修改器安装失败:', error);
    }
}

// 开始安装
console.log('🚀 炼金修改器启动中...');
waitForGame();
