[1, ["a630IDm51O1YjoAijQHuqC@f9941", "bbcnXUjzxOOK3LSFoNz28z@f9941"], ["node", "_spriteFrame", "_parent", "root", "data"], [["cc.Node", ["_name", "_components", "_prefab", "_children", "_parent", "_lpos", "_lscale", "_euler"], 2, 9, 4, 2, 1, 5, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["sp.Skeleton.SpineSocket", ["path", "target"], 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Label", ["_string", "_actualFontSize", "_lineHeight", "_overflow", "_isBold", "node", "__prefab", "_color", "_outlineColor"], -2, 1, 4, 5, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Layout", ["_resizeMode", "_layoutType", "_paddingLeft", "_paddingTop", "_paddingBottom", "_affectedByScale", "node", "__prefab"], -3, 1, 4], ["cc.UIOpacity", ["_opacity", "node", "__prefab"], 2, 1, 4], ["sp.Skeleton", ["_premultipliedAlpha", "_preCacheMode", "node", "__prefab", "_sockets"], 1, 1, 4, 9], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["0ffbcbl89xEj5yYYH4SQgf+", ["i18n_string", "i18n_params", "node", "__prefab"], 1, 1, 4], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4]], [[5, 0, 2], [7, 0, 1, 2, 3, 4, 5, 5], [1, 0, 1, 2, 1], [0, 0, 4, 3, 1, 2, 2], [0, 0, 4, 1, 2, 5, 2], [1, 0, 1, 1], [1, 0, 1, 3, 1], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 6], [8, 0, 1, 2, 3, 4, 5, 6, 7, 7], [9, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4, 3], [0, 0, 3, 1, 2, 6, 2], [2, 1, 2, 3, 4, 2], [3, 1], [4, 0, 2], [0, 0, 3, 1, 2, 2], [0, 0, 3, 1, 2, 5, 7, 2], [0, 0, 4, 3, 1, 2, 6, 2], [2, 0, 1, 2, 3, 4, 3], [2, 0, 2, 3, 4, 2], [3, 0, 1, 2], [11, 0, 1, 1], [12, 0, 1, 2, 3, 3], [13, 0, 1, 2, 3, 4, 4]], [[14, "kaichang"], [15, "kaichang", [-4], [[2, -2, [0, "cbNt558zVBT5jfkgXGRPug"], [5, 640, 1280]], [23, 45, 640, 1000, -3, [0, "26rlsp6J1B7Z9yupnPDCog"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [3, "bg", 1, [-9, -10, -11], [[2, -5, [0, "82nJyIov1Nx6SZRscGy38h"], [5, 640, 1400]], [19, 1, -6, [0, "eb6mRNletL+Im8e24xZziw"], 3], [21, -7, [0, "25VDBeVwlEYosrAYQs28MB"]], [22, "bg_kaichang", "kaichang", -8, [0, "3d/4N/++xGRJUhEiE934xn"]]], [1, "33t/c7TeBDe5jGen0gs4CD", null, null, null, 1, 0]], [16, "qipao1", [-14], [[5, -12, [0, "d9jQL0/n1Bt4bneyhRfbGM"]], [9, 0, -13, [0, "4b0MDUe1pPAIMrPZG2Y5iG"]]], [1, "43nO00745Ca7ArN8XH6ZV0", null, null, null, 1, 0], [1, 119.04998016357422, 596.47998046875, 0], [1, 0, 0, 2.504478065487657e-06]], [17, "bg_talk", 3, [-18], [[2, -15, [0, "37TuIDeWNKtLb89u9KKjSP"], [5, 701, 375.6]], [18, 1, 0, -16, [0, "b6XYPrFHRMR6yiYwZkOvKn"], 0], [8, 1, 3, 112, 170, 130, true, -17, [0, "1ccTaDpeVFkbCm36lISzld"]]], [1, "94Shvv8MVC57wkVXjb3rC5", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [11, "bg_talk", [-22], [[2, -19, [0, "e3eHF2V+JJraK5e97wLI9O"], [5, 701, 301.6]], [12, 0, -20, [0, "648126qpFEyJdIwY62gP1X"], 1], [8, 1, 3, 105.4, 116, 110, true, -21, [0, "b8WA0rr/pJC6yLxDztsWtF"]]], [1, "13rOty/ylFoJdGTxKsv4K5", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [11, "bg_talk", [-26], [[2, -23, [0, "6bjp1Q4zJCrI3UUZ9CKNQp"], [5, 734.4, 325.6]], [12, 0, -24, [0, "9bcpc+1AVANbbcWXQJF9vn"], 2], [8, 1, 3, 128, 140, 110, true, -25, [0, "34GpmIyPJPoay4JOQ6J+6U"]]], [1, "2aXDkfMoJIl54NVR9S8hPc", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [3, "ani_p1", 2, [3], [[6, -27, [0, "cctCKsRsJCcYf6Zim20u0w"], [0, 0.5, 0.3787013918636514]], [10, false, 0, -28, [0, "3c/5ByOMZM6L3nMO8YsZzi"], [[20, "root/ui_dhk", 3]]]], [1, "7dyKsNJFJLE6WzuudiZ9KD", null, null, null, 1, 0]], [3, "ani_p2", 2, [-31], [[6, -29, [0, "e33FDXcONKBrr0a95KWhpE"], [0, 0.5, 0.5625610789179829]], [10, false, 0, -30, [0, "37ygT9FytBFLDd6asUUfh5"], [[13]]]], [1, "80BTUOmMZFNr9LGDJs9Imb", null, null, null, 1, 0]], [3, "qipao2", 8, [5], [[5, -32, [0, "39XgfdLsBHFJDA6hswsBHh"]], [9, 0, -33, [0, "8cLRHLEh9Mh7XeUUboPMb5"]]], [1, "0ftBB9yepO4b2djRUhBbsQ", null, null, null, 1, 0]], [3, "ani_p3", 2, [-36], [[6, -34, [0, "28OBNnBItKBZPitn7DQtd0"], [0, 0.5, 1.3954230186885437]], [10, false, 0, -35, [0, "8fYPLDxYRL0qt53yAysBRE"], [[13]]]], [1, "03bo2ReAREsoNAz1n8wYix", null, null, null, 1, 0]], [3, "qipao3", 10, [6], [[5, -37, [0, "06fptfYVlIfrHYW0RFRX7p"]], [9, 0, -38, [0, "2apiwaGB1BDJ6ABEIcZElN"]]], [1, "05rEjhJwdFCKvhL9qSh27R", null, null, null, 1, 0]], [4, "txt", 4, [[2, -39, [0, "1f+p/eGPtNeacn7OBaqCYS"], [5, 526, 75.6]], [7, "", 40, 60, 3, true, -40, [0, "09TqIEOUdCTo9zo8xxeHmZ"], [4, 4280098330], [4, 4294967295]]], [1, "9a5IF5TGFDW42NQKmzRkBa", null, null, null, 1, 0], [1, 24.5, -20, 0]], [4, "txt", 5, [[2, -41, [0, "8b2j7CUkhAnZozYW8PZCTw"], [5, 526, 75.6]], [7, "", 40, 60, 3, true, -42, [0, "efWMjsbwlLGIAetr/nis+0"], [4, 4280098330], [4, 4294967295]]], [1, "34VxpaZpFI+p80Yv5DDG29", null, null, null, 1, 0], [1, 17.900000000000006, -3, 0]], [4, "txt", 6, [[2, -43, [0, "7a4pPm3P9LCokFGDQmPUEp"], [5, 526, 75.6]], [7, "", 40, 60, 3, true, -44, [0, "1dNdRLxZ9KrLj52a2fdB4K"], [4, 4280098330], [4, 4294967295]]], [1, "b4Ue0rcKRLPJS93s4HxJ+J", null, null, null, 1, 0], [1, 23.80000000000001, -15, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, -2, 8, 0, -3, 10, 0, 0, 3, 0, 0, 3, 0, -1, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 12, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 13, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 14, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, -1, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 4, 1, 3, 2, 7, 5, 2, 9, 6, 2, 11, 44], [0, 0, 0, 0], [1, 1, 1, 1], [0, 0, 0, 1]]