const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/psp-c85b646c-C82U0n06.js","assets/psp-661cb157-D6Az8Wj_.js","assets/psp-c04fe67a-CfnMsVlF.js","assets/psp-cd6a1b8d-CSw1A9l7.js"])))=>i.map(i=>d[i]);
import{_ as s}from"./psp-cd6a1b8d-CSw1A9l7.js";const e="20250730-26701e9";let n;async function r(){return n||(n=(async()=>{const t=window.OPTION?.datadogLogsOption;if(!t)return;console.log("🚀 ~ [psp][datadogLogs] initDatadogLogs ~");const{datadogLogs:o}=await s(async()=>{const{datadogLogs:a}=await import("./psp-c85b646c-C82U0n06.js");return{datadogLogs:a}},__vite__mapDeps([0,1]));return o.init(t),o})(),n)}let i;async function g(){return i||(i=(async()=>{const t=window.OPTION?.datadogRumOption;if(console.log("🚀 ~ [psp][datadogRum] initDatadogRum ~",t),!t)return;console.log("🚀 ~ [psp][datadogRum] initDatadogRum ~");const{datadogRum:o}=await s(async()=>{const{datadogRum:a}=await import("./psp-c04fe67a-CfnMsVlF.js").then(d=>d.z);return{datadogRum:a}},__vite__mapDeps([2,1,3]));return o.init({...t,version:e,beforeSend(a,d){return!0}}),window.datadogRum=o,o})(),i)}const c=()=>{r(),g()};export{g as a,c as i};
