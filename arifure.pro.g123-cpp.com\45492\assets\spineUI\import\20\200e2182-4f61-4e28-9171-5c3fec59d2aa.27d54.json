[1, ["65wwiFno9GKYv5MbQp6rOD@6c48a"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "img_ty_shenyuan", "\nimg_ty_shenyuan.png\nsize: 141,96\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\ndi\n  rotate: true\n  xy: 2, 2\n  size: 92, 98\n  orig: 94, 100\n  offset: 1, 1\n  index: -1\nzs2\n  rotate: false\n  xy: 102, 26\n  size: 13, 21\n  orig: 15, 23\n  offset: 1, 1\n  index: -1\nzs2_glow\n  rotate: false\n  xy: 102, 49\n  size: 37, 45\n  orig: 39, 47\n  offset: 1, 1\n  index: -1\n", ["img_ty_shenyuan.png"], {"skeleton": {"hash": "zRKOuVg4oHZtndZ6ZpZc8KsTed0", "spine": "3.8.99", "x": -50.26, "y": -53, "width": 102.76, "height": 114.5, "images": "./images/", "audio": "F:/pingfanzhiye_work/界面/主界面图标动效/深渊"}, "bones": [{"name": "root"}, {"name": "zs2", "parent": "root", "x": -31.5, "y": 38.5}, {"name": "zs3", "parent": "root", "x": 32.95, "y": 38.5}], "slots": [{"name": "di", "bone": "root", "attachment": "di"}, {"name": "zs2", "bone": "zs2", "attachment": "zs2"}, {"name": "zs3", "bone": "zs3", "attachment": "zs2"}, {"name": "zs2_glow", "bone": "zs3", "color": "ffffff00", "attachment": "zs2_glow", "blend": "additive"}, {"name": "zs2_glow2", "bone": "zs2", "color": "ffffff00", "attachment": "zs2_glow", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"di": {"di": {"x": -1, "y": -3, "width": 94, "height": 100}}, "zs2": {"zs2": {"width": 15, "height": 23}}, "zs2_glow": {"zs2_glow": {"x": 0.05, "y": -0.5, "width": 39, "height": 47}}, "zs2_glow2": {"zs2_glow": {"x": 0.74, "y": -0.5, "width": 39, "height": 47}}, "zs3": {"zs2": {"width": 15, "height": 23}}}}], "animations": {"animation": {"slots": {"zs2_glow": {"color": [{"color": "ffffff24", "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffff80", "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "color": "ffffff24"}]}, "zs2_glow2": {"color": [{"color": "ffffff5c", "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffff80", "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "color": "ffffff5c"}]}}, "bones": {"zs2": {"translate": [{"y": 3.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "y": -1.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 5.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "y": 3.34}]}, "zs3": {"translate": [{"y": 0.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "y": -1.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 5.26, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "y": 0.42}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]