const e={account:{blocked:{content:"此SNS已經與其他G123帳號綁定。",title:"您的帳號已被鎖定。"},links:{complete:"SNS 已綁定",link:"綁定",linked:"已綁定",linked_description:"為了保護您的帳號安全, 建議您進行 SNS 綁定。",success:"綁定成功",title:"SNS 綁定",title_short:"SNS 綁定",unlink:"解除綁定",unlink_confirm:"確定要解除 {{provider}} 的綁定嗎？",unlink_failed:"解除綁定失敗",unlink_success:"解除綁定成功",unlinked_description:"您可以透過與 SNS 的綁定來創建 G123 帳號。"},login:{complete:"已登入",linked_description:"如果您想使用另一個 G123 帳號, 請從以下切換帳戶。",success:"登入成功",success_and_return:"已登入, 將重新回到主頁。",title:"帳號切換",title_short:"帳號切換",unlinked_description:"如果您已經有 G123 帳號，您可以通過以下方式進行登入。"},management:"帳號管理",recovery:{description:"可以使用支付紀錄資訊來恢復您的帳號。",title:"帳號恢復",title_short:"恢復"},sdk:{passkey:{not_supported:"此瀏覽器不支援 Passkey",not_platform_authenticator:"此設備不支援生物識別認證或安全密鑰",user_rejected_or_timeout:"身份驗證已取消或超時。",invalid_state:"認證器狀態無效導致驗證失敗。",operation_not_supported:"您的設備不支援此身份驗證操作。",security_error:"因安全限制導致身份驗證失敗，請檢查網域設定。",operation_aborted:"身份驗證被中斷。",constraint_error:"憑證參數無效導致身份驗證失敗。",data_error:"憑證資料格式無效導致身份驗證失敗。",unknown_error:"發生未知的身份驗證錯誤。",network_error:"網路問題導致身份驗證失敗，請檢查網路連線。"},authentication_success:"認證成功",canceled:"取消",error:"發生錯誤。{{code}}",links:{account_used:"此SNS帳號已被使用。",already_link_others:"此{{providerName}}帳號已經被其他 G123ID 使用",create_link_sns_already_linked:"此 {{providerName}} 帳號已綁定成功",bad_credential:"綁定失敗。(BAD_CREDENTIAL)"},login:{not_linked:"登錄失敗，此SNS帳號未註冊。",bad_credential:"登錄失敗。(BAD_CREDENTIAL)"},imlink:{validate_token_not_found:"此連結已失效，請再次傳送訊息給官方帳號以取得新的綁定連結。"}},title:"帳號"}},t={common:e};export{e as common,t as default};
