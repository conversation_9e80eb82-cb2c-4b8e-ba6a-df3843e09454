const Ke={log:"log",debug:"debug",info:"info",warn:"warn",error:"error"},E=console,$={};Object.keys(Ke).forEach(e=>{$[e]=E[e]});const Y="Datadog Browser SDK:",b={debug:$.debug.bind(E,Y),log:$.log.bind(E,Y),info:$.info.bind(E,Y),warn:$.warn.bind(E,Y),error:$.error.bind(E,Y)},qe="https://docs.datadoghq.com",bn=`${qe}/real_user_monitoring/browser/troubleshooting`,He="More details:";function St(e,t){return(...n)=>{try{return e(...n)}catch(r){b.error(t,r)}}}function we(e){return e!==0&&Math.random()*100<=e}function yn(e,t){return+e.toFixed(t)}function hn(e){return Et(e)&&e>=0&&e<=100}function Et(e){return typeof e=="number"}const j=1e3,x=60*j,_t=60*x,Sn=24*_t,Ot=365*Sn;function So(e){return{relative:e,timeStamp:En(e)}}function Eo(e){return{relative:On(e),timeStamp:e}}function En(e){const t=I()-performance.now();return t>ee()?Math.round(_e(t,e)):Tn(e)}function _o(){return Math.round(I()-_e(ee(),performance.now()))}function Oo(e){return Et(e)?yn(e*1e6,0):e}function I(){return new Date().getTime()}function K(){return I()}function be(){return performance.now()}function k(){return{relative:be(),timeStamp:K()}}function _n(){return{relative:0,timeStamp:ee()}}function Tt(e,t){return t-e}function _e(e,t){return e+t}function On(e){return e-ee()}function Tn(e){return Math.round(_e(ee(),e))}function To(e){return e<Ot}let Ce;function ee(){return Ce===void 0&&(Ce=performance.timing.navigationStart),Ce}const J=1024,vt=1024*J,vn=/[^\u0000-\u007F]/;function wt(e){return vn.test(e)?window.TextEncoder!==void 0?new TextEncoder().encode(e).length:new Blob([e]).size:e.length}function vo(e){const t=e.reduce((s,o)=>s+o.length,0),n=new Uint8Array(t);let r=0;for(const s of e)n.set(s,r),r+=s.length;return n}function wn(e){return{...e}}function Ct(e,t){return Object.keys(e).some(n=>e[n]===t)}function Oe(e){return Object.keys(e).length===0}function wo(e,t){const n={};for(const r of Object.keys(e))n[r]=t(e[r]);return n}function L(){if(typeof globalThis=="object")return globalThis;Object.defineProperty(Object.prototype,"_dd_temp_",{get(){return this},configurable:!0});let e=_dd_temp_;return delete Object.prototype._dd_temp_,typeof e!="object"&&(typeof self=="object"?e=self:typeof window=="object"?e=window:e={}),e}function D(e,t){const n=L();let r;return n.Zone&&typeof n.Zone.__symbol__=="function"&&(r=e[n.Zone.__symbol__(t)]),r||(r=e[t]),r}let ye,Rt=!1;function Cn(e){ye=e}function Rn(e){Rt=e}function Co(e,t,n){const r=n.value;n.value=function(...s){return(ye?h(r):r).apply(this,s)}}function h(e){return function(){return X(e,this,arguments)}}function X(e,t,n){try{return e.apply(t,n)}catch(r){At(r)}}function At(e){if(Pe(e),ye)try{ye(e)}catch(t){Pe(t)}}function Pe(...e){Rt&&b.error("[MONITOR]",...e)}function q(e,t){return D(L(),"setTimeout")(h(e),t)}function It(e){D(L(),"clearTimeout")(e)}function ze(e,t){return D(L(),"setInterval")(h(e),t)}function xt(e){D(L(),"clearInterval")(e)}class y{constructor(t){this.onFirstSubscribe=t,this.observers=[]}subscribe(t){return this.observers.push(t),this.observers.length===1&&this.onFirstSubscribe&&(this.onLastUnsubscribe=this.onFirstSubscribe(this)||void 0),{unsubscribe:()=>{this.observers=this.observers.filter(n=>t!==n),!this.observers.length&&this.onLastUnsubscribe&&this.onLastUnsubscribe()}}}notify(t){this.observers.forEach(n=>n(t))}}function kt(...e){return new y(t=>{const n=e.map(r=>r.subscribe(s=>t.notify(s)));return()=>n.forEach(r=>r.unsubscribe())})}function An(e,t,n){const r=n&&n.leading!==void 0?n.leading:!0,s=n&&n.trailing!==void 0?n.trailing:!0;let o=!1,i,u;return{throttled:(...a)=>{if(o){i=a;return}r?e(...a):i=a,o=!0,u=q(()=>{s&&i&&e(...i),o=!1,i=void 0},t)},cancel:()=>{It(u),o=!1,i=void 0}}}function B(){}function O(e){return e?(parseInt(e,10)^Math.random()*16>>parseInt(e,10)/4).toString(16):`10000000-1000-4000-8000-${1e11}`.replace(/[018]/g,O)}const he=/([\w-]+)\s*=\s*([^;]+)/g;function In(e,t){for(he.lastIndex=0;;){const n=he.exec(e);if(n){if(n[1]===t)return n[2]}else break}}function xn(e){const t=new Map;for(he.lastIndex=0;;){const n=he.exec(e);if(n)t.set(n[1],n[2]);else break}return t}function kn(e,t,n=""){const r=e.charCodeAt(t-1),o=r>=55296&&r<=56319?t+1:t;return e.length<=o?e:`${e.slice(0,o)}${n}`}function Ln(){return Lt()===0}function Ro(){return Lt()===1}let oe;function Lt(){return oe??(oe=Nn())}function Nn(e=window){var t;const n=e.navigator.userAgent;return e.chrome||/HeadlessChrome/.test(n)?0:((t=e.navigator.vendor)===null||t===void 0?void 0:t.indexOf("Apple"))===0||/safari/i.test(n)&&!/chrome|android/i.test(n)?1:2}function Te(e,t,n=0,r){const s=new Date;s.setTime(s.getTime()+n);const o=`expires=${s.toUTCString()}`,i=r&&r.crossSite?"none":"strict",u=r&&r.domain?`;domain=${r.domain}`:"",a=r&&r.secure?";secure":"",l=r&&r.partitioned?";partitioned":"";document.cookie=`${e}=${t};${o};path=/;samesite=${i}${u}${a}${l}`}function We(e){return In(document.cookie,e)}let Re;function C(e){return Re||(Re=xn(document.cookie)),Re.get(e)}function Nt(e,t){Te(e,"",0,t)}function $n(e){if(document.cookie===void 0||document.cookie===null)return!1;try{const t=`dd_cookie_test_${O()}`,n="test";Te(t,n,x,e);const r=We(t)===n;return Nt(t,e),r}catch(t){return b.error(t),!1}}let Ae;function Un(){if(Ae===void 0){const e=`dd_site_test_${O()}`,t="test",n=window.location.hostname.split(".");let r=n.pop();for(;n.length&&!We(e);)r=`${n.pop()}.${r}`,Te(e,t,j,{domain:r});Nt(e,{domain:r}),Ae=r}return Ae}const te="_dd_s";function Ao(e,t){for(let n=e.length-1;n>=0;n-=1){const r=e[n];if(t(r,n,e))return r}}function $t(e){return Object.values(e)}function Mn(e){return Object.entries(e)}const Ye=4*_t,Ut=15*x,Pn=Ot,Mt="0",Q={COOKIE:"cookie",LOCAL_STORAGE:"local-storage"},Pt=/^([a-zA-Z]+)=([a-z0-9-]+)$/,Ve="&";function Dn(e){return!!e&&(e.indexOf(Ve)!==-1||Pt.test(e))}const Bn="1";function v(e,t){const n={isExpired:Bn};return t.trackAnonymousUser&&(e?.anonymousId?n.anonymousId=e?.anonymousId:n.anonymousId=O()),n}function fe(e){return Oe(e)}function Dt(e){return!fe(e)}function Se(e){return e.isExpired!==void 0||!Fn(e)}function Fn(e){return(e.created===void 0||I()-Number(e.created)<Ye)&&(e.expire===void 0||I()<Number(e.expire))}function Bt(e){e.expire=String(I()+Ut)}function Ft(e){return Mn(e).map(([t,n])=>t==="anonymousId"?`aid=${n}`:`${t}=${n}`).join(Ve)}function Gt(e){const t={};return Dn(e)&&e.split(Ve).forEach(n=>{const r=Pt.exec(n);if(r!==null){const[,s,o]=r;s==="aid"?t.anonymousId=o:t[s]=o}}),t}const Gn="_dd",jn="_dd_r",Kn="_dd_l",qn="rum",Hn="logs";function zn(e){if(!C(te)){const n=C(Gn),r=C(jn),s=C(Kn),o={};n&&(o.id=n),s&&/^[01]$/.test(s)&&(o[Hn]=s),r&&/^[012]$/.test(r)&&(o[qn]=r),Dt(o)&&(Bt(o),e.persistSession(o))}}function ct(e){const t=Yn(e);return $n(t)?{type:Q.COOKIE,cookieOptions:t}:void 0}function Wn(e,t){const n={isLockEnabled:Ln(),persistSession:r=>at(t,e,r,Ut),retrieveSession:jt,expireSession:r=>at(t,e,v(r,e),Ye)};return zn(n),n}function at(e,t,n,r){Te(te,Ft(n),t.trackAnonymousUser?Pn:r,e)}function jt(){const e=We(te);return Gt(e)}function Yn(e){const t={};return t.secure=!!e.useSecureSessionCookie||!!e.usePartitionedCrossSiteSessionCookie,t.crossSite=!!e.usePartitionedCrossSiteSessionCookie,t.partitioned=!!e.usePartitionedCrossSiteSessionCookie,e.trackSessionAcrossSubdomains&&(t.domain=Un()),t}const Vn="_dd_test_";function ut(){try{const e=O(),t=`${Vn}${e}`;localStorage.setItem(t,e);const n=localStorage.getItem(t);return localStorage.removeItem(t),e===n?{type:Q.LOCAL_STORAGE}:void 0}catch{return}}function Jn(e){return{isLockEnabled:!1,persistSession:Kt,retrieveSession:Xn,expireSession:t=>Qn(t,e)}}function Kt(e){localStorage.setItem(te,Ft(e))}function Xn(){const e=localStorage.getItem(te);return Gt(e)}function Qn(e,t){Kt(v(e,t))}function H(e,t,n){if(typeof e!="object"||e===null)return JSON.stringify(e);const r=U(Object.prototype),s=U(Array.prototype),o=U(Object.getPrototypeOf(e)),i=U(e);try{return JSON.stringify(e,t,n)}catch{return"<error: unable to serialize object>"}finally{r(),s(),o(),i()}}function U(e){const t=e,n=t.toJSON;return n?(delete t.toJSON,()=>{t.toJSON=n}):B}const Zn=220*J,er="$",tr=3;function Z(e,t=Zn){const n=U(Object.prototype),r=U(Array.prototype),s=[],o=new WeakMap,i=Ie(e,er,void 0,s,o),u=JSON.stringify(i);let a=u?u.length:0;if(a>t){xe(t,"discarded",e);return}for(;s.length>0&&a<t;){const l=s.shift();let c=0;if(Array.isArray(l.source))for(let d=0;d<l.source.length;d++){const f=Ie(l.source[d],l.path,d,s,o);if(f!==void 0?a+=JSON.stringify(f).length:a+=4,a+=c,c=1,a>t){xe(t,"truncated",e);break}l.target[d]=f}else for(const d in l.source)if(Object.prototype.hasOwnProperty.call(l.source,d)){const f=Ie(l.source[d],l.path,d,s,o);if(f!==void 0&&(a+=JSON.stringify(f).length+c+d.length+tr,c=1),a>t){xe(t,"truncated",e);break}l.target[d]=f}}return n(),r(),i}function Ie(e,t,n,r,s){const o=sr(e);if(!o||typeof o!="object")return nr(o);const i=De(o);if(i!=="[Object]"&&i!=="[Array]"&&i!=="[Error]")return i;const u=e;if(s.has(u))return`[Reference seen at ${s.get(u)}]`;const a=n!==void 0?`${t}.${n}`:t,l=Array.isArray(o)?[]:{};return s.set(u,a),r.push({source:o,target:l,path:a}),l}function nr(e){return typeof e=="bigint"?`[BigInt] ${e.toString()}`:typeof e=="function"?`[Function] ${e.name||"unknown"}`:typeof e=="symbol"?`[Symbol] ${e.description||e.toString()}`:e}function De(e){try{if(e instanceof Event)return rr(e);if(e instanceof RegExp)return`[RegExp] ${e.toString()}`;const n=Object.prototype.toString.call(e).match(/\[object (.*)\]/);if(n&&n[1])return`[${n[1]}]`}catch{}return"[Unserializable]"}function rr(e){return{type:e.type,isTrusted:e.isTrusted,currentTarget:e.currentTarget?De(e.currentTarget):null,target:e.target?De(e.target):null}}function sr(e){const t=e;if(t&&typeof t.toJSON=="function")try{return t.toJSON()}catch{}return e}function xe(e,t,n){b.warn(`The data provided has been ${t} as it is over the limit of ${e} characters:`,n)}const F="?";function ne(e){var t,n;const r=[];let s=ke(e,"stack");const o=String(e);if(s&&s.startsWith(o)&&(s=s.slice(o.length)),s&&s.split(`
`).forEach(i=>{const u=cr(i)||ur(i)||dr(i)||mr(i);u&&(!u.func&&u.line&&(u.func=F),r.push(u))}),r.length>0&&hr()&&e instanceof Error){const i=[];let u=e;for(;(u=Object.getPrototypeOf(u))&&Ht(u);){const a=((t=u.constructor)===null||t===void 0?void 0:t.name)||F;i.push(a)}for(let a=i.length-1;a>=0&&((n=r[0])===null||n===void 0?void 0:n.func)===i[a];a--)r.shift()}return{message:ke(e,"message"),name:ke(e,"name"),stack:r}}const qt="((?:file|https?|blob|chrome-extension|electron|native|eval|webpack|snippet|<anonymous>|\\w+\\.|\\/).*?)",G="(?::(\\d+))",or=new RegExp(`^\\s*at (.*?) ?\\(${qt}${G}?${G}?\\)?\\s*$`,"i"),ir=new RegExp(`\\((\\S*)${G}${G}\\)`);function cr(e){const t=or.exec(e);if(!t)return;const n=t[2]&&t[2].indexOf("native")===0,r=t[2]&&t[2].indexOf("eval")===0,s=ir.exec(t[2]);return r&&s&&(t[2]=s[1],t[3]=s[2],t[4]=s[3]),{args:n?[t[2]]:[],column:t[4]?+t[4]:void 0,func:t[1]||F,line:t[3]?+t[3]:void 0,url:n?void 0:t[2]}}const ar=new RegExp(`^\\s*at ?${qt}${G}?${G}??\\s*$`,"i");function ur(e){const t=ar.exec(e);if(t)return{args:[],column:t[3]?+t[3]:void 0,func:F,line:t[2]?+t[2]:void 0,url:t[1]}}const lr=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i;function dr(e){const t=lr.exec(e);if(t)return{args:[],column:t[4]?+t[4]:void 0,func:t[1]||F,line:+t[3],url:t[2]}}const fr=/^\s*(.*?)(?:\((.*?)\))?(?:(?:(?:^|@)((?:file|https?|blob|chrome|webpack|resource|capacitor|\[native).*?|[^@]*bundle|\[wasm code\])(?::(\d+))?(?::(\d+))?)|@)\s*$/i,pr=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i;function mr(e){const t=fr.exec(e);if(!t)return;const n=t[3]&&t[3].indexOf(" > eval")>-1,r=pr.exec(t[3]);return n&&r&&(t[3]=r[1],t[4]=r[2],t[5]=void 0),{args:t[2]?t[2].split(","):[],column:t[5]?+t[5]:void 0,func:t[1]||F,line:t[4]?+t[4]:void 0,url:t[3]}}function ke(e,t){if(typeof e!="object"||!e||!(t in e))return;const n=e[t];return typeof n=="string"?n:void 0}function gr(e,t,n,r){if(t===void 0)return;const{name:s,message:o}=yr(e);return{name:s,message:o,stack:[{url:t,column:r,line:n}]}}const br=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?([\s\S]*)$/;function yr(e){let t,n;return{}.toString.call(e)==="[object String]"&&([,t,n]=br.exec(e)),{name:t,message:n}}function Ht(e){return String(e.constructor).startsWith("class ")}let ie;function hr(){if(ie!==void 0)return ie;class e extends Error{constructor(){super(),this.name="Error"}}const[t,n]=[e,Error].map(r=>new r);return ie=Ht(Object.getPrototypeOf(t))&&n.stack!==t.stack,ie}function zt(e){const n=new Error(e);n.name="HandlingStack";let r;return X(()=>{const s=ne(n);s.stack=s.stack.slice(2),r=re(s)}),r}function re(e){let t=Wt(e);return e.stack.forEach(n=>{const r=n.func==="?"?"<anonymous>":n.func,s=n.args&&n.args.length>0?`(${n.args.join(", ")})`:"",o=n.line?`:${n.line}`:"",i=n.line&&n.column?`:${n.column}`:"";t+=`
  at ${r}${s} @ ${n.url}${o}${i}`}),t}function Wt(e){return`${e.name||"Error"}: ${e.message}`}const Yt="No stack, consider using an instance of Error";function Vt({stackTrace:e,originalError:t,handlingStack:n,componentStack:r,startClocks:s,nonErrorPrefix:o,useFallbackStack:i=!0,source:u,handling:a}){const l=N(t);return!e&&l&&(e=ne(t)),{startClocks:s,source:u,handling:a,handlingStack:n,componentStack:r,originalError:t,type:e?e.name:void 0,message:Sr(e,l,o,t),stack:e?re(e):i?Yt:void 0,causes:l?Or(t,u):void 0,fingerprint:Er(t),context:_r(t)}}function Sr(e,t,n,r){return e?.message&&e?.name?e.message:t?"Empty message":`${n} ${H(Z(r))}`}function Er(e){return N(e)&&"dd_fingerprint"in e?String(e.dd_fingerprint):void 0}function _r(e){if(e!==null&&typeof e=="object"&&"dd_context"in e)return e.dd_context}function Io(e){var t;return(t=/@ (.+)/.exec(e))===null||t===void 0?void 0:t[1]}function N(e){return e instanceof Error||Object.prototype.toString.call(e)==="[object Error]"}function Or(e,t){let n=e;const r=[];for(;N(n?.cause)&&r.length<10;){const s=ne(n.cause);r.push({message:n.cause.message,source:t,type:s?.name,stack:s&&re(s)}),n=n.cause}return r.length?r:void 0}var Be;(function(e){e.TRACK_INTAKE_REQUESTS="track_intake_requests",e.WRITABLE_RESOURCE_GRAPHQL="writable_resource_graphql"})(Be||(Be={}));const Je=new Set;function xo(e){Array.isArray(e)&&Tr(e.filter(t=>Ct(Be,t)))}function Tr(e){e.forEach(t=>{Je.add(t)})}function ko(e){return Je.has(e)}function vr(){return Je}const wr="datad0g.com",Cr="dd0g-gov.com",M="datadoghq.com",Lo="datadoghq.eu",Rr="ddog-gov.com",Ar="pci.browser-intake-datadoghq.com",Ir=["ddsource","ddtags"];function xr(e,t){const n=window.__ddBrowserSdkExtensionCallback;n&&n({type:e,payload:t})}function Xe(e){return e===null?"null":Array.isArray(e)?"array":typeof e}function Ee(e,t,n=Lr()){if(t===void 0)return e;if(typeof t!="object"||t===null)return t;if(t instanceof Date)return new Date(t.getTime());if(t instanceof RegExp){const s=t.flags||[t.global?"g":"",t.ignoreCase?"i":"",t.multiline?"m":"",t.sticky?"y":"",t.unicode?"u":""].join("");return new RegExp(t.source,s)}if(n.hasAlreadyBeenSeen(t))return;if(Array.isArray(t)){const s=Array.isArray(e)?e:[];for(let o=0;o<t.length;++o)s[o]=Ee(s[o],t[o],n);return s}const r=Xe(e)==="object"?e:{};for(const s in t)Object.prototype.hasOwnProperty.call(t,s)&&(r[s]=Ee(r[s],t[s],n));return r}function kr(e){return Ee(void 0,e)}function Qe(...e){let t;for(const n of e)n!=null&&(t=Ee(t,n));return t}function Lr(){if(typeof WeakSet<"u"){const t=new WeakSet;return{hasAlreadyBeenSeen(n){const r=t.has(n);return r||t.add(n),r}}}const e=[];return{hasAlreadyBeenSeen(t){const n=e.indexOf(t)>=0;return n||e.push(t),n}}}function Nr(){var e;const t=window.navigator;return{status:t.onLine?"connected":"not_connected",interfaces:t.connection&&t.connection.type?[t.connection.type]:void 0,effective_type:(e=t.connection)===null||e===void 0?void 0:e.effectiveType}}function No(e){const t=new Set;return e.forEach(n=>t.add(n)),Array.from(t)}function Jt(e,t){const n=e.indexOf(t);n>=0&&e.splice(n,1)}const $r=500;function Ur(){const e=[];return{add:s=>{e.push(s)>$r&&e.splice(0,1)},remove:s=>{Jt(e,s)},drain:s=>{e.forEach(o=>o(s)),e.length=0}}}function Mr(e){return e>=500}function $o(e){try{return e.clone()}catch{return}}const se={AGENT:"agent",CONSOLE:"console",CUSTOM:"custom",LOGGER:"logger",NETWORK:"network",SOURCE:"source",REPORT:"report"},Pr=80*J,Dr=32,Xt=3*vt,Br=x,Qt=j;function Zt(e,t,n,r,s){t.transportStatus===0&&t.queuedPayloads.size()===0&&t.bandwidthMonitor.canHandle(e)?tn(e,t,n,{onSuccess:()=>nn(0,t,n,r,s),onFailure:()=>{t.queuedPayloads.enqueue(e),en(t,n,r,s)}}):t.queuedPayloads.enqueue(e)}function en(e,t,n,r){e.transportStatus===2&&q(()=>{const s=e.queuedPayloads.first();tn(s,e,t,{onSuccess:()=>{e.queuedPayloads.dequeue(),e.currentBackoffTime=Qt,nn(1,e,t,n,r)},onFailure:()=>{e.currentBackoffTime=Math.min(Br,e.currentBackoffTime*2),en(e,t,n,r)}})},e.currentBackoffTime)}function tn(e,t,n,{onSuccess:r,onFailure:s}){t.bandwidthMonitor.add(e),n(e,o=>{t.bandwidthMonitor.remove(e),Fr(o)?(t.transportStatus=t.bandwidthMonitor.ongoingRequestCount>0?1:2,e.retry={count:e.retry?e.retry.count+1:1,lastFailureStatus:o.status},s()):(t.transportStatus=0,r())})}function nn(e,t,n,r,s){e===0&&t.queuedPayloads.isFull()&&!t.queueFullReported&&(s({message:`Reached max ${r} events size queued for upload: ${Xt/vt}MiB`,source:se.AGENT,startClocks:k()}),t.queueFullReported=!0);const o=t.queuedPayloads;for(t.queuedPayloads=rn();o.size()>0;)Zt(o.dequeue(),t,n,r,s)}function Fr(e){return e.type!=="opaque"&&(e.status===0&&!navigator.onLine||e.status===408||e.status===429||Mr(e.status))}function Gr(){return{transportStatus:0,currentBackoffTime:Qt,bandwidthMonitor:jr(),queuedPayloads:rn(),queueFullReported:!1}}function rn(){const e=[];return{bytesCount:0,enqueue(t){this.isFull()||(e.push(t),this.bytesCount+=t.bytesCount)},first(){return e[0]},dequeue(){const t=e.shift();return t&&(this.bytesCount-=t.bytesCount),t},size(){return e.length},isFull(){return this.bytesCount>=Xt}}}function jr(){return{ongoingRequestCount:0,ongoingByteCount:0,canHandle(e){return this.ongoingRequestCount===0||this.ongoingByteCount+e.bytesCount<=Pr&&this.ongoingRequestCount<Dr},add(e){this.ongoingRequestCount+=1,this.ongoingByteCount+=e.bytesCount},remove(e){this.ongoingRequestCount-=1,this.ongoingByteCount-=e.bytesCount}}}function Kr(e,t,n){const r=Gr(),s=(o,i)=>zr(e,t,o,i);return{send:o=>{Zt(o,r,s,e.trackType,n)},sendOnExit:o=>{qr(e,t,o)}}}function qr(e,t,n){if(!!navigator.sendBeacon&&n.bytesCount<t)try{const s=e.build("beacon",n);if(navigator.sendBeacon(s,n.data))return}catch(s){Hr(s)}Fe(e,n)}let lt=!1;function Hr(e){lt||(lt=!0,At(e))}function zr(e,t,n,r){if(Wr()&&n.bytesCount<t){const o=e.build("fetch-keepalive",n);fetch(o,{method:"POST",body:n.data,keepalive:!0,mode:"cors"}).then(h(i=>r?.({status:i.status,type:i.type}))).catch(h(()=>Fe(e,n,r)))}else Fe(e,n,r)}function Fe(e,t,n){const r=e.build("fetch",t);fetch(r,{method:"POST",body:t.data,mode:"cors"}).then(h(s=>n?.({status:s.status,type:s.type}))).catch(h(()=>n?.({status:0})))}function Wr(){try{return window.Request&&"keepalive"in new Request("http://a")}catch{return!1}}function Ze(){const e=Vr();if(e)return{getCapabilities(){var t;return JSON.parse(((t=e.getCapabilities)===null||t===void 0?void 0:t.call(e))||"[]")},getPrivacyLevel(){var t;return(t=e.getPrivacyLevel)===null||t===void 0?void 0:t.call(e)},getAllowedWebViewHosts(){return JSON.parse(e.getAllowedWebViewHosts())},send(t,n,r){const s=r?{id:r}:void 0;e.send(JSON.stringify({eventType:t,event:n,view:s}))}}}function Uo(e){const t=Ze();return!!t&&t.getCapabilities().includes(e)}function Yr(e){var t;e===void 0&&(e=(t=L().location)===null||t===void 0?void 0:t.hostname);const n=Ze();return!!n&&n.getAllowedWebViewHosts().some(r=>e===r||e.endsWith(`.${r}`))}function Vr(){return L().DatadogEventBridge}function z(e,t,n,r,s){return et(e,t,[n],r,s)}function et(e,t,n,r,{once:s,capture:o,passive:i}={}){const u=h(f=>{!f.isTrusted&&!f.__ddIsTrusted&&!e.allowUntrustedEvents||(s&&d(),r(f))}),a=i?{capture:o,passive:i}:o,l=window.EventTarget&&t instanceof EventTarget?window.EventTarget.prototype:t,c=D(l,"addEventListener");n.forEach(f=>c.call(t,f,u,a));function d(){const f=D(l,"removeEventListener");n.forEach(m=>f.call(t,m,u,a))}return{stop:d}}const pe={HIDDEN:"visibility_hidden",UNLOADING:"before_unload",PAGEHIDE:"page_hide",FROZEN:"page_frozen"};function Mo(e){return new y(t=>{const{stop:n}=et(e,window,["visibilitychange","freeze"],s=>{s.type==="visibilitychange"&&document.visibilityState==="hidden"?t.notify({reason:pe.HIDDEN}):s.type==="freeze"&&t.notify({reason:pe.FROZEN})},{capture:!0}),r=z(e,window,"beforeunload",()=>{t.notify({reason:pe.UNLOADING})}).stop;return()=>{n(),r()}})}function Jr(e){return $t(pe).includes(e)}function Xr({encoder:e,request:t,flushController:n,messageBytesLimit:r}){let s={};const o=n.flushObservable.subscribe(d=>c(d));function i(d,f,m){n.notifyBeforeAddMessage(f),m!==void 0?(s[m]=d,n.notifyAfterAddMessage()):e.write(e.isEmpty?d:`
${d}`,g=>{n.notifyAfterAddMessage(g-f)})}function u(d){return d!==void 0&&s[d]!==void 0}function a(d){const f=s[d];delete s[d];const m=e.estimateEncodedBytesCount(f);n.notifyAfterRemoveMessage(m)}function l(d,f){const m=H(d),g=e.estimateEncodedBytesCount(m);if(g>=r){b.warn(`Discarded a message whose size was bigger than the maximum allowed size ${r}KB. ${He} ${bn}/#technical-limitations`);return}u(f)&&a(f),i(m,g,f)}function c(d){const f=$t(s).join(`
`);s={};const m=Jr(d.reason),g=m?t.sendOnExit:t.send;if(m&&e.isAsync){const S=e.finishSync();S.outputBytesCount&&g(dt(S));const _=[S.pendingData,f].filter(Boolean).join(`
`);_&&g({data:_,bytesCount:wt(_)})}else f&&e.write(e.isEmpty?f:`
${f}`),e.finish(S=>{g(dt(S))})}return{flushController:n,add:l,upsert:l,stop:o.unsubscribe}}function dt(e){let t;return typeof e.output=="string"?t=e.output:t=new Blob([e.output],{type:"text/plain"}),{data:t,bytesCount:e.outputBytesCount,encoding:e.encoding}}function Qr({messagesLimit:e,bytesLimit:t,durationLimit:n,pageMayExitObservable:r,sessionExpireObservable:s}){const o=r.subscribe(g=>c(g.reason)),i=s.subscribe(()=>c("session_expire")),u=new y(()=>()=>{o.unsubscribe(),i.unsubscribe()});let a=0,l=0;function c(g){if(l===0)return;const S=l,_=a;l=0,a=0,m(),u.notify({reason:g,messagesCount:S,bytesCount:_})}let d;function f(){d===void 0&&(d=q(()=>{c("duration_limit")},n))}function m(){It(d),d=void 0}return{flushObservable:u,get messagesCount(){return l},notifyBeforeAddMessage(g){a+g>=t&&c("bytes_limit"),l+=1,a+=g,f()},notifyAfterAddMessage(g=0){a+=g,l>=e?c("messages_limit"):a>=t&&c("bytes_limit")},notifyAfterRemoveMessage(g){a-=g,l-=1,l===0&&m()}}}function Zr(e,t,n,r,s,o,i=Xr){const u=l(e,t),a=n&&l(e,n);function l(c,{endpoint:d,encoder:f}){return i({encoder:f,request:Kr(d,c.batchBytesLimit,r),flushController:Qr({messagesLimit:c.batchMessagesLimit,bytesLimit:c.batchBytesLimit,durationLimit:c.flushTimeout,pageMayExitObservable:s,sessionExpireObservable:o}),messageBytesLimit:c.messageBytesLimit})}return{flushObservable:u.flushController.flushObservable,add(c,d=!0){u.add(c),a&&d&&a.add(n.transformMessage?n.transformMessage(c):c)},upsert:(c,d)=>{u.upsert(c,d),a&&a.upsert(n.transformMessage?n.transformMessage(c):c,d)},stop:()=>{u.stop(),a&&a.stop()}}}const R={LOG:"log",CONFIGURATION:"configuration",USAGE:"usage"},es=["https://www.datadoghq-browser-agent.com","https://www.datad0g-browser-agent.com","https://d3uc069fcn7uxw.cloudfront.net","https://d20xtzwzcl0ceb.cloudfront.net","http://localhost","<anonymous>"],ts=[Rr];let sn=Ur(),W=e=>{sn.add(()=>W(e))};function Po(e,t,n,r,s){const o=new y,{stop:i}=rs(t,n,r,s,o),{enabled:u,setContextProvider:a}=ns(e,t,o);return{setContextProvider:a,stop:i,enabled:u}}function ns(e,t,n){const r=new Set,s=new Map,o=!ts.includes(t.site)&&we(t.telemetrySampleRate),i={[R.LOG]:o,[R.CONFIGURATION]:o&&we(t.telemetryConfigurationSampleRate),[R.USAGE]:o&&we(t.telemetryUsageSampleRate)},u=ss();return W=l=>{const c=H(l);if(i[l.type]&&r.size<t.maxTelemetryEventsPerPage&&!r.has(c)){const d=a(e,l,u);n.notify(d),xr("telemetry",d),r.add(c)}},sn.drain(),Cn(is),{setContextProvider:(l,c)=>s.set(l,c),enabled:o};function a(l,c,d){const f={type:"telemetry",date:K(),service:l,version:"6.13.0",source:"browser",_dd:{format_version:2},telemetry:Qe(c,{runtime_env:d,connectivity:Nr(),sdk_setup:"npm"}),experimental_features:Array.from(vr())};for(const[m,g]of s)f[m]=g();return f}}function rs(e,t,n,r,s){const o=[];if(Yr()){const i=Ze(),u=s.subscribe(a=>i.send("internal_telemetry",a));o.push(()=>u.unsubscribe())}else{const i=Zr(e,{endpoint:e.rumEndpointBuilder,encoder:r(4)},e.replica&&{endpoint:e.replica.rumEndpointBuilder,encoder:r(5)},t,n,new y);o.push(()=>i.stop());const u=s.subscribe(a=>i.add(a,os(e)));o.push(()=>u.unsubscribe())}return{stop:()=>o.forEach(i=>i())}}function ss(){return{is_local_file:window.location.protocol==="file:",is_worker:"WorkerGlobalScope"in self}}function os(e){return e.site===wr}function on(e,t){Pe(Ke.debug,e,t),W({type:R.LOG,message:e,status:"debug",...t})}function is(e,t){W({type:R.LOG,status:"error",...as(e),...t})}function Do(e){W({type:R.CONFIGURATION,configuration:e})}function cs(e){W({type:R.USAGE,usage:e})}function as(e){if(N(e)){const t=ne(e);return{error:{kind:t.name,stack:re(us(t))},message:t.message}}return{error:{stack:Yt},message:`Uncaught ${H(e)}`}}function us(e){return e.stack=e.stack.filter(t=>!t.url||es.some(n=>t.url.startsWith(n))),e}const ls=10,ds=100,fs=j,cn="--",an=[];let me;function w(e,t,n=0){var r;const{isLockEnabled:s,persistSession:o,expireSession:i}=t,u=f=>o({...f,lock:l}),a=()=>{const{lock:f,...m}=t.retrieveSession();return{session:m,lock:f&&!ms(f)?f:void 0}};if(me||(me=e),e!==me){an.push(e);return}if(s&&n>=ds){on("Aborted session operation after max lock retries",{currentStore:a()}),ft(t);return}let l,c=a();if(s){if(c.lock){ce(e,t,n);return}if(l=ps(),u(c.session),c=a(),c.lock!==l){ce(e,t,n);return}}let d=e.process(c.session);if(s&&(c=a(),c.lock!==l)){ce(e,t,n);return}if(d&&(Se(d)?i(d):(Bt(d),s?u(d):o(d))),s&&!(d&&Se(d))){if(c=a(),c.lock!==l){ce(e,t,n);return}o(c.session),d=c.session}(r=e.after)===null||r===void 0||r.call(e,d||c.session),ft(t)}function ce(e,t,n){q(()=>{w(e,t,n+1)},ls)}function ft(e){me=void 0;const t=an.shift();t&&w(t,e)}function ps(){return O()+cn+K()}function ms(e){const[,t]=e.split(cn);return!t||Tt(Number(t),K())>fs}const pt=j;function gs(e){switch(e.sessionPersistence){case Q.COOKIE:return ct(e);case Q.LOCAL_STORAGE:return ut();case void 0:{let t=ct(e);return!t&&e.allowFallbackToLocalStorage&&(t=ut()),t}default:b.error(`Invalid session persistence '${String(e.sessionPersistence)}'`)}}function bs(e,t,n,r){const s=new y,o=new y,i=new y,u=e.type===Q.COOKIE?Wn(t,e.cookieOptions):Jn(t),{expireSession:a}=u,l=ze(g,pt);let c;_();const{throttled:d,cancel:f}=An(()=>{w({process:p=>{if(fe(p))return;const T=S(p);return dn(T),T},after:p=>{Dt(p)&&!ve()&&mn(p),c=p}},u)},pt);function m(){w({process:p=>ve()?S(p):void 0},u)}function g(){w({process:p=>Se(p)?v(p,t):void 0,after:S},u)}function S(p){return Se(p)&&(p=v(p,t)),ve()&&(fn(p)?pn():(i.notify({previousState:c,newState:p}),c=p)),p}function _(){w({process:p=>{if(fe(p))return v(p,t)},after:p=>{c=p}},u)}function dn(p){if(fe(p))return!1;const T=r(p[n]);p[n]=T,delete p.isExpired,T!==Mt&&!p.id&&(p.id=O(),p.created=String(I()))}function ve(){return c?.[n]!==void 0}function fn(p){return c.id!==p.id||c[n]!==p[n]}function pn(){c=v(c,t),o.notify()}function mn(p){c=p,s.notify()}function gn(p){w({process:T=>({...T,...p}),after:S},u)}return{expandOrRenewSession:d,expandSession:m,getSession:()=>c,renewObservable:s,expireObservable:o,sessionStateUpdateObservable:i,restartSession:_,expire:()=>{f(),a(c),S(v(c,t))},stop:()=>{xt(l)},updateSessionState:gn}}const Ge={GRANTED:"granted",NOT_GRANTED:"not-granted"};function Bo(e){const t=new y;return{tryToInit(n){e||(e=n)},update(n){e=n,t.notify()},isGranted(){return e===Ge.GRANTED},observable:t}}function Fo(e){const t=Xe(e);return t==="string"||t==="function"||e instanceof RegExp}function ys(e,t,n=!1){return e.some(r=>{try{if(typeof r=="function")return r(t);if(r instanceof RegExp)return r.test(t);if(typeof r=="string")return n?t.startsWith(r):r===t}catch(s){b.error(s)}return!1})}const hs=["chrome-extension://","moz-extension://"];function mt(e){return hs.some(t=>e.includes(t))}function Ss(e,t=new Error().stack){return!mt(e)&&mt(t||"")}const Es="Running the Browser SDK in a Web extension content script is discouraged and will be forbidden in a future major release unless the `allowedTrackingOrigins` option is provided.",_s="SDK initialized on a non-allowed domain.";function Os(e,t=typeof location<"u"?location.origin:"",n){const r=e.allowedTrackingOrigins;if(!r)return Ss(t,n)&&b.warn(Es),!0;const s=ys(r,t);return s||b.error(_s),s}function tt(e){return nt(e,location.href).href}function Go(e){try{return!!nt(e)}catch{return!1}}function jo(e){const t=nt(e).pathname;return t[0]==="/"?t:`/${t}`}function nt(e,t){const n=Ts();if(n)try{return t!==void 0?new n(e,t):new n(e)}catch(o){throw new Error(`Failed to construct URL: ${String(o)} ${H({url:e,base:t})}`)}if(t===void 0&&!/:/.test(e))throw new Error(`Invalid URL: '${e}'`);let r=document;const s=r.createElement("a");if(t!==void 0){r=document.implementation.createHTMLDocument("");const o=r.createElement("base");o.href=t,r.head.appendChild(o),r.body.appendChild(s)}return s.href=e,s}const gt=URL;let ae;function Ts(){if(ae===void 0)try{ae=new gt("http://test/path").href==="http://test/path"}catch{ae=!1}return ae?gt:void 0}function P(e,t,n){const r=vs(e,t);return{build(s,o){const i=Cs(e,t,n,s,o);return r(i)},tags:n,urlPrefix:r(""),trackType:t}}function vs(e,t){const n=`/api/v2/${t}`,r=e.proxy;if(typeof r=="string"){const o=tt(r);return i=>`${o}?ddforward=${encodeURIComponent(`${n}?${i}`)}`}if(typeof r=="function")return o=>r({path:n,parameters:o});const s=ws(t,e);return o=>`https://${s}${n}?${o}`}function ws(e,t){const{site:n=M,internalAnalyticsSubdomain:r}=t;if(e==="logs"&&t.usePciIntake&&n===M)return Ar;if(r&&n===M)return`${r}.${M}`;if(n===Cr)return`http-intake.logs.${n}`;const s=n.split("."),o=s.pop();return`browser-intake-${s.join("-")}.${o}`}function Cs({clientToken:e,internalAnalyticsSubdomain:t},n,r,s,{retry:o,encoding:i}){const u=["sdk_version:6.13.0",`api:${s}`].concat(r);o&&u.push(`retry_count:${o.count}`,`retry_after:${o.lastFailureStatus}`);const a=["ddsource=browser",`ddtags=${encodeURIComponent(u.join(","))}`,`dd-api-key=${e}`,`dd-evp-origin-version=${encodeURIComponent("6.13.0")}`,"dd-evp-origin=browser",`dd-request-id=${O()}`];return i&&a.push(`dd-evp-encoding=${i}`),n==="rum"&&a.push(`batch_time=${K()}`),t&&a.reverse(),a.join("&")}const Rs=200;function As(e){const{env:t,service:n,version:r,datacenter:s}=e,o=[];return t&&o.push(ue("env",t)),n&&o.push(ue("service",n)),r&&o.push(ue("version",r)),s&&o.push(ue("datacenter",s)),o}function ue(e,t){const n=Rs-e.length-1;(t.length>n||Is(t))&&b.warn(`${e} value doesn't meet tag requirements and will be sanitized. ${He} ${qe}/getting_started/tagging/#defining-tags`);const r=t.replace(/,/g,"_");return`${e}:${r}`}function Is(e){return xs()?new RegExp("[^\\p{Ll}\\p{Lo}0-9_:./-]","u").test(e):!1}function xs(){try{return new RegExp("[\\p{Ll}]","u"),!0}catch{return!1}}function ks(e){const t=e.site||M,n=As(e),r=Ls(e,n);return{replica:Ns(e,n),site:t,...r}}function Ls(e,t){return{logsEndpointBuilder:P(e,"logs",t),rumEndpointBuilder:P(e,"rum",t),profilingEndpointBuilder:P(e,"profile",t),sessionReplayEndpointBuilder:P(e,"replay",t)}}function Ns(e,t){if(!e.replica)return;const n={...e,site:M,clientToken:e.replica.clientToken},r={logsEndpointBuilder:P(n,"logs",t),rumEndpointBuilder:P(n,"rum",t)};return{applicationId:e.replica.applicationId,...r}}function Ko(e){return Ir.every(t=>e.includes(t))}const qo={ALLOW:"allow",MASK:"mask",MASK_USER_INPUT:"mask-user-input"},Ho={ALL:"all",SAMPLED:"sampled"};function Le(e,t){return e!=null&&typeof e!="string"?(b.error(`${t} must be defined as a string`),!1):!0}function $s(e){return e&&typeof e=="string"&&!/(datadog|ddog|datad0g|dd0g)/.test(e)?(b.error(`Site should be a valid Datadog site. ${He} ${qe}/getting_started/site/.`),!1):!0}function le(e,t){return e!==void 0&&!hn(e)?(b.error(`${t} Sample Rate should be a number between 0 and 100`),!1):!0}function zo(e){var t,n,r,s,o,i;if(!e||!e.clientToken){b.error("Client Token is not configured, we will not send any data.");return}if(e.allowedTrackingOrigins!==void 0&&!Array.isArray(e.allowedTrackingOrigins)){b.error("Allowed Tracking Origins must be an array");return}if(!(!$s(e.site)||!le(e.sessionSampleRate,"Session")||!le(e.telemetrySampleRate,"Telemetry")||!le(e.telemetryConfigurationSampleRate,"Telemetry Configuration")||!le(e.telemetryUsageSampleRate,"Telemetry Usage")||!Le(e.version,"Version")||!Le(e.env,"Env")||!Le(e.service,"Service")||!Os(e))){if(e.trackingConsent!==void 0&&!Ct(Ge,e.trackingConsent)){b.error('Tracking Consent should be either "granted" or "not-granted"');return}return{beforeSend:e.beforeSend&&St(e.beforeSend,"beforeSend threw an error:"),sessionStoreStrategyType:gs(e),sessionSampleRate:(t=e.sessionSampleRate)!==null&&t!==void 0?t:100,telemetrySampleRate:(n=e.telemetrySampleRate)!==null&&n!==void 0?n:20,telemetryConfigurationSampleRate:(r=e.telemetryConfigurationSampleRate)!==null&&r!==void 0?r:5,telemetryUsageSampleRate:(s=e.telemetryUsageSampleRate)!==null&&s!==void 0?s:5,service:e.service||void 0,silentMultipleInit:!!e.silentMultipleInit,allowUntrustedEvents:!!e.allowUntrustedEvents,trackingConsent:(o=e.trackingConsent)!==null&&o!==void 0?o:Ge.GRANTED,trackAnonymousUser:(i=e.trackAnonymousUser)!==null&&i!==void 0?i:!0,storeContextsAcrossPages:!!e.storeContextsAcrossPages,batchBytesLimit:16*J,eventRateLimiterThreshold:3e3,maxTelemetryEventsPerPage:15,flushTimeout:30*j,batchMessagesLimit:50,messageBytesLimit:256*J,...ks(e)}}}function Wo(e){return{session_sample_rate:e.sessionSampleRate,telemetry_sample_rate:e.telemetrySampleRate,telemetry_configuration_sample_rate:e.telemetryConfigurationSampleRate,telemetry_usage_sample_rate:e.telemetryUsageSampleRate,use_before_send:!!e.beforeSend,use_partitioned_cross_site_session_cookie:e.usePartitionedCrossSiteSessionCookie,use_secure_session_cookie:e.useSecureSessionCookie,use_proxy:!!e.proxy,silent_multiple_init:e.silentMultipleInit,track_session_across_subdomains:e.trackSessionAcrossSubdomains,track_anonymous_user:e.trackAnonymousUser,session_persistence:e.sessionPersistence,allow_fallback_to_local_storage:!!e.allowFallbackToLocalStorage,store_contexts_across_pages:!!e.storeContextsAcrossPages,allow_untrusted_events:!!e.allowUntrustedEvents,tracking_consent:e.trackingConsent,use_allowed_tracking_origins:Array.isArray(e.allowedTrackingOrigins)}}function A(e,t,n,{computeHandlingStack:r}={}){let s=e[t];if(typeof s!="function")if(t in e&&t.startsWith("on"))s=B;else return{stop:B};let o=!1;const i=function(){if(o)return s.apply(this,arguments);const u=Array.from(arguments);let a;X(n,null,[{target:this,parameters:u,onPostCall:c=>{a=c},handlingStack:r?zt("instrumented method"):void 0}]);const l=s.apply(this,u);return a&&X(a,null,[l]),l};return e[t]=i,{stop:()=>{o=!0,e[t]===i&&(e[t]=s)}}}function Yo(e,t,n){const r=Object.getOwnPropertyDescriptor(e,t);if(!r||!r.set||!r.configurable)return{stop:B};const s=B;let o=(u,a)=>{q(()=>{o!==s&&n(u,a)},0)};const i=function(u){r.set.call(this,u),o(this,u)};return Object.defineProperty(e,t,{set:i}),{stop:()=>{var u;((u=Object.getOwnPropertyDescriptor(e,t))===null||u===void 0?void 0:u.set)===i&&Object.defineProperty(e,t,r),o=s}}}function Vo(e){const t=(s,o)=>{const i=Vt({stackTrace:o,originalError:s,startClocks:k(),nonErrorPrefix:"Uncaught",source:se.SOURCE,handling:"unhandled"});e.notify(i)},{stop:n}=Us(t),{stop:r}=Ms(t);return{stop:()=>{n(),r()}}}function Us(e){return A(window,"onerror",({parameters:[t,n,r,s,o]})=>{let i;N(o)||(i=gr(t,n,r,s)),e(o??t,i)})}function Ms(e){return A(window,"onunhandledrejection",({parameters:[t]})=>{e(t.reason||"Empty reason")})}function Jo(e){const t={version:"6.13.0",onReady(n){n()},...e};return Object.defineProperty(t,"_setDebug",{get(){return Rn},enumerable:!1}),t}function Xo(e,t,n){const r=e[t];r&&!r.q&&r.version&&b.warn("SDK is loaded more than once. This is unsupported and might have unexpected behavior."),e[t]=n,r&&r.q&&r.q.forEach(s=>St(s,"onReady callback threw an error:")())}function Qo(e,t){t.silentMultipleInit||b.error(`${e} is already initialized.`)}const je={intervention:"intervention",deprecation:"deprecation",cspViolation:"csp_violation"};function Zo(e,t){const n=[];t.includes(je.cspViolation)&&n.push(Ds(e));const r=t.filter(s=>s!==je.cspViolation);return r.length&&n.push(Ps(r)),kt(...n)}function Ps(e){return new y(t=>{if(!window.ReportingObserver)return;const n=h((s,o)=>s.forEach(i=>t.notify(Bs(i)))),r=new window.ReportingObserver(n,{types:e,buffered:!0});return r.observe(),()=>{r.disconnect()}})}function Ds(e){return new y(t=>{const{stop:n}=z(e,document,"securitypolicyviolation",r=>{t.notify(Fs(r))});return n})}function Bs(e){const{type:t,body:n}=e;return un({type:n.id,message:`${t}: ${n.message}`,originalError:e,stack:ln(n.id,n.message,n.sourceFile,n.lineNumber,n.columnNumber)})}function Fs(e){const t=`'${e.blockedURI}' blocked by '${e.effectiveDirective}' directive`;return un({type:e.effectiveDirective,message:`${je.cspViolation}: ${t}`,originalError:e,csp:{disposition:e.disposition},stack:ln(e.effectiveDirective,e.originalPolicy?`${t} of the policy "${kn(e.originalPolicy,100,"...")}"`:"no policy",e.sourceFile,e.lineNumber,e.columnNumber)})}function un(e){return{startClocks:k(),source:se.REPORT,handling:"unhandled",...e}}function ln(e,t,n,r,s){return n?re({name:e,message:t,stack:[{func:"?",url:n,line:r??void 0,column:s??void 0}]}):void 0}const de=1/0,Gs=x;let V=null;const ge=new Set;function js(){ge.forEach(e=>e())}function Ks({expireDelay:e,maxEntries:t}){let n=[];V||(V=ze(()=>js(),Gs));const r=()=>{const c=be()-e;for(;n.length>0&&n[n.length-1].endTime<c;)n.pop()};ge.add(r);function s(c,d){const f={value:c,startTime:d,endTime:de,remove:()=>{Jt(n,f)},close:m=>{f.endTime=m}};return t&&n.length>=t&&n.pop(),n.unshift(f),f}function o(c=de,d={returnInactive:!1}){for(const f of n)if(f.startTime<=c){if(d.returnInactive||c<=f.endTime)return f.value;break}}function i(c){const d=n[0];d&&d.endTime===de&&d.close(c)}function u(c=de,d=0){const f=_e(c,d);return n.filter(m=>m.startTime<=f&&c<=m.endTime).map(m=>m.value)}function a(){n=[]}function l(){ge.delete(r),ge.size===0&&V&&(xt(V),V=null)}return{add:s,find:o,closeActive:i,findAll:u,reset:a,stop:l}}const qs="datadog-synthetics-public-id",Hs="datadog-synthetics-result-id",zs="datadog-synthetics-injects-rum";function ei(){return!!(window._DATADOG_SYNTHETICS_INJECTS_RUM||C(zs))}function Ws(){const e=window._DATADOG_SYNTHETICS_PUBLIC_ID||C(qs);return typeof e=="string"?e:void 0}function Ys(){const e=window._DATADOG_SYNTHETICS_RESULT_ID||C(Hs);return typeof e=="string"?e:void 0}function Vs(){return!!(Ws()&&Ys())}const Js=x,Xs=Ye;function ti(e,t,n,r){const s=new y,o=new y,i=bs(e.sessionStoreStrategyType,e,t,n),u=Ks({expireDelay:Xs});i.renewObservable.subscribe(()=>{u.add(a(),be()),s.notify()}),i.expireObservable.subscribe(()=>{o.notify(),u.closeActive(be())}),i.expandOrRenewSession(),u.add(a(),_n().relative),r.observable.subscribe(()=>{r.isGranted()?i.expandOrRenewSession():i.expire()}),Qs(e,()=>{r.isGranted()&&i.expandOrRenewSession()}),Zs(e,()=>i.expandSession()),eo(e,()=>i.restartSession());function a(){const l=i.getSession();if(!l){const c=jt(),d=document.cookie.split(/\s*;\s*/).filter(f=>f.startsWith("_dd_s"));return on("Unexpected session state",{session:c,isSyntheticsTest:Vs(),createdTimestamp:c?.created,expireTimestamp:c?.expire,cookie:{count:d.length,...d}}),{id:"invalid",trackingType:Mt,isReplayForced:!1,anonymousId:void 0}}return{id:l.id,trackingType:l[t],isReplayForced:!!l.forcedReplay,anonymousId:l.anonymousId}}return{findSession:(l,c)=>u.find(l,c),renewObservable:s,expireObservable:o,sessionStateUpdateObservable:i.sessionStateUpdateObservable,expire:i.expire,updateSessionState:i.updateSessionState}}function Qs(e,t){const{stop:n}=et(e,window,["click","touchstart","keydown","scroll"],t,{capture:!0,passive:!0})}function Zs(e,t){const n=()=>{document.visibilityState==="visible"&&t()},{stop:r}=z(e,document,"visibilitychange",n);ze(n,Js)}function eo(e,t){const{stop:n}=z(e,window,"resume",t,{capture:!0})}function ni(){let e="",t=0;return{isAsync:!1,get isEmpty(){return!e},write(n,r){const s=wt(n);t+=s,e+=n,r&&r(s)},finish(n){n(this.finishSync())},finishSync(){const n={output:e,outputBytesCount:t,rawBytesCount:t,pendingData:""};return e="",t=0,n},estimateEncodedBytesCount(n){return n.length}}}class ri{constructor(){this.callbacks={}}notify(t,n){const r=this.callbacks[t];r&&r.forEach(s=>s(n))}subscribe(t,n){return this.callbacks[t]||(this.callbacks[t]=[]),this.callbacks[t].push(n),{unsubscribe:()=>{this.callbacks[t]=this.callbacks[t].filter(r=>n!==r)}}}}function si(e,t,n){let r=0,s=!1;return{isLimitReached(){if(r===0&&q(()=>{r=0},x),r+=1,r<=t||s)return s=!1,!1;if(r===t+1){s=!0;try{n({message:`Reached max number of ${e}s by minute: ${t}`,source:se.AGENT,startClocks:k()})}finally{s=!1}}return!0}}}let Ne;const rt=new WeakMap;function oi(e){return Ne||(Ne=to(e)),Ne}function to(e){return new y(t=>{const{stop:n}=A(XMLHttpRequest.prototype,"open",no),{stop:r}=A(XMLHttpRequest.prototype,"send",o=>{ro(o,e,t)},{computeHandlingStack:!0}),{stop:s}=A(XMLHttpRequest.prototype,"abort",so);return()=>{n(),r(),s()}})}function no({target:e,parameters:[t,n]}){rt.set(e,{state:"open",method:String(t).toUpperCase(),url:tt(String(n))})}function ro({target:e,handlingStack:t},n,r){const s=rt.get(e);if(!s)return;const o=s;o.state="start",o.startClocks=k(),o.isAborted=!1,o.xhr=e,o.handlingStack=t;let i=!1;const{stop:u}=A(e,"onreadystatechange",()=>{e.readyState===XMLHttpRequest.DONE&&a()}),a=()=>{if(l(),u(),i)return;i=!0;const c=s;c.state="complete",c.duration=Tt(o.startClocks.timeStamp,K()),c.status=e.status,r.notify(wn(c))},{stop:l}=z(n,e,"loadend",a);r.notify(o)}function so({target:e}){const t=rt.get(e);t&&(t.isAborted=!0)}let $e;function ii(){return $e||($e=oo()),$e}function oo(){return new y(e=>{if(!window.fetch)return;const{stop:t}=A(window,"fetch",n=>io(n,e),{computeHandlingStack:!0});return t})}function io({parameters:e,onPostCall:t,handlingStack:n},r){const[s,o]=e;let i=o&&o.method;i===void 0&&s instanceof Request&&(i=s.method);const u=i!==void 0?String(i).toUpperCase():"GET",a=s instanceof Request?s.url:tt(String(s)),l=k(),c={state:"start",init:o,input:s,method:u,startClocks:l,url:a,handlingStack:n};r.notify(c),e[0]=c.input,e[1]=c.init,t(d=>co(r,d,c))}function co(e,t,n){const r=n;function s(o){r.state="resolve",Object.assign(r,o),e.notify(r)}t.then(h(o=>{s({response:o,responseType:o.type,status:o.status,isAborted:!1})}),h(o=>{var i,u;s({status:0,isAborted:((u=(i=r.init)===null||i===void 0?void 0:i.signal)===null||u===void 0?void 0:u.aborted)||o instanceof DOMException&&o.code===DOMException.ABORT_ERR,error:o})}))}let Ue={};function ci(e){const t=e.map(n=>(Ue[n]||(Ue[n]=ao(n)),Ue[n]));return kt(...t)}function ao(e){return new y(t=>{const n=E[e];return E[e]=(...r)=>{n.apply(console,r);const s=zt("console error");X(()=>{t.notify(uo(r,e,s))})},()=>{E[e]=n}})}function uo(e,t,n){const r=e.map(s=>lo(s)).join(" ");if(t===Ke.error){const s=e.find(N),o=Vt({originalError:s,handlingStack:n,startClocks:k(),source:se.CONSOLE,handling:"handled",nonErrorPrefix:"Provided",useFallbackStack:!1});return o.message=r,{api:t,message:r,handlingStack:n,error:o}}return{api:t,message:r,error:void 0,handlingStack:n}}function lo(e){return typeof e=="string"?Z(e):N(e)?Wt(ne(e)):H(Z(e),void 0,2)}function fo(e){const t=Xe(e)==="object";return t||b.error("Unsupported context:",e),t}function Me(e,t,n){const r={...e};for(const[s,{required:o,type:i}]of Object.entries(t))i==="string"&&!bt(r[s])&&(r[s]=String(r[s])),o&&bt(r[s])&&b.warn(`The property ${s} of ${n} is required; context will not be sent to the intake.`);return r}function bt(e){return e==null||e===""}function st(e="",{propertiesConfig:t={}}={}){let n={};const r=new y,s={getContext:()=>kr(n),setContext:o=>{fo(o)?n=Z(Me(o,t,e)):s.clearContext(),r.notify()},setContextProperty:(o,i)=>{n=Z(Me({...n,[o]:i},t,e)),r.notify()},removeContextProperty:o=>{delete n[o],Me(n,t,e),r.notify()},clearContext:()=>{n={},r.notify()},changeObservable:r};return s}function ai(e,t,n,r){return h((...s)=>(r&&cs({feature:r}),e()[t][n](...s)))}function ui(e,t,n){e.changeObservable.subscribe(()=>{const r=e.getContext();n.add(s=>s[t].setContext(r))})}const po="_dd_c",mo=[];function ot(e,t,n,r){const s=go(n,r);mo.push(z(e,window,"storage",({key:l})=>{s===l&&i()})),t.changeObservable.subscribe(u);const o=Qe(a(),t.getContext());Oe(o)||t.setContext(o);function i(){t.setContext(a())}function u(){localStorage.setItem(s,JSON.stringify(t.getContext()))}function a(){const l=localStorage.getItem(s);return l?JSON.parse(l):{}}}function go(e,t){return`${po}_${e}_${t}`}const yt="DISCARDED",it="SKIPPED";function li(){const e={};return{register(t,n){return e[t]||(e[t]=[]),e[t].push(n),{unregister:()=>{e[t]=e[t].filter(r=>r!==n)}}},triggerHook(t,n){const r=e[t]||[],s=[];for(const o of r){const i=o(n);if(i===yt)return yt;i!==it&&s.push(i)}return Qe(...s)}}}function di(e,t,n){const r=bo();return t.storeContextsAcrossPages&&ot(t,r,n,4),e.register(0,()=>{const s=r.getContext();return Oe(s)||!s.id?it:{account:s}}),r}function bo(){return st("account",{propertiesConfig:{id:{type:"string",required:!0},name:{type:"string"}}})}function fi(e,t,n,r){const s=yo();return t.storeContextsAcrossPages&&ot(t,s,n,2),e.register(0,()=>{const o=s.getContext();return r?{context:o}:o}),s}function yo(){return st("global context")}function pi(e,t,n,r){const s=ho();return t.storeContextsAcrossPages&&ot(t,s,r,1),e.register(0,({eventType:o,startTime:i})=>{const u=s.getContext(),a=n.findTrackedSession(i);return a&&a.anonymousId&&!u.anonymous_id&&t.trackAnonymousUser&&(u.anonymous_id=a.anonymousId),Oe(u)?it:{type:o,usr:u}}),s}function ho(){return st("user",{propertiesConfig:{id:{type:"string"},name:{type:"string"},email:{type:"string"}}})}const mi={userContext:"userContext",globalContext:"globalContext",accountContext:"accountContext"},gi={getContext:"getContext",setContext:"setContext",setContextProperty:"setContextProperty",removeContextProperty:"removeContextProperty",clearContext:"clearContext"};function bi(e,t,n){const r=e.getReader(),s=[];let o=0;i();function i(){r.read().then(h(a=>{if(a.done){u();return}n.collectStreamBody&&s.push(a.value),o+=a.value.length,o>n.bytesLimit?u():i()}),h(a=>t(a)))}function u(){r.cancel().catch(B);let a,l;if(n.collectStreamBody){let c;if(s.length===1)c=s[0];else{c=new Uint8Array(o);let d=0;s.forEach(f=>{c.set(f,d),d+=f.length})}a=c.slice(0,n.bytesLimit),l=c.length>n.bytesLimit}t(void 0,a,l)}}const ht={DOCUMENT:"document",XHR:"xhr",BEACON:"beacon",FETCH:"fetch",CSS:"css",JS:"js",IMAGE:"image",FONT:"font",MEDIA:"media",OTHER:"other"},yi={FETCH:ht.FETCH,XHR:ht.XHR};export{bi as $,Jo as A,Bo as B,Ke as C,ai as D,se as E,h as F,gi as G,kr as H,cs as I,ti as J,y as K,we as L,si as M,On as N,J as O,yt as P,ci as Q,je as R,Zo as S,Io as T,oi as U,Ko as V,Mr as W,$o as X,re as Y,ne as Z,yi as _,Vt as a,To as a$,Vo as a0,ri as a1,$ as a2,E as a3,Zr as a4,ni as a5,Ze as a6,on as a7,li as a8,it as a9,Ct as aA,Et as aB,ws as aC,X as aD,Eo as aE,D as aF,A as aG,Ks as aH,Ye as aI,ko as aJ,Be as aK,Go as aL,ht as aM,jo as aN,et as aO,be as aP,kn as aQ,pe as aR,x as aS,_e as aT,In as aU,So as aV,Ao as aW,yn as aX,An as aY,_n as aZ,ze as a_,ei as aa,Ys as ab,Ws as ac,xr as ad,Po as ae,di as af,pi as ag,fi as ah,Mo as ai,Xo as aj,L as ak,z as al,I as am,It as an,j as ao,Tt as ap,Oo as aq,O as ar,Fo as as,Xe as at,ys as au,Ho as av,wn as aw,Mn as ax,le as ay,qo as az,k as b,xt as b0,Oe as b1,wo as b2,Uo as b3,C as b4,Vs as b5,Nr as b6,_o as b7,wr as b8,Lo as b9,M as ba,vo as bb,qe as bc,is as bd,At as be,nt as bf,Ro as bg,Yo as bh,Jr as bi,Kr as bj,st as c,Qe as d,zt as e,b as f,Wo as g,yo as h,ui as i,bo as j,ho as k,xo as l,Co as m,B as n,$t as o,Yr as p,q,No as r,Z as s,K as t,ii as u,zo as v,Ur as w,mi as x,Do as y,Qo as z};
