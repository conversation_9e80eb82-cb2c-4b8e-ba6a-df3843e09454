[1, ["10I8iCpWVA062UoMj6NsQ4", "28bfwm51dOHoLVvMxcWZRh@f9941", "a2wtSHnrJCBqaFoXZ2V/hZ@f9941", "5dRMB/7/1Ki4hTzroiNsxC@f9941", "09Yng9V0ZOC6g+bFLu3bRL", "9a7ojTp0dCYo0YJERsgFoY@f9941", "2172tKx+VPmYntxxgisw3m@f9941", "8esgF/4NJKA4U67TKFSc9s@f9941", "62ubb4IE9Hqb2mvTzWX2Rk", "a7ZHpooe1A07RKUYPDmCm2", "bbETP+byhBN4TeISsx9XKY@f9941", "1ef2nvtFBCCL33lxTSXy7m@f9941", "22GZ9DJdFEjZ2Dt0DYmu/G@f9941", "19NYbVUb9Fz5+WfQmWwMys@f9941", "fc5KYzrxRGZZFwHFncwS3y@f9941", "3c6LAjF5tC4YsNRG/z6I9U@f9941", "72dKtP0/dGM6H2pOzpILTA@f9941", "e7PRZicGlMiYFwlaXJ2xpI@f9941"], ["node", "targetInfo", "_spriteFrame", "root", "_parent", "value", "asset", "data", "target", "source", "_defaultClip", "_target"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_lpos", "_children", "_lscale"], -2, 4, 9, 1, 5, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_lineHeight", "_isBold", "_fontSize", "_overflow", "_enableOutline", "_outlineWidth", "_horizontalAlign", "node", "__prefab", "_color", "_outlineColor"], -6, 1, 4, 5, 5], ["cc.Widget", ["_alignFlags", "_top", "_originalWidth", "_originalHeight", "_left", "_bottom", "_right", "node", "__prefab"], -4, 1, 4], ["cc.Sprite", ["_type", "_sizeMode", "_isTrimmedMode", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Layout", ["_resizeMode", "_layoutType", "_affectedByScale", "_spacingX", "node", "__prefab"], -1, 1, 4], ["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "_prefab", "_components", "_lscale", "_parent", "__editorExtras__"], 0, 4, 12, 5, 1, 11], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedChil<PERSON>n", "mountedComponents", "removedComponents"], 2, 1, 9, 9, 9, 9], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab", "_normalColor", "_target"], 1, 1, 4, 5, 1], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetOverrideInfo", ["source", "sourceInfo", "target", "targetInfo"], 3, 1, 4, 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.MountedChildrenInfo", ["targetInfo", "nodes"], 3, 4, 2], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 6], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 6], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4], ["cc.Animation", ["playOnLoad", "node", "__prefab", "_clips", "_defaultClip"], 2, 1, 4, 3, 6], ["7563fP+WuxL1p5rM8Q2NGlY", ["node", "__prefab", "spfs"], 3, 1, 4, 3]], [[10, 0, 2], [15, 0, 2], [12, 0, 1, 2, 3, 4, 5, 5], [6, 0, 1, 2, 1], [18, 0, 1, 2, 3], [19, 0, 1, 2, 2], [20, 0, 1, 2, 2], [0, 0, 1, 7, 6, 5, 8, 10, 3], [0, 0, 1, 7, 9, 6, 5, 8, 3], [3, 0, 1, 3, 4, 5, 3], [3, 3, 4, 5, 1], [25, 0, 1, 2, 2], [0, 0, 1, 9, 6, 5, 8, 3], [0, 0, 1, 7, 9, 6, 5, 3], [13, 0, 1, 2, 3, 4, 5, 4], [21, 0, 1, 2, 3], [22, 0, 1, 2, 2], [3, 3, 4, 1], [9, 0, 2], [0, 0, 1, 9, 6, 5, 3], [0, 0, 1, 7, 6, 5, 8, 3], [0, 0, 1, 7, 6, 5, 3], [0, 0, 1, 7, 6, 5, 10, 3], [5, 2, 6, 3, 7, 2], [6, 0, 1, 2, 3, 1], [2, 0, 5, 7, 8, 3], [2, 0, 1, 7, 8, 3], [4, 4, 5, 1], [4, 0, 1, 2, 4, 5, 4], [7, 0, 1, 2, 2], [0, 3, 4, 7, 5, 3], [0, 0, 2, 1, 7, 9, 6, 5, 8, 4], [0, 0, 2, 1, 7, 6, 5, 8, 4], [5, 0, 1, 4, 3, 5, 3], [2, 0, 2, 3, 7, 8, 4], [2, 0, 4, 6, 1, 5, 2, 3, 7, 8, 8], [2, 0, 7, 8, 2], [2, 0, 4, 1, 7, 8, 4], [11, 0, 1, 2, 3, 4, 5, 3], [14, 0, 1, 2, 3, 1], [4, 0, 1, 3, 2, 4, 5, 5], [7, 0, 1, 3, 4, 2, 5, 2], [16, 0, 1, 1], [17, 0, 1, 1], [23, 0, 1, 2, 2], [3, 0, 1, 3, 4, 3], [3, 0, 3, 4, 5, 2], [3, 2, 3, 4, 5, 2], [24, 0, 1, 1], [1, 0, 1, 4, 2, 3, 9, 10, 11, 6], [1, 0, 1, 4, 2, 5, 3, 9, 10, 7], [1, 0, 8, 1, 4, 2, 5, 3, 9, 10, 8], [1, 0, 1, 2, 3, 9, 10, 5], [1, 0, 1, 2, 3, 9, 10, 11, 5], [1, 0, 1, 2, 3, 6, 7, 9, 10, 11, 7], [1, 0, 1, 2, 3, 6, 7, 9, 10, 7], [1, 0, 1, 2, 5, 3, 6, 7, 9, 10, 12, 8], [26, 0, 1, 2, 3, 4, 2], [8, 0, 1, 2, 3, 3], [8, 0, 1, 2, 3, 4, 5, 3], [27, 0, 1, 2, 1]], [[[[18, "guaji_jiazhong"], [19, "guaji_jiazhong", 33554432, [-9], [[3, -7, [0, "6fUyudUT1IFo6FvRHKFGRJ"], [5, 640, 1280]], [34, 45, 720, 1280, -8, [0, "6b1cvTbIZFyJGZXVrsGtve"]]], [38, "95ptoTxBhATrAjGlSVUme9", null, -6, 0, [[39, -5, [1, ["28m6c/BjFKa4sKaBfaebwz"]], -4, [1, ["28m6c/BjFKa4sKaBfaebwz"]]]], [-1, -2, -3]]], [12, "neirong", 33554432, [-13, -14, -15, -16, -17], [[3, -10, [0, "aaJpjQcuxBA6G/l2SbxiF5"], [5, 557, 353]], [27, -11, [0, "6708FesS9JAovuydwwVcvT"]], [35, 41, 20.5, 20.5, 101.952, -12.5, 482, 420, -12, [0, "5eZdaeA3xHQI4CPyYgk/kQ"]]], [2, "1fsEC25OBJgoi9GEKoIKfz", null, null, null, 1, 0], [1, 0, -38.452, 0]], [30, 0, {}, 1, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -41, [41, "445fHfLeRLm692m27mHuIa", 1, [[42, [1, ["1fsEC25OBJgoi9GEKoIKfz"]], [-36, -37, -38, -39, -40]]], [[43, [1, ["78n3BOogNNKaIOv5y9wQoP"]], [-34, -35]]], [[4, "ty_tips_1", ["_name"], -18], [5, ["_lpos"], -19, [1, 0, 0, 0]], [5, ["_lrot"], -20, [3, 0, 0, 0, 1]], [5, ["_euler"], -21, [1, 0, 0, 0]], [6, ["_contentSize"], [1, ["47Cs6IYElOBK5jyf6MFKqL"]], [5, 720, 1280]], [4, true, ["_active"], -22], [6, ["_lpos"], [1, ["24tCD+YXhKCLwFSDt4YRnG"]], [1, 0, 0, 0]], [4, true, ["_active"], -23], [5, ["_lpos"], -24, [1, 0, 149.5, 0]], [15, 89, ["_top"], [1, ["dasWxlv1RNrrhMkFumI+ux"]]], [6, ["_contentSize"], [1, ["0baviFw+VGYZTyKzUPGLoM"]], [5, 450, 3]], [6, ["_contentSize"], [1, ["a8yqhYeWNJcLI3gVLCtZZ1"]], [5, 598, 480]], [6, ["_contentSize"], [1, ["aaJpjQcuxBA6G/l2SbxiF5"]], [5, 557, 353]], [5, ["_lpos"], -25, [1, 0, 198, 0]], [4, "", ["_string"], -26], [4, 101.952, ["_top"], -27], [4, -12.5, ["_bottom"], -28], [6, ["_lpos"], [1, ["78n3BOogNNKaIOv5y9wQoP"]], [1, 0, 0, 0]], [6, ["_color"], [1, ["b3J8hPlwRGIZBp39Y3kOHz"]], [4, 3204448255]], [5, ["_lpos"], -29, [1, 0, -38.452, 0]], [6, ["_contentSize"], [1, ["88MR1y1E9HsLmj95IcTT4P"]], [5, 523, 504]], [6, ["_lpos"], [1, ["9aMeua5xxHo7Wg7Ik8OTd0"]], [1, 243.5, 59.5, 0]], [15, 1, ["_sizeMode"], [1, ["46ptCyrGlH9oRpEAXLnZIn"]]], [15, 17, ["_top"], [1, ["70/3vA5OpN8LaNyd5FUwzR"]]], [4, 52, ["_fontSize"], -30], [4, 53, ["_actualFontSize"], -31], [4, 41, ["_alignFlags"], -32], [4, false, ["_active"], -33], [6, ["_lpos"], [1, ["29Ph8qd5NBALHzar7VE2v1"]], [1, 0, -240, 0]]], [[1, ["61yc/ka4FCa6g58f2jFAL7"]], [1, ["cd78E25WJGjYaH+t1GrRxa"]], [1, ["8c1iVLCMhKp45Eh8+X1jnc"]], [1, ["0651iL5dtHZI7k1b1YOLrj"]]]], 0]], [8, "bg_di_hb1", 33554432, 2, [-44, -45, -46, -47], [[3, -42, [0, "3dRNXOKHpMNYG+FdjDSJmP"], [5, 486, 109.7]], [45, 1, 0, -43, [0, "b241NGkT1CBKin4GqkHTZl"]]], [2, "fahmLJ9tlKo5UuLTB5BzB7", null, null, null, 1, 0], [1, 0, 19.448, 0]], [12, "bg_tips_1", 33554432, [-51, -52, 2], [[3, -48, [0, "a8yqhYeWNJcLI3gVLCtZZ1"], [5, 598, 480]], [9, 1, 0, -49, [0, "6ar4qEo+1BvqNyFi7XMf3l"], 7], [48, -50, [0, "08Pgego05LQpoLd8O0Y5ah"]]], [2, "adU4hcIF1KBIngaH7EmOZ6", null, null, null, 1, 0], [1, 0, 12, 0]], [33, "txt_gb", 33554432, [[[3, -53, [0, "4fWK/e/RpHoIUitppwo9Rm"], [5, 308, 75.6]], [49, "點擊空白處關閉", 44, 44, 60, true, -54, [0, "b3J8hPlwRGIZBp39Y3kOHz"], [4, 3204448255]], [25, 2, -125.39999999999998, -55, [0, "16bUIVxypFRrhyDk8X1FnZ"]], [11, "djkb", -56, [0, "4eBrrFUMxBB5WWmciQILOX"]], -57, -58], 4, 4, 4, 4, 1, 1], [2, "78n3BOogNNKaIOv5y9wQoP", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [13, "ani_in", 33554432, 3, [-64], [[3, -59, [0, "d9OrvkIAVDmbEjJx3Cr0Xo"], [5, 452, 535]], [27, -60, [0, "08NLvQ4JpHR42gPYCLSfbv"]], [57, true, -61, [0, "a6rFwUB3dLSJ2bJ71B4og3"], [1], 2], [17, -62, [0, "a26BcbLtpAboaZx3mV/JuP"]], [36, 2, -63, [0, "188ss4RxFKpI0DWLudxjoO"]]], [2, "24tCD+YXhKCLwFSDt4YRnG", null, null, null, 1, 0]], [12, "click_close", 33554432, [-67, 6, -68], [[3, -65, [0, "e4D+Prbk5MErMgRCU2caSo"], [5, 270, 24]], [40, 1, 1, 2, true, -66, [0, "8ezZ89Ni9LR7x96Xo6WnJk"]]], [2, "29Ph8qd5NBALHzar7VE2v1", null, null, null, 1, 0], [1, 0, -240, 0]], [13, "<PERSON>_suofang", 33554432, 7, [5, 8], [[3, -69, [0, "88MR1y1E9HsLmj95IcTT4P"], [5, 523, 504]], [28, 1, 2, true, -70, [0, "bbY45haOlM5qLSY7/7nS7G"]]], [2, "10U9/vnbBONoruSblOhHzL", null, null, null, 1, 0]], [31, "img_xian_2", false, 33554432, 5, [-74], [[3, -71, [0, "0baviFw+VGYZTyKzUPGLoM"], [5, 450, 3]], [46, 1, -72, [0, "46ptCyrGlH9oRpEAXLnZIn"], 6], [26, 1, 89, -73, [0, "dasWxlv1RNrrhMkFumI+ux"]]], [2, "ecdLmaKdFMOqhExD1woACw", null, null, null, 1, 0], [1, 0, 149.5, 0]], [32, "btn_close", false, 33554432, 10, [[3, -75, [0, "69dLSBEyBP/6SdzwdLZ0KL"], [5, 59, 61]], [10, -76, [0, "f252Zzgq9C/6SR8iAEnw25"], 5], [37, 9, 445, -85, -77, [0, "f22AcbXlNFxINSS0W0MnO0"]], [58, 3, 0.9, -78, [0, "e0ES++bdRON7wqagLbPS/g"]]], [2, "9aMeua5xxHo7Wg7Ik8OTd0", null, null, null, 1, 0], [1, 243.5, 59.5, 0]], [8, "bg_di_title1", 33554432, 2, [-81], [[24, -79, [0, "24vu7zpp1OFo5sEadyzoaw"], [5, 280, 37], [0, 0, 0.5]], [9, 1, 0, -80, [0, "0auqEjUmBEhZh/c0Fmps+m"], 14]], [2, "78dMz8i8JCrKdfAghuACcT", null, null, null, 1, 0], [1, -232, 97.09699999999998, 0]], [8, "bg_sd3", 33554432, 2, [-84], [[3, -82, [0, "c0ROyaeZZNppVMfPQ9aihw"], [5, 463, 45]], [9, 1, 0, -83, [0, "60D1JSkcNF57o9iGwpelSe"], 15]], [2, "89Lzjc5vxL8Zn7QpW1Iivv", null, null, null, 1, 0], [1, 0, 162.779, 0]], [13, "bg_sd3", 33554432, 13, [-87, -88], [[3, -85, [0, "94c7djgmRIOJgn4E+GFXqg"], [5, 184.4921875, 34.5]], [28, 1, 1, true, -86, [0, "22kxno1y1F2adFKgwtYCpo"]]], [2, "65cNZ1Ln1LIZVsvpCzp8oZ", null, null, null, 1, 0]], [7, "txt_title1", 33554432, 5, [[3, -89, [0, "8a1ZxWWyxPyIyY//yWxbVN"], [5, 700, 100]], [50, "", 53, 52, 70, 2, true, -90, [0, "daLVJEkn9IhpizEzbnEOJb"]], [26, 1, 17, -91, [0, "70/3vA5OpN8LaNyd5FUwzR"]]], [2, "b0Z2p+e89KwbvfAK7hx5EH", null, null, null, 1, 0], [1, 0, 198, 0], [1, 0.5, 0.5, 1]], [23, 0, 2, [14, "79BcNar8tDmZucBHLjoa8/", null, null, -96, [29, "aa3Gucv+pGpIqYkEzOMfAR", 1, [[4, "list_jf1", ["_name"], -92], [5, ["_lpos"], -93, [1, 0, -68.054, 0]], [5, ["_lrot"], -94, [3, 0, 0, 0, 1]], [5, ["_euler"], -95, [1, 0, 0, 0]]]], 8], [{}, "mountedRoot", 1, 3]], [1, ["79BcNar8tDmZucBHLjoa8/"]], [23, 0, 2, [14, "03XfDFaqNOdp6SCwdhIQpD", null, null, -108, [29, "1fjjUO8T5DAb8Yp5JBwxwc", 1, [[4, "btn_ty_75", ["_name"], -97], [5, ["_lpos"], -98, [1, 0, -117.798, 0]], [5, ["_lrot"], -99, [3, 0, 0, 0, 1]], [5, ["_euler"], -100, [1, 0, 0, 0]], [16, ["spfs", "0"], -101, 10], [16, ["spfs", "1"], -102, 11], [16, ["spfs", "2"], -103, 12], [6, ["_contentSize"], [1, ["89Kmzp7M5JRJxKSraI3F2C"]], [5, 190, 64]], [44, ["_spriteFrame"], [1, ["43Z+zpMHNH95/fghPESQqh"]], 13], [5, ["_outlineColor"], -104, [4, 4279270831]], [4, 52, ["_fontSize"], -105], [4, 53, ["_actualFontSize"], -106], [4, "", ["_string"], -107]]], 9], [{}, "mountedRoot", 1, 3]], [1, ["03XfDFaqNOdp6SCwdhIQpD"]], [1, ["bc648ctydDD5l0O0o5vV7i"]], [7, "txt_item_bw1", 33554432, 12, [[24, -109, [0, "00cd2zPENNtYFmp6cW1MPM"], [5, 472.8, 90.72], [0, 0, 0.5]], [51, "永久提升掛機時間上限", 0, 45, 44, 72, 2, true, -110, [0, "bej/YRI7ZCe6PhMeFNwkY4"]], [11, "guaji_19", -111, [0, "978t3ING5FFr7xsVzQxxR7"]]], [2, "6f35nk3tFHMJJeubCTZ4ts", null, null, null, 1, 0], [1, 20, 3.256, 0], [1, 0.5, 0.5, 1]], [7, "txt_item_bw2", 33554432, 14, [[3, -112, [0, "45vchqsX9H7L1q7uUBF8l3"], [5, 160, 55.44]], [52, "挂机剩余", 40, 44, true, -113, [0, "28m6c/BjFKa4sKaBfaebwz"]], [11, "guaji_32", -114, [0, "cfqDi5c41E16dnKkQrlND4"]]], [2, "ed/MUISDdEL4v2pba4A7Vt", null, null, null, 1, 0], [1, -52.24609375, 0, 0], [1, 0.5, 0.5, 1]], [8, "img_zhong_lan", 33554432, 4, [-117], [[3, -115, [0, "e8Eja6m/tMUq9HLAxHosc1"], [5, 89, 50]], [17, -116, [0, "edjAW1xA9KwZ3+jRp5/bd4"]]], [2, "97yVDjjBtFq6Oxf/twpT68", null, null, null, 1, 0], [1, 110, 0, 0]], [8, "img_zhong_lv", 33554432, 4, [-120], [[3, -118, [0, "beqj631cxL/YqXu+Zp8g9c"], [5, 89, 50]], [17, -119, [0, "946ZjOoNdGTKUpk+eMytaE"]]], [2, "b9uLpgDepGLbs5ZEcWKD2F", null, null, null, 1, 0], [1, -110, 0, 0]], [1, ["79BcNar8tDmZucBHLjoa8/"]], [7, "img_jt1", 33554432, 8, [[3, -121, [0, "eee/9eN+NJzKI628HWK7x1"], [5, 56, 31]], [10, -122, [0, "68BOlH9JlMJJNVsCv0L5TE"], 3]], [2, "5cO5jcQ/dJF502OHYmrDg3", null, null, null, 1, 0], [1, -107, 0, 0], [1, -1, 1, 1]], [20, "img_jt2", 33554432, 8, [[3, -123, [0, "881UsWnGJCfapWV3Ch/EkM"], [5, 56, 31]], [10, -124, [0, "3bWlSrUlRPELoUCq41DPmO"], 4]], [2, "a1v7P3vClBB7uq9CDdFhA7", null, null, null, 1, 0], [1, 107, 0, 0]], [1, ["2fM+Ep5oJCAbVEaTu9NhXO"]], [7, "txt_item_bw3", 33554432, 14, [[3, -125, [0, "3f3QVoy3JFM5BcFA99kbB7"], [5, 208.984375, 55.44]], [53, "12時間34分", 40, 44, true, -126, [0, "20ZMfCt8dKO4bqA8zXQ5Zt"], [4, 4290117376]]], [2, "6dRzpx5j5Mua7PzZtMIgLt", null, null, null, 1, 0], [1, 40, 0, 0], [1, 0.5, 0.5, 1]], [21, "bg_di_hb2", 33554432, 4, [[3, -127, [0, "3bw1VDfkpLM4+InRWBz9RK"], [5, 464, 96]], [9, 1, 0, -128, [0, "c5Lhem42ZKQYGszPPUCwfJ"], 16]], [2, "6cwyevhT9MEY6gvSQqI90a", null, null, null, 1, 0]], [22, "txt_zw1", 33554432, 23, [[3, -129, [0, "3cfLUg3ENEnZmR85gdmV1e"], [5, 108.24609375, 106.8]], [54, "6小时", 40, 80, true, true, 3, -130, [0, "77NY4smDpAbpBqMK2tshql"], [4, 4290117376]]], [2, "1cCqmXKFBBZ524dCfZNLLX", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [20, "jt_green", 33554432, 4, [[3, -131, [0, "02/uXpqC9ET6KMhXaK8BDh"], [5, 21, 27]], [10, -132, [0, "78rxascXBNsJRAU+7d6Dj1"], 17]], [2, "96Bn1DBzpNPYoaqbm8XuEh", null, null, null, 1, 0], [1, 2, 0, 0]], [22, "txt_zw2", 33554432, 24, [[3, -133, [0, "38Cevo1TxNMYI1KvdjIZmC"], [5, 108.24609375, 106.8]], [55, "6小时", 40, 80, true, true, 3, -134, [0, "ee1gJNpyVAJap+AJPHQR7d"]]], [2, "7b4tAlJ35Kl4iREtbBdyel", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [1, ["daLVJEkn9IhpizEzbnEOJb"]], [1, ["5eZdaeA3xHQI4CPyYgk/kQ"]], [11, "djkb", 6, [0, "c1e5+XKVdOQq9C+2omFyiv"]], [25, 4, 203.1, 6, [0, "e88ihEHlZKsp2VxaWuMOqo"]], [1, ["1fsEC25OBJgoi9GEKoIKfz"]], [1, ["b0Z2p+e89KwbvfAK7hx5EH"]], [1, ["ecdLmaKdFMOqhExD1woACw"]]], 0, [0, -1, 3, 0, -2, 18, 0, -3, 16, 0, 8, 3, 0, 9, 3, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, -2, 16, 0, -3, 18, 0, -4, 12, 0, -5, 13, 0, 1, 25, 0, 1, 25, 0, 1, 25, 0, 1, 25, 0, 1, 38, 0, 1, 39, 0, 1, 40, 0, 1, 39, 0, 1, 34, 0, 1, 35, 0, 1, 35, 0, 1, 38, 0, 1, 34, 0, 1, 34, 0, 1, 35, 0, 1, 40, 0, -1, 36, 0, -2, 37, 0, -1, 4, 0, -2, 16, 0, -3, 18, 0, -4, 12, 0, -5, 13, 0, 3, 3, 0, 0, 4, 0, 0, 4, 0, -1, 30, 0, -2, 23, 0, -3, 32, 0, -4, 24, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 15, 0, -2, 10, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -5, 36, 0, -6, 37, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 9, 0, 0, 8, 0, 0, 8, 0, -1, 26, 0, -3, 27, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, -1, 11, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, -1, 21, 0, 0, 13, 0, 0, 13, 0, -1, 14, 0, 0, 14, 0, 0, 14, 0, -1, 22, 0, -2, 29, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 1, 17, 0, 1, 17, 0, 1, 17, 0, 1, 17, 0, 3, 16, 0, 1, 19, 0, 1, 19, 0, 1, 19, 0, 1, 19, 0, 1, 28, 0, 1, 28, 0, 1, 28, 0, 1, 20, 0, 1, 20, 0, 1, 20, 0, 1, 20, 0, 3, 18, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, -1, 31, 0, 0, 24, 0, 0, 24, 0, -1, 33, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 7, 1, 2, 4, 5, 5, 4, 9, 6, 4, 8, 8, 4, 9, 134], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [6, -1, 10, 2, 2, 2, 2, 2, 6, 6, 5, 5, 5, 5, 2, 2, 2, 2], [4, 0, 0, 1, 1, 5, 6, 7, 8, 9, 10, 2, 11, 2, 12, 13, 14, 15]], [[[18, "btn_ty_75"], [19, "btn_ty_75", 33554432, [-5, -6], [[3, -2, [0, "1657nTijNEh7Bns5UYA0sV"], [5, 160, 66]], [59, 3, 0.9, -4, [0, "8eFPdJ6WlJk47YJqq4w+Iz"], [4, 4292269782], -3]], [2, "03XfDFaqNOdp6SCwdhIQpD", null, null, null, -1, 0]], [21, "btn_tab_1", 33554432, 1, [[3, -7, [0, "89Kmzp7M5JRJxKSraI3F2C"], [5, 152, 51]], [47, false, -8, [0, "43Z+zpMHNH95/fghPESQqh"], 0], [60, -9, [0, "2fM+Ep5oJCAbVEaTu9NhXO"], [1, 2, 3]]], [2, "5eZAD6dg1PkIthJGeTXCdA", null, null, null, 1, 0]], [7, "txt_tab_1", 33554432, 1, [[3, -10, [0, "77Mt/A7RxGrJ0Q7zFDQxkY"], [5, 300, 70.56]], [56, "", 41, 56, 2, true, true, 3, -11, [0, "bc648ctydDD5l0O0o5vV7i"], [4, 4282867204]]], [2, "5cvq8qCo5I7pXjdLcFwDsb", null, null, null, 1, 0], [1, 0, 2, 0], [1, 0.5, 0.5, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 11, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 7, 1, 11], [0, 0, 0, 0], [2, -1, -2, -3], [3, 3, 16, 17]]]]