[1, ["9fdWyXcTxIKpWiBD9lWN8Z@f9941", "a7ZHpooe1A07RKUYPDmCm2", "79Qu9fuxJCDJ7fKtUpA8Fs@f9941"], ["node", "_spriteFrame", "root", "asset", "data"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos", "_lscale", "_lrot", "_euler"], -2, 4, 9, 1, 2, 5, 5, 5, 5], ["cc.Label", ["_string", "_actualFontSize", "_overflow", "_isBold", "_enableOutline", "_outlineWidth", "_fontSize", "_lineHeight", "_verticalAlign", "_enableWrapText", "node", "__prefab", "_outlineColor", "_color"], -7, 1, 4, 5, 5], ["cc.Sprite", ["_isTrimmedMode", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["41ebaVoPpRA4Kp67XEdHfZn", ["i18n_stringKey", "node", "__prefab"], 2, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["cc.TargetInfo", ["localID"], 2], ["cc.<PERSON><PERSON>", ["_transition", "_zoomScale", "node", "__prefab"], 1, 1, 4]], [[5, 0, 2], [6, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [12, 0, 1, 2, 2], [0, 0, 1, 7, 6, 5, 9, 10, 3], [0, 0, 1, 7, 6, 5, 3], [3, 0, 1, 2, 3, 1], [2, 2, 3, 4, 1], [4, 0, 2], [0, 0, 1, 8, 6, 5, 3], [0, 0, 2, 1, 7, 8, 6, 5, 9, 11, 10, 12, 4], [0, 0, 1, 7, 6, 5, 10, 3], [0, 0, 2, 1, 7, 8, 6, 5, 9, 4], [0, 3, 4, 7, 5, 3], [2, 0, 2, 3, 2], [2, 1, 0, 2, 3, 3], [7, 0, 1, 2, 3, 4, 5, 4], [8, 0, 1, 2, 3, 4, 5, 4], [1, 0, 8, 1, 6, 7, 2, 3, 4, 5, 10, 11, 12, 10], [1, 0, 1, 6, 2, 9, 3, 4, 5, 10, 11, 13, 12, 9], [1, 0, 1, 7, 2, 3, 4, 5, 10, 11, 12, 8], [1, 0, 1, 6, 7, 2, 3, 4, 5, 10, 11, 12, 9], [9, 0, 1, 2, 2], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3], [13, 0, 2], [14, 0, 1, 2, 3, 3]], [[8, "btn_ty_tb1"], [9, "btn_ty_tb1", 33554432, [-5, -6, -7, -8, -9, -10], [[2, -3, [0, "1657nTijNEh7Bns5UYA0sV"], [5, 66, 84]], [26, 3, 0.9, -4, [0, "dfiQkwOqVBAJpdkOZwswOk"]]], [17, "03XfDFaqNOdp6SCwdhIQpD", null, null, -2, 0, [-1]]], [12, "img_hd_tips", false, 33554432, 1, [-13, -14], [[6, -11, [0, "55CKXCKcRMCKhJMbpbgeTh"], [5, 197, 117], [0, 1, 0.5]], [7, -12, [0, "2042uhVnBGfInmFA8PLxvy"], 2]], [1, "e0CYaRyHJHIKaSQIx3kp7L", null, null, null, 1, 0], [1, -42, 0, 0]], [25, ["03XfDFaqNOdp6SCwdhIQpD"]], [10, "img_bq_tqk1", false, 33554432, 1, [-17], [[2, -15, [0, "5cV35K25pOpr4Li3P9KGaq"], [5, 102, 101]], [7, -16, [0, "006xdEoTVDk7LxH3VPr/vV"], 0]], [1, "79B6utQj1JlpnKGpF7f/Zc", null, null, null, 1, 0], [1, -22, 28, 0], [3, 0, 0, 0.10452846326765347, 0.9945218953682733], [1, 0.5, 0.5, 1], [1, 0, 0, 12]], [11, "Label", 33554432, 4, [[2, -18, [0, "4bMYhJ77hP4ZgIKeq2NLcW"], [5, 170, 69]], [20, "兑换期", 40, 50, 3, true, true, 3, -19, [0, "18z2kwvFRN250HtLspuqVf"], [4, 4279308648]], [22, "btn_ty_tb1_Label_2", -20, [0, "0eCyjdUXtARoaDiE6y1b1b"]]], [1, "73h9Hm/eNM2oUvxoHRSk7D", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [5, "btn_tab_1", 33554432, 1, [[2, -21, [0, "89Kmzp7M5JRJxKSraI3F2C"], [5, 79, 80]], [14, false, -22, [0, "43Z+zpMHNH95/fghPESQqh"]]], [1, "5eZAD6dg1PkIthJGeTXCdA", null, null, null, 1, 0]], [5, "img_ty_jt", 33554432, 1, [[2, -23, [0, "62A7e9F2FKLqhbhnm909P7"], [5, 105, 108]], [15, 2, false, -24, [0, "1dyDVFaRtBTYa66kKi3PVs"]]], [1, "77SxDon5ZJoJUh/zsu54vj", null, null, null, 1, 0]], [4, "txt_tab_1", 33554432, 1, [[6, -25, [0, "77Mt/A7RxGrJ0Q7zFDQxkY"], [5, 185, 80], [0, 0.5, 1]], [18, "", 0, 35, 34, 36, 2, true, true, 3, -26, [0, "bc648ctydDD5l0O0o5vV7i"], [4, 4278453288]]], [1, "5cvq8qCo5I7pXjdLcFwDsb", null, null, null, 1, 0], [1, 0, -19, 0], [1, 0.5, 0.5, 1]], [4, "txt_tab_djs", 33554432, 1, [[2, -27, [0, "84zOyLXrZNGrwGvqq7uEtm"], [5, 180, 56.4]], [19, "", 32, 32, 2, false, true, true, 3, -28, [0, "b4vWX0DApIwYUF+QPtdycW"], [4, 4283095295], [4, 4278453288]]], [1, "53BaSFbsBBXaOTrvJVtgp7", null, null, null, 1, 0], [1, 0, -12, 0], [1, 0.5, 0.5, 1]], [4, "txt_ts", 33554432, 2, [[2, -29, [0, "10B/i7WO1CK5XTFlKkx2Hb"], [5, 347, 53.88]], [21, "活动提示文本", 36, 36, 38, 3, true, true, 3, -30, [0, "0dFKYDm3tKBYCao9EcT43W"], [4, 4280229916]]], [1, "8fWTXgn51KJ4jW1t0TLbJK", null, null, null, 1, 0], [1, -103.172, 25.811, 0], [1, 0.5, 0.5, 1]], [13, 0, {}, 2, [16, "03XfDFaqNOdp6SCwdhIQpD", null, null, -31, [23, "53sKJm/RhKoaQ3trZJXraW", 1, [[24, "btn_ty_75", ["_name"], 3], [3, ["_lpos"], 3, [1, -103.172, -22.859, 0]], [3, ["_lrot"], 3, [3, 0, 0, 0, 1]], [3, ["_euler"], 3, [1, 0, 0, 0]], [3, ["_lscale"], 3, [1, 0.8, 0.8, 1]]]], 1]]], 0, [0, -1, 11, 0, 2, 1, 0, 0, 1, 0, 0, 1, 0, -1, 6, 0, -2, 7, 0, -3, 8, 0, -4, 9, 0, -5, 4, 0, -6, 2, 0, 0, 2, 0, 0, 2, 0, -1, 10, 0, -2, 11, 0, 0, 4, 0, 0, 4, 0, -1, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 2, 11, 0, 4, 1, 31], [0, 0, 0], [1, 3, 1], [0, 1, 2]]